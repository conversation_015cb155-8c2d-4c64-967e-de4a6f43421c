<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.baidu.bailing.sql.exe</groupId>
  <artifactId>lib</artifactId>
  <name>lib</name>
  <version>0.0.1-SNAPSHOT</version>
  <description>lib</description>
  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>
    </resources>
    <finalName>sql-exe-lib</finalName>
    <plugins>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <args>
                <arg>-dependencyfile</arg>
                <arg>${project.build.directory}/.scala_dependencies</arg>
              </args>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.3</version>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
          <finalName>sql-exe-lib</finalName>
          <appendAssemblyId>false</appendAssemblyId>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.9.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-core_2.11</artifactId>
      <version>*******-baidu-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-sql_2.11</artifactId>
      <version>*******-baidu-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>5.1.8</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <distributionManagement>
    <repository>
      <id>Baidu_Local</id>
      <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
    </repository>
    <snapshotRepository>
      <id>Baidu_Local_Snapshots</id>
      <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <source.encoding>UTF-8</source.encoding>
    <spark.baidu.version>*******-baidu-SNAPSHOT</spark.baidu.version>
    <slf4j.version>1.7.16</slf4j.version>
    <scala.version>2.12.12</scala.version>
    <java.version>1.8</java.version>
    <hadoop.version>2.7.7</hadoop.version>
    <spark.version>2.4.2</spark.version>
  </properties>
</project>
