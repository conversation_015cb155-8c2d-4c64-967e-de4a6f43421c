package com.baidu.sql.utils

import TimeFormat._

import java.text.SimpleDateFormat
import java.util.Calendar
import java.time.{Duration, Instant}


/**
  * <AUTHOR>
  *         Description: 时间操作函数
  */
object TimeOperateUtil {

  /**
    * Description: 获取当前时间
    **/
  def getCurrentTime: java.util.Date = {
    val cal = Calendar.getInstance()
    cal.getTime
  }

  /**
   * Description: 获取当前时间字符串，输出日期格式自定义,默认yyyyMMdd
   * @param dateFormat  自定义日期格式：默认YYYYMMDD
   * @return
   */
  def getCurrentTimeStr(dateFormat:String = DAY_FORMAT): String = {
    val cal = Calendar.getInstance()
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }


  /**
   * * Description: 获取当前时间毫秒级时间戳
   * @return
   */
  def currentTimestampMillis(take:Int = 13): Long = {
    Instant.now().toEpochMilli.toString.take(take).toLong
  }

  /**
   * Description: 计算num天之后的时间戳
   * @param num 天数
   * @return Long num天之后的毫秒级时间戳
   */
  def timestampAfterDays(num: Long): Long = {
    val currentMillis = currentTimestampMillis()
    val daysInMillis = Duration.ofDays(num).toMillis
    currentMillis + daysInMillis
  }

  /**
   * Description: 豪秒级时间戳，默认13位时间戳
   *
   * @param dataDate 输入日期的字符串
   * @param dateFormat  自定义日期格式：默认YYYYMMDD
   * @return 转换后时间戳
   */
  def getTimeStamp(dates: String,dateFormat:String = DAY_FORMAT): Long = {
    val date = new SimpleDateFormat(dateFormat).parse(dates)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(date)
    cal.getTimeInMillis
  }

  /**
   * Description: 豪秒级时间戳，截取前几位，默认13位时间戳
   * @param dataDate 输入日期的字符串
   * @param dateFormat  自定义日期格式：默认YYYYMMDD
   * @return 转换后时间戳
   */
  def getTimeStampTake(dates: String,take:Int,dateFormat:String = DAY_FORMAT): Long = {
    val date = new SimpleDateFormat(dateFormat).parse(dates)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(date)
    val timestamp = cal.getTimeInMillis.toString // 将时间戳转换为字符串
    if (take >= timestamp.length) {
      timestamp.toLong // 如果take大于或等于时间戳字符串的长度，则直接返回整个字符串
    } else {
      timestamp.take(take).toLong // 否则，返回前take个字符的字符串
    }
  }

  /**
   * Description: 根据时间戳获取日期字符串
   * @param timeStamp 时间戳
   * @param dateFormat 自定义日期格式：默认YYYYMMDD
   * @return
   */
  def getDateTime(timeStamp:String,dateFormat:String = DAY_FORMAT): String = {
    val DATE_FORMAT = new SimpleDateFormat(dateFormat)
    DATE_FORMAT.format(timeStamp.toLong)
  }

  /**
   * Description: 计算前后n天日期,并输出yyyyMMdd型的日期字符串
   *
   * @param date  : 输入时间，字符串格式为：yyyyMMdd
   * @param dateFormat  自定义日期格式：默认YYYYMMDD
   * @param range : 时间范围
   * @return 转换后日期
   **/
  def calcnDate(dates: String, range:Int = 0,dateFormat:String = DAY_FORMAT) = {
    val date = new SimpleDateFormat(dateFormat).parse(dates)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, range)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
   * Description: 计算前后n天日期,并输出yyyyMMdd型的日期字符串
   *
   * @param date  : 输入时间，字符串格式为：yyyyMMdd
   * @param dateFormat  自定义日期格式：默认YYYY-MM-DD
   * @param range : 时间范围
   * @return 转换后日期
   **/
  def calcnDateFormat(dates: String, range:Int = 0,dateInFormat:String = DAY_FORMAT,dataOutFormat:String = DAY_FORMAT_MYSQL) = {
    val date = new SimpleDateFormat(dateInFormat).parse(dates)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, range)
    new SimpleDateFormat(dataOutFormat).format(cal.getTime)
  }

  /**
    * Description: 获取n小时前后冻结时间(yyyy-MM-dd HH:mm:00)
    *
    * @param paDate : 输入时间
    * @param range  : 分钟范围
    * @return n小时前后冻结日期
    **/
  def getFrozenHourTime(paDate: String, range: Int = 0): java.util.Date = {
    val date = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(paDate)
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.HOUR_OF_DAY, range)
    cal.set(Calendar.SECOND, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.getTime
  }

  /**
    * Description: 获取n日前后冻结日期(yyyy-MM-dd 00:00:00)
    *
    * @param paDate : 输入时间
    * @param range  : 时间范围
    * @return n日前后冻结日期
    **/
  def getFrozenTime(paDate: String, range: Int = 0): java.util.Date = {
    val date = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(paDate)
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, range)
    cal.set(Calendar.HOUR_OF_DAY, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.set(Calendar.SECOND, 0)
    cal.getTime
  }

  /**
    * Description: 获取n月前后冻结日期(yyyy-MM-dd 00:00:00)
    *
    * @param paDate : 输入时间
    * @param range  : 时间范围
    * @return n月前后冻结日期
    **/
  def getMonthFrozenTime(paDate: String, range: Int = 0): java.util.Date = {
    val date = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(paDate)
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.MONTH, range)
    cal.set(Calendar.HOUR_OF_DAY, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.set(Calendar.SECOND, 0)
    cal.getTime
  }

  /**
    * Description: 获取n年前后冻结日期(yyyy-MM-dd 00:00:00)
    *
    * @param paDate : 输入时间
    * @param range  : 时间范围
    * @return n年前后冻结日期
    **/
  def getYearFrozenTime(paDate: String, range: Int = 0): java.util.Date = {
    val date = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(paDate)
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.YEAR, range)
    cal.set(Calendar.HOUR_OF_DAY, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.set(Calendar.SECOND, 0)
    cal.getTime
  }

  /**
   * Description: 计算前后n天日期,并输出自定义的日期字符串,默认格式为yyyyMMdd
   *
   * @param date  : 输入时间
   * @param dateFormat:日期格式，默认格式为yyyyMMdd
   * @param range : 时间范围
   * @return 转换后日期字符串
   **/
  def calcnday(date: java.util.Date, range: Int = 0,dateformat:String = DAY_FORMAT) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, range)
    new SimpleDateFormat(dateformat).format(cal.getTime)
  }

  /**
    * Description: 计算前后n月日期，并输出Date
    *
    * @param date  : 输入时间
    * @param dateFormat:日期格式，默认格式为yyyyMMdd
    * @param range : 时间范围
    * @return 转换后日期字符串
    **/
  def calcnmonth(date: java.util.Date,range: Int = 0,dateFormat:String = DAY_FORMAT) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.MONTH, range)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
   * Description: 计算前后n年日期,并输出自定义的日期字符串
   * @param paDate : 输入时间
   * @param dateFormat : 自定义日期格式：默认YYYYMMDD
   * @param range : 时间范围
   * @return
   */
  def calcYear(paDate: java.util.Date,range: Int = 0,dateFormat:String = DAY_FORMAT): String = {
    val cal = Calendar.getInstance()
    cal.setTime(paDate)
    cal.add(Calendar.YEAR, range)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
    * Description: 计算日期所在月第一天日期
    *
    * @param date : 输入时间
    * @param dateFormat:日期格式，默认格式为yyyyMMdd
    * @return 转换后日期字符串
    **/
  def calcdayfirst(date: java.util.Date,dateFormat:String = DAY_FORMAT) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.set(Calendar.DAY_OF_MONTH, 1)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
    * Description: 计算日期所在月最后一天日期
    *
    * @param date : 输入时间
    * @param dateFormat:日期格式，默认格式为yyyyMMdd
    * @return 转换后日期字符串
    **/
  def calcdaylast(date: java.util.Date,dateFormat:String = DAY_FORMAT) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.MONTH, 1)
    cal.set(Calendar.DAY_OF_MONTH, 0)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
    * Description: 计算日期所在年第一天,输出yyyyMMdd型结果
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calcfirstdayyear(date: java.util.Date) = {
    new SimpleDateFormat(YEAR_FORMAT).format(date) + "0101"
  }

  /**
    * Description: 计算日期所在年第一天,输出yyyy-MM-dd型结果
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calcfirstdayyearora(date: java.util.Date) = {
    new SimpleDateFormat(YEAR_FORMAT).format(date) + "-01-01"
  }

  /**
    * Description: 计算日期所在年最后一天,输出yyyyMM型结果
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayyear(date: java.util.Date) = {
    new SimpleDateFormat(YEAR_FORMAT).format(date) + "1231"
  }

  /**
    * Description: 计算日期所在年最后一天,输出yyyy-MM-dd型结果
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayyearora(date: java.util.Date) = {
    new SimpleDateFormat(YEAR_FORMAT).format(date) + "-12-31"
  }


  /**
    * Description: 计算日期所在季度的字符串，格式YYYYSS，2019年第一季度表示为201901，
    * 其余季度按时间顺序依次为201902-201904
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def getSeasonStr(date: java.util.Date): String = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH)
    if (month >= 0 && month <= 2) {
      s"${year}01"
    } else if (month >= 3 && month <= 5) {
      s"${year}02"
    } else if (month >= 6 && month <= 8) {
      s"${year}03"
    } else if (month >= 9 && month <= 11) {
      s"${year}04"
    } else {
      null
    }
  }

  /**
    * Description: 计算日期所在季度的第一天的日期字符串，格式YYYYMMDD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calcfirstdayseason(date: java.util.Date): String = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH)
    if (month >= 0 && month <= 2) {
      s"${year}0101"
    } else if (month >= 3 && month <= 5) {
      s"${year}0401"
    } else if (month >= 6 && month <= 8) {
      s"${year}0701"
    } else if (month >= 9 && month <= 11) {
      s"${year}1001"
    } else {
      null
    }
  }

  /**
    * Description: 计算日期所在季度的第一天的日期字符串，格式YYYY-MM-DD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calcfirstdayseasonora(date: java.util.Date): String = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH)
    if (month >= 0 && month <= 2) {
      s"${year}-01-01"
    } else if (month >= 3 && month <= 5) {
      s"${year}-04-01"
    } else if (month >= 6 && month <= 8) {
      s"${year}-07-01"
    } else if (month >= 9 && month <= 11) {
      s"${year}-10-01"
    } else {
      null
    }
  }

  /**
    * Description: 计算日期所在季度的最后一天的日期字符串，格式YYYYMMDD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayseason(date: java.util.Date): String = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH)
    if (month >= 0 && month <= 2) {
      s"${year}0331"
    } else if (month >= 3 && month <= 5) {
      s"${year}0630"
    } else if (month >= 6 && month <= 8) {
      s"${year}0930"
    } else if (month >= 9 && month <= 11) {
      s"${year}1231"
    } else {
      null
    }
  }

  /**
    * Description: 计算日期所在季度的最后一天的日期字符串，格式YYYY-MM-DD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayseasonora(date: java.util.Date): String = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH)
    if (month >= 0 && month <= 2) {
      s"${year}-03-31"
    } else if (month >= 3 && month <= 5) {
      s"${year}-06-30"
    } else if (month >= 6 && month <= 8) {
      s"${year}-09-30"
    } else if (month >= 9 && month <= 11) {
      s"${year}-12-31"
    } else {
      null
    }
  }

  /**
    * Description: 计算日期所在周的星期一的日期字符串
    *
    * @param date : 输入时间
    * @param dateFormat : 输入时间格式，默认YYYYMMDD
    * @return 转换后日期字符串
    **/
  def calcfirstdayweek(date: java.util.Date,dateFormat:String = DAY_FORMAT) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
    new SimpleDateFormat(dateFormat).format(cal.getTime)
  }

  /**
    * Description: 计算日期所在周的星期日的日期字符串，格式YYYYMMDD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayweek(date: java.util.Date) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, 7)
    cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
    new SimpleDateFormat(DAY_FORMAT).format(cal.getTime)
  }

  /**
    * Description: 计算日期所在周的星期日的日期字符串，格式YYYY-MM-DD
    *
    * @param date : 输入时间
    * @return 转换后日期字符串
    **/
  def calclastdayweekora(date: java.util.Date) = {
    val cal = Calendar.getInstance()
    cal.setTime(date)
    cal.add(Calendar.DATE, 7)
    cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
    new SimpleDateFormat(DAY_FORMAT_MYSQL).format(cal.getTime)
  }


  /**
    * Description: 获取两天时间之差(两天时间均为String类型)，start为开始时间，end为结束时间
    *
    * @param start : 开始时间字符串
    * @param end   : 结束时间字符串
    * @return 日期天数差值
    **/
  def calcDateInterval(start: String, end: String): java.lang.Integer = {
    var interval: java.lang.Integer = null
    var startDate: java.util.Date = null
    var endDate: java.util.Date = null
    if (start != null && end != null) {
      start match {
        case oradate if (oradate.matches(ORA_DATE_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01") < 0 && start.compareTo("1900-01-01") > 0)
              startDate = new SimpleDateFormat(DAY_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oraminute if (oraminute.matches(ORA_MINUTE_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00") < 0 && start.compareTo("1900-01-01 00:00") > 0)
              startDate = new SimpleDateFormat(MIN_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oratime if (oratime.matches(ORA_TIME_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00:00") < 0 && start.compareTo("1900-01-01 00:00:00") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oratimestamp if (oratimestamp.matches(ORA_TIMESTAMP_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00:00.000") < 0 && start.compareTo("1900-01-01 00:00:00.000") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(start.split("\\.")(0))
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case date if (date.matches(DATE_PATTERN) == true) => {
          try {
            if (start.compareTo("21000101") < 0 && start.compareTo("19000101") > 0)
              startDate = new SimpleDateFormat(DAY_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case minute if (minute.matches(MINUTE_PATTERN) == true) => {
          try {
            if (start.compareTo("210001010000") < 0 && start.compareTo("190001010000") > 0)
              startDate = new SimpleDateFormat(MIN_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case time if (time.matches(TIME_PATTERN) == true) => {
          try {
            if (start.compareTo("21000101000000") < 0 && start.compareTo("19000101000000") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case _ => startDate = null
      }
      end match {
        case oradate if (oradate.matches(ORA_DATE_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01") < 0 && end.compareTo("1900-01-01") > 0)
              endDate = new SimpleDateFormat(DAY_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oraminute if (oraminute.matches(ORA_MINUTE_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00") < 0 && end.compareTo("1900-01-01 00:00") > 0)
              endDate = new SimpleDateFormat(MIN_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oratime if (oratime.matches(ORA_TIME_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00:00") < 0 && end.compareTo("1900-01-01 00:00:00") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oratimestamp if (oratimestamp.matches(ORA_TIMESTAMP_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00:00.000") < 0 && end.compareTo("1900-01-01 00:00:00.000") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(end.split("\\.")(0))
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case date if (date.matches(DATE_PATTERN) == true) => {
          try {
            if (end.compareTo("21000101") < 0 && end.compareTo("19000101") > 0)
              endDate = new SimpleDateFormat(DAY_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case minute if (minute.matches(MINUTE_PATTERN) == true) => {
          try {
            if (end.compareTo("210001010000") < 0 && end.compareTo("190001010000") > 0)
              endDate = new SimpleDateFormat(MIN_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case time if (time.matches(TIME_PATTERN) == true) => {
          try {
            if (end.compareTo("21000101000000") < 0 && end.compareTo("19000101000000") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case _ => endDate = null
      }
      interval =
        if (startDate == null || endDate == null) {
          null
        } else {
          (endDate.getTime / (1000 * 60 * 60 * 24l)).toInt - (startDate.getTime / (1000 * 60 * 60 * 24l)).toInt
        }
    } else {
      interval = null
    }
    interval
  }

  /**
    * Description: 获取两个时间点之间的差(两天时间均为String类型)，start为开始时间，end为结束时间，结果精确到秒
    *
    * @param start : 开始时间字符串
    * @param end   : 结束时间字符串
    * @return 日期秒数差值
    **/
  def calcTimeInterval(start: String, end: String): BigDecimal = {
    var interval: BigDecimal = null
    var startDate: java.util.Date = null
    var endDate: java.util.Date = null
    if (start != null && end != null) {
      start match {
        case oradate if (oradate.matches(ORA_DATE_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01") < 0 && start.compareTo("1900-01-01") > 0)
              startDate = new SimpleDateFormat(DAY_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oraminute if (oraminute.matches(ORA_MINUTE_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00") < 0 && start.compareTo("1900-01-01 00:00") > 0)
              startDate = new SimpleDateFormat(MIN_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oratime if (oratime.matches(ORA_TIME_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00:00") < 0 && start.compareTo("1900-01-01 00:00:00") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case oratimestamp if (oratimestamp.matches(ORA_TIMESTAMP_PATTERN) == true) => {
          try {
            if (start.compareTo("2100-01-01 00:00:00.000") < 0 && start.compareTo("1900-01-01 00:00:00.000") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(start.split("\\.")(0))
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case date if (date.matches(DATE_PATTERN) == true) => {
          try {
            if (start.compareTo("21000101") < 0 && start.compareTo("19000101") > 0)
              startDate = new SimpleDateFormat(DAY_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case minute if (minute.matches(MINUTE_PATTERN) == true) => {
          try {
            if (start.compareTo("210001010000") < 0 && start.compareTo("190001010000") > 0)
              startDate = new SimpleDateFormat(MIN_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case time if (time.matches(TIME_PATTERN) == true) => {
          try {
            if (start.compareTo("21000101000000") < 0 && start.compareTo("19000101000000") > 0)
              startDate = new SimpleDateFormat(SEC_FORMAT).parse(start)
            else
              startDate = null
          } catch {
            case e: Exception => {
              startDate = null
            }
          }
        }
        case _ => startDate = null
      }
      end match {
        case oradate if (oradate.matches(ORA_DATE_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01") < 0 && end.compareTo("1900-01-01") > 0)
              endDate = new SimpleDateFormat(DAY_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oraminute if (oraminute.matches(ORA_MINUTE_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00") < 0 && end.compareTo("1900-01-01 00:00") > 0)
              endDate = new SimpleDateFormat(MIN_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oratime if (oratime.matches(ORA_TIME_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00:00") < 0 && end.compareTo("1900-01-01 00:00:00") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case oratimestamp if (oratimestamp.matches(ORA_TIMESTAMP_PATTERN) == true) => {
          try {
            if (end.compareTo("2100-01-01 00:00:00.000") < 0 && end.compareTo("1900-01-01 00:00:00.000") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT_MYSQL).parse(end.split("\\.")(0))
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case date if (date.matches(DATE_PATTERN) == true) => {
          try {
            if (end.compareTo("21000101") < 0 && end.compareTo("19000101") > 0)
              endDate = new SimpleDateFormat(DAY_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case minute if (minute.matches(MINUTE_PATTERN) == true) => {
          try {
            if (end.compareTo("210001010000") < 0 && end.compareTo("190001010000") > 0)
              endDate = new SimpleDateFormat(MIN_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case time if (time.matches(TIME_PATTERN) == true) => {
          try {
            if (end.compareTo("21000101000000") < 0 && end.compareTo("19000101000000") > 0)
              endDate = new SimpleDateFormat(SEC_FORMAT).parse(end)
            else
              endDate = null
          } catch {
            case e: Exception => {
              endDate = null
            }
          }
        }
        case _ => endDate = null
      }
      interval =
        if (startDate == null || endDate == null) {
          null
        } else {
          BigDecimal(endDate.getTime - startDate.getTime) / BigDecimal(1000)
        }
    } else {
      interval = null
    }
    interval
  }


  /**
    * Description: 获取当前时间点所在指定时间段的后N个时间段(分钟)的起止时刻，
    * 如8:08以15分钟为分段时间取前一个时间段的开始时间应取得7:45
    *
    * @param curTime   : 当前时间
    * @param statCycle : 分段时间
    * @param delay     : 延时
    * @return 结束时刻
    **/
  def formatNowDateMin(curTime: java.util.Date, statCycle: Int = 15, delay: Int = 0): String = {
    // 获取对应时间 = 当前时间 + 延时 * 时间段(ms)的时间字符串
    val corpTime = new java.util.Date(curTime.getTime + delay * statCycle * 60 * 1000l)
    val corpTimeStr = new SimpleDateFormat(MIN_FORMAT).format(corpTime)
    // 获取该时刻对应分钟
    val corpMinuteStr = corpTimeStr.substring(corpTimeStr.length - 2)
    val minInt = corpMinuteStr.toInt / statCycle * statCycle
    val minStr = if (minInt < 10) "0" + minInt.toString else "" + minInt.toString
    corpTimeStr.substring(0, corpTimeStr.length() - 2) + minStr
  }

}
