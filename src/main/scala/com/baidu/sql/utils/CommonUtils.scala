package com.baidu.sql.utils

import com.baidu.sql.customized.keyproduct.commons.{EsConf, MysqlConf}
import com.baidu.sql.utils.PropertiesUtils.ESUfoProperties
import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}
import org.elasticsearch.action.search.{ClearScrollRequest, ClearScrollResponse, SearchRequest, SearchResponse, SearchScrollRequest}
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.unit.TimeValue
import org.elasticsearch.index.query.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryBuilders}
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.elasticsearch.search.{Scroll, SearchHit}
import org.elasticsearch.spark.rdd.EsSpark
import org.elasticsearch.spark.sql.EsSparkSQL

import java.io.IOException
import java.time.{LocalDateTime, ZoneId}
import java.time.format.DateTimeFormatter
import java.sql.{Connection, DriverManager, ResultSet}
import java.util.Properties
import scala.collection.JavaConverters.propertiesAsScalaMapConverter
import scala.collection.mutable.ListBuffer

object CommonUtils {
  val properties: Properties = new Properties()

  /**
   * 判断Mysql数据库中是否存在此表
   *
   * @param url
   * @param user
   * @param password
   * @param tableName 查询的表名
   * @return
   */
  def checkTableExists(
                        properties: Properties,
                        tableName: String): Boolean = {
    var exists = false
    var connection: Connection = null
    try {
      // 建立数据库连接
      connection = DriverManager.getConnection(properties.getProperty("url"), properties.getProperty("user"), properties.getProperty("password"))
      val statement = connection.createStatement
      // 查询表是否存在
      val resultSet: ResultSet = statement.executeQuery(s"SHOW TABLES LIKE '$tableName'")
      if (resultSet.next()) {
        exists = true
      }
    } catch {
      case e: Exception =>
        e.printStackTrace()
    } finally {
      if (connection != null) {
        try {
          connection.close()
        } catch {
          case e: Exception => e.printStackTrace()
        }
      }
    }
    exists
  }

  /**
   * 读取Mysql的链接
   *
   * @param spark      SparkSession
   * @param properties url 数据库链接地址 user用户名 password密码
   * @param query      查询sql语句
   * @return
   */
  def mysqlOperate(
                    spark: SparkSession,
                    properties: Properties,
                    query: String): DataFrame = {
    // 参数校验
    require(properties.containsKey("url"), "Property 'url' is required.")
    require(properties.containsKey("user"), "Property 'user' is required.")
    require(properties.containsKey("password"), "Property 'password' is required.")
    require(query.nonEmpty, "Query string cannot be empty.")

    try {
      spark.read
        .format("jdbc")
        .option("url", properties.getProperty("url"))
        // 更新驱动类名，以匹配最新的MySQL Connector/J
        .option("driver",MysqlConf.Driver)
        .option("user", properties.getProperty("user"))
        .option("password", properties.getProperty("password"))
        .option("query", query)
        .load()
    } catch {
      case e: Exception =>
        throw new RuntimeException("Error occurred while reading from MySQL.", e)
    }
  }

  /**
   * MySQL数据写入方法
   *
   * @param spark      SparkSession
   * @param properties 包含url、user、password的连接配置
   * @param df         要写入的DataFrame
   * @param tableName  目标表名
   * @param mode 写入模式，默认为SaveMode.Overwrite
   */
  def mysqlWriteOperate(
                         spark: SparkSession,
                         properties: Properties,
                         df: DataFrame,
                         tableName: String,
                         mode: SaveMode = SaveMode.Overwrite): Unit = {

    // 参数校验
    require(properties.containsKey("url"), "Property 'url' is required.")
    require(properties.containsKey("user"), "Property 'user' is required.")
    require(properties.containsKey("password"), "Property 'password' is required.")
    require(tableName.nonEmpty, "Table name cannot be empty.")

    try {
      if (mode == SaveMode.Overwrite){ // 覆盖模式写入
        df.write
          .format("jdbc")
          .option("url", properties.getProperty("url"))
          .option("dbtable", tableName)
          .option("user", properties.getProperty("user"))
          .option("password", properties.getProperty("password"))
          .option("truncate", "true")       // 清空表后再写入
          .mode(mode)         // 覆盖模式写入
          .save()
      }else { // 追加模式写入
        df.write
          .format("jdbc")
          .option("url", properties.getProperty("url"))
          .option("dbtable", tableName)
          .option("user", properties.getProperty("user"))
          .option("password", properties.getProperty("password"))
          .mode(mode)
          .save()
      }
    } catch {
      case e: Exception =>
        throw new RuntimeException("MySQL write operation failed", e)
    }
  }

  /**
   * 读取Mysql的链接，并按照id进行分区
   * @param spark
   * @param properties
   * @param query
   * @param tableName
   * @return
   */
  def mysqlOperatePartition(spark: SparkSession,
                            properties: Properties,
                            query: String,
                            tableName: String,
                            numPartitions:Int): DataFrame = {
    // 参数校验
    require(properties.containsKey("url"), "Property 'url' is required.")
    require(properties.containsKey("user"), "Property 'user' is required.")
    require(properties.containsKey("password"), "Property 'password' is required.")
    require(query.nonEmpty, "Query string cannot be empty.")

    try {
      // 从原始查询中提取WHERE条件（去掉SELECT部分）
      val condition = query.split("(?i)WHERE")(1).trim
      println("condition:" + condition)

      // 优化后的boundaryQuery：添加时间过滤条件
      val boundaryQuery =
        s"""(SELECT
           |  FLOOR(MIN(id)/1000)*1000 as lower,
           |  CEIL(MAX(id)/1000)*1000 as upper
           |FROM ${tableName}
           |WHERE ${condition}) as tmp""".stripMargin

      val bounds = spark.read.jdbc(properties.getProperty("url"), boundaryQuery, properties).first()
      val lowerBound = bounds.getAs[java.math.BigDecimal](0).longValue()
      val upperBound = bounds.getAs[java.math.BigDecimal](1).longValue()

      // 手动创建分区谓词（结合时间条件+ID范围）
      //val numPartitions = 3
      val step = (upperBound - lowerBound) / numPartitions
      val predicates = Array.tabulate(numPartitions) { i =>
        val start = lowerBound + i * step
        val end = lowerBound + (i + 1) * step
        s"($condition) AND (id >= $start AND id < $end)"
      }

      spark.read.jdbc(
        url = properties.getProperty("url"),
        table = tableName,
        predicates = predicates,
        connectionProperties = properties
      )
    } catch {
      case e: Exception =>
        throw new RuntimeException("Error occurred while reading from MySQL.", e)
    }
  }

  /**
   * 从mysql数据库中删除数据
   * @return
   */
  def deleteMysql(properties: Properties,query:String)= {
    var connection: java.sql.Connection = null
    try {
      connection = java.sql.DriverManager.getConnection(
        properties.getProperty("url"),
        properties.getProperty("user"),
        properties.getProperty("password")
      )
      val statement = connection.createStatement()
      statement.executeUpdate(query)
    }catch {
      case e: Exception => println(s"mysql deleteMysqlTable error:${e}")
    }finally {
      connection.close()
    }
  }

  /**
   *
   * @param spark      SparkSession
   * @param indexName  ES索引名
   * @param properties ES的配置信息，包含host，port，user，password
   * @param query      查询ES的语句
   * @return RDD[(String, String)]
   */
  def ElasticSearchOperate(
                            spark: SparkSession,
                            indexName: String,
                            properties: Properties,
                            query: String): RDD[(String, String)] = {

    // 参数校验
    require(indexName != null && !indexName.isEmpty, "Index name cannot be null or empty.")
    require(properties != null, "Properties cannot be null.")
    require(properties.containsKey("host"), "Hosts property is required.")
    require(properties.containsKey("port"), "Port property is required.")
    require(properties.containsKey("user"), "User property is required.")
    require(properties.containsKey("password"), "Password property is required.")

    try {
      val options = Map(
        "es.nodes.wan.only" -> "true",
        "es.nodes" -> properties.getProperty("host"),
        "es.port" -> properties.getProperty("port"),
        "es.net.http.auth.user" -> properties.getProperty("user", ""), // 提供默认值以避免NullPointerException
        "es.net.http.auth.pass" -> properties.getProperty("password", ""), // 提供默认值以避免NullPointerException
        "inferSchema" -> "true",
        "es.read.field.as.array.include" -> ""
      )
      // 使用JavaEsSpark替代EsSpark以支持更新版本的Elasticsearch和Spark连接库（如果EsSpark是旧版本的话）
      EsSpark.esJsonRDD(spark.sparkContext, indexName, query, options)
    } catch {
      case e: Exception => {
        // 异常处理逻辑，可以记录日志或抛出更具体的异常信息
        throw new RuntimeException(s"Failed to read from Elasticsearch: ${e.getMessage}", e)
      }
    }
  }

  /**
   * 创建es客户端，ES版本在5.*版本时使用
   * @param properties 配置文件信息
   * @return RestHighLevelClient
   */
  def createElasticsearchClient(properties: Properties): RestHighLevelClient = {
    val host = properties.getProperty("host")
    val port = properties.getProperty("port").toInt
    val user = properties.getProperty("user")
    val password = properties.getProperty("password")

    // 创建RestHighLevelClient客户端
    val lowLevelRestClient: RestClientBuilder = RestClient.builder(new HttpHost(host, port, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    // 设置用户名和密码
    credentialsProvider.setCredentials(
      AuthScope.ANY,
      new UsernamePasswordCredentials(user, password)
    )
    // 配置客户端
    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    // 创建客户端并返回
    new RestHighLevelClient(lowLevelRestClient)
  }

  /**
   * 使用游标的方式读取ES中的数据，ES版本在5.*版本时使用
   * @param startTimeStamp 开始时间戳
   * @param endTimeStamp 结束时间戳
   * @param indexName     索引名
   * @param client 客户端
   * @param boolQueryBuilder 查询条件
   * @return ListBuffer[String]
   */
  def getScrollData(startTimeStamp: Long,
                    endTimeStamp: Long,
                    indexName: String,
                    client: RestHighLevelClient,
                    boolQueryBuilder: BoolQueryBuilder): ListBuffer[String] = {
    val list: ListBuffer[String] = ListBuffer[String]()
    // 创建查询请求
    val searchRequest: SearchRequest = new SearchRequest(indexName)
    val searchSourceBuilder: SearchSourceBuilder = new SearchSourceBuilder
    // 设置查询条件
    searchSourceBuilder.query(boolQueryBuilder)
    searchRequest.source(searchSourceBuilder)
    // 设置分页
    searchSourceBuilder.size(500)
    searchRequest.source(searchSourceBuilder)
    // 设置滚动
    searchRequest.scroll(TimeValue.timeValueMinutes(5L))
    try {
      var searchResponse: SearchResponse = client.search(searchRequest, RequestOptions.DEFAULT)
      var scrollId: String = searchResponse.getScrollId
      var hits: Array[SearchHit] = searchResponse.getHits.getHits
      hits.foreach(searchHit => {
        list.append(searchHit.getSourceAsString)
      })
      val scroll: Scroll = new Scroll(TimeValue.timeValueMinutes(5L))
      // 循环读取数据
      while (hits.length > 0) {
        val scrollRequest: SearchScrollRequest = new SearchScrollRequest(scrollId)
        scrollRequest.scroll(scroll)
        searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT)
        scrollId = searchResponse.getScrollId
        hits = searchResponse.getHits.getHits
        hits.foreach(searchHit => {
          list.append(searchHit.getSourceAsString)
        })
      }
      // 创建清理滚动
      val clearScrollRequest: ClearScrollRequest = new ClearScrollRequest
      // 设置滚动id
      clearScrollRequest.addScrollId(scrollId)
      // 清理滚动
      val clearScrollResponse: ClearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT)
      val succeeded: Boolean = clearScrollResponse.isSucceeded
    } catch {
      case e: IOException =>
        // TODO Auto-generated catch block
        e.printStackTrace()
    }
    list
  }

  /**
   * 读取MongoDB数据库的数据
   *
   * @param spark
   * @param properties 所有配置文件信息
   * @return
   */
  def mongoDbOperate(spark: SparkSession,
                     properties: Properties): DataFrame = {

    // 参数校验
    val requiredProperties = List("uri", "database", "collection")
    requiredProperties.foreach {
      prop =>
        require(properties.containsKey(prop), s"Property '$prop' is required.")
    }

    try {
      // 使用properties动态设置所有选项
      val reader = spark.read.format("com.mongodb.spark.sql.DefaultSource") // 根据你的Connector版本，可能需要更改这里的格式
      properties.asScala.foreach { case (key, value) =>
        reader.option(key, value)
      }
      // 加载数据并返回DataFrame
      reader.load()
    } catch {
      case e: Exception =>
        throw new RuntimeException("Error occurred while reading from MongoDB.", e)
    }
  }
}
