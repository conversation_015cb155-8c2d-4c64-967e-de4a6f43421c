package com.baidu.sql.utils

/**
  * <AUTHOR>
  *         Description: 日期格式
  */
object TimeFormat extends Serializable {

  //时间格式字符串
  final val SEC_FORMAT_MYSQL = "yyyy-MM-dd HH:mm:ss"
  final val SEC_FORMAT = "yyyyMMddHHmmss"
  final val DAY_FORMAT = "yyyyMMdd"
  final val DAY_FORMAT_MYSQL = "yyyy-MM-dd"
  final val MONTH_FORMAT = "yyyyMM"
  final val MONTH_FORMAT_MYSQL = "yyyy-MM"
  final val YEAR_FORMAT = "yyyy"
  final val MIN_FORMAT = "yyyyMMddHHmm"
  final val MIN_FORMAT_MYSQL = "yyyy-MM-dd HH:mm"

  //时间匹配表达式,不做详细校验
  final val YEAR_PATTERN = "[0-9]{4}"
  final val ORA_MONTH_PATTERN = "[0-9]{4}-[0-9]{2}"
  final val ORA_DATE_PATTERN = "[0-9]{4}-[0-9]{2}-[0-9]{2}"
  final val ORA_HOUR_PATTERN = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}"
  final val ORA_MINUTE_PATTERN = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}"
  final val ORA_TIME_PATTERN = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}"
  final val ORA_TIMESTAMP_PATTERN = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}.[0-9]{1,3}"
  final val MONTH_PATTERN = "[0-9]{4}[0-9]{2}"
  final val DATE_PATTERN = "[0-9]{4}[0-9]{2}[0-9]{2}"
  final val HOUR_PATTERN = "[0-9]{4}[0-9]{2}[0-9]{2}[0-9]{2}"
  final val MINUTE_PATTERN = "[0-9]{4}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}"
  final val TIME_PATTERN = "[0-9]{4}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}"
}
