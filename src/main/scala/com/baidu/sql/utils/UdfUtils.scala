package com.baidu.sql.utils

import com.baidu.sql.utils.SparkUtils._
import com.baidu.sql.utils.TimeOperateUtil._
import TimeFormat._
import com.alibaba.fastjson.JSON
import org.apache.spark.sql.functions._

import scala.concurrent.Await
import scala.concurrent.duration.DurationInt

/**
 * 自定义udf函数公共类
 */
object UdfUtils {

  /**
   * 通过电话号码获取用户id
   */
  val getUserId = udf((phoneNumber: String) => {
    if (phoneNumber == null || phoneNumber.isEmpty || phoneNumber.equals("")) {
      ""
    }else{
      val userid = getUserIdByPhone(phoneNumber)
      if (userid.equals("0") || userid == null) "" else userid
    }
  })

  // JSON有效性验证UDF
  val json_format_valid = udf((jsonStr: String) => {
    try {
      JSON.parseObject(jsonStr)
      true
    } catch {
      case _: Exception => false
    }
  })

  /**
   * 普通数值空值判断
   */
  val getNullVal = udf((data: String) => {
    if (data == null || data.isEmpty || data.equals(",,")  || data.equals("--")) {
      ""
    } else {
      data
        .replaceAll("[\\t\\r]", "")
        .replaceAll("\\\\t", "")
        .replaceAll("\\\\\\\\t", "")
        .replaceAll("\\\\n", "")
        .replaceAll("\\\\r", "")
        .replaceAll("\\\\\\\\", "'")
        .replaceAll("\\\\\"", "")
        .replaceAll("\\n", " ")
    }
  })

  /**
   * 时间戳转换日期格式,默认格式为yyyy-MM-dd HH:mm:ss
   */
  val getDateTimeVal = udf((timestamp: String) => {
    if (timestamp == null || timestamp.isEmpty || timestamp.equals("")) {
      ""
    } else {
      getDateTime(timestamp,SEC_FORMAT_MYSQL)
    }
  })

  /**
   * 去除电话里的特殊符号
   */
  val dealSpecial = udf((data:String) => {
    if(data.equals("")){
      ""
    }else{
      //使用正则表达式替换所有非数字字符为空字符串
      val phone = data.replaceAll("[^0-9]", "")
      if(phone.startsWith("0")) phone.substring(1) else phone
    }
  })

  /**
   * 判断ip地址是否是内网IP，包含IPv4和IPv6
   */
  val isPrivateIPUDF = udf((ip: String) => isPrivateIP(ip))

  /**
   * 清理除键盘输入以外的特殊字符
   */
  val cleanChars = udf(cleanKeyboardChars _)

  /**
   * 将json字符串转换为标准json字符串
   */
  val convertToStandardJson = udf((json: String)  => {
    if (json == null || json.isEmpty) {
      ""
    } else {
      try {
        val jsonObj = json.replace("\\\"", "\"") // 去除键名中的转义字符
          .replace("\\\\", "\\")
          .replace("\\/", "/")
          .replace("\\b", "\b")
          .replace("\\f", "\f")
          .replace("\\n", "\n")
          .replace("\\r", "\r")
          .replace("\\t", "\t")
        JSON.parseObject(jsonObj).toJSONString
      } catch {
        case _: Exception => "json转换失败"
      }
    }
  })

}
