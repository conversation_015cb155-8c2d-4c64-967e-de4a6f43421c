package com.baidu.sql.utils

object JDBCUtils {
  val user = "feedback_admin"
  val password = "c942e54d88243bc79468fdc79913259c"
  val url = "***********************************************************************************************************************************************************************************************************"
  val driver = "com.mysql.jdbc.Driver"

  // 引擎线上库
  val strategyUser = "work"
  val strategyPassword = "Asd123"
  val strategyUrl = "**************************************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"

  val strategyTestUser = "work"
  val strategyTestPassword = "Asd123"
  val strategyTestUrl = "**********************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"

  var quiryUrl: String = "***************************************************************?" +
    "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
    "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
  var quiryUser = "feedback_wr"
  var quiryPassword = "c942e54d88243bc79468fdc79913259c"

}
