package com.baidu.sql.utils
import com.baidu.sql.customized.keyproduct.commons.{EsConf, MysqlConf}
import java.util.Properties

object PropertiesUtils {
  //热线Mysql数据库连接
  val MysqlHyccaCustomerProperties = new Properties()
  MysqlHyccaCustomerProperties.setProperty("url",MysqlConf.HuccaCustomerUrl)
  MysqlHyccaCustomerProperties.setProperty("user",MysqlConf.HuccaCustomerUser)
  MysqlHyccaCustomerProperties.setProperty("password",MysqlConf.HuccaCustomerPassword)

  val MysqlWorkFlowProperties = new Properties()
  MysqlWorkFlowProperties.setProperty("url",MysqlConf.WorkFlowUrl)
  MysqlWorkFlowProperties.setProperty("user",MysqlConf.WorkFlowUser)
  MysqlWorkFlowProperties.setProperty("password",MysqlConf.WorkFlowPassword)

  val MysqlRobotNewProperties = new Properties()
  MysqlRobotNewProperties.setProperty("url",MysqlConf.RobotNewUrl)
  MysqlRobotNewProperties.setProperty("user",MysqlConf.RobotNewUser)
  MysqlRobotNewProperties.setProperty("password",MysqlConf.RobotNewPassword)

  val MysqlHyccaProperties = new Properties()
  MysqlHyccaProperties.setProperty("url",MysqlConf.HyccaUrl)
  MysqlHyccaProperties.setProperty("user",MysqlConf.HyccaUser)
  MysqlHyccaProperties.setProperty("password",MysqlConf.HyccaPassword)

  //在线Mysql数据库连接
  val MysqlFollowProperties = new Properties()
  MysqlFollowProperties.setProperty("url",MysqlConf.FollowUrl)
  MysqlFollowProperties.setProperty("user",MysqlConf.FollowUser)
  MysqlFollowProperties.setProperty("password",MysqlConf.FollowPassword)

  //Duke版权系统Mysql数据库连接
  val MysqlDukeProperties = new Properties()
  MysqlDukeProperties.setProperty("url",MysqlConf.DukeOnlineUrl)
  MysqlDukeProperties.setProperty("user",MysqlConf.DukeOnlineUser)
  MysqlDukeProperties.setProperty("password",MysqlConf.DukeOnlinePassword)

  //在线Mysql产品线配置查询
  val MysqlFollowIdProperties = new Properties()
  MysqlFollowIdProperties.setProperty("url",MysqlConf.FollowIdUrl)
  MysqlFollowIdProperties.setProperty("user",MysqlConf.FollowIdUser)
  MysqlFollowIdProperties.setProperty("password",MysqlConf.FollowIdPassword)

  //风铃Mysql数据库连接
  val MysqlFengLingProperties = new Properties()
  MysqlFengLingProperties.setProperty("url",MysqlConf.FengLingUrl)
  MysqlFengLingProperties.setProperty("user",MysqlConf.FengLingUser)
  MysqlFengLingProperties.setProperty("password",MysqlConf.FengLingPassword)

  //客服Mysql数据库连接
  val MysqlKeFuProperties = new Properties()
  MysqlKeFuProperties.setProperty("url",MysqlConf.KeFuUrl)
  MysqlKeFuProperties.setProperty("user",MysqlConf.KeFuUser)
  MysqlKeFuProperties.setProperty("password",MysqlConf.KeFuPassword)

  //QA数据库连接
  val MysqlQaProperties = new Properties()
  MysqlQaProperties.setProperty("url",MysqlConf.QaUrl)
  MysqlQaProperties.setProperty("user",MysqlConf.QaUser)
  MysqlQaProperties.setProperty("password",MysqlConf.QaPassword)

  //在线ES数据库连接
  val ESImProperties = new Properties()
  ESImProperties.setProperty("user",EsConf.ImUser)
  ESImProperties.setProperty("password",EsConf.ImPassword)
  ESImProperties.setProperty("host",EsConf.ImHost)
  ESImProperties.setProperty("port",EsConf.ImPort.toString)

  //热线ES数据库连接
  val ESDukeProperties = new Properties()
  ESDukeProperties.setProperty("user",EsConf.DukeUser)
  ESDukeProperties.setProperty("password",EsConf.DukePassword)
  ESDukeProperties.setProperty("host",EsConf.DukeHost)
  ESDukeProperties.setProperty("port",EsConf.DukePort.toString)

  //Duke版权系统ES数据库连接
  val ESDukeBanQuanProperties = new Properties()
  ESDukeBanQuanProperties.setProperty("user",EsConf.DukeBanQuanUser)
  ESDukeBanQuanProperties.setProperty("password",EsConf.DukeBanQuanPassword)
  ESDukeBanQuanProperties.setProperty("host",EsConf.DukeBanQuanHost)
  ESDukeBanQuanProperties.setProperty("port",EsConf.DukeBanQuanPort.toString)

  //迟滞UFO的ES数据库连接
  val ESUfoProperties = new Properties()
  ESUfoProperties.setProperty("user",EsConf.UfoUser)
  ESUfoProperties.setProperty("password",EsConf.UfoPassword)
  ESUfoProperties.setProperty("host",EsConf.UfoHost)
  ESUfoProperties.setProperty("port",EsConf.UfoPort.toString)

  //阿拉丁数据库连接
  val MysqlALaDingProperties = new Properties()
  MysqlALaDingProperties.setProperty("url",MysqlConf.ALaDingUrl)
  MysqlALaDingProperties.setProperty("user",MysqlConf.ALaDingUser)
  MysqlALaDingProperties.setProperty("password",MysqlConf.ALaDingPassword)

  //阿拉丁线上数据库连接
  val MysqlALaDingOnlineProperties = new Properties()
  MysqlALaDingOnlineProperties.setProperty("url",MysqlConf.ALaDingOnlineUrl)
  MysqlALaDingOnlineProperties.setProperty("user",MysqlConf.ALaDingOnlineUser)
  MysqlALaDingOnlineProperties.setProperty("password",MysqlConf.ALaDingOnlinePassword)

  //电商线上库连接
  val MysqlDianShangProperties = new Properties()
  MysqlDianShangProperties.setProperty("url",MysqlConf.DianShangUrl)
  MysqlDianShangProperties.setProperty("user",MysqlConf.DianShangUser)
  MysqlDianShangProperties.setProperty("password",MysqlConf.DianShangPassword)

  //风铃库，查询的电商物流短信表
  val MysqlFenglingCaseProperties = new Properties()
  MysqlFenglingCaseProperties.setProperty("url",JDBCUtils.quiryUrl)
  MysqlFenglingCaseProperties.setProperty("user",JDBCUtils.quiryUser)
  MysqlFenglingCaseProperties.setProperty("password",JDBCUtils.quiryPassword)

  //新回声库，查询的热线ES工单的问题分类字段
  val MysqlXinHuiShengProperties = new Properties()
  MysqlXinHuiShengProperties.setProperty("url",MysqlConf.XinHuiShengUrl)
  MysqlXinHuiShengProperties.setProperty("user",MysqlConf.XinHuiShengUser)
  MysqlXinHuiShengProperties.setProperty("password",MysqlConf.XinHuiShengPassword)

  //风控策略引擎线上库
  val MysqlFengKongProperties = new Properties()
  MysqlFengKongProperties.setProperty("url",MysqlConf.FengKongUrl)
  MysqlFengKongProperties.setProperty("user",MysqlConf.FengKongUser)
  MysqlFengKongProperties.setProperty("password",MysqlConf.FengKongPassword)

  //风控风险召回线上库
  val MysqlFengKongZhaoHuiProperties = new Properties()
  MysqlFengKongZhaoHuiProperties.setProperty("url",MysqlConf.FengKongZhaoHuiUrl)
  MysqlFengKongZhaoHuiProperties.setProperty("user",MysqlConf.FengKongZhaoHuiUser)
  MysqlFengKongZhaoHuiProperties.setProperty("password",MysqlConf.FengKongZhaoHuiPassword)

  //风控feed模型数据库
  val MysqlFeedAuditProperties = new Properties()
  MysqlFeedAuditProperties.setProperty("url",MysqlConf.FeedAuditUrl)
  MysqlFeedAuditProperties.setProperty("user",MysqlConf.FeedAuditUser)
  MysqlFeedAuditProperties.setProperty("password",MysqlConf.FeedAuditPassword)

  //风控feed模型数据库只读权限
  val MysqlFeedAuditReadProperties = new Properties()
  MysqlFeedAuditReadProperties.setProperty("url",MysqlConf.FeedAuditReadUrl)
  MysqlFeedAuditReadProperties.setProperty("user",MysqlConf.FeedAuditReadUser)
  MysqlFeedAuditReadProperties.setProperty("password",MysqlConf.FeedAuditReadPassword)

}
