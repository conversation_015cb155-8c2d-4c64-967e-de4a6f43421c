package com.baidu.sql.utils

import com.alibaba.fastjson.JSON
import com.baidu.bailing.lib.ral.dto.QueryString
import com.baidu.bailing.lib.ral.util.UrlTargetUtil
import com.baidu.noah.naming.{BNSClient, BNSInstance}
import com.baidu.sql.utils.TimeOperateUtil.currentTimestampMillis
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.hadoop.conf.Configuration
import org.apache.spark.sql.types.StringType
import org.apache.spark.sql.functions.udf
import org.json4s._
import org.json4s.jackson.JsonMethods._
import scala.util.Try
import java.math.BigInteger
import java.net.{<PERSON><PERSON><PERSON><PERSON><PERSON>, URI, URLDecoder}
import java.security.MessageDigest
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util
import java.util.concurrent.atomic.AtomicLong
import java.util.zip.CRC32
import scala.collection.mutable.ListBuffer
import scala.concurrent.{Await, ExecutionContext, Future, Promise}
import scala.concurrent.duration._
import scala.util.{Failure, Random, Success, Try}
import java.util.concurrent.{Executors, Semaphore, TimeUnit}


object SparkUtils {
  val user = JDBCUtils.strategyTestUser
  val url = JDBCUtils.strategyTestUrl
  val password = JDBCUtils.strategyTestPassword
  val driver = JDBCUtils.driver

  //日期格式
  val DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd")

  //下面是通过用户手机号调用bns接口获取userid
  var apId= 1252
  var otpKey= "FuTH5ZVQzgGxzIzUyaRc4txbxaDqsdkPDjsGo7R9aAzFJfNP9qORKRyL9xlEr1V7"
  var passTokenVersion = 1
  var passKeyVersion = 1

  //匹配userid的bns地址
  var passgateHost= "bns://group.smartbns-from_product=ess-passgate%group.flow-npg.passport.all:http"
  //var passgateHost= "bns://group.smartbns-from_product=ess-session%group.flow-ssngate.passport.all:http"

  //follow获取操作日志的bns地址
  var followUrl = "bns://group.opera-AccessRTBTask-follow-all.EP-BRAVO.all"

  //电商赔付推送数据的bns地址
  val dianShangUrl = "bns://group.epbravo-riskChannelEcom.EP-BRAVO.all"
  //var passgateHost= "http://***********:8300"
  //var sessionHost = "http://***********:8937"
  var appUser= "ufo"
  var appPasswd= "ufo"
  val authToken = getOtpAuthToken(apId, otpKey, passTokenVersion, passKeyVersion)
  val okHttpClient = new OkHttpClient

  def concatAllId (string: String): String = {
    "(" + string + ")"
  }

  def getJdbcDf (spark: SparkSession, fetchMysql: String): DataFrame = {
    spark.read.format("jdbc")
      .option("url", url)
      .option("user", user)
      .option("password", password)
      .option("driver", driver)
      .option("query", fetchMysql)
      .load()
  }

  def sink2Afs(spark: SparkSession, fileName: String, json: String): Unit = {
    val fs = FileSystem.get(new URI("afs://pegasus.afs.baidu.com:9902"), spark.sparkContext.hadoopConfiguration)
    // afs输出路径
    val outputPath = "/user/baisheng/tethysEngine/profile/detail/"
    val outputAfsPath = new Path(outputPath, s"${fileName}.json")
    // 设置为true 如果有数据覆盖掉
    val outputStream = fs.create(outputAfsPath)
    outputStream.write(json.getBytes("UTF-8"))
    outputStream.close()
  }

  /**
   * 通过手机号获取userid的接口
   * @param key 手机号
   * @return userid
   */
  def getUserIdByPhone(key:String): String = {
    if (key == null || key.length() == 0) {
      ""
    }else{
      var response: Response = null
      var res = ""
      var userid = ""
      val url = getBnsUrl(authToken, key)
      val request = new Request.Builder()
        .url(url)
        .build()

      try {
        response = okHttpClient.newCall(request).execute
        if (response.isSuccessful) {
          res = response.body.string
        } else {
          res = "Empty response"
        }
      } catch {
        case _: Exception =>
          res = "Empty response"
      } finally if (response != null) response.close()

      if (res == null || res.length() == 0 || res.equals("Empty response")) {
        userid = ""
      } else {
        val jsonObject = JSON.parseObject(res)

        if (jsonObject.containsKey("err_no") && jsonObject.getInteger("err_no") == 0) {
          val tmp = jsonObject.getJSONObject("result_params").getJSONArray("userid")
          //println("tmp:" + tmp)
          if (tmp == null || tmp.isEmpty) {
            userid = ""
          } else {
            userid = tmp.get(0).toString
          }
        }
      }
      userid
    }
  }

  /**
   * 获取bns的url地址，用于调用接口获取userid
   * @param authToken
   * @param key
   * @return
   */
  def getBnsUrl(authToken:String,key:String): String = {
    val url = UrlTargetUtil.buildUrl(passgateHost, "/passgate", QueryString.build()
      .addQsKv("service_name", "userinfo_all")
      .addQsKv("method", "batget")
      .addQsKv("app_user", this.appUser)
      .addQsKv("app_passwd", this.appPasswd)
      .addQsKv("inter_encoding", "utf8")
      .addQsKv("auth_type", "otp")
      .addQsKv("auth_token", authToken)
      .addQsKv("securemobil", URLDecoder.decode("[\"" + key + "\"]", "UTF-8"))
      .addQsKv("req_fields", URLDecoder.decode("[\"username\",\"userid\",\"displayname\"]", "UTF-8")))
    url
  }

  def getOtpAuthToken(appId: Int, otpKey: String, passTokenVersion: Int, passKeyVersion: Int): String = {
    val time = System.currentTimeMillis() / 1000L
    val originOtp = otpKey + time + appId
    val crc32 = new CRC32()
    crc32.update(originOtp.getBytes())
    val otp = crc32.getValue()

    val tokenArr = new Array[Byte](12)
    // 使用Scala的方式替换 System.arraycopy
    Array.copy(reverseArr(short2Byte(passTokenVersion)), 0, tokenArr, 0, 2)
    Array.copy(reverseArr(short2Byte(passKeyVersion)), 0, tokenArr, 2, 2)
    Array.copy(reverseArr(long2Byte(time)), 0, tokenArr, 4, 4)
    Array.copy(reverseArr(long2Byte(otp)), 0, tokenArr, 8, 4)

    byte2Hex(tokenArr)
  }

  def reverseArr(src: Array[Byte]): Array[Byte] = {
    val dest = new Array[Byte](src.length)

    for (i <- 0 until src.length) {
      dest(src.length - 1 - i) = src(i)
    }

    dest
  }

  def short2Byte(s: Int): Array[Byte] = {
    val dest = new Array[Byte](2)
    for (i <- 0 until 2) {
      val offset = 16 - (i + 1) * 8
      dest(i) = ((s >> offset) & 255).toByte
    }
    dest
  }

  def long2Byte(a: Long): Array[Byte] = {
    Array[Byte](
      ((a >> 56) & 0xFF).toByte,
      ((a >> 48) & 0xFF).toByte,
      ((a >> 40) & 0xFF).toByte,
      ((a >> 32) & 0xFF).toByte,
      ((a >> 24) & 0xFF).toByte,
      ((a >> 16) & 0xFF).toByte,
      ((a >> 8) & 0xFF).toByte,
      (a & 0xFF).toByte
    )
  }

  def byte2Hex(b: Array[Byte]): String = {
    val stringBuilder = new StringBuilder()
    var tmp: String = ""

    for (n <- b.indices) {
      tmp = Integer.toHexString(b(n) & 255)
      if (tmp.length() == 1) {
        stringBuilder.append("0").append(tmp)
      } else {
        stringBuilder.append(tmp)
      }
    }

    tmp = null
    stringBuilder.toString().toLowerCase()
  }

  /**
   * 获取bns地址，使用BNSClient的方式
   * @param spark
   * @param bnsurl group.opera-risk-riskChannelEcom-000-bj.ECommerceRisk.all
   * @return 177634080:2009（ip：port）
   */
  def getBnsClient(spark: SparkSession,bnsurl:String):String  = {
    val client = new BNSClient()
    val timeOut = 3000
    val status = 0
    var instances:util.List[BNSInstance] = null
    var addressList = new ListBuffer[String]()
      try {
        instances = client.getInstanceByService(bnsurl, timeOut)
      } catch {
        case e: Exception => println("load bns exp." + e.getMessage)
      }
      if (null == instances || instances.isEmpty()) {
        println("bns {} ins is empty, please check.", bnsurl);
        return ""
      }

      instances.forEach(x => {
        if (x.getStatus == status){
          addressList.append(x.getIp + ":" + x.getPort)
        }else{
          println("ins {}:{} status not normal.", x.getDottedIP(), x.getPort())
        }
      })
    // 返回第一个地址
    addressList(0)
  }

  /**
   * 安全删除hdfs路径，单个文件的删除
   * @param hdfsPath HDFS路径
   * @param conf 配置
   */
  def safeDeleteHdfsPath(hdfsPath: String, conf: Configuration): Unit = {
    val fs = FileSystem.get(conf)
    val path = new Path(hdfsPath)
    try {
      if (fs.exists(path)) {
        val success = fs.delete(path, true)
        if (success) {
          println(s"成功删除: $hdfsPath")
        } else {
          println(s"删除失败: $hdfsPath")
        }
      } else {
        println(s"路径不存在: $hdfsPath")
      }
    } catch {
      case e: Exception =>
        println(s"删除异常: ${e.getMessage}")
    } finally {
      fs.close()
    }
  }

  /**
   * * 解析bns地址,获取httpurl
   */
  def getHttpurl(url:String = followUrl,lastUrl:String = ""): String = {
    var httpurl = ""
    val ipPattern =
      "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}:\\d{4}";

    val addressList = new ListBuffer[String]
    //获取ip,如果ip则直接使用，否则通过bns获取
    if (url.matches(ipPattern)) {
      addressList.append(url)
      httpurl = "http://" + addressList.head + lastUrl
    } else {
      var urlhttp = ""
      try {
        urlhttp = UrlTargetUtil.getHttpPath(url)
      } catch {
        case e: Exception => println("load bns exp." + e.getMessage)
      }

      if (urlhttp.length > 0) {
        httpurl = urlhttp + lastUrl
      }
    }
    println("解析后的httpurl:" + httpurl)
    httpurl
  }

  //下面是判断IP是否是内网IP，包含IPv4和IPv6
  // 预定义私有IP范围
  private val ipv4Ranges = List(
    cidrToRangeIPv4("10.0.0.0/8"),
    cidrToRangeIPv4("**********/12"),
    cidrToRangeIPv4("***********/16"),
    cidrToRangeIPv4("*********/8"),
    cidrToRangeIPv4("***********/16")
  )

  private val ipv6Ranges = List(
    cidrToRangeIPv6("fc00::/7"),
    cidrToRangeIPv6("fe80::/10"),
    cidrToRangeIPv6("::1/128")
  )

  // 主判断逻辑
  def isPrivateIP(ip: String): Boolean = {
    if (ip.equals("") || ip.equals("-")){
      true
    }else{
      val inetAddress = InetAddress.getByName(ip)
      inetAddress.isLoopbackAddress || checkIPv4(inetAddress) || checkIPv6(inetAddress)
    }
  }

  // IPv4检查
  private def checkIPv4(inetAddress: InetAddress): Boolean = {
    if (!inetAddress.isInstanceOf[java.net.Inet4Address]) return false

    val bytes = inetAddress.getAddress
    val ipLong = ((bytes(0) & 0xFFL) << 24) |
      ((bytes(1) & 0xFFL) << 16) |
      ((bytes(2) & 0xFFL) << 8) |
      (bytes(3) & 0xFFL)

    ipv4Ranges.exists { case (start, end) =>
      ipLong >= start && ipLong <= end
    }
  }

  // IPv6检查
  private def checkIPv6(inetAddress: InetAddress): Boolean = {
    if (!inetAddress.isInstanceOf[java.net.Inet6Address]) return false

    val bytes = inetAddress.getAddress
    val ipBigInt = new BigInteger(1, bytes)

    ipv6Ranges.exists { case (start, end) =>
      ipBigInt.compareTo(start) >= 0 && ipBigInt.compareTo(end) <= 0
    }
  }

  // IPv4 CIDR转换工具
  private def cidrToRangeIPv4(cidr: String): (Long, Long) = {
    val parts = cidr.split("/")
    val ip = parts(0)
    val prefix = parts(1).toInt

    val mask = (0xFFFFFFFFL << (32 - prefix)) & 0xFFFFFFFFL
    val ipLong = ipToLong(ip)
    val start = ipLong & mask
    val end = start | (~mask & 0xFFFFFFFFL)
    (start, end)
  }

  // IPv6 CIDR转换工具
  private def cidrToRangeIPv6(cidr: String): (BigInteger, BigInteger) = {
    val parts = cidr.split("/")
    val ip = parts(0)
    val prefix = parts(1).toInt

    val inetAddress = InetAddress.getByName(ip)
    val bytes = inetAddress.getAddress
    val ipBigInt = new BigInteger(1, bytes)

    val mask = BigInteger.ONE.shiftLeft(128 - prefix).subtract(BigInteger.ONE).not()
    val start = ipBigInt.and(mask)
    val end = start.add(mask.not().add(BigInteger.ONE))
    (start, end)
  }

  // IPv4地址转Long
  private def ipToLong(ip: String): Long = {
    val bytes = InetAddress.getByName(ip).getAddress
    ((bytes(0) & 0xFF) << 24) |
      ((bytes(1) & 0xFF) << 16) |
      ((bytes(2) & 0xFF) << 8) |
      (bytes(3) & 0xFF)
  }

  /**
   * 如果df1不为空，则合并df1和df2，否则直接返回df2
   * @param df1
   * @param df2
   * @return
   */
  def unionIfNotEmpty(df1: DataFrame, df2: DataFrame): DataFrame = {
    if (!df2.isEmpty) {
      df1.unionByName(df2)
    } else {
      println(s"${df2}数据集为空，直接返回")
      df1
    }
  }

  /**
   * 核心过滤逻辑（线程安全且无副作用）
   * @param input 输入字符串
   * @return 过滤后的字符串
   */
  def cleanKeyboardChars(input: String): String = {
    if (input == null) return ""  // 处理空值

    // 定义允许的字符正则表达式（精确匹配键盘可见字符）
    val allowedCharsPattern =
      """[^a-zA-Z0-9""" +       // 基础字母数字
        """~!@#$%^&*()_+""" +     // 第一行符号
        """{}|:"<>?`""" +         // 第二行符号（注意转义）
        """\[\]\\;',./-=]"""      // 方括号、反斜杠等特殊处理

    // 执行过滤（使用预编译正则表达式更高效）
    input.replaceAll(allowedCharsPattern, "")
  }

  /**
   * 写入Excel文件（线程安全且无副作用）
   * @param data DataFrame
   * @param mode 保存模式（默认：overwrite）
   * @param filePath 文件路径
   * @param maxStringLength 超长文本截断长度（默认：32767）
   */
  def writeExcel(
                  data: DataFrame,
                  filePath: String,
                  mode: String = "overwrite",
                  maxStringLength: Int = 32767,
                  sheetName: Option[String] = None,
                  overwriteSheet: Boolean = false  // 新增参数控制是否覆盖同名sheet
                ): Unit = {
    // 参数有效性校验
    if (mode != "overwrite" && mode != "append" && mode != "ignore") {
      throw new IllegalArgumentException(s"Invalid save mode: $mode. Only 'overwrite', 'append' and 'ignore' are supported for Excel.")
    }

    if (data == null) {
      throw new IllegalArgumentException("DataFrame cannot be null")
    }

    // 空数据安全处理
    if (data.isEmpty) {
      println(s"DataFrame is empty, skipping write operation for $filePath")
      return
    }

    // 路径安全处理
    val file = new java.io.File(filePath)
    val parentDir = file.getParentFile
    if (parentDir != null && !parentDir.exists()) {
      if (!parentDir.mkdirs()) {
        throw new RuntimeException(s"Failed to create directories for: ${parentDir.getAbsolutePath}")
      }
    }

    // 将所有列转换为string类型并处理超长文本
    val convertedDF = data.schema.fields.foldLeft(data) { (tempDF, field) =>
      tempDF
        .withColumn(field.name, regexp_replace(col(field.name), "^[=@+\\-]", ""))
        .withColumn(field.name,
          when(
            length(col(field.name)) > maxStringLength, {
              println(s"Truncating column ${field.name} (length: ${length(col(field.name))}) to $maxStringLength chars")
              substring(col(field.name), 1, maxStringLength)
            }
          ).otherwise(col(field.name))
        )
        .withColumn(field.name,
          when(
            col(field.name).isNull,
            lit("")
          ).otherwise(col(field.name).cast(StringType))
        )
    }

    // 构建基础选项
    val baseOptions = Map(
      "header" -> "true",
      "timestampFormat" -> "yyyy-MM-dd HH:mm:ss",
      "dateFormat" -> "yyyyMMdd",
      "ignoreFormula" -> "true",
      "addEmptyColumns" -> "false"
    )

    // 添加sheet名称选项（如果提供）
    val finalOptions = sheetName match {
      case Some(name) =>
        val sanitizedSheetName = name.replaceAll("[\\\\/:*?\"\\[\\]']", "_")
        baseOptions + ("dataAddress" -> s"$sanitizedSheetName!A1")
      case None => baseOptions
    }

    // 执行写入操作（带异常处理）
    try {
      val writer = convertedDF.write
        .format("com.crealytics.spark.excel")
        .options(finalOptions)

      // 处理追加模式
      if (mode == "append") {
        val fileExists = new java.io.File(filePath).exists()

        if (fileExists && overwriteSheet && sheetName.isDefined) {
          // 覆盖模式：先删除文件再写入，实现覆盖同名sheet
          writer.mode("overwrite")
        } else if (fileExists) {
          // 标准追加模式
          writer.mode("append")
        } else {
          // 文件不存在时使用覆盖模式
          writer.mode("overwrite")
        }
      } else {
        writer.mode(mode)
      }

      writer.save(filePath)

      // 添加详细日志
      val actionMsg = if (overwriteSheet && sheetName.isDefined) {
        s"overwritten sheet '${sheetName.get}' in"
      } else if (sheetName.isDefined) {
        s"appended to sheet '${sheetName.get}' in"
      } else {
        "written to"
      }

      println(s"Successfully $actionMsg $filePath with ${convertedDF.count()} records")
    } catch {
      case e: org.apache.spark.sql.AnalysisException =>
        throw new RuntimeException(s"Spark SQL Analysis Error: ${e.getMessage}", e)
      case e: java.io.IOException =>
        throw new RuntimeException(s"I/O Error writing to $filePath: ${e.getMessage}", e)
      case e: Exception =>
        throw new RuntimeException(s"Unexpected error writing Excel file: ${e.getMessage}", e)
    }
  }


  /**
   * 读取Excel文件
 *
   * @param spark SparkSession实例
   * @param path Excel文件路径
   * @param options 可选参数:
   *                - sheetName: 工作表名称(默认"Sheet1")
   *                - header: 是否使用表头(默认true)
   *                - inferSchema: 是否推断数据类型(默认true)
   *                - dateFormat: 日期格式(默认"yyyy-MM-dd")
   *                - timestampFormat: 时间戳格式(默认"yyyy-MM-dd HH:mm:ss")
   *                - Map(
   *                  "sheetName" -> "DataSheet",
   *                  "header" -> "true",
   *                  "inferSchema" -> "false"
   *                  )
   * @return 包含Excel数据的DataFrame
   */
  def readExcel(
                 spark: SparkSession,
                 path: String,
                 sheetName: Option[String] = None,  // 新增sheet名称参数
                 options: Map[String, String] = Map.empty
               ): DataFrame = {

    // 设置合并选项
    val mergedOptions = options ++ Map(
      "header" -> "true",
      "inferSchema" -> "true",
      "dateFormat" -> "yyyy-MM-dd",
      "timestampFormat" -> "yyyy-MM-dd HH:mm:ss"
    )

    // 添加sheet名称选项（如果提供）
    val finalOptions = sheetName match {
      case Some(name) =>
        // 清理非法字符
        val sanitizedSheetName = name.replaceAll("[\\\\/:*?\"\\[\\]']", "_")
        mergedOptions + ("sheetName" -> sanitizedSheetName)
      case None => mergedOptions
    }

    try {
      spark.read
        .format("com.crealytics.spark.excel")
        .options(finalOptions)
        .load(path)
    } catch {
      case e: java.io.FileNotFoundException =>
        throw new RuntimeException(s"Excel文件未找到: $path", e)
      case e: org.apache.spark.sql.AnalysisException if e.getMessage.contains("Sheet not found") =>
        throw new RuntimeException(s"Sheet页未找到: ${sheetName.getOrElse("默认sheet")}", e)
      case e: Exception =>
        throw new RuntimeException(s"读取Excel文件失败: ${e.getMessage}", e)
    }
  }

  /**
   * 将Parquet文件转换为Excel文件
   * @param spark SparkSession实例
   * @param inputPath Parquet文件路径
   * @param outputPath  Excel文件路径
   * @param maxStringLength 单个字符串的最大长度
   */
  def convertParquetToExcel(
                             spark: SparkSession,
                             inputPath: String,
                             outputPath: String
                           ): Unit = {
    // 参数有效性校验
    if (spark == null) throw new IllegalArgumentException("SparkSession cannot be null")
    if (inputPath == null || inputPath.trim.isEmpty) throw new IllegalArgumentException("Input path cannot be empty")
    if (outputPath == null || outputPath.trim.isEmpty) throw new IllegalArgumentException("Output path cannot be empty")

    var df: DataFrame = null
    try {
      // 读取Parquet文件（带异常处理）
      df = spark.read.parquet(inputPath)
      println(s"Successfully loaded Parquet file from: $inputPath")

      // 数据质量检查
      if (df.isEmpty) {
        throw new RuntimeException("Input Parquet file contains no data")
      }

      // 写入Excel配置优化
      writeExcel(df,outputPath)

    } catch {
      case e: org.apache.spark.sql.AnalysisException =>
        throw new RuntimeException(s"Spark解析失败: ${e.getMessage} | 输入路径: $inputPath", e)
      case e: java.io.FileNotFoundException =>
        throw new RuntimeException(s"文件未找到: ${if (e.getMessage.contains(inputPath)) inputPath else outputPath}", e)
      case e: java.io.IOException =>
        throw new RuntimeException(s"I/O错误: ${e.getMessage} | 输出路径: $outputPath", e)
      case e: Exception =>
        throw new RuntimeException(s"转换失败: ${e.getMessage}", e)
    } finally {
      // 资源清理
      if (df != null) df.unpersist(blocking = false)
      // 删除临时文件（根据实际需求调整）
      // safeDeleteHdfsPath(outputPath, spark.sparkContext.hadoopConfiguration)
    }
  }

  /**
   * 获取HDFS路径下最新日期目录的日期字符串
   * @param hdfsUri HDFS地址，例如 "hdfs://namenode:8020"
   * @param basePath 要扫描的基础路径，例如 "/data/daily"
   * @return 最新日期的字符串（格式：yyyyMMdd），如果没有找到则返回空字符串
   */
  def getLatestDateDir(hdfsUri: String, basePath: String): String = {
    val conf = new Configuration()
    conf.set("hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    var fs: FileSystem = null

    try {
      // 初始化HDFS文件系统
      fs = FileSystem.get(new URI(hdfsUri), conf)
      val baseDir = new Path(basePath)

      // 验证基础路径是否存在
      if (!fs.exists(baseDir)) {
        println(s"警告：基础路径不存在 $basePath")
        return ""
      }

      // 列出所有子目录
      val dirs = fs.listStatus(baseDir)

      // 用于存储有效日期目录
      var latestDate: Option[LocalDate] = None

      // 遍历所有子目录，解析日期
      for (status <- dirs if status.isDirectory) {
        val dirName = status.getPath.getName
        parseDate(dirName) match {
          case Some(date) =>
            // 比较并更新最新日期
            if (latestDate.isEmpty || date.isAfter(latestDate.get)) {
              latestDate = Some(date)
            }
          case None => // 忽略无效日期格式
        }
      }

      // 返回结果
      latestDate match {
        case Some(date) => date.format(DATE_FORMAT)
        case None =>
          println(s"警告：在 $basePath 下未找到有效日期目录")
          ""
      }

    } catch {
      case ex: Exception =>
        println(s"获取最新日期失败: ${ex.getMessage}")
        ex.printStackTrace()
        ""
    } finally {
      // 确保关闭文件系统连接
      if (fs != null) Try(fs.close())
    }
  }

  /**
   * 尝试解析日期字符串
   * @param dateStr 日期字符串
   * @return 解析成功的LocalDate对象
   */
  private def parseDate(dateStr: String): Option[LocalDate] = {
    Try(LocalDate.parse(dateStr, DATE_FORMAT)).toOption
  }

  /**
   * QPS控制器类
   * @param maxQps
   */
  class QpsController(maxQps: Int) extends Serializable {
    @transient lazy val semaphore = new Semaphore(maxQps)
    @transient lazy val lastRequestTime = new AtomicLong(0)

    def acquirePermit(): Unit = {
      semaphore.acquire()
      val currentTime = System.currentTimeMillis()
      val interval = 150 // 200ms间隔

      synchronized {
        val lastTime = lastRequestTime.get()
        val timeDiff = currentTime - lastTime
        if (timeDiff < interval) {
          Thread.sleep(interval - timeDiff)
        }
        lastRequestTime.set(System.currentTimeMillis())
      }
    }

    def releasePermit(): Unit = {
      semaphore.release()
    }
  }

}
