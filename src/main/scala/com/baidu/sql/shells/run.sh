#!/bin/bash
${nohup}  /home/<USER>/dc3/baidu/spark-2.4.2.4-baidu-bin-2.7.3.6-baidu/bin/spark-submit \
--class com.baidu.sql.lib.${mainClass} \
--conf spark.sql.hive.convertMetastoreOrc=false \
--conf spark.sql.hive.convertMetastoreParquet=false \
--conf spark.cores.max=${cores} \
--conf spark.task.maxFailures=${maxFailures} \
/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar ${sql_file} ${sql_desc} ${cores} ${maxFailures} ${sql_type} ${writeBackTable} ${ampersand}