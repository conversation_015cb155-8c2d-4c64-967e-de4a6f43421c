package com.baidu.sql.tds

import com.baidu.sql.utils.JDBCUtils
import org.apache.log4j.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession, functions}

import java.util.Properties

/**
 * <AUTHOR>
 */
object ReadMario2Mysql2Tds {
  def main(args: Array[String]): Unit = {
    val logger = Logger.getLogger(getClass.getName)

//    var YESTERDAY = sys.env("DATE")
    var YESTERDAY = args(0)

    YESTERDAY = YESTERDAY.substring(0, 4) + "-" + YESTERDAY.substring(4, 6) + "-" + YESTERDAY.substring(6, 8)
    logger.info(s"YESTERDAY { ${YESTERDAY} }")
    // 创建conf对象，指定参数
    val conf = new SparkConf().setAppName("ReadMario2Mysql")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()


    // TODO 0.测试库 后面要改线上库
    var driver = JDBCUtils.driver
    var url = JDBCUtils.strategyUrl
    var username = JDBCUtils.strategyUser
    var password = JDBCUtils.strategyPassword
    // TODO 1. 数据落地表
    var tb_table = "engine_strategy_experiment_result"

    var sourceMysql =
      """
        |select DISTINCT
        |  unique_code as strategy_id,
        |  `name` as strategy_name,
        |  DATE_FORMAT(create_time, '%Y-%m-%d') as create_time
        |from
        |  engine_strategy
        |where
        |  (unique_code, st_version) in (
        |    select
        |      st_unique_code,
        |      st_version
        |    from
        |      engine_st_run_config
        |    where
        |      is_delete = 0
        |  )
        |  and app_id = 11
        |  and is_delete = 0
        |""".stripMargin

    logger.info(s"sourceMysql { ${sourceMysql}")
    // 查找MySQL的数据
    var jdbcDf = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", username)
      .option("password", password)
      .option("query", sourceMysql).load()

    val properties = new Properties()
    properties.setProperty("driver", driver)
    properties.setProperty("url", url)
    properties.setProperty("user", username)
    properties.setProperty("password", password)

    // 需要捞全部数据
    var sql =
      s"""
         |with mario_source as (
         |  SELECT
         |    date_format(audit_time, 'yyyy-MM-dd') as c_month,
         |    mario_req,
         |    get_json_object(public_sentiment_out, '$$.name') as name,
         |    get_json_object(public_sentiment_out, '$$.desc') as descs,
         |    get_json_object(public_sentiment_out, '$$.res') as res,
         |    strategy_name
         |  FROM
         |    udw_ns.default.help_ods_engine_mario_publicsentiment_di
         |),
         |req_cnt as (
         |  SELECT
         |    c_month,
         |    count(1) as online_count
         |  FROM
         |    mario_source
         |  group by
         |    c_month
         |),
         |sent_cnt as (
         |  SELECT
         |    c_month,
         |    strategy_name,
         |    sum(
         |      if(
         |        name != ''
         |        and descs != ''
         |        and res != '',
         |        1,
         |        0
         |      )
         |    ) as online_hit_count
         |  FROM
         |    mario_source
         |  where strategy_name != ''
         |  group by
         |    c_month,strategy_name
         |)
         |SELECT
         |  sent_cnt.c_month as `current_date`,
         |  req_cnt.online_count,
         |  sent_cnt.online_hit_count,
         |  sent_cnt.strategy_name as strategy_name
         |FROM
         |  sent_cnt
         |LEFT JOIN req_cnt on sent_cnt.c_month = req_cnt.c_month
         |""".stripMargin

    logger.info(s"hSql { ${sql}")

    var hdf = spark.sql(sql)

    hdf = hdf.filter(hdf("current_date") === lit(YESTERDAY))


    val current_date = hdf.select(col("current_date"))
      .distinct().collect().mkString(",")
      .replaceAll("\\[", "")
      .replaceAll("\\]", "")

    logger.info(s"current_date { ${current_date}")

    val online_count = hdf.select(col("online_count"))
      .distinct().collect().mkString(",")
      .replaceAll("\\[", "")
      .replaceAll("\\]", "")

    logger.info(s"online_count { ${online_count}")


    // TODO 修改表数据 app_id, app_name, strategy_id, strategy_name
     hdf = jdbcDf.join(hdf, jdbcDf("strategy_name") === hdf("strategy_name"), "left")
        .withColumn("create_time", functions.current_timestamp())
        .withColumn("update_time", functions.current_timestamp())
        .withColumn("app_id", lit(11))
        .withColumn("app_name", lit("全网舆情回捞"))
        .withColumn("online_hit_count", functions.coalesce(hdf("online_hit_count"), lit(0)))
        .withColumn("current_date", lit(current_date))
        .withColumn("online_count", lit(online_count))
        .drop(hdf("strategy_name"))


    hdf.write.mode(SaveMode.Append).jdbc(url, tb_table, properties)

  }
}
