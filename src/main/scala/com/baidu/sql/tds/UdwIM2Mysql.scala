package com.baidu.sql.tds

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.vdurmont.emoji.EmojiParser
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * <AUTHOR>
 * @Date 2024-05-31
 */
object UdwIM2Mysql {

  val productList = List("大搜反馈","百度文库","百度APP","文心一言","百度贴吧","好看视频","任务系统","运营活动")

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YESTERDAY: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_im_2_mysql")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()
    import spark.implicits._

    val emo = udf((str: String) => {
      EmojiParser.removeAllEmojis(EmojiParser.parseToUnicode(str))
    })

    val result = spark.sql(
      s"""select
        |  product_line,
        |  session_content,
        |  case when submit_time = '' then null else submit_time end as submit_time,
        |  feedback_type,
        |  product_type,
        |  function_type,
        |  function_detail_type,
        |  customlv4,
        |  customlv5,
        |  risk_type,
        |  risk_level,
        |  im_session_status,
        |  icafe_last_pull_time,
        |  icafe_status,
        |  icafe_flag,
        |  status,
        |  extend_feedback_channel,
        |  quesiton_desc,
        |  remark,
        |  accept_group,
        |  is_change,
        |  ernie_bot_flag,
        |  close_type,
        |  custom_label,
        |  session_id,
        |  sys_info,
        |  rx_subject,
        |  rx_reply_record,
        |  rx_temp_call_cnt,
        |  rx_process_definition_name,
        |  userid,
        |  user_name,
        |  cuid as CUID,
        |  rx_temp_sex,
        |  rx_temp_call_phone,
        |  evaluate_csi,
        |  auto_content_csi,
        |  auto_content_fcr,
        |  is_solve,
        |  user_evaluate,
        |  case when rx_temp_create_time = '' then null else rx_temp_create_time end as create_time,
        |  case when update_time = '' then null else update_time end as update_time,
        |  event_day,
        |  channel,
        |  channel_platform_space,
        |  mobile_model,
        |  mobile_brand,
        |  ip,
        |  ip_province,
        |  im_session_id,
        |  rx_process_instance_id,
        |  mobile_app_name,
        |  app_vn,
        |  cz_extend_query,
        |  cz_extend_url,
        |  cz_fb_url
        |from
        |  udw_ns.default.help_ods_ufo_key_point_productline_di
        |where
        |  product_line in ('${productList.mkString("','")}')
        |  and event_day = ${YESTERDAY}""".stripMargin)
      .withColumn("session_content",encode($"session_content","utf-8"))
      .withColumn("quesiton_desc",encode($"quesiton_desc","utf-8"))
      .withColumn("remark",encode($"remark","utf-8"))
      .withColumn("cz_extend_query",encode($"cz_extend_query","utf-8"))
      .withColumn("cz_fb_url",encode($"cz_fb_url","utf-8"))
      .withColumn("cz_extend_url",encode($"cz_extend_url","utf-8"))
      .withColumn("sub_product_line",col("product_line"))
      .withColumn("product_line",when(col("product_line") === "运营活动" or col("product_line") === "任务系统","用户增长").otherwise(col("product_line")))


    println(s"查询的${YESTERDAY}的数据一共："+result.count())
    //result.show(10,false)

    val properties = PropertiesUtils.MysqlQaProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    val deleteSql = s"delete from uecl_info_online where event_day = ${YESTERDAY} and channel in ('在线','热线','迟滞')"
    //将当天的数据先删除再写入
    CommonUtils.deleteMysql(properties,deleteSql)

    result.repartition(10).write.mode(SaveMode.Append).jdbc(properties.getProperty("url"), "uecl_info_online", properties)

    spark.close()
  }
}
