package com.baidu.sql.tds

import cn.hutool.core.date.{DateField, DateUtil}
import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

import java.util.Properties

/**
 * <AUTHOR>
 */
object PassIdReadMysqlToHiveTds {

  private val mysqlLib = "mysql_lib"

  def main(args: Array[String]): Unit = {

    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_mysql_2_udw")
      .enableHiveSupport()
      .getOrCreate()

    val url = JDBCUtils.url
    val user = JDBCUtils.user
    val password = JDBCUtils.password
    val driver = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)


    val mysqlDF = spark.read
      .jdbc(url, mysqlLib, properties)
      .where("id = " + args(0))
      .select("sql_statement", "table")

    var event_day = sys.env("DATE")
    var event_hour = sys.env("HOUR")
    val eventTime = sys.env("BASETIME")

    var timeStr: String = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6,
      8) + " " + event_hour + ":00"
    // 获取程序启动时间
    val baseTime = DateUtil.parse(timeStr, "yyyy-MM-dd HH:mm")
    val offsetDateTime = DateUtil.offset(baseTime, DateField.HOUR, -1)
    val formatDate = DateUtil.format(offsetDateTime, "yyyyMMddHHmmss")


    event_day = formatDate.substring(0, 8)
    event_hour = formatDate.substring(8, 10)

    val sqlStatement = mysqlDF.first().getString(0).replace("{date}", event_day).replace("{BASETIME}", eventTime)
    val writeBackTable = mysqlDF.first().getString(1)

    val result = spark.read
      .format("jdbc")
      .option("url", JDBCUtils.strategyUrl)
      .option("driver", driver)
      .option("user", JDBCUtils.strategyUser)
      .option("password", JDBCUtils.strategyPassword)
      .option("query", sqlStatement).load()

    val tempTable = "temp_table_" + args(0)

    result.toDF().createOrReplaceTempView(tempTable)

    val typeInfo: String = if (args(0) == "8" || args(0) == "9") "into" else "overwrite"

    val sql = "insert " + typeInfo + " table " + writeBackTable + " partition (event_day = '" + event_day + "'," +
      "event_hour= '" + event_hour + "') " + "select phone, sms_send_time, null from " + tempTable

    spark.sql(sql)

    spark.close()
  }


}
