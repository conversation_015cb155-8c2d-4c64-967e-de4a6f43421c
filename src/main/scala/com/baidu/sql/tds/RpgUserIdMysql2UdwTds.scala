package com.baidu.sql.tds

import com.baidu.sql.utils.DevTethysJDBC
import org.apache.log4j.Logger
import org.apache.spark.sql.SparkSession

/**
 * <AUTHOR>
 */
object RpgUserIdMysql2UdwTds {
  def main(args: Array[String]): Unit = {
    val spark = SparkSession
      .builder()
      .enableHiveSupport()
      .appName("RpgUserIdMysql2Udw")
      .getOrCreate()

    val logger = Logger.getLogger(getClass.getName)
//    val event_day = sys.env("DATE")
    val event_day = args(0)
    logger.info(s"event_day: ${event_day}")
    var mysqlView = "temp_table_rpg"

    var sourceMysql =
      """
        |select
        |  author_pass_id as user_id
        |from
        |  tethys_assess_records
        |group by
        |  author_pass_id
        |""".stripMargin

    logger.info(s"sourceMysql: ${sourceMysql}")

    val jdbcDF = spark.read.format("jdbc")
      .option("url", DevTethysJDBC.onlineUrl)
      .option("user", DevTethysJDBC.onlineUser)
      .option("password", DevTethysJDBC.onlinePassword)
      .option("driver", DevTethysJDBC.driver)
      .option("query", sourceMysql)
      .load()

    jdbcDF.toDF().createOrReplaceTempView(mysqlView)

    var insertSql =
      s"""
        |insert overwrite table
        |  udw_ns.default.help_ods_rpg_user_id_df partition (event_day = ${event_day})
        |select
        |  *
        |from
        |  ${mysqlView}
        |""".stripMargin

    logger.info(s"insertSql: ${insertSql}")

    spark.sql(insertSql)

    spark.close()
  }
}
