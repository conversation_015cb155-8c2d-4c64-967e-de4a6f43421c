package com.baidu.sql.tds
import com.baidu.sql.utils.SparkUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

/*
   wise 搜索数据
   Wise（Wireless Search）即无线搜索，取每个单词前2个字母组合；也有人说移动搜索，移动端的网页搜索。
 */
object SearchData2AfsTds {
  def main(args: Array[String]): Unit = {
    // 日期参数
    val YESTERDAY = args(0)
    // 需要排除的 engine_profile_class_id
    val notClassId = args(1)

    val conf = new SparkConf()
      .setAppName("SearchData2Afs")

    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
//      .master("local")
      .enableHiveSupport()
      .getOrCreate()

    spark.sparkContext.hadoopConfiguration.set("hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    import spark.implicits._

    var fetchMysql =
      s"""
         |SELECT
         |	b.id as id,
         |	b.pass_id as uid
         |FROM engine_profile_user_classification a
         |LEFT JOIN engine_profile_user b on a.pass_id = b.pass_id
         |WHERE a.is_delete = 0
         |AND b.is_delete = 0
         |AND b.portrait_status = 5
         |AND a.engine_profile_class_id not in (${notClassId})
         |limit 10000
         |""".stripMargin


    val jdbcDF = SparkUtils.getJdbcDf(spark, fetchMysql)

    jdbcDF.cache()

    var buildWhere = jdbcDF
      .agg(concat_ws(", ", collect_list(concat(lit("'"), col("uid"), lit("'")))).as("allUid"))

    val allUid = SparkUtils.concatAllId(buildWhere.first().getString(0))


    var fetchHiveSql =
      s"""
         |select
         |  t1.uid as uid,
         |  cast(
         |    collect_list(
         |      to_json(
         |        named_struct(
         |          'action_name',
         |          action_name,
         |          'original_query',
         |          original_query,
         |          'url_title',
         |          url_title,
         |          'url',
         |          url,
         |          'stat_date',
         |          stat_date,
         |          'stat_time',
         |          stat_time
         |        )
         |      )
         |    ) as String
         |  ) as data
         |from
         |  (
         |    select
         |      uid,
         |      case
         |        when action_name = 'se' then '检索'
         |        when action_name = 'al' then '阿拉丁'
         |        when action_name = 'as' then '自然结果'
         |        when action_name = 'pl' then '品专'
         |        when action_name = 'pp' then '凤巢'
         |        when action_name = 'tc' then '老的转码页标识'
         |        else action_name
         |      end as action_name,
         |      original_query,
         |      url_title,
         |      url,
         |      stat_date,
         |      stat_time
         |    from
         |      ubs_search.search_wise_resultpage_query_click
         |    where
         |      event_day = '${YESTERDAY}'
         |      and log_source in ('click', 'se')
         |      and uid in ${allUid}
         |  ) t1
         |group by
         |  t1.uid
         |""".stripMargin

    println(fetchHiveSql)

    var df = spark.sql(fetchHiveSql)
    df = df.withColumn("filename", concat(lit("profile_info_search_"), col("uid")))
      .selectExpr("to_json(Struct(*)) as res_data")
      .withColumn("filename", get_json_object(col("res_data"), "$.filename"))

    df.foreach(row => {
      val filename = row.getAs[String]("filename")
      val data = row.getAs[String]("res_data")
      SparkUtils.sink2Afs(spark, filename, data)
    })
  }
}
