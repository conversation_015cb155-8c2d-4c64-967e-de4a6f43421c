package com.baidu.sql.tds

import cn.hutool.core.date.{DateField, DateUtil}
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.lit

import java.util.Properties

/**
 * <AUTHOR>
 */
object DataTransSMSToHiveTds {
  /*
  * 表同步至udw
  * 通过时间宏传入日期, 及运行时小时
  * 新方案 每小时同步，脚本中使用基准时间,基于create_time，每小时从mysql取 只同步pass_id不为null且大于0的数据的数据
  * 使用动态分区，从sms_send中的中取年月日 小时进行分区写入
  *
  * */
  def main(args: Array[String]): Unit = {
    // 最终落地表
    val writeBackTable: String = args(0)
    // 源表
    val sourceTable: String = "engine_profile_sms"
    //基准日期
    var event_day = args(1)
    //基准小时
    var event_hour = args(2)


    //取数时间处理
    var timeStr: String = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6,
      8) + " " + event_hour + ":00"
    val baseTime = DateUtil.parse(timeStr, "yyyy-MM-dd HH:mm")
    val offsetDateTime = DateUtil.offset(baseTime, DateField.HOUR, -12)
    val formatDate = DateUtil.format(offsetDateTime, "yyyyMMddHHmmss")
    //时间字符串拼接
    event_day = formatDate.substring(0, 8)
    event_hour = formatDate.substring(8, 10)
    timeStr = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6, 8) + " " + event_hour

    // sparkConf
    val sparkConf = new SparkConf().setAppName("DataTransSMSToHive")
//      .setMaster("local[*]")
    // sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    // 数据源mysql的配置
    val url: String = "************************************************************?" +
      "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
      "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
    val user: String = "readonly"
    val password: String = "Asd123"
    val driver: String = "com.mysql.jdbc.Driver"

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    val sqlStatement =
      s"""
         |select
         |*
         |from $sourceTable
         |where sms_send_time like '$timeStr%'
         |and pass_id is not null
         |and pass_id > 0
         |""".stripMargin

    // 从 MySQL 读取数据,并加入所需的标记字段
    val jdbcDf = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", sqlStatement)
      .load()
      .withColumn("sms_send_code",lit("1"))

    val result = jdbcDf.selectExpr(
      "phone",
      "cast(sms_send_time as string) as sms_send_time",
      "cast(pass_id as string) as pass_id",
      "sms_send_code",
      "date_format(sms_send_time, 'yyyyMMdd') AS event_day",
      "cast(hour(sms_send_time) as string) AS event_hour")

    result.createOrReplaceTempView("temp_table")
    // 将数据写入最终落地表
    val sql = "insert overwrite table " + writeBackTable + " partition (event_day = '" + event_day + "'," +
      "event_hour= '" + event_hour + "') " + "select phone,sms_send_time,pass_id,sms_send_code from temp_table"

    spark.sql(sql)

    spark.close()
  }

}
