package com.baidu.sql.tds

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.util.Properties


object ReadMysql2CsvTds {
  def main(args: Array[String]): Unit = {
    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .setAppName("read_mysql_2_csv")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .getOrCreate()

    spark.sparkContext.hadoopConfiguration.set("hadoop.job.ugi", "help_majia2,wuzijian02majia2")

    val url = JDBCUtils.url
    val user = JDBCUtils.user
    val password = JDBCUtils.password
    val driver = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    var sql =
      """
        |SELECT
        | *
        |FROM
        |tb_fengling_xianshang_info_online
        |""".stripMargin

    // 查找MySQL的数据
    val result = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", sql).load()

//    val YESTERDAY: String = sys.env("DATE")
    val YESTERDAY: String = args(0)
    val filePath = s"afs://baihua.afs.baidu.com:9902/user/help_majia2/crm/$YESTERDAY" // 指定HDFS路径

    result.repartition(1).write.format("csv")
      .option("header", "true")
      .mode(SaveMode.Overwrite)
      .save(filePath)
  }
}
