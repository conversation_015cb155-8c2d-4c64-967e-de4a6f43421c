package com.baidu.sql.tds

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.text.SimpleDateFormat
import java.util.{Calendar, Properties}


/**
 * <AUTHOR>
 * @Date 2023-02-23
 */
object Udw2MysqlTds {

  private val hqlLibTable = "hive_sql_lib"

  def main(args: Array[String]): Unit = {

    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_2_mysql")
      .getOrCreate()

    var url = JDBCUtils.url
    var user = JDBCUtils.user
    var password = JDBCUtils.password
    val driver = JDBCUtils.driver

    var properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)

    val mysqlDF = spark.read
      .option("driver", driver)
      .jdbc(url, hqlLibTable, properties)
      .where("id = " + args(0))
      .select("hql", "table")

    val hql = mysqlDF.first().getString(0)
    val writeBackTable = mysqlDF.first().getString(1)

    if (hql != null) {

      val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
      val cal = Calendar.getInstance()

      val eventDay = dateFormat.format(cal.getTime)

      cal.add(Calendar.DATE, -1)
      val yesterday = dateFormat.format(cal.getTime)

      cal.setTime(dateFormat.parse(eventDay))
      cal.add(Calendar.DATE, -90)
      val forward_90_day = dateFormat.format(cal.getTime)

      val hqlWithEventDay = hql.replace("{date}", eventDay)
        .replace("{yesterday}", yesterday)
        .replace("{forward_90_day}", forward_90_day).stripMargin

      println(hqlWithEventDay)

      val result = spark.sql(hqlWithEventDay)

      result.repartition(500).write.option("truncate", value = true)
        .mode(SaveMode.Overwrite).jdbc(url, writeBackTable, properties)
    }
    spark.close()
  }
}
