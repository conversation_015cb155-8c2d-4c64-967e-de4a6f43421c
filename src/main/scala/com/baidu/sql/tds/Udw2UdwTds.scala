package com.baidu.sql.tds

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

import java.text.SimpleDateFormat
import java.util.{Calendar, Properties}

/**
 * <AUTHOR>
 * @date 2023-07-18
 * @description
 */
object Udw2UdwTds {

  private val hqlLibTable = "hive_sql_lib"

  def main(args: Array[String]): Unit = {

    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_2_udw")
      .getOrCreate()

    val url = JDBCUtils.url
    val user = JDBCUtils.user
    val password = JDBCUtils.password
    val driver = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)

    val mysqlDF = spark.read
      .option("driver", driver)
      .jdbc(url, hqlLibTable, properties)
      .where("id = " + args(0))
      .select("hql", "table")

    val hql = mysqlDF.first().getString(0)

    if (hql != null) {

            val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
            val cal = Calendar.getInstance()
            cal.add(Calendar.DATE, -1)
            val yesterday = dateFormat.format(cal.getTime)

            val today = Calendar.getInstance()
            today.add(Calendar.DATE, -2)
            val dayBeforeYesterday = dateFormat.format(today.getTime)

            today.add(Calendar.MONTH, -6)
            val halfYearAgo = dateFormat.format(today.getTime)

      val hqlWithEventDay = hql
        .replace("{dayBeforeYesterday}", dayBeforeYesterday)
        .replace("{halfYearAgo}", halfYearAgo)
        .replace("{yesterday}", yesterday).stripMargin
      println(hqlWithEventDay)
      spark.sql(hqlWithEventDay)
    }
  }
}
