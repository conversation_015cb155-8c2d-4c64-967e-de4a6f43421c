package com.baidu.sql.tds;

import com.baidu.sql.utils.JDBCUtils;
import org.apache.spark.SparkConf;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import java.util.Properties;

/**
 * <AUTHOR>
 */
public class Udw2MysqlJavaTds {
    private static final String HQL_LIB_TABLE = "hive_sql_lib";
    public static void main(String[] args) {
        SparkConf conf = new SparkConf();
        SparkSession spark = SparkSession.builder()
                .appName("Udw2MysqlJavaTds")
                .config(conf)
                .getOrCreate();

        String flag = args[0];

        String url = JDBCUtils.url();
        String driver = JDBCUtils.driver();
        String name = JDBCUtils.user();
        String password = JDBCUtils.password();

        Properties properties = new Properties();
        properties.setProperty("user", name);
        properties.setProperty("password", password);

        Dataset<Row> jdbcDf = spark.read()
                .option("driver", driver)
                .jdbc(url, HQL_LIB_TABLE, properties)
                .where("id = " + flag)
                .select("hql", "table");

        String hql = jdbcDf.first().getString(0);
        String writeBackTable = jdbcDf.first().getString(1);

        Dataset<Row> df = spark.sql(hql);

        df.repartition(50).write().option("truncate", "true")
                .mode(SaveMode.Overwrite).jdbc(url, writeBackTable, properties);

    }
}
