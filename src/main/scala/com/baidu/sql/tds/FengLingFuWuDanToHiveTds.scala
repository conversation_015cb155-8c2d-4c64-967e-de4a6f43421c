package com.baidu.sql.tds

import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.baidu.sql.utils.TimeOperateUtil.calcnDate
import com.baidu.sql.utils.UdfUtils._
import org.apache.spark.SparkConf
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}

import java.text.SimpleDateFormat
import java.util.Date
import scala.collection.mutable.ArrayBuffer
import scala.util.matching.Regex


/**
 * <AUTHOR>
 */
object FengLingFuWuDanToHiveTds {

  val dataFormat = new SimpleDateFormat("yyyy-MM-dd")
  val pTagRegex: Regex = "<p>(.*?)</p>".r
  var yesterDay: String = ""

  /*
  * 风铃监管投诉服务表单同步至udw
  * */
  def main(args: Array[String]): Unit = {
    // 分区时间
    yesterDay = args(0)

    val sparkConf = new SparkConf().setAppName("FengLingFuWuDanToTds")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", "true")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    //获取处理人回复
    val imgFunc: UserDefinedFunction = udf((data: String) => {
      var pdata = ""
      if (data == null || data == "") {
        ""
      } else {
        pdata = pTagRegex.findAllMatchIn(data).map(x => x.group(1)).toArray.mkString(" ").replaceAll("<span.*?>", "").replaceAll("</span>", "").replaceAll("<br>", "").replaceAll("&nbsp;", "").replaceAll("\" rel=\".*?\">", " ").replaceAll("<.*?=\"", " ").replaceAll("</.*?>", " ").replaceAll("\">", " ").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
      }
      pdata
    })

    val schema: StructType = StructType(Seq(
      StructField("case_id_filter", LongType, false),
      StructField("case_type", StringType, false),
      StructField("supervisory_department", StringType, false),
      StructField("warm_reminder", StringType, false),
      StructField("complainant", StringType, false),
      StructField("contact_number", StringType, false),
      StructField("maturity_time", StringType, false),
      StructField("complaint_content", StringType, false),
      StructField("problem_type", StringType, false),
      StructField("complaint_link", StringType, false),
      StructField("complainant_type", StringType, false),
      StructField("complainant_body", StringType, false),
      StructField("case_number", StringType, false),
      StructField("query_code", StringType, false),
      StructField("remark", StringType, false),
      StructField("Identity_document", StringType, false),
      StructField("upload_attachment", StringType, false),
      StructField("upload_attachment1", StringType, false),
      StructField("whether_the_user_is_satisfied", StringType, false),
      StructField("coordination_details", StringType, false),
      StructField("amount_involved", StringType, false),
      StructField("voiceover", StringType, false),
      StructField("account_name", StringType, false),
      StructField("company_name", StringType, false),
      StructField("operating_unit", StringType, false),
      StructField("industry", StringType, false),
      StructField("violation_or_not", StringType, false),
      StructField("involve_problem", StringType, false),
      StructField("risk_exposure_cause", StringType, false),
      StructField("order_number", StringType, false),
      StructField("whether_to_apply_for_after", StringType, false),
      StructField("feedback_situation", StringType, false),
      StructField("upload_attachment2", StringType, false)
    ))

    //查询新建数据
    val createDf = transDataFrame(spark, yesterDay, imgFunc, schema,"新建")
    println("查询新建数据一共:" + createDf.count())
    createDf.show(5,false)
    createDf.createOrReplaceTempView("create_table")

    spark.sql("ALTER TABLE udw_ns.default.help_ods_fengling_public_opinion_monitoring DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")

    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_fengling_public_opinion_monitoring partition (event_day = ${yesterDay} )
         |select
         |   '监管投诉' as product_line, --产品线
         |   question_type_first,   --一级问题类型
         |   question_type_second,  --二级问题类型
         |   case_id,      --case编号
         |   userid,       --userid
         |   submit_user,     --提交人
         |   case_type,        --案件类型
         |   supervisory_department,     --监管部门
         |   submit_time,  --提交时间
         |   warm_reminder,  --温馨提示
         |   complainant,    --投诉人
         |   contact_number, --联系电话
         |   maturity_time,  --到期时间
         |   complaint_content,  --投诉内容
         |   problem_type,   --问题类型
         |   complaint_link,  --投诉链接
         |   complainant_type,  --申诉人类型
         |   complainant_body,  --投诉人主体
         |   case_number,    --案件编号
         |   query_code,     --查询码
         |   remark,         --备注
         |   identity_document,  --身份证件
         |   upload_attachment,  --上传附件
         |   upload_attachment1, --上传附件1
         |   whether_the_user_is_satisfied,  --用户是否满意
         |   coordination_details,  --协调详情
         |   amount_involved,   --涉及金额
         |   voiceover,        --回复话术
         |   feedback_situation,        --反馈情况
         |   comment_str,        --处理人回复
         |   upload_attachment2,        --上传附件2
         |   account_name,        --账户名
         |   company_name,        --公司名称
         |   operating_unit,        --运营单位
         |   industry,        --所属行业
         |   violation_or_not,        --是否违规
         |   involve_problem,        --涉及问题
         |   risk_exposure_cause,        --风险暴露原因
         |   order_number,        --订单号
         |   whether_to_apply_for_after,        --是否申请售后
         |   case_status,        --case状态
         |   is_recall,       --是否已撤回
         |   product_line_im, -- 在线进线业务名称
         |   product_line_duke, -- 热线进线业务名称
         |   zx_incoming_status, -- 在线是否进线
         |   rx_incoming_status, -- 热线是否进线
         |   product_risk_line_im, -- 在线风险业务名称
         |   product_risk_line_duke, -- 热线风险业务名称
         |   zx_risk_level, -- 在线风险等级
         |   rx_risk_level, -- 热线风险等级
         |   rx_risk_type_classification, -- 热线风险类型-含二级分类
         |   rx_risk_type_classification_new -- 热线风险类型分类（新）
         |from create_table""".stripMargin)

    //查询已关闭数据
    val updateResDf = transDataFrame(spark, yesterDay, imgFunc, schema,"已关闭")
      .withColumn("event_day",regexp_replace(substring($"submit_time",1,11),"-",""))
      .withColumn("product_line",lit("监管投诉"))
    println("查询已关闭数据一共:" + updateResDf.count())

    updateResDf.createOrReplaceTempView("update_table")

    val updateDay = spark.sql("select event_day,count(*) as event_num from update_table group by event_day order by event_day")
    updateDay.show(false)

    val eventDayList = updateDay.select("event_day").collect().toList
    for (row <- eventDayList) {
      val updateDay = row.getAs[String]("event_day")
      val totalDf = spark.sql(
          s"""
             |select
             |   product_line, --产品线
             |   question_type_first,   --一级问题类型
             |   question_type_second,  --二级问题类型
             |   case_id,      --case编号
             |   userid,       --userid
             |   submit_user,     --提交人
             |   case_type,        --案件类型
             |   supervisory_department,     --监管部门
             |   submit_time,  --提交时间
             |   warm_reminder,  --温馨提示
             |   complainant,    --投诉人
             |   contact_number, --联系电话
             |   maturity_time,  --到期时间
             |   complaint_content,  --投诉内容
             |   problem_type,   --问题类型
             |   complaint_link,  --投诉链接
             |   complainant_type,  --申诉人类型
             |   complainant_body,  --投诉人主体
             |   case_number,    --案件编号
             |   query_code,     --查询码
             |   remark,         --备注
             |   identity_document,  --身份证件
             |   upload_attachment,  --上传附件
             |   upload_attachment1, --上传附件1
             |   whether_the_user_is_satisfied,  --用户是否满意
             |   coordination_details,  --协调详情
             |   amount_involved,   --涉及金额
             |   voiceover,        --回复话术
             |   feedback_situation,        --反馈情况
             |   comment_str,        --处理人回复
             |   upload_attachment2,        --上传附件2
             |   account_name,        --账户名
             |   company_name,        --公司名称
             |   operating_unit,        --运营单位
             |   industry,        --所属行业
             |   violation_or_not,        --是否违规
             |   involve_problem,        --涉及问题
             |   risk_exposure_cause,        --风险暴露原因
             |   order_number,        --订单号
             |   whether_to_apply_for_after,        --是否申请售后
             |   case_status,        --case状态
             |   is_recall,       --是否已撤回
             |   product_line_im, -- 在线进线业务名称
             |   product_line_duke, -- 热线进线业务名称
             |   zx_incoming_status, -- 在线是否进线
             |   rx_incoming_status, -- 热线是否进线
             |   product_risk_line_im, -- 在线风险业务名称
             |   product_risk_line_duke, -- 热线风险业务名称
             |   zx_risk_level, -- 在线风险等级
             |   rx_risk_level, -- 热线风险等级
             |   rx_risk_type_classification, -- 热线风险类型-含二级分类
             |   rx_risk_type_classification_new -- 热线风险类型分类（新）
             |from udw_ns.default.help_ods_fengling_public_opinion_monitoring
             |where event_day = ${updateDay} """.stripMargin)
        .withColumn("event_day",regexp_replace(substring($"submit_time",1,11),"-",""))
      println(s"查询${updateDay}日期数据一共:" + totalDf.count())

      //将其他case数据与当天关闭的case做差集
      val createResDf = totalDf.as("U")
        .join(updateResDf.as("P"), $"U.case_id" === $"P.case_id" and $"U.event_day" === $"P.event_day","left_anti")

      //要重新写入的数据集
      val u_table = updateResDf.filter($"event_day" === updateDay).as("U")
        .join(totalDf.as("C"), $"U.case_id" === $"C.case_id" and $"U.event_day" === $"C.event_day","inner")
        .select(
          $"U.product_line",
          $"U.question_type_first",
          $"U.question_type_second",
          $"U.case_id",
          $"U.userid",
          $"U.submit_user",
          $"U.case_type",
          $"U.supervisory_department",
          $"U.submit_time",
          $"U.warm_reminder",
          $"U.complainant",
          $"U.contact_number",
          $"U.maturity_time",
          $"U.complaint_content",
          $"U.problem_type",
          $"U.complaint_link",
          $"U.complainant_type",
          $"U.complainant_body",
          $"U.case_number",
          $"U.query_code",
          $"U.remark",
          $"U.identity_document",
          $"U.upload_attachment",
          $"U.upload_attachment1",
          $"U.whether_the_user_is_satisfied",
          $"U.coordination_details",
          $"U.amount_involved",
          $"U.voiceover",
          $"U.feedback_situation",
          $"U.comment_str",
          $"U.upload_attachment2",
          $"U.account_name",
          $"U.company_name",
          $"U.operating_unit",
          $"U.industry",
          $"U.violation_or_not",
          $"U.involve_problem",
          $"U.risk_exposure_cause",
          $"U.order_number",
          $"U.whether_to_apply_for_after",
          $"U.case_status",
          $"U.is_recall",
          $"C.product_line_im",
          $"C.product_line_duke",
          $"C.zx_incoming_status",
          $"C.rx_incoming_status",
          $"C.product_risk_line_im",
          $"C.product_risk_line_duke",
          $"C.zx_risk_level",
          $"C.rx_risk_level",
          $"C.rx_risk_type_classification",
          $"C.rx_risk_type_classification_new",
          $"U.event_day"
        )
      println("left_anti后的数据有:" + createResDf.count() + ",u_table:" + u_table.count())

      val res_table = createResDf.unionByName(u_table)

      println("开始更新:" + updateDay + "更新数据一共:" + res_table.count())
      res_table.createOrReplaceTempView("res_table")

      spark.sql(
        s"""
           |insert overwrite table udw_ns.default.help_ods_fengling_public_opinion_monitoring partition (event_day = ${updateDay} )
           |select
           |   product_line, --产品线
           |   question_type_first,   --一级问题类型
           |   question_type_second,  --二级问题类型
           |   case_id,      --case编号
           |   userid,       --userid
           |   submit_user,     --提交人
           |   case_type,        --案件类型
           |   supervisory_department,     --监管部门
           |   submit_time,  --提交时间
           |   warm_reminder,  --温馨提示
           |   complainant,    --投诉人
           |   contact_number, --联系电话
           |   maturity_time,  --到期时间
           |   complaint_content,  --投诉内容
           |   problem_type,   --问题类型
           |   complaint_link,  --投诉链接
           |   complainant_type,  --申诉人类型
           |   complainant_body,  --投诉人主体
           |   case_number,    --案件编号
           |   query_code,     --查询码
           |   remark,         --备注
           |   identity_document,  --身份证件
           |   upload_attachment,  --上传附件
           |   upload_attachment1, --上传附件1
           |   whether_the_user_is_satisfied,  --用户是否满意
           |   coordination_details,  --协调详情
           |   amount_involved,   --涉及金额
           |   voiceover,        --回复话术
           |   feedback_situation,        --反馈情况
           |   comment_str,        --处理人回复
           |   upload_attachment2,        --上传附件2
           |   account_name,        --账户名
           |   company_name,        --公司名称
           |   operating_unit,        --运营单位
           |   industry,        --所属行业
           |   violation_or_not,        --是否违规
           |   involve_problem,        --涉及问题
           |   risk_exposure_cause,        --风险暴露原因
           |   order_number,        --订单号
           |   whether_to_apply_for_after,        --是否申请售后
           |   case_status,        --case状态
           |   is_recall,       --是否已撤回
           |   product_line_im, -- 在线进线业务名称
           |   product_line_duke, -- 热线进线业务名称
           |   zx_incoming_status, -- 在线是否进线
           |   rx_incoming_status, -- 热线是否进线
           |   product_risk_line_im, -- 在线风险业务名称
           |   product_risk_line_duke, -- 热线风险业务名称
           |   zx_risk_level, -- 在线风险等级
           |   rx_risk_level, -- 热线风险等级
           |   rx_risk_type_classification, -- 热线风险类型-含二级分类
           |   rx_risk_type_classification_new -- 热线风险类型分类（新）
           |from res_table """.stripMargin)
    }

    println("更新结束")

    spark.close()
  }

  /**
   * 读取一天的case信息
   * @param spark
   * @param yestrtday 运行日期
   * @param imgFunc 自定义的udf
   * @param schema 数据模型
   * @param dataType 查询数据类型：新建、已关闭
   * @return
   */
  def transDataFrame(spark:SparkSession,yestrtday:String,imgFunc:UserDefinedFunction,schema: StructType,dataType:String): DataFrame = {
    import spark.implicits._

    val queryDate = dataType match {
      case "新建" => "create_time"
      case "已关闭" => "update_time"
    }

    val querySql = dataType match {
      case "新建" => ""
      case "已关闭" => "and DATE_FORMAT(update_time,'%Y%m%d') != DATE_FORMAT(create_time,'%Y%m%d') and status = '6'"
    }

    //监管投诉的id为330
    var caseInfo =
      s"""
         |SELECT
         |	p.case_id,              #case编号
         |	cast(p.submit_time as char) as submit_time,              #提交时间
         |	t.`name` as question_type_first,    #一级问题分类
         |	q.`name` as question_type_second, #二级问题分类
         |	u.user_name as submit_user, #提交人,
         |  l.comment_str as comment_str, #处理人回复
         |  l.deal_type as deal_type, #处理状态
         |  p.status as case_status, #处理人回复
         |  cast(l.deal_end_time as char) as deal_end_time #处理最后时间
         |FROM
         |	( SELECT product_line_id, question_id AS question_id2, id AS case_id,submit_user_id,submit_time,status FROM case_info
         |    WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '330' ${querySql}) p
         |	JOIN (SELECT id,`name`,parent_id from question) q ON p.question_id2 = q.id
         |	JOIN (select id, `name` from question) t on q.parent_id = t.id
         |	JOIN (select id,user_name from userz) u on p.submit_user_id = u.id
         |	LEFT JOIN (select case_id,comment_str,deal_end_time,deal_type from deal_log order by deal_end_time) l on p.case_id = l.case_id
         |""".stripMargin

    //获取一天的case信息
    val caseInfoDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseInfo)
    println("读取caseInfoDf的数量为" + caseInfoDf.count())

    //将comment_str按照case_id维度进行聚合成一行
    val caseCommentDf = caseInfoDf.withColumn("comment_str", imgFunc($"comment_str"))
      .rdd.map(row =>
        (row.getAs[Long]("case_id"),
          (row.getAs[String]("submit_time"),
          row.getAs[String]("question_type_first"),
          row.getAs[String]("question_type_second"),
          row.getAs[String]("submit_user"),
          row.getAs[Integer]("case_status"),
          row.getAs[Integer]("deal_type"),
          row.getAs[String]("deal_end_time"),
          row.getAs[String]("comment_str"))))
      .groupByKey()
      .mapValues(_.toArray)
      .map({ case (caseId, caseInfoArray) =>
        val commentList = ArrayBuffer.empty[String]
        var submit_time = ""
        var question_type_first = ""
        var question_type_second = ""
        var submit_user = ""
        var case_status = ""
        var deal_type = ""
        for (key <- 0 to caseInfoArray.size - 1) {
          val caseInfo = caseInfoArray(key)
          submit_time = caseInfo._1.toString
          question_type_first = caseInfo._2
          question_type_second = caseInfo._3
          submit_user = caseInfo._4
          case_status = caseInfo._5.toString
          deal_type = caseInfo._6.toString
          val deal_end_time = caseInfo._7
          if (deal_end_time != null) {
            commentList.append("回复时间:" + deal_end_time + ",内容：" + caseInfo._8)
          }
        }
        (caseId, submit_time, question_type_first, question_type_second, submit_user, case_status,deal_type, commentList.mkString(";"))
      }).toDF("case_id", "submit_time", "question_type_first", "question_type_second", "submit_user", "case_status","deal_type","comment_str")
      .repartition(2)

    println("读取caseCommentDf的数量为" + caseCommentDf.count())

    val deal_log =
      s"""
         |select
         |  case_id,
         |  update_time,
         |  form_eng_name,
         |  value
         |from
         |deal_log_filed
         |where case_id in ( SELECT id FROM case_info WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '330' ${querySql})
         |and update_time in
         |(select
         | max(update_time) as update_time
         |from deal_log_filed
         |where case_id in ( SELECT id FROM case_info WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '330' ${querySql})
         |group by case_id,form_eng_name)
         |""".stripMargin

    val dealLogDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,deal_log)
    println("读取dealLogDf的数量为" + dealLogDf.count())

    val jonDf = caseCommentDf.as("I").join(dealLogDf.as("D"), $"I.case_id" === $"D.case_id")
      .select(
        $"I.case_id" as "case_id",
        $"I.submit_time" as "submit_time",
        $"I.question_type_first",
        $"I.question_type_second",
        $"I.submit_user",
        $"I.case_status",
        $"I.deal_type" as "is_recall",
        $"I.comment_str" as "comment_str",
        $"D.form_eng_name",
        $"D.value")
      .withColumn("case_status", when($"case_status" === "3", "处理中").when($"case_status" === "4", "待关闭").when($"case_status" === "6", "已关闭").when($"case_status" === "2", "处理中-委派").otherwise("其他"))
      //是否撤回
      .withColumn("is_recall", when($"is_recall" === "15", "是").otherwise("否"))
      .repartition(2)

    println("读取jonDf的数量为" + jonDf.count())

    val filterDf = jonDf.rdd.map(row => (row.getAs[Long]("case_id"), row.getAs[String]("form_eng_name") -> row.getAs[String]("value")))
      .groupByKey()
      .mapValues(iter => iter.toMap)
      .map({ case (caseId, engNameArray) =>
        val buffer = ArrayBuffer.empty[Any]
        val case_type = engNameArray.getOrElse("Case_type", "").replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "")
        val supervisory_department = engNameArray.getOrElse("Supervisory_department", "").replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "")
        val warm_reminder = engNameArray.getOrElse("Warm_reminder", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        var complainant = if (engNameArray.getOrElse("Complainant", "") == "") engNameArray.getOrElse("complainant", "").replaceAll("[\\t|\\r|?]", "").replaceAll("\\n", " ") else engNameArray.getOrElse("Complainant", "").replaceAll("[\\t|\\r|?]", "").replaceAll("\\n", " ")
        complainant = complainant.replaceAll("[0-9]", "")
        val contact_number = engNameArray.getOrElse("Contact_number", "")
        val maturity_time = if (engNameArray.getOrElse("Maturity_time", "") == "") "" else dataFormat.format(new Date(engNameArray.getOrElse("Maturity_time", "").toLong))
        val complaint_content = engNameArray.getOrElse("Complaint_content", "").replaceAll("<.*?>", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ").replaceAll("&nbsp;", "")
        var problem_type = engNameArray.getOrElse("Problem_type", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\",\"", "-")
        if (problem_type == "[]" || problem_type == ""){
          problem_type = engNameArray.getOrElse("Problem_type2", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\",\"", "-")
        }
        if (problem_type == "[]") {
          problem_type = ""
        }
        val complaint_link = engNameArray.getOrElse("Complaint_link", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val complainant_type = engNameArray.getOrElse("Complainant_type", "").replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "")
        val complainant_body = engNameArray.getOrElse("Complainant_body", "").replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "")
        val case_number = engNameArray.getOrElse("Case_number", "")
        val query_code = engNameArray.getOrElse("Query_code", "")
        val remark = engNameArray.getOrElse("Remark", "")
        val Identity_document = engNameArray.getOrElse("Identity_document", "")
        var upload_attachment = ""
        if (engNameArray.getOrElse("upload_attachment", "") != "") {
          upload_attachment = engNameArray.getOrElse("upload_attachment", "").split("\":\"")(2).split("\"")(0)
        }

        var upload_attachment1 = ""
        if (engNameArray.getOrElse("Upload_attachment", "") == "") {
          if (engNameArray.getOrElse("Upload_attachment1", "") != "") {
            upload_attachment1 = engNameArray.getOrElse("Upload_attachment1", "").split("\":\"")(2).split("\"")(0)
          }
        } else {
          upload_attachment1 = engNameArray.getOrElse("Upload_attachment", "").split("\":\"")(2).split("\"")(0)
        }

        val whether_the_user_is_satisfied = engNameArray.getOrElse("Whether_the_user_is_satisfied", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val coordination_details = engNameArray.getOrElse("Coordination_details", "").replaceAll("\\[\"", "").replaceAll("\"]", "")
        val amount_involved = engNameArray.getOrElse("Amount_involved", "")
        val voiceover = engNameArray.getOrElse("Voiceover", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val account_name = engNameArray.getOrElse("Account_name", "")
        val company_name = if (engNameArray.getOrElse("Company_name", "") == "") engNameArray.getOrElse("company_name", "") else engNameArray.getOrElse("Company_name", "")
        val operating_unit = engNameArray.getOrElse("Operating_unit", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val industry = engNameArray.getOrElse("Industry", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val violation_or_not = engNameArray.getOrElse("Violation_or_not", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val involve_problem = engNameArray.getOrElse("Involve_problem", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val risk_exposure_cause = engNameArray.getOrElse("Risk_exposure_cause", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val order_number = engNameArray.getOrElse("Order_number", "")
        val whether_to_apply_for_after = engNameArray.getOrElse("Whether_to_apply_for_afte", "")
        val feedback_situation = engNameArray.getOrElse("Feedback_situation", "").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        var upload_attachment2 = ""
        if (engNameArray.getOrElse("Upload_attachment2", "") != "") {
          upload_attachment2 = engNameArray.getOrElse("Upload_attachment2", "").split("\":\"")(2).split("\"")(0)
        }
        buffer.append(caseId)
        buffer.append(case_type)
        buffer.append(supervisory_department)
        buffer.append(warm_reminder)
        buffer.append(complainant)
        buffer.append(contact_number)
        buffer.append(maturity_time)
        buffer.append(complaint_content)
        buffer.append(problem_type)
        buffer.append(complaint_link)
        buffer.append(complainant_type)
        buffer.append(complainant_body)
        buffer.append(case_number)
        buffer.append(query_code)
        buffer.append(remark)
        buffer.append(Identity_document)
        buffer.append(if (upload_attachment == "") "" else "https://ifengling.baidu.com/api/common/bos/file-dowload?objectKey=" + upload_attachment)
        buffer.append(if (upload_attachment1 == "") "" else "https://ifengling.baidu.com/api/common/bos/file-dowload?objectKey=" + upload_attachment1)
        buffer.append(whether_the_user_is_satisfied)
        buffer.append(coordination_details)
        buffer.append(amount_involved)
        buffer.append(voiceover)
        buffer.append(account_name)
        buffer.append(company_name)
        buffer.append(operating_unit)
        buffer.append(industry)
        buffer.append(violation_or_not)
        buffer.append(involve_problem)
        buffer.append(risk_exposure_cause)
        buffer.append(order_number)
        buffer.append(whether_to_apply_for_after)
        buffer.append(feedback_situation)
        buffer.append(if (upload_attachment2 == "") "" else "https://ifengling.baidu.com/api/common/bos/file-dowload?objectKey=" + upload_attachment2)
        Row.fromSeq(buffer)
      })

    val filterRes = spark.createDataFrame(filterDf, schema)
      //电话号码获取用户id
      .withColumn("userid", getUserId(col("contact_number")))
      .dropDuplicates("case_id_filter")
      .repartition(2)

    println("读取filterRes的数量为" + filterRes.count())

    var resDf = filterRes
      .join(jonDf, filterRes("case_id_filter") === jonDf("case_id"))
      .select(
          $"question_type_first".cast("string") as "question_type_first",
          $"question_type_second".cast("string") as "question_type_second",
          $"case_id".cast("string") as "case_id",
          $"userid".cast("string") as "userid",
          $"submit_user".cast("string") as "submit_user",
          $"is_recall".cast("string") as "is_recall",
          $"case_type".cast("string") as "case_type",
          $"supervisory_department".cast("string") as "supervisory_department",
          $"submit_time".cast("string") as "submit_time",
          $"warm_reminder".cast("string") as "warm_reminder",
          $"complainant".cast("string") as "complainant",
          $"contact_number".cast("string") as "contact_number",
          $"maturity_time".cast("string") as "maturity_time",
          $"complaint_content".cast("string") as "complaint_content",
          $"problem_type".cast("string") as "problem_type",
          $"complaint_link".cast("string") as "complaint_link",
          $"complainant_type".cast("string") as "complainant_type",
          $"complainant_body".cast("string") as "complainant_body",
          $"case_number".cast("string") as "case_number",
          $"query_code".cast("string") as "query_code",
          $"remark".cast("string") as "remark",
          $"identity_document".cast("string") as "identity_document",
          $"upload_attachment".cast("string") as "upload_attachment",
          $"upload_attachment1".cast("string") as "upload_attachment1",
          $"whether_the_user_is_satisfied".cast("string") as "whether_the_user_is_satisfied",
          $"coordination_details".cast("string") as "coordination_details",
          $"amount_involved".cast("string") as "amount_involved",
          $"voiceover".cast("string") as "voiceover",
          $"feedback_situation".cast("string") as "feedback_situation",
          $"comment_str".cast("string") as "comment_str",
          $"upload_attachment2".cast("string") as "upload_attachment2",
          $"account_name".cast("string") as "account_name",
          $"company_name".cast("string") as "company_name",
          $"operating_unit".cast("string") as "operating_unit",
          $"industry".cast("string") as "industry",
          $"violation_or_not".cast("string") as "violation_or_not",
          $"involve_problem".cast("string") as "involve_problem",
          $"risk_exposure_cause".cast("string") as "risk_exposure_cause",
          $"order_number".cast("string") as "order_number",
          $"whether_to_apply_for_after".cast("string") as "whether_to_apply_for_after",
          $"case_status".cast("string") as "case_status"
      )
      .dropDuplicates("case_id")
    resDf.createOrReplaceTempView("ResTable")

    if (dataType.equals("新建")) {
      //读取hive的在线和热线数据,关联userid和电话号码
      getHiveDF(spark,"在线").createOrReplaceTempView("ImTable")
      getHiveDF(spark,"热线").createOrReplaceTempView("DukeTable")
      val hiveSql =
        """
          |select
          |   r.*,
          |   case when i.product_line is null then '' else i.product_line end as product_line_im, -- 在线进线业务名称
          |   case when d.product_line is null then '' else d.product_line end as product_line_duke, -- 热线进线业务名称
          |   case when i.userid is not null then '是' else '否' end as zx_incoming_status, -- 在线是否进线
          |   case when d.rx_temp_call_phone is not null then '是' else '否' end as rx_incoming_status, -- 热线是否进线
          |   case when i.product_risk_line is null then '' else i.product_risk_line end as product_risk_line_im, -- 在线高危业务名称
          |   case when d.product_risk_line is null then '' else d.product_risk_line end as product_risk_line_duke, -- 热线高危业务名称
          |   case when i.risk_level is null then '' else i.risk_level end as zx_risk_level, -- 在线风险等级
          |   case when d.rx_risk_level is null then '' else d.rx_risk_level end as rx_risk_level, -- 热线是否进线
          |   case when d.rx_risk_type_classification is null then '' else d.rx_risk_type_classification end as rx_risk_type_classification, -- 热线风险类型-含二级分类
          |   case when d.rx_risk_type_classification_new is null then '' else d.rx_risk_type_classification_new end as rx_risk_type_classification_new -- 热线风险类型分类（新）
          |from ResTable r
          |left join ImTable i on r.userid = i.userid
          |left join DukeTable d on r.contact_number = d.rx_temp_call_phone
          |""".stripMargin

      resDf = spark.sql(hiveSql).repartition(10)
    }

    resDf
  }


  /**
   * 获取hive的在线和热线数据
   * @param spark
   * @param channel 在线或热线
   * @return
   */
  def getHiveDF(spark: SparkSession, channel: String): DataFrame = {
    //关联字段
    val joinCol = channel match {
      case "在线" => "userid"
      case "热线" => "rx_temp_call_phone"
      case _ => ""
    }

    //查询的字段
    val queryCol = channel match {
      case "在线" => List("risk_level")
      case "热线" => List("rx_risk_level","rx_risk_type_classification","rx_risk_type_classification_new")
      case _ => List("")
    }
    // 构建SQL过滤条件
    val sqlFilters = queryCol.map(field => s"$field != '' and $field != '无'").mkString("(", ") or (", ")")
    // 构建SQL聚合条件
    val sqlConcat = queryCol.map(field => s"concat_ws(',',collect_set($field)) as $field").mkString(",")
    //7天前日期
    val sevenDay = calcnDate(yesterDay,-7)

    val querySql =
      s"""select
         |  ${joinCol},
         |  ${queryCol.mkString(",")},
         |  product_line
         |from
         |  udw_ns.default.help_ods_ufo_key_point_productline_di
         |where
         |  channel = '${channel}'
         |  and event_day <= ${yesterDay}
         |  and event_day >= ${sevenDay}
         |  and ${joinCol} != '' """.stripMargin

    //查询hive数据
    val hiveDf = spark.sql(querySql).dropDuplicates().repartition(10).cache()
    println(s"${channel}数据量: ${hiveDf.count()}")
    hiveDf.createOrReplaceTempView("HiveTable")

    //查询关联到的业务名称的hive数据
    val productSql =
      s"""
          |select
          |  ${joinCol},
          |  concat_ws(',',collect_set(product_line)) as product_line
          |from HiveTable
          |group by ${joinCol}""".stripMargin

    println(s"${channel}关联业务名称Sql: ${productSql}")
    val productDf = spark.sql(productSql).repartition(10).cache()
    println(s"${channel}关联业务名称数据量: ${productDf.count()}")

    //查询风险等级的hive数据
    val riskSql =
      s"""
         |select
         |  ${joinCol},
         |  concat_ws(',',collect_set(product_line)) as product_risk_line,
         |  ${sqlConcat}
         |from HiveTable
         |where ${sqlFilters}
         |group by ${joinCol}""".stripMargin

    println(s"${channel}关联风险等级Sql: ${riskSql}")
    val riskHiveDf = spark.sql(riskSql).repartition(10).cache()
    println(s"${channel}关联风险等级数据量: ${riskHiveDf.count()}")

    val resDf = productDf.join(riskHiveDf,Seq(joinCol),"left_outer").na.fill("")
    resDf
  }
}
