package com.baidu.sql.tds.customer.shell.hiveToMysql

import com.baidu.sql.tds.customer.entity.CusomerDfDataEntity2
//import com.baidu.sql.tds.customer.utils.{CustometDataSync, MysqlSyncUtils}

object hiveToMysqlTest {
  def main(args: Array[String]): Unit = {
    val sourceTable = "udw_ns.default.help_ods_tb_fengling_publish_info_online"
    val sinkTable = "tb_fengling_publish_info_online"
    println(s" sourceTable: '${sourceTable}' ====> sinkTable: '${sinkTable}'")

    val eventDay = args(1)
    val shufflePartitions = args(2).toInt
    //val properties = MysqlSyncUtils.getProperTies("feedback_online")

    val hiveSql =
      s"""
        |select
        |   id,
        |   name,
        |   submit_time
        |from ${sourceTable}
        |where event_day = ${eventDay}
        |""".stripMargin

    val entity = CusomerDfDataEntity2(
      eventDay,
      hiveSql,
      shufflePartitions,
      sourceTable,
      sinkTable,
      "submit_time like '%${eventFormat}%'",
      "overwrite",
      "tb_fengling_publish_info_online_sync"
    )
    //CustometDataSync.HiveDfData2Mysql(entity, properties)
  }
}
