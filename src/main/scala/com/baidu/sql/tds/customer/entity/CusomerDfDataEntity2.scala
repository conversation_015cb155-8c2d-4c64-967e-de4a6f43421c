package com.baidu.sql.tds.customer.entity

/**
 * <AUTHOR>
 */
case class CusomerDfDataEntity2(
                                // 分区时间
                                eventDay: String,
                                // hiveSql语句
                                hiveSql: String,
                                // spark 分区数 用在reparation 和 coalesce
                                shufflePartitions: Int,
                                // 源表 Udw表
                                sourceTable: String,
                                // 目的表 MySQL表
                                sinkTable: String,
                                // 过滤条件 后加 因为上游平台刷数会失败 加上这个条件防止下游使用的数据并非最新数据
                                // feedback_time  like '%${eventFormat}%'
                                filterStr: String,
                                // 数据写入mysql的方式，追加还是覆盖
                                writeMode: String = "overwrite",
                                // 任务同步名称
                                schedulerName: String,
                               )

