package com.baidu.sql.tds

import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.baidu.sql.utils.SparkUtils.getUserIdByPhone
import com.baidu.sql.utils.TimeOperateUtil.calcnDate
import org.apache.spark.SparkConf
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}

import java.text.SimpleDateFormat
import scala.collection.mutable.ArrayBuffer
import scala.util.matching.Regex

/**
 * <AUTHOR>
 */
object FengLingYuQingToHiveTds {

  val dataFormat = new SimpleDateFormat("yyyy-MM-dd")
  val pTagRegex: Regex = "<p>(.*?)</p>".r
  val tdTagRegex: Regex = "<td>(.*?)</td>".r
  var yesterDay: String = ""


  /*
  * 风铃舆情监控同步至udw
  * */
  def main(args: Array[String]): Unit = {
    // 分区时间
    yesterDay = args(0)

    val sparkConf = new SparkConf().setAppName("FengLingYuQingToTds")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", "true")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._
    spark.sparkContext.setLogLevel("WARN")

    //获取处理人回复
    val imgFunc: UserDefinedFunction = udf((data: String) => {
      var pdata = ""
      if (data == null || data == "") {
        ""
      } else {
        if (data.contains("<p>") && data.contains("<td>")) {
          pdata = pTagRegex.findAllMatchIn(data).map(x => x.group(1)).toArray.mkString(" ").replaceAll("<span.*?>", "").replaceAll("(</span>|<br>|<br />)", "").replaceAll("&nbsp;", "").replaceAll("\" rel=\".*?\">", " ").replaceAll("<.*?=\"", " ").replaceAll("</.*?>", " ").replaceAll("\">", " ").replaceAll("style=.*?/>","").replaceAll("width=.*?/>","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ") + tdTagRegex.findAllMatchIn(data).map(x => x.group(1)).toArray.mkString(" ").replaceAll("(<br>|<br />)", "").replaceAll("width=.*?/>","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        }else if (data.contains("<p>")) {
          pdata = pTagRegex.findAllMatchIn(data).map(x => x.group(1)).toArray.mkString(" ").replaceAll("<span.*?>", "").replaceAll("(</span>|<br>|<br />)", "").replaceAll("&nbsp;", "").replaceAll("\" rel=\".*?\">", " ").replaceAll("<.*?=\"", " ").replaceAll("</.*?>", " ").replaceAll("\">", " ").replaceAll("style=.*?/>","").replaceAll("width=.*?/>","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        }else if (data.contains("<td>")) {
          pdata = tdTagRegex.findAllMatchIn(data).map(x => x.group(1)).toArray.mkString(" ").replaceAll("(<br>|<br />)", "").replaceAll("width=.*?/>","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        }
      }
      pdata
    })

    val schema: StructType = StructType(Seq(
        StructField("case_id_filter", LongType, false),
        StructField("topic_overview",StringType,false),
        StructField("problem_description",StringType,false),
        StructField("public_opinion_channel",StringType,false),
        StructField("exposure_platform",StringType,false),
        StructField("public_opinion_exposure_time",StringType,false),
        StructField("media_name",StringType,false),
        StructField("industry_label",StringType,false),
        StructField("product_type",StringType,false),
        StructField("problem_classification",StringType,false),
        StructField("whether_bring_baidu",StringType,false),
        StructField("exposure_mode",StringType,false),
        StructField("complaining_party",StringType,false),
        StructField("user_complained_party",StringType,false),
        StructField("url_complained_party",StringType,false),
        StructField("contact_person",StringType,false),
        StructField("contact_information",StringType,false),
        StructField("operation_unit_account_name",StringType,false),
        StructField("operation_unit_account_name2",StringType,false),
        StructField("search_terms",StringType,false),
        StructField("brand",StringType,false),
        StructField("category",StringType,false),
        StructField("search_area",StringType,false),
        StructField("yes_or_no_feedback",StringType,false),
        StructField("public_opinion_attachment",StringType,false),
        StructField("search_channel",StringType,false),
        StructField("public_opinion_link",StringType,false),
        StructField("submit_remark", StringType, false),
        StructField("pr_processing_result",StringType,false),
        StructField("gr_processing_result",StringType,false),
        StructField("policy_processing_result",StringType,false),
        StructField("legal_processing_result",StringType,false),
        StructField("product_handling_result",StringType,false),
        StructField("audit_processing_result",StringType,false),
        StructField("safe_processing_result",StringType,false),
        StructField("sales_unit_processing_result",StringType,false),
        StructField("guarantee_processing_result",StringType,false),
        StructField("other_processing_result",StringType,false),
        StructField("rating_of_public_opinion",StringType,false),
        StructField("score_or_not",StringType,false),
        StructField("processor_remark",StringType,false),
        StructField("account_name", StringType, false),
        StructField("receiving_account_number",StringType,false),
        StructField("game_oid",StringType,false),
        StructField("operating_activity_name",StringType,false),
        StructField("feedback_method_collaborator",StringType,false),
        StructField("question_off",StringType,false)
    ))

    //查询新建数据
    val createDf = transDataFrame(spark, yesterDay, imgFunc, schema,"新建")
    //createDf.show(10,false)
    println("查询新建数据一共:" + createDf.count())
    createDf.createOrReplaceTempView("create_table")

    spark.sql("ALTER TABLE udw_ns.default.help_ods_fengling_public_feelings_monitoring DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")

    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_fengling_public_feelings_monitoring partition (event_day = ${yesterDay} )
         |select
         |   '舆情监控' as product_line, --产品线
         |   question_type_first,   --一级问题类型
         |   question_type_second,  --二级问题类型
         |   case_id,      --case编号
         |   case when userid = '0' then '' when userid = '0、0' then '' when userid is null then '' else userid end as userid, --userid
         |   submit_user,     --提交人
         |   submit_time,     --提交时间
         |   topic_overview,        --主题概况
         |   problem_description,     --问题描述
         |   public_opinion_channel,  --舆情渠道
         |   exposure_platform,  --曝光平台
         |   public_opinion_exposure_time,  --舆情曝光时间
         |   media_name,    --媒体名称
         |   industry_label, --行业标签
         |   product_type,  --产品类型
         |   problem_classification,  --问题分类
         |   whether_bring_baidu,   --是否带百度
         |   exposure_mode,  --曝光方式
         |   complaining_party,  --投诉方
         |   user_complained_party,  --投诉方被投诉方用户名
         |   url_complained_party,  --被投诉方URL/名称
         |   contact_person,    --联系人
         |   contact_information,     --联系方式
         |   operation_unit_account_name,         --运营单位账户名/名称
         |   operation_unit_account_name2,  --运营单位账户名/名称2
         |   search_terms,  --搜索词
         |   brand, --品牌
         |   category,  --品类
         |   search_area,  --搜索地域
         |   yes_or_no_feedback,   --是否反馈
         |   public_opinion_attachment,        --舆情截图
         |   search_channel,        --搜索渠道
         |   public_opinion_link,        --舆情链接
         |   submit_remark,        --提交人备注
         |   pr_processing_result,        --PR处理结果
         |   gr_processing_result,        --GR处理结果
         |   policy_processing_result,        --策略处理结果
         |   legal_processing_result,        --法务处理结果
         |   product_handling_result,        --产品处理结果
         |   audit_processing_result,        --审核处理结果
         |   safe_processing_result,        --安全处理结果
         |   sales_unit_processing_result,        --销售单位处理结果
         |   guarantee_processing_result,        --保障处理结果
         |   other_processing_result,        --其他处理结果
         |   rating_of_public_opinion,        --舆情定级
         |   score_or_not,        --是否计分
         |   processor_remark,        --处理人备注
         |   receiving_account_number,        --接收账号
         |   account_name,        --账号名
         |   game_oid,        --游戏oid
         |   operating_activity_name,        --运营活动名称
         |   feedback_method_collaborator,        --反馈方式/协同方
         |   comment_str,        --处理人回复
         |   question_off,        --问题是否关闭
         |   case_status,        --case状态
         |   is_recall,       --是否已撤回
         |   product_line_im, -- 在线进线业务名称
         |   product_line_duke, -- 热线进线业务名称
         |   zx_incoming_status, -- 在线是否进线
         |   rx_incoming_status, -- 热线是否进线
         |   product_risk_line_im, -- 在线风险业务名称
         |   product_risk_line_duke, -- 热线风险业务名称
         |   zx_risk_level, -- 在线风险等级
         |   rx_risk_level, -- 热线风险等级
         |   rx_risk_type_classification, -- 热线风险类型-含二级分类
         |   rx_risk_type_classification_new -- 热线风险类型分类（新）
         |from create_table""".stripMargin)


    //查询已关闭数据
    val updateResDf = transDataFrame(spark, yesterDay, imgFunc, schema,"已关闭")
      .withColumn("event_day",regexp_replace(substring($"submit_time",1,11),"-",""))
      .withColumn("product_line",lit("舆情监控"))
    //updateResDf.show(10,false)
    println("查询已关闭数据一共:" + updateResDf.count())

    updateResDf.createOrReplaceTempView("update_table")

    val updateDay = spark.sql("select event_day,count(*) as event_num from update_table group by event_day order by event_day")
    updateDay.show(false)

    val eventDayList = updateDay.select("event_day").collect().toList
    for (row <- eventDayList) {
      val updateDay = row.getAs[String]("event_day")
      val totalDf = spark.sql(
          s"""
             |select
             |   product_line, --产品线
             |   question_type_first,   --一级问题类型
             |   question_type_second,  --二级问题类型
             |   case_id,      --case编号
             |   userid,       --userid
             |   submit_user,     --提交人
             |   submit_time,     --提交时间
             |   topic_overview,        --主题概况
             |   problem_description,     --问题描述
             |   public_opinion_channel,  --舆情渠道
             |   exposure_platform,  --曝光平台
             |   public_opinion_exposure_time,  --舆情曝光时间
             |   media_name,    --媒体名称
             |   industry_label, --行业标签
             |   product_type,  --产品类型
             |   problem_classification,  --问题分类
             |   whether_bring_baidu,   --是否带百度
             |   exposure_mode,  --曝光方式
             |   complaining_party,  --投诉方
             |   user_complained_party,  --投诉方被投诉方用户名
             |   url_complained_party,  --被投诉方URL/名称
             |   contact_person,    --联系人
             |   contact_information,     --联系方式
             |   operation_unit_account_name,         --运营单位账户名/名称
             |   operation_unit_account_name2,  --运营单位账户名/名称2
             |   search_terms,  --搜索词
             |   brand, --品牌
             |   category,  --品类
             |   search_area,  --搜索地域
             |   yes_or_no_feedback,   --是否反馈
             |   public_opinion_attachment,        --舆情截图
             |   search_channel,        --搜索渠道
             |   public_opinion_link,        --舆情链接
             |   submit_remark,        --提交人备注
             |   pr_processing_result,        --PR处理结果
             |   gr_processing_result,        --GR处理结果
             |   policy_processing_result,        --策略处理结果
             |   legal_processing_result,        --法务处理结果
             |   product_handling_result,        --产品处理结果
             |   audit_processing_result,        --审核处理结果
             |   safe_processing_result,        --安全处理结果
             |   sales_unit_processing_result,        --销售单位处理结果
             |   guarantee_processing_result,        --保障处理结果
             |   other_processing_result,        --其他处理结果
             |   rating_of_public_opinion,        --舆情定级
             |   score_or_not,        --是否计分
             |   processor_remark,        --处理人备注
             |   receiving_account_number,        --接收账号
             |   account_name,        --账号名
             |   game_oid,        --游戏oid
             |   operating_activity_name,        --运营活动名称
             |   feedback_method_collaborator,        --反馈方式/协同方
             |   comment_str,        --处理人回复
             |   question_off,        --问题是否关闭
             |   case_status,        --case状态
             |   is_recall,       --是否已撤回
             |   product_line_im, -- 在线进线业务名称
             |   product_line_duke, -- 热线进线业务名称
             |   zx_incoming_status, -- 在线是否进线
             |   rx_incoming_status, -- 热线是否进线
             |   product_risk_line_im, -- 在线风险业务名称
             |   product_risk_line_duke, -- 热线风险业务名称
             |   zx_risk_level, -- 在线风险等级
             |   rx_risk_level, -- 热线风险等级
             |   rx_risk_type_classification, -- 热线风险类型-含二级分类
             |   rx_risk_type_classification_new -- 热线风险类型分类（新）
             |from udw_ns.default.help_ods_fengling_public_feelings_monitoring
             |where event_day = ${updateDay} """.stripMargin)
        .withColumn("event_day",regexp_replace(substring($"submit_time",1,11),"-",""))
      println(s"查询${updateDay}日期数据一共:" + totalDf.count())

      //将其他case数据与当天关闭的case做差集
      val createResDf = totalDf.as("U")
        .join(updateResDf.as("P"), $"U.case_id" === $"P.case_id" and $"U.event_day" === $"P.event_day","left_anti")

      //要重新写入的数据集
      val u_table = updateResDf.filter($"event_day" === updateDay).as("U")
        .join(totalDf.as("C"), $"U.case_id" === $"C.case_id" and $"U.event_day" === $"C.event_day","inner")
        .select(
            $"U.product_line",
            $"U.question_type_first",
            $"U.question_type_second",
            $"U.case_id",
            $"U.userid",
            $"U.submit_user",
            $"U.submit_time",
            $"U.topic_overview",
            $"U.problem_description",
            $"U.public_opinion_channel",
            $"U.exposure_platform",
            $"U.public_opinion_exposure_time",
            $"U.media_name",
            $"U.industry_label",
            $"U.product_type",
            $"U.problem_classification",
            $"U.whether_bring_baidu",
            $"U.exposure_mode",
            $"U.complaining_party",
            $"U.user_complained_party",
            $"U.url_complained_party",
            $"U.contact_person",
            $"U.contact_information",
            $"U.operation_unit_account_name",
            $"U.operation_unit_account_name2",
            $"U.search_terms",
            $"U.brand",
            $"U.category",
            $"U.search_area",
            $"U.yes_or_no_feedback",
            $"U.public_opinion_attachment",
            $"U.search_channel",
            $"U.public_opinion_link",
            $"U.submit_remark",
            $"U.pr_processing_result",
            $"U.gr_processing_result",
            $"U.policy_processing_result",
            $"U.legal_processing_result",
            $"U.product_handling_result",
            $"U.audit_processing_result",
            $"U.safe_processing_result",
            $"U.sales_unit_processing_result",
            $"U.guarantee_processing_result",
            $"U.other_processing_result",
            $"U.rating_of_public_opinion",
            $"U.score_or_not",
            $"U.processor_remark",
            $"U.receiving_account_number",
            $"U.account_name",
            $"U.game_oid",
            $"U.operating_activity_name",
            $"U.feedback_method_collaborator",
            $"U.comment_str",
            $"U.question_off",
            $"U.case_status",
            $"U.is_recall",
            $"C.product_line_im",
            $"C.product_line_duke",
            $"C.zx_incoming_status",
            $"C.rx_incoming_status",
            $"C.product_risk_line_im",
            $"C.product_risk_line_duke",
            $"C.zx_risk_level",
            $"C.rx_risk_level",
            $"C.rx_risk_type_classification",
            $"C.rx_risk_type_classification_new",
            $"U.event_day"
          )

      println("left_anti后的数据有:" + createResDf.count() + ",u_table:" + u_table.count())

      val res_table = createResDf.unionByName(u_table)

      println("开始更新:" + updateDay + "更新数据一共:" + res_table.count())
      res_table.createOrReplaceTempView("res_table")

      spark.sql(
        s"""
           |insert overwrite table udw_ns.default.help_ods_fengling_public_feelings_monitoring partition (event_day = ${updateDay} )
           |select
           |   product_line, --产品线
           |   question_type_first,   --一级问题类型
           |   question_type_second,  --二级问题类型
           |   case_id,      --case编号
           |   case when userid = '0' then '' when userid = '0、0' then '' when userid is null then '' else userid end as userid,       --userid
           |   submit_user,     --提交人
           |   submit_time,     --提交时间
           |   topic_overview,        --主题概况
           |   problem_description,     --问题描述
           |   public_opinion_channel,  --舆情渠道
           |   exposure_platform,  --曝光平台
           |   public_opinion_exposure_time,  --舆情曝光时间
           |   media_name,    --媒体名称
           |   industry_label, --行业标签
           |   product_type,  --产品类型
           |   problem_classification,  --问题分类
           |   whether_bring_baidu,   --是否带百度
           |   exposure_mode,  --曝光方式
           |   complaining_party,  --投诉方
           |   user_complained_party,  --投诉方被投诉方用户名
           |   url_complained_party,  --被投诉方URL/名称
           |   contact_person,    --联系人
           |   contact_information,     --联系方式
           |   operation_unit_account_name,         --运营单位账户名/名称
           |   operation_unit_account_name2,  --运营单位账户名/名称2
           |   search_terms,  --搜索词
           |   brand, --品牌
           |   category,  --品类
           |   search_area,  --搜索地域
           |   yes_or_no_feedback,   --是否反馈
           |   public_opinion_attachment,        --舆情截图
           |   search_channel,        --搜索渠道
           |   public_opinion_link,        --舆情链接
           |   submit_remark,        --提交人备注
           |   pr_processing_result,        --PR处理结果
           |   gr_processing_result,        --GR处理结果
           |   policy_processing_result,        --策略处理结果
           |   legal_processing_result,        --法务处理结果
           |   product_handling_result,        --产品处理结果
           |   audit_processing_result,        --审核处理结果
           |   safe_processing_result,        --安全处理结果
           |   sales_unit_processing_result,        --销售单位处理结果
           |   guarantee_processing_result,        --保障处理结果
           |   other_processing_result,        --其他处理结果
           |   rating_of_public_opinion,        --舆情定级
           |   score_or_not,        --是否计分
           |   processor_remark,        --处理人备注
           |   receiving_account_number,        --接收账号
           |   account_name,        --账号名
           |   game_oid,        --游戏oid
           |   operating_activity_name,        --运营活动名称
           |   feedback_method_collaborator,        --反馈方式/协同方
           |   comment_str,        --处理人回复
           |   question_off,        --问题是否关闭
           |   case_status,        --case状态
           |   is_recall,       --是否已撤回
           |   product_line_im, -- 在线进线业务名称
           |   product_line_duke, -- 热线进线业务名称
           |   zx_incoming_status, -- 在线是否进线
           |   rx_incoming_status, -- 热线是否进线
           |   product_risk_line_im, -- 在线风险业务名称
           |   product_risk_line_duke, -- 热线风险业务名称
           |   zx_risk_level, -- 在线风险等级
           |   rx_risk_level, -- 热线风险等级
           |   rx_risk_type_classification, -- 热线风险类型-含二级分类
           |   rx_risk_type_classification_new -- 热线风险类型分类（新）
           |from res_table """.stripMargin)
    }

    println("更新结束")

    spark.close()
  }

  /**
   * 读取一天的case信息
   * @param spark
   * @param yestrtday 运行日期
   * @param imgFunc 自定义的udf
   * @param schema 数据模型
   * @param dataType 查询数据类型：新建、已关闭
   * @return
   */
  def transDataFrame(spark:SparkSession,yestrtday:String,imgFunc:UserDefinedFunction,schema: StructType,dataType:String): DataFrame = {
    import spark.implicits._

    val queryDate = dataType match {
      case "新建" => "create_time"
      case "已关闭" => "update_time"
    }

    val querySql = dataType match {
      case "新建" => ""
      case "已关闭" => "and DATE_FORMAT(update_time,'%Y%m%d') != DATE_FORMAT(create_time,'%Y%m%d') and status = '6'"
    }

    //舆情监控的id为337
    var caseInfo =
      s"""
         |SELECT
         |	p.case_id,              #case编号
         |	cast(p.submit_time as char) as submit_time,              #提交时间
         |	t.`name` as question_type_first,    #一级问题分类
         |	q.`name` as question_type_second, #二级问题分类
         |	u.user_name as submit_user, #提交人
         |  l.comment_str as comment_str, #处理人回复
         |  l.deal_type as deal_type, #处理状态
         |  p.status as case_status, #case状态
         |  cast(l.deal_end_time as char) as deal_end_time #处理最后时间
         |FROM
         |	( SELECT product_line_id, question_id AS question_id2, id AS case_id,submit_user_id,submit_time,status FROM case_info
         |    WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '337' ${querySql}) p
         |	JOIN (SELECT id,`name`,parent_id from question) q ON p.question_id2 = q.id
         |	JOIN (select id, `name` from question) t on q.parent_id = t.id
         |	JOIN (select id,user_name from userz) u on p.submit_user_id = u.id
         |	LEFT JOIN (select case_id,comment_str,deal_end_time,deal_type from deal_log order by deal_end_time) l on p.case_id = l.case_id
         |""".stripMargin

    //获取一天的case信息
    val caseInfoDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseInfo)
    println("读取caseInfoDf的数量为" + caseInfoDf.count())

    val caseCommentDf = caseInfoDf.withColumn("comment_str", imgFunc($"comment_str"))
      .rdd.map(row => (row.getAs[Long]("case_id"),
        (row.getAs[String]("submit_time"),
          row.getAs[String]("question_type_first"),
          row.getAs[String]("question_type_second"),
          row.getAs[String]("submit_user"),
          row.getAs[Integer]("case_status"),
          row.getAs[Integer]("deal_type"),
          row.getAs[String]("deal_end_time"),
          row.getAs[String]("comment_str"))))
      .groupByKey()
      .mapValues(_.toArray)
      .map({ case (caseId, caseInfoArray) =>
        val commentList = ArrayBuffer.empty[String]
        var submit_time = ""
        var question_type_first = ""
        var question_type_second = ""
        var submit_user = ""
        var case_status = ""
        var deal_type = ""
        for (key <- 0 to caseInfoArray.size - 1) {
          val caseInfo = caseInfoArray(key)
          submit_time = caseInfo._1
          question_type_first = caseInfo._2
          question_type_second = caseInfo._3
          submit_user = caseInfo._4
          case_status = caseInfo._5.toString
          deal_type = caseInfo._6.toString
          val deal_end_time = caseInfo._7
          if (deal_end_time != null) {
            commentList.append("回复时间:" + deal_end_time + ",内容：" + caseInfo._8)
          }
        }
        (caseId, submit_time, question_type_first, question_type_second, submit_user, case_status,deal_type, commentList.mkString(";"))
      }).toDF("case_id", "submit_time", "question_type_first", "question_type_second", "submit_user", "case_status","deal_type","comment_str")
      .repartition(2)

    println("读取caseCommentDf的数量为" + caseCommentDf.count())

    val deal_log =
      s"""
         |select
         |  case_id,
         |  update_time,
         |  form_eng_name,
         |  value
         |from
         |deal_log_filed
         |where case_id in ( SELECT id FROM case_info WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '337' ${querySql})
         |and update_time in
         |(select
         | max(update_time) as update_time
         |from deal_log_filed
         |where case_id in ( SELECT id FROM case_info WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$yestrtday' and product_line_id = '337' ${querySql})
         |group by case_id,form_eng_name)
         |""".stripMargin

    val dealLogDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,deal_log)

    println("读取dealLogDf的数量为" + dealLogDf.count())

    val jonDf = caseCommentDf.as("I").join(dealLogDf.as("D"), $"I.case_id" === $"D.case_id")
      .select(
        $"I.case_id" as "case_id",
        $"I.submit_time" as "submit_time",
        $"I.question_type_first",
        $"I.question_type_second",
        $"I.submit_user",
        $"I.case_status",
        $"I.deal_type" as "is_recall",
        $"I.comment_str" as "comment_str",
        $"D.form_eng_name",
        $"D.value")
      .withColumn("case_status", when($"case_status" === "3", "处理中").when($"case_status" === "4", "待关闭").when($"case_status" === "6", "已关闭").when($"case_status" === "2", "处理中-委派").otherwise("其他"))
      //是否撤回
      .withColumn("is_recall", when($"is_recall" === "15", "是").otherwise("否"))
      .repartition(2)

    println("读取jonDf的数量为" + jonDf.count())

    val filterDf = jonDf.rdd.map(row => (row.getAs[Long]("case_id"), row.getAs[String]("form_eng_name") -> row.getAs[String]("value")))
      .groupByKey()
      .mapValues(iter => iter.toMap)
      .map({ case (caseId, engNameArray) =>
        val buffer = ArrayBuffer.empty[Any]
        val topic_overview = engNameArray.getOrElse("Topic_overview","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        //需要调用自定义函数
        val problem_description = engNameArray.getOrElse("Problem_description","")
        val public_opinion_channel = engNameArray.getOrElse("Public_opinion_channel","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val exposure_platform = engNameArray.getOrElse("Exposure_platform","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val public_opinion_exposure_time = engNameArray.getOrElse("Public_opinion_exposure_time","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val media_name = engNameArray.getOrElse("Media_name","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val industry_label = engNameArray.getOrElse("Industry_label","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val product_type = (if (engNameArray.getOrElse("Product_line","") == "") engNameArray.getOrElse("Product_type","") else engNameArray.getOrElse("Product_line","")).replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val problem_classification = engNameArray.getOrElse("Problem_classification","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val whether_bring_baidu = engNameArray.getOrElse("Whether_to_bring_Baidu","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val exposure_mode = engNameArray.getOrElse("Exposure_mode","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val complaining_party = engNameArray.getOrElse("Complaining_party","")
        val user_complained_party = if (engNameArray.getOrElse("User_name_of_the_complained_party","") == "") engNameArray.getOrElse("Complaint_account","") else engNameArray.getOrElse("User_name_of_the_complained_party","")
        val url_complained_party = engNameArray.getOrElse("URL_name_of_the_complained_party","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val contact_person = engNameArray.getOrElse("Contact_person","")
        val contact_information = engNameArray.getOrElse("Contact_information","")
        val operation_unit_account_name = engNameArray.getOrElse("Operation_unit_account_name","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val operation_unit_account_name2 = engNameArray.getOrElse("Operation_unit_account_name2","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "").replaceAll(",", "-")
        val search_terms = if (engNameArray.getOrElse("Search_terms","") == "") engNameArray.getOrElse("Search_term","") else engNameArray.getOrElse("Search_terms","")
        val brand = engNameArray.getOrElse("brand","")
        val category = engNameArray.getOrElse("category","")
        val search_area = engNameArray.getOrElse("search_area","")
        val yes_or_no_feedback = engNameArray.getOrElse("Yes_or_no_feedback","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val public_opinion_attachment = engNameArray.getOrElse("public_opinion_attachment","")
        val search_channel = engNameArray.getOrElse("search_channel","")
        val public_opinion_link = engNameArray.getOrElse("Public_opinion_link","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val submit_remark = (if (engNameArray.getOrElse("remark","") == "") engNameArray.getOrElse("Remark","") else engNameArray.getOrElse("remark","")).replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val pr_processing_result = engNameArray.getOrElse("PR_processing_result","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val gr_processing_result = engNameArray.getOrElse("GR_processing_result","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val policy_processing_result = engNameArray.getOrElse("Policy_processing_result","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val legal_processing_result = engNameArray.getOrElse("Legal_processingresults","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val product_handling_result = engNameArray.getOrElse("Product_handling_result","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val audit_processing_result = (if (engNameArray.getOrElse("Audit_processing_result","") == "") engNameArray.getOrElse("Audit_processingresults","") else engNameArray.getOrElse("Audit_processing_result","")).replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val safe_processing_result = engNameArray.getOrElse("Safe_processing_result","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val sales_unit_processing_result = engNameArray.getOrElse("Sales_unit_processing_results","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val guarantee_processing_result = (if (engNameArray.getOrElse("Guarantee_processing_result","") == "") engNameArray.getOrElse("guarantee_ProcessingResults","") else engNameArray.getOrElse("Guarantee_processing_result","")).replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val other_processing_result = engNameArray.getOrElse("Other_processing_results","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val rating_of_public_opinion = engNameArray.getOrElse("Rating_of_public_opinion","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val score_or_not = engNameArray.getOrElse("Score_or_not","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val processor_remark = engNameArray.getOrElse("remark2","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val account_name = engNameArray.getOrElse("Account_name","")
        val receiving_account_number = engNameArray.getOrElse("Receiving_account_number","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")
        val game_oid = engNameArray.getOrElse("game_oid","")
        val operating_activity_name = engNameArray.getOrElse("Operating_activity_name","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val feedback_method_collaborator = engNameArray.getOrElse("Feedback_method_collaborator","").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val question_off = engNameArray.getOrElse("Question_off","").replaceAll("\\[\"", "").replaceAll("\"]", "").replaceAll("\"", "")

        buffer.append(caseId)
        buffer.append(topic_overview)
        buffer.append(problem_description)
        buffer.append(public_opinion_channel)
        buffer.append(exposure_platform)
        buffer.append(public_opinion_exposure_time)
        buffer.append(media_name)
        buffer.append(industry_label)
        buffer.append(product_type)
        buffer.append(problem_classification)
        buffer.append(whether_bring_baidu)
        buffer.append(exposure_mode)
        buffer.append(complaining_party)
        buffer.append(user_complained_party)
        buffer.append(url_complained_party)
        buffer.append(contact_person)
        buffer.append(contact_information)
        buffer.append(operation_unit_account_name)
        buffer.append(operation_unit_account_name2)
        buffer.append(search_terms)
        buffer.append(brand)
        buffer.append(category)
        buffer.append(search_area)
        buffer.append(yes_or_no_feedback)
        buffer.append(public_opinion_attachment)
        buffer.append(search_channel)
        buffer.append(public_opinion_link)
        buffer.append(submit_remark)
        buffer.append(pr_processing_result)
        buffer.append(gr_processing_result)
        buffer.append(policy_processing_result)
        buffer.append(legal_processing_result)
        buffer.append(product_handling_result)
        buffer.append(audit_processing_result)
        buffer.append(safe_processing_result)
        buffer.append(sales_unit_processing_result)
        buffer.append(guarantee_processing_result)
        buffer.append(other_processing_result)
        buffer.append(rating_of_public_opinion)
        buffer.append(score_or_not)
        buffer.append(processor_remark)
        buffer.append(account_name)
        buffer.append(receiving_account_number)
        buffer.append(game_oid)
        buffer.append(operating_activity_name)
        buffer.append(feedback_method_collaborator)
        buffer.append(question_off)

        Row.fromSeq(buffer)
      })

    val filterRes = spark.createDataFrame(filterDf, schema)
      .dropDuplicates("case_id_filter")
      .withColumn("problem_description",regexp_replace(imgFunc($"problem_description"),"\"",""))
      .repartition(2)

    println("读取filterRes的数量为" + filterRes.count())

    // 拉取用于接口获取userid数据的字段
    val numberDF = filterRes.select("case_id_filter","contact_information").filter(col("contact_information") =!= "" and col("contact_information") =!= "无").distinct().repartition(1)
    val userIdDf = numberDF.map(row => {
        val case_id_filter = row.getAs[Long]("case_id_filter")
        val temp_call_phone = row.getAs[String]("contact_information")
        var userid = ""
        if (temp_call_phone.contains("、")){
          val phone1 = temp_call_phone.split("、")(0)
          val phone2 = temp_call_phone.split("、")(1)
          userid = getUserIdByPhone(phone1) + "、" + getUserIdByPhone(phone2)
        }else {
          userid = getUserIdByPhone(temp_call_phone)
        }
        (case_id_filter,temp_call_phone,userid)
      }).toDF("case_id_number","temp_call_phone_user","userid")
      .dropDuplicates("case_id_number")
      .repartition(2)

    var resDf = filterRes
      .join(jonDf, filterRes("case_id_filter") === jonDf("case_id"))
      .join(userIdDf, filterRes("case_id_filter") === userIdDf("case_id_number"),"left")
      .select(
          $"question_type_first".cast("string") as "question_type_first",
          $"question_type_second".cast("string") as "question_type_second",
          $"case_id".cast("string") as "case_id",
          $"userid".cast("string") as "userid",
          $"submit_user".cast("string") as "submit_user",
          $"is_recall".cast("string") as "is_recall",
          $"submit_time".cast("string") as "submit_time",
          $"topic_overview".cast("string") as "topic_overview",
          $"problem_description".cast("string") as "problem_description",
          $"public_opinion_channel".cast("string") as "public_opinion_channel",
          $"exposure_platform".cast("string") as "exposure_platform",
          $"public_opinion_exposure_time".cast("string") as "public_opinion_exposure_time",
          $"media_name".cast("string") as "media_name",
          $"industry_label".cast("string") as "industry_label",
          $"product_type".cast("string") as "product_type",
          $"problem_classification".cast("string") as "problem_classification",
          $"whether_bring_baidu".cast("string") as "whether_bring_baidu",
          $"exposure_mode".cast("string") as "exposure_mode",
          $"complaining_party".cast("string") as "complaining_party",
          $"user_complained_party".cast("string") as "user_complained_party",
          $"url_complained_party".cast("string") as "url_complained_party",
          $"contact_person".cast("string") as "contact_person",
          $"contact_information".cast("string") as "contact_information",
          $"operation_unit_account_name".cast("string") as "operation_unit_account_name",
          $"operation_unit_account_name2".cast("string") as "operation_unit_account_name2",
          $"search_terms".cast("string") as "search_terms",
          $"brand".cast("string") as "brand",
          $"category".cast("string") as "category",
          $"search_area".cast("string") as "search_area",
          $"yes_or_no_feedback".cast("string") as "yes_or_no_feedback",
          $"public_opinion_attachment".cast("string") as "public_opinion_attachment",
          $"search_channel".cast("string") as "search_channel",
          $"public_opinion_link".cast("string") as "public_opinion_link",
          $"submit_remark".cast("string") as "submit_remark",
          $"pr_processing_result".cast("string") as "pr_processing_result",
          $"gr_processing_result".cast("string") as "gr_processing_result",
          $"policy_processing_result".cast("string") as "policy_processing_result",
          $"legal_processing_result".cast("string") as "legal_processing_result",
          $"product_handling_result".cast("string") as "product_handling_result",
          $"audit_processing_result".cast("string") as "audit_processing_result",
          $"safe_processing_result".cast("string") as "safe_processing_result",
          $"sales_unit_processing_result".cast("string") as "sales_unit_processing_result",
          $"guarantee_processing_result".cast("string") as "guarantee_processing_result",
          $"other_processing_result".cast("string") as "other_processing_result",
          $"rating_of_public_opinion".cast("string") as "rating_of_public_opinion",
          $"score_or_not".cast("string") as "score_or_not",
          $"processor_remark".cast("string") as "processor_remark",
          $"account_name".cast("string") as "account_name",
          $"receiving_account_number".cast("string") as "receiving_account_number",
          $"game_oid".cast("string") as "game_oid",
          $"operating_activity_name".cast("string") as "operating_activity_name",
          $"feedback_method_collaborator".cast("string") as "feedback_method_collaborator",
          $"comment_str".cast("string") as "comment_str",
          $"question_off".cast("string") as "question_off",
          $"case_status".cast("string") as "case_status"
      )
      .dropDuplicates("case_id")

    resDf.createOrReplaceTempView("ResTable")

    if (dataType.equals("新建")) {
      //读取hive的在线和热线数据,关联userid和电话号码
      getHiveDF(spark,"在线").createOrReplaceTempView("ImTable")
      getHiveDF(spark,"热线").createOrReplaceTempView("DukeTable")
      val hiveSql =
        """
          |select
          |   r.*,
          |   case when i.product_line is null then '' else i.product_line end as product_line_im, -- 在线进线业务名称
          |   case when d.product_line is null then '' else d.product_line end as product_line_duke, -- 热线进线业务名称
          |   case when i.userid is not null then '是' else '否' end as zx_incoming_status, -- 在线是否进线
          |   case when d.rx_temp_call_phone is not null then '是' else '否' end as rx_incoming_status, -- 热线是否进线
          |   case when i.product_risk_line is null then '' else i.product_risk_line end as product_risk_line_im, -- 在线高危业务名称
          |   case when d.product_risk_line is null then '' else d.product_risk_line end as product_risk_line_duke, -- 热线高危业务名称
          |   case when i.risk_level is null then '' else i.risk_level end as zx_risk_level, -- 在线风险等级
          |   case when d.rx_risk_level is null then '' else d.rx_risk_level end as rx_risk_level, -- 热线风险等级
          |   case when d.rx_risk_type_classification is null then '' else d.rx_risk_type_classification end as rx_risk_type_classification, -- 热线风险类型-含二级分类
          |   case when d.rx_risk_type_classification_new is null then '' else d.rx_risk_type_classification_new end as rx_risk_type_classification_new -- 热线风险类型分类（新）
          |from ResTable r
          |left join ImTable i on r.userid = i.userid
          |left join DukeTable d on r.contact_information = d.rx_temp_call_phone
          |""".stripMargin

      resDf = spark.sql(hiveSql).repartition(10)
    }

    resDf
  }

  /**
   * 获取hive的在线和热线数据
   * @param spark
   * @param channel 在线或热线
   * @return
   */
  def getHiveDF(spark: SparkSession, channel: String): DataFrame = {
    //关联字段
    val joinCol = channel match {
      case "在线" => "userid"
      case "热线" => "rx_temp_call_phone"
      case _ => ""
    }

    //查询的字段
    val queryCol = channel match {
      case "在线" => List("risk_level")
      case "热线" => List("rx_risk_level","rx_risk_type_classification","rx_risk_type_classification_new")
      case _ => List("")
    }
    // 构建SQL过滤条件
    val sqlFilters = queryCol.map(field => s"$field != '' and $field != '无'").mkString("(", ") or (", ")")
    // 构建SQL聚合条件
    val sqlConcat = queryCol.map(field => s"concat_ws(',',collect_set($field)) as $field").mkString(",")
    //7天前日期
    val sevenDay = calcnDate(yesterDay,-7)

    val querySql =
      s"""select
         |  ${joinCol},
         |  ${queryCol.mkString(",")},
         |  product_line
         |from
         |  udw_ns.default.help_ods_ufo_key_point_productline_di
         |where
         |  channel = '${channel}'
         |  and event_day <= ${yesterDay}
         |  and event_day >= ${sevenDay}
         |  and ${joinCol} != '' """.stripMargin

    //查询hive数据
    val hiveDf = spark.sql(querySql).dropDuplicates().repartition(10).cache()
      println(s"${channel}数据量: ${hiveDf.count()}")
    hiveDf.createOrReplaceTempView("HiveTable")

    //查询关联到的业务名称的hive数据
    val productSql = s"""
       |select
       |  ${joinCol},
       |  concat_ws(',',collect_set(product_line)) as product_line
       |from HiveTable
       |group by ${joinCol}""".stripMargin

    val productDf = spark.sql(productSql).repartition(10).cache()
    println(s"${channel}关联业务名称数据量: ${productDf.count()}")

    //查询风险等级的hive数据
    val riskSql =
      s"""
         |select
         |  ${joinCol},
         |  concat_ws(',',collect_set(product_line)) as product_risk_line,
         |  ${sqlConcat}
         |from HiveTable
         |where ${sqlFilters}
         |group by ${joinCol}""".stripMargin

    val riskHiveDf = spark.sql(riskSql).repartition(10).cache()
    println(s"${channel}关联风险等级数据量: ${riskHiveDf.count()}")

    val resDf = productDf.join(riskHiveDf,Seq(joinCol),"left_outer").na.fill("")
    resDf
  }

}
