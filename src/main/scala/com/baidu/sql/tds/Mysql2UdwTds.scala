package com.baidu.sql.tds

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

import java.util.Properties

/**
 * <AUTHOR>
 * @Date 2023-03-21
 */
object Mysql2UdwTds {

  private val mysqlLib = "mysql_lib"

  def main(args: Array[String]): Unit = {

    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_mysql_2_udw")
      .enableHiveSupport()
      .getOrCreate()

    val url = JDBCUtils.url
    val user = JDBCUtils.user
    val password = JDBCUtils.password
    val driver = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)

    val mysqlDF = spark.read
      .option("driver", driver)
      .jdbc(url, mysqlLib, properties)
      .where("id = " + args(0))
      .select("sql_statement", "table")

    val sqlStatement = mysqlDF.first().getString(0)
    val writeBackTable = mysqlDF.first().getString(1)

//    val eventDay = sys.env("DATE")
    val eventDay = args(1)

    val result = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", sqlStatement).load()

    val tempTable = "temp_table_" + args(0)

    result.toDF().createOrReplaceTempView(tempTable)

    val typeInfo: String = if (args(0) == "8" || args(0) == "9") "into" else "overwrite"
    spark.sql("insert " + typeInfo + " table " + writeBackTable +" partition (event_day =" + eventDay + ") " +
      "select * from " + tempTable)

    spark.close()
  }

}
