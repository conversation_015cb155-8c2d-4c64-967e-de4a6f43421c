package com.baidu.sql.tds

import cn.hutool.core.date.{DateField, DateUtil}
import com.alibaba.fastjson.{JSONArray, JSONObject}
import okhttp3._
import org.apache.spark.SparkConf
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._


/**
 * <AUTHOR>
 */
object GetUrlDFTds {

  def main(args: Array[String]): Unit = {

    // 最终落地表
    val writeBackTable: String = args(0)

    //分区日期
    var event_day: String = args(1)
    var event_hour: String = args(2)

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .setAppName("getApiDF")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    //分区日期格式处理
    var timeStr: String = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6,
      8) + " " + event_hour + ":00"

    val baseTime = DateUtil.parse(timeStr, "yyyy-MM-dd HH:mm")
    val offsetDateTime = DateUtil.offset(baseTime, DateField.HOUR, -12)
    val formatDate = DateUtil.format(offsetDateTime, "yyyyMMddHHmmss")


    event_day = formatDate.substring(0, 8)
    event_hour = formatDate.substring(8, 10)

    var fetchHiveSql =
      s"""
         |select
         |  uid as user_id
         |from
         |  ubs_feed.feed_dwd_pub_log_hi
         |where event_day = $event_day and event_hour = $event_hour
         |group by
         |  user_id
         |order by rand()
         |limit 100000
         |""".stripMargin

    println(fetchHiveSql)

    var df: DataFrame = spark.sql(fetchHiveSql)

    // 注册udf
    val functionPost: UserDefinedFunction = functions.udf(postRequest _)

    df.show(20)

    df = df.repartition(50)

    df = df.withColumn("resData", functionPost(col("user_id")))
      .withColumn("loc_source", functions.get_json_object(col("resData"), "$.user_attribute[0].rt_loc[0].adjusted_coordination.loc_source"))
      .withColumn("latitude", functions.get_json_object(col("resData"), "$.user_attribute[0].rt_loc[0].adjusted_coordination.latitude"))
      .withColumn("radius", functions.get_json_object(col("resData"), "$.user_attribute[0].rt_loc[0].adjusted_coordination.radius"))
      .withColumn("longitude", functions.get_json_object(col("resData"), "$.user_attribute[0].rt_loc[0].adjusted_coordination.longitude"))
      .withColumn("timestamp", functions.get_json_object(col("resData"), "$.user_attribute[0].rt_loc[0].adjusted_coordination.timestamp"))
      .withColumn("current_time", current_timestamp())
      .drop(col("resData"))

    df.show(20)

    df.createOrReplaceTempView("loc_fields")


    val sql = "insert overwrite table " + writeBackTable + " partition (event_day = '" + event_day + "'," +
      "event_hour= '" + event_hour + "') " + "select user_id, loc_source, latitude, radius, longitude, timestamp, current_time from loc_fields"

    spark.sql(sql)
  }

  def postRequest(uid: String): String = {

    val body = new JSONObject

    val userAttribute = new JSONArray

    val userAttributeItem = new JSONObject

    val userId = new JSONObject
    userId.put("id", uid)
    userId.put("type", "USERID")

    val attribute = new JSONArray

    val attributeItem = new JSONObject
    attributeItem.put("name", "rt_loc")
    attribute.add(attributeItem)

    userAttributeItem.put("user_id", userId)
    userAttributeItem.put("attribute", attribute)

    userAttribute.add(userAttributeItem)


    body.put("user_attribute", userAttribute)
    body.put("plugin_name", "rt_loc_plugin")

    var response: Response = null

    val requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON
      .toString), body.toString())
    val okHttpClient = new OkHttpClient
    val request = new Request.Builder().url("http://uas.baidu-int.com:8081/json/v2").addHeader("Authorization", "haowen_fengkong").addHeader("Password", "fengkong801").post(requestBody).build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

}
