package com.baidu.sql.customized.hubmatrix.duke

import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils, TimeOperateUtil}
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataTypes, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}

/**
 * 服务运营运算分析汇集数据(Duke)
 */
object DukeHubMatrix {
  val indexName = "work_order_info"
  var YesterDay = ""

  //呼出明细的业务线匹配
  val calledMap = Map(
    "百度热线技能组" ->	"百度热线",
    "百度大会员" ->	"Du会员热线",
    "暖阳热线" ->	"暖阳热线",
    "青少年热线" ->	"青少年热线",
    "文心一言" ->	"文心一言热线",
    "百度文库咨询技能组" ->	"文库热线",
    "百度文库投诉技能组" ->	"文库热线",
    "百度文库续费技能组" ->	"文库热线",
    "网信办组" ->	"网信办热线",
    "收银台客服组" ->	"收银台热线",
    "商业搜索咨询组" ->	"商业广告热线",
    "商业搜索投诉组" ->	"商业广告热线",
    "认证咨询组" ->	"商业广告热线",
    "认证投诉组" ->	"商业广告热线",
    "政策解读组" ->	"商业广告热线",
    "百度游戏" ->	"百度游戏热线",
    "百度教育咨询技能组" ->	"百度教育热线",
    "百度教育投诉技能组" ->	"百度教育热线",
    "百度教育续费技能组" ->	"百度教育热线",
    "爱企查" ->	"爱企查热线",
    "IDG外呼组01" ->	"萝卜快跑热线",
    "萝卜快跑业务组" ->	"萝卜快跑热线",
    "上海12345热线绿通" ->	"上海12345绿通",
    "服务专家" ->	"服务专家",
    "百度反诈" ->	"百度反诈",
    "上海保障" ->	"上海保障",
    "回访调研" ->	"客户调研"
  )

  //呼入明细的业务线匹配
  val callingMap = Map(
    "文心一言" ->	"文心一言热线",
    "百度文库咨询技能组" ->	"文库热线",
    "百度文库续费技能组" ->	"文库热线",
    "网信办组" ->	"网信办热线",
    "收银台客服组" ->	"收银台热线",
    "商业搜索咨询组" ->	"商业广告热线",
    "商业搜索投诉组" ->	"商业广告热线",
    "认证咨询组" ->	"商业广告热线",
    "认证投诉组" ->	"商业广告热线",
    "政策解读组" ->	"商业广告热线",
    "青少年热线" ->	"青少年热线",
    "暖阳热线" ->	"暖阳热线",
    "萝卜快跑业务组" ->	"萝卜快跑热线",
    "百度游戏" ->	"百度游戏热线",
    "百度热线技能组" ->	"百度热线",
    "百度教育咨询技能组" ->	"百度教育热线",
    "百度教育续费技能组" ->	"百度教育热线",
    "爱企查" ->	"爱企查热线",
    "百度大会员" ->	"Du会员热线",
    "上海12345热线绿通" ->	"上海12345绿通"
  )

  //呼出明细按照这个优先级来排序业务线
  val calledSortMap = Map(
      "百度热线" -> 1,
      "Du会员热线" -> 2,
      "暖阳热线" -> 3,
      "青少年热线" -> 4,
      "文库热线" -> 5,
      "收银台热线" -> 6,
      "网信办热线" -> 7,
      "商业广告热线" -> 8,
      "百度游戏热线" -> 9,
      "客户调研" -> 10,
      "文心一言热线" -> 11,
      "爱企查热线" -> 12,
      "百度教育热线" -> 13,
      "上海12345绿通" -> 14,
      "服务专家" -> 15,
      "百度反诈" -> 16,
      "上海保障" -> 17
  )

  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)
    //是否删除分区数据
    val isTrunc = args(1)

    println(s"YesterDay:${YesterDay}")
    //sparkSession配置
    val sparkConf = new SparkConf().setAppName("ReadEs")
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val sc = spark.sparkContext
    sc.setLogLevel("WARN")
    import spark.implicits._

    // 获取yesterday豪秒级时间戳
    val startTimeStamp = TimeOperateUtil.getTimeStamp(YesterDay)
    val endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))

    //读取ES的BAIDU数据库字段数据
    val esBaiduDf = getEsData(spark, "baidu", startTimeStamp, endTimeStamp)
    println(s"读取ES的baidu登陆的数据有：${esBaiduDf.count()}")

    //读取ES的BDIDG数据库字段数据
    val esBdidgDf = getEsData(spark, "bdidg", startTimeStamp, endTimeStamp)
    println(s"读取ES的bdidg登陆的数据有：${esBdidgDf.count()}")

    //读取新版历史呼入明细页面字段数据:baidu
    val callingBaiduDf = getCallInDetail(spark,"baidu")
    println(s"呼入明细BaiduDf数据量为${callingBaiduDf.count()}条")

    //读取新版历史呼入明细页面字段数据:bdidg
    val callingBdidgDf = getCallInDetail(spark,"bdidg")
    println(s"呼入明细BdidgDf数据量为${callingBdidgDf.count()}条")

    //呼入明细和ES数据关联
    //BAIDU
    val callingBaiduRes = callingBaiduDf.as("C")
      .join(esBaiduDf.as("E"),
        $"E.rx_process_instance_id" === $"C.rx_process_instance_id",
        "left_outer")
      .select(
        $"C.form_name",
        $"C.business_line",
        $"C.session_id",
        $"C.resource_id",
        $"C.resource_name",
        $"C.start_time",
        $"C.end_time",
        $"C.rx_phone_calling",
        $"C.rx_phone_called",
        $"C.skill_group_name",
        $"C.rx_queue_duration",
        $"C.rx_ring_duration",
        $"C.talk_duration",
        $"C.rx_end_type_name",
        $"C.keypress_name",
        $"C.rx_process_instance_id",
        when($"E.rx_temp_title".isNull,"").otherwise($"E.rx_temp_title") as "rx_temp_title",
        when($"E.rx_temp_desc".isNull,"").otherwise($"E.rx_temp_desc") as "rx_temp_desc"
      )
      .dropDuplicates()
      .repartition(10)

    //BDIDG
    val callingBdidgRes = callingBdidgDf.as("C")
      .join(esBdidgDf.as("E"),
        $"E.rx_process_instance_id" === $"C.rx_process_instance_id",
        "left_outer")
      .select(
        $"C.form_name",
        $"C.business_line",
        $"C.session_id",
        $"C.resource_id",
        $"C.resource_name",
        $"C.start_time",
        $"C.end_time",
        $"C.rx_phone_calling",
        $"C.rx_phone_called",
        $"C.skill_group_name",
        $"C.rx_queue_duration",
        $"C.rx_ring_duration",
        $"C.talk_duration",
        $"C.rx_end_type_name",
        $"C.keypress_name",
        $"C.rx_process_instance_id",
        when($"E.rx_temp_title".isNull,"").otherwise($"E.rx_temp_title") as "rx_temp_title",
        when($"E.rx_temp_desc".isNull,"").otherwise($"E.rx_temp_desc") as "rx_temp_desc"
      )
      .dropDuplicates()
      .repartition(10)

    //合并
    val callingRes = callingBaiduRes.unionByName(callingBdidgRes)
    println(s"和ES关联后的callingBaiduRes数据：${callingBaiduRes.count()}," +
      s"callingBdidgRes数据量：${callingBdidgRes.count()}," +
      s"总量为${callingRes.count()}")

    //baidu登陆的呼出明细数据
    val calledBaiduDf = getCallOutDetail(spark,"baidu")
    println(s"读取呼出明细Baidu数据为:${calledBaiduDf.count()}条")

    //bdidg登陆的呼出明细数据
    val calledBdidgDf = getCallOutDetail(spark,"bdidg")
    println(s"读取呼出明细Bdidg数据为:${calledBdidgDf.count()}条")

    //呼出明细和ES数据关联
    //BAIDU
    val calledBaiduRes = calledBaiduDf.as("C")
      .join(esBaiduDf.as("E"),
        $"E.rx_process_instance_id" === $"C.rx_process_instance_id",
        "left_outer")
      .select(
        $"C.form_name",
        $"C.business_line",
        $"C.session_id",
        $"C.resource_id",
        $"C.resource_name",
        $"C.start_time",
        $"C.end_time",
        $"C.rx_phone_calling",
        $"C.rx_phone_called",
        $"C.skill_group_name",
        $"C.rx_ring_duration",
        $"C.talk_duration",
        $"C.rx_acw_duration",
        $"C.rx_end_type_name",
        $"C.keypress_name",
        $"C.rx_process_instance_id",
        when($"E.rx_temp_title".isNull,"").otherwise($"E.rx_temp_title") as "rx_temp_title",
        when($"E.rx_temp_desc".isNull,"").otherwise($"E.rx_temp_desc") as "rx_temp_desc"
      )
      .dropDuplicates()
      .repartition(10)

    //BDIDG
    val calledBdidgRes = calledBdidgDf.as("C")
      .join(esBdidgDf.as("E"),
        $"E.rx_process_instance_id" === $"C.rx_process_instance_id",
        "left_outer")
      .select(
        $"C.form_name",
        $"C.business_line",
        $"C.session_id",
        $"C.resource_id",
        $"C.resource_name",
        $"C.start_time",
        $"C.end_time",
        $"C.rx_phone_calling",
        $"C.rx_phone_called",
        $"C.skill_group_name",
        $"C.rx_ring_duration",
        $"C.talk_duration",
        $"C.rx_acw_duration",
        $"C.rx_end_type_name",
        $"C.keypress_name",
        $"C.rx_process_instance_id",
        when($"E.rx_temp_title".isNull,"").otherwise($"E.rx_temp_title") as "rx_temp_title",
        when($"E.rx_temp_desc".isNull,"").otherwise($"E.rx_temp_desc") as "rx_temp_desc"
      )
      .dropDuplicates()
      .repartition(10)

    //合并
    val calledRes = calledBaiduRes.unionByName(calledBdidgRes)
    println(s"和ES关联后的calledBaiduRes数据：${calledBaiduRes.count()}," +
      s"calledBdidgRes数据量：${calledBdidgRes.count()}," +
      s"总量为${calledRes.count()}")

    //坐席话务统计
    val agentBaiduDf = getAgentStatistics(spark,"baidu")
    println(s"读取baidu坐席话务统计的数据有：${agentBaiduDf.count()}")

    val agentBdidgDf = getAgentStatistics(spark,"bdidg")
    println(s"读取bdidg坐席话务统计的数据有：${agentBdidgDf.count()}")
    //合并
    val agentRes = agentBaiduDf.unionByName(agentBdidgDf)

    //录音收听下载历史页面数据
    val recordBaiduDf = getRecordDownload(spark,"baidu")
    println(s"读取baidu录音收听下载历史的数据有：${recordBaiduDf.count()}")

    val recordBdidgDf = getRecordDownload(spark,"bdidg")
    println(s"读取bdidg录音收听下载历史的数据有：${recordBdidgDf.count()}")
    //合并
    val recordRes = recordBaiduDf.unionByName(recordBdidgDf)

    //合并所有表单数据
    val resDf = callingRes
      .unionByName(calledRes,true)
      .unionByName(agentRes,true)
      .unionByName(recordRes,true)
      .withColumn("system_name",lit("duke"))
      .na.fill("")
      .dropDuplicates()
      .repartition(10)

    resDf.createOrReplaceTempView("resTable")
    println(s"合并完后总数据量为:${resDf.count()}条")

    //第二个参数是delete才删除分区数据
    if (isTrunc.equals("delete")) {
      // 在插入数据前先drop掉要插入的分区，保证写入数据幂等性及弥补原有分区无法新加字段问题
      spark.sql(
        s"""
           |ALTER TABLE udw_ns.default.help_ods_mosaic_hub_matrix
           |DROP IF EXISTS PARTITION (event_day = $YesterDay )
           |""".stripMargin)
    }

    //写入数仓表
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_mosaic_hub_matrix partition (event_day = $YesterDay )
        |select
        |   system_name, -- 系统名称
        |   form_name, -- 表单名称
        |   business_line, -- 业务线
        |   session_id, -- 会话id
        |   resource_id, -- 客服账号
        |   resource_name, -- 客服姓名
        |   start_time, -- 开始时间
        |   end_time, -- 结束时间
        |   rx_phone_calling, -- 主叫号码
        |   rx_phone_called, -- 被叫号码
        |   skill_group_name, -- 技能组
        |   rx_queue_duration, -- 排队时长
        |   rx_ring_duration, -- 振铃时长
        |   talk_duration, -- 会话时长
        |   rx_acw_duration, -- 事后整理时长
        |   rx_end_type_name, -- 结束类型
        |   keypress_name, -- 满意度
        |   rx_process_instance_id, -- 工单编号
        |   rx_temp_title, -- 工单标题
        |   rx_temp_desc, -- 工单内容
        |   rx_call_type, -- 呼叫方向
        |   rx_answer_calling_num, -- 呼入接听量
        |   rx_calling_duratoin, -- 呼入通话时长
        |   rx_called_num, -- 外呼量
        |   rx_answer_called_num, -- 外呼接听量
        |   rx_called_duration, -- 外呼通话时长
        |   rx_leave_all_duration, -- 置闲总时长
        |   rx_work_all_duration, -- 工作总时长
        |   rx_busy_all_duration, -- 置忙总时长
        |   login_all_duration -- 登录时长
        |from resTable
        |""".stripMargin)

    spark.close()

  }

  /**
   * 匹配呼入明细的业务线字段
   */
  val getCallingLine = udf((skillGroup: String) => {
    callingMap.getOrElse(skillGroup, "-")
  })

  /**
   * 匹配呼出明细的业务线字段
   */
  val getCalledLine = udf((skillGroup: String) => {
    var business_line = ""
    //如果包含多个技能组，就拆开来匹配
    if(skillGroup.contains(";")){
      val strings = skillGroup.split(";")
        //先用技能组匹配业务线名称
        .map(x => calledMap.getOrElse(x,"-"))
        //然后按照业务线优先级排序，取第一个
        .sortBy(line => calledSortMap.getOrElse(line,Int.MaxValue))
      //取优先级最高的
      business_line = strings.head
    }else{
      business_line = calledMap.getOrElse(skillGroup, "-")
    }
    business_line
  })

  /**
   * 获取热线呼出明细数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getCallOutDetail(spark: SparkSession, loginUser: String):DataFrame = {
    val dataMonth = YesterDay.substring(0, 6)

    //登陆用户不通，读取的表名不同
    val tableName = loginUser match {
      case "baidu" =>
        //先判断表是否存在
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"baidu_call_out_result_data_${dataMonth}")) {
          s"baidu_call_out_result_data_${dataMonth}"
        } else {
          "baidu_call_out_result_data"
        }*/
        "baidu_call_out_result_data"
      case "bdidg" =>
       /* if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"bdidg_call_out_result_data_${dataMonth}")) {
          s"bdidg_call_out_result_data_${dataMonth}"
        } else {
          "bdidg_call_out_result_data"
        }*/
        "bdidg_call_out_result_data"
    }

    //读取新版历史呼入明细页面字段数据
    val callDeail =
      s"""SELECT
         |	s.*,
         |	p.process_instance_id as rx_process_instance_id
         |FROM
         |	   (SELECT
         |	    	session_id,-- 会话标识
         |	    	CONCAT(HEX( session_id ),':','${loginUser.toUpperCase()}') as session_id_hex,-- 16进制会话标识编号
         |        resource_id, -- 坐席工号
         |		    resource_name, -- 坐席姓名
         |		    start_time, -- 开始时间
         |		    end_time, -- 结束时间
         |		    local_url as rx_phone_calling, -- 主叫号码
         |		    remote_url as rx_phone_called, -- 被叫号码
         |	    	skill_desc as skill_group_name,-- 技能组
         |	    	alert_duration as rx_ring_duration,-- 振铃时长
         |	    	talk_duration,-- 人工通话时长
         |      	acw_duration as rx_acw_duration,-- 事后整理时长
         |	    	end_type as rx_end_type_name,-- 结束类型
         |	    	agent_talk_duration as keypress_name-- 满意度评价
         |	    FROM hycca.${tableName}
         |	    WHERE DATE_FORMAT( start_time, '%Y%m%d' ) = '${YesterDay}') s
         |	LEFT JOIN
         |    (SELECT
         |       session_id,
         |       process_instance_id -- 工单编号
         |     FROM workflow.${loginUser}_workflow_process_instance
         |     WHERE DATE_FORMAT( create_time, '%Y%m%d' ) = '${YesterDay}') p
         | ON s.session_id_hex = p.session_id
         |""".stripMargin

    var processDf: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaProperties,callDeail)
    processDf = processDf.select(
        when(col("session_id").isNull,"").otherwise(col("session_id")) as "session_id",
        when(col("resource_id").isNull,"").otherwise(col("resource_id")) as "resource_id",
        when(col("resource_name").isNull,"").otherwise(col("resource_name")) as "resource_name",
        when(col("start_time").isNull,"").otherwise(col("start_time")) as "start_time",
        when(col("end_time").isNull,"").otherwise(col("end_time")) as "end_time",
        when(col("rx_phone_calling").isNull,"").otherwise(col("rx_phone_calling")) as "rx_phone_calling",
        dealSpecial(when(col("rx_phone_called").isNull,"").otherwise(col("rx_phone_called"))) as "rx_phone_called",
        when(col("skill_group_name").isNull,"").otherwise(col("skill_group_name")) as "skill_group_name",
        when(col("rx_ring_duration").isNull,"").otherwise(col("rx_ring_duration")) as "rx_ring_duration",
        when(col("talk_duration").isNull,"").otherwise(col("talk_duration")) as "talk_duration",
        when(col("rx_acw_duration").isNull,"").otherwise(col("rx_acw_duration")) as "rx_acw_duration",
        when(col("rx_end_type_name").isNull,"").otherwise(col("rx_end_type_name")) as "rx_end_type_name",
        when(col("keypress_name").isNull,"").otherwise(col("keypress_name")) as "keypress_name",
        when(col("rx_process_instance_id").isNull,"").otherwise(col("rx_process_instance_id")) as "rx_process_instance_id"
      )
      .withColumn("form_name",lit("呼出明细历史表"))
      .withColumn("business_line",getCalledLine(col("skill_group_name")))

    processDf
  }

  /**
   * 获取热线呼入明细数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getCallInDetail(spark: SparkSession, loginUser: String):DataFrame = {
    val dataMonth = YesterDay.substring(0, 6)

    //登陆用户不通，读取的表名不同
    val tableName = loginUser match {
      case "baidu" =>
        //先判断表是否存在
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"baidu_call_in_result_data_${dataMonth}")) {
          s"baidu_call_in_result_data_${dataMonth}"
        } else {
          "baidu_call_in_result_data"
        }*/
        "baidu_call_in_result_data"
      case "bdidg" =>
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"bdidg_call_in_result_data_${dataMonth}")) {
          s"bdidg_call_in_result_data_${dataMonth}"
        } else {
          "bdidg_call_in_result_data"
        }*/
        "bdidg_call_in_result_data"
    }

    //读取新版历史呼入明细页面字段数据
    val callDeail =
      s"""SELECT
         |	s.*,
         |	p.process_instance_id as rx_process_instance_id
         |FROM
         |	   (SELECT
         |	    	session_id,-- 会话标识
         |	    	CONCAT(HEX( session_id ),':','${loginUser.toUpperCase()}') as session_id_hex,-- 16进制会话标识编号
         |        resource_id, -- 坐席工号
         |		    resource_name, -- 坐席姓名
         |		    start_time, -- 开始时间
         |		    end_time, -- 结束时间
         |		    remote_url as rx_phone_calling, -- 主叫号码
         |		    local_url as rx_phone_called, -- 被叫号码
         |	    	accept_skill_name as skill_group_name,-- 技能组
         |	    	queue_duration as rx_queue_duration,-- 排队时长
         |	    	alert_duration as rx_ring_duration,-- 振铃时长
         |	    	handle_duration as talk_duration,-- 人工通话时长
         |	    	end_type as rx_end_type_name,-- 结束类型
         |	    	keypress_name -- 满意度评价
         |	    FROM hycca.${tableName}
         |	    WHERE DATE_FORMAT( start_time, '%Y%m%d' ) = '${YesterDay}') s
         |	LEFT JOIN
         |    (SELECT
         |       session_id,
         |       process_instance_id -- 工单编号
         |     FROM workflow.${loginUser}_workflow_process_instance
         |     WHERE DATE_FORMAT( create_time, '%Y%m%d' ) = '${YesterDay}') p
         | ON s.session_id_hex = p.session_id
         |""".stripMargin

    var processDf: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaProperties,callDeail)
    processDf = processDf.select(
      when(col("session_id").isNull,"").otherwise(col("session_id")) as "session_id",
      when(col("resource_id").isNull,"").otherwise(col("resource_id")) as "resource_id",
      when(col("resource_name").isNull,"").otherwise(col("resource_name")) as "resource_name",
      when(col("start_time").isNull,"").otherwise(col("start_time")) as "start_time",
      when(col("end_time").isNull,"").otherwise(col("end_time")) as "end_time",
      when(col("rx_phone_calling").isNull,"").otherwise(col("rx_phone_calling")) as "rx_phone_calling",
      when(col("rx_phone_called").isNull,"").otherwise(col("rx_phone_called")) as "rx_phone_called",
      when(col("skill_group_name").isNull,"").otherwise(col("skill_group_name")) as "skill_group_name",
      when(col("rx_queue_duration").isNull,"").otherwise(col("rx_queue_duration")) as "rx_queue_duration",
      when(col("rx_ring_duration").isNull,"").otherwise(col("rx_ring_duration")) as "rx_ring_duration",
      when(col("talk_duration").isNull,"").otherwise(col("talk_duration")) as "talk_duration",
      when(col("rx_end_type_name").isNull,"").otherwise(col("rx_end_type_name")) as "rx_end_type_name",
      when(col("keypress_name").isNull,"").otherwise(col("keypress_name")) as "keypress_name",
      when(col("rx_process_instance_id").isNull,"").otherwise(col("rx_process_instance_id")) as "rx_process_instance_id"
    )
      .withColumn("form_name",lit("呼入明细历史表"))
      .withColumn("business_line",getCallingLine(col("skill_group_name")))

    processDf
  }

  /**
   * 获取按坐席统计话务（半小时）数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getAgentStatistics(spark: SparkSession, loginUser: String):DataFrame = {
    //登陆用户不通，读取的表名不同
    val tableName = loginUser match {
      case "baidu" => "baidu_t_ag_work_3"
      case "bdidg" => "bdidg_t_ag_work_3"
    }

    //读取新版历史呼入明细页面字段数据
    val callDeail =
      s"""
         |	SELECT
         |		obj_id, -- 工单编号
         |		u.username, -- 工单姓名
         |		start_time, -- 开始时间
         |    at_acw, -- 整理总时长(秒),事后处理时长
         |		an_tl_ib_an, -- 呼入接听总量(次)
         |		at_tl_ib_an, -- 呼入通话时长
         |		an_tl_ob, -- 外呼量（次）
         |		an_tl_ob_an,-- 外呼接听量（次）
         |		at_tl_ob_an,-- 外呼通话时长
         |		at_rdy,-- 置闲总时长,就绪总时长
         |		at_tk,-- 工作总时长
         |		at_owk,-- 置忙总时长
         |		(at_rdy+at_tk+at_owk+at_acw+at_lck+at_rn) as at_tol -- 登陆时长
         |	FROM hycca.${tableName} t
         |	LEFT JOIN hycca.${loginUser}_sys_user u
         |	ON t.obj_id = u.account_id
         |	WHERE DATE_FORMAT(start_time,'%Y%m%d') = '${YesterDay}'
         |""".stripMargin

    var processDf: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaProperties,callDeail)
    processDf = processDf.select(
        when(col("obj_id").isNull,"").otherwise(col("obj_id")) as "resource_id",
        when(col("username").isNull,"").otherwise(col("username")) as "resource_name",
        when(col("start_time").isNull,"").otherwise(col("start_time")) as "start_time",
        when(col("at_acw").isNull,"").otherwise(col("at_acw")) as "rx_acw_duration",
        when(col("an_tl_ib_an").isNull,"").otherwise(col("an_tl_ib_an")) as "rx_answer_calling_num",
        when(col("at_tl_ib_an").isNull,"").otherwise(col("at_tl_ib_an")) as "rx_calling_duratoin",
        when(col("an_tl_ob").isNull,"").otherwise(col("an_tl_ob")) as "rx_called_num",
        when(col("an_tl_ob_an").isNull,"").otherwise(col("an_tl_ob_an")) as "rx_answer_called_num",
        when(col("at_tl_ob_an").isNull,"").otherwise(col("at_tl_ob_an")) as "rx_called_duration",
        when(col("at_rdy").isNull,"").otherwise(col("at_rdy")) as "rx_leave_all_duration",
        when(col("at_tk").isNull,"").otherwise(col("at_tk")) as "rx_work_all_duration",
        when(col("at_owk").isNull,"").otherwise(col("at_owk")) as "rx_busy_all_duration",
        when(col("at_tol").isNull,"").otherwise(col("at_tol")) as "login_all_duration")
      .withColumn("form_name",lit("按坐席统计话务"))
      .withColumn("business_line",lit(loginUser.toUpperCase()))
      .dropDuplicates()
      .repartition(10)

    processDf
  }


  /**
   * 获取录音收听下载历史数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getRecordDownload(spark: SparkSession, loginUser: String):DataFrame = {
    val dataMonth = YesterDay.substring(0, 6)

    //登陆用户不通，读取的表名不同
    val tableName = loginUser match {
      case "baidu" =>
        //先判断表是否存在
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"baidu_r_record_${dataMonth}")) {
          s"baidu_r_record_${dataMonth}"
        } else {
          "baidu_r_record"
        }*/
        "baidu_r_record"
      case "bdidg" =>
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"bdidg_r_record_${dataMonth}")) {
          s"bdidg_r_record_${dataMonth}"
        } else {
          "bdidg_r_record"
        }*/
        "bdidg_r_record"
    }

    //读取录音收听下载历史数据
    val callDeail =
      s"""  SELECT
         |	    	session_id,-- 会话标识
         |        agent_id as resource_id, -- 坐席工号
         |		    start_time, -- 开始时间
         |		    end_time, -- 结束时间
         |		    remote_uri as rx_phone_calling, -- 主叫号码
         |		    local_uri as rx_phone_called, -- 被叫号码
         |	    	duration as talk_duration,-- 录音时长
         |        call_type as rx_call_type -- 呼叫方向
         |	 FROM hycca.${tableName}
         |	 WHERE DATE_FORMAT( start_time, '%Y%m%d' ) = '${YesterDay}'
         |""".stripMargin

    var processDf: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaProperties,callDeail)
    processDf = processDf.select(
        when(col("session_id").isNull,"").otherwise(col("session_id")) as "session_id",
        when(col("resource_id").isNull,"").otherwise(col("resource_id")) as "resource_id",
        when(col("start_time").isNull,"").otherwise(col("start_time")) as "start_time",
        when(col("end_time").isNull,"").otherwise(col("end_time")) as "end_time",
        when(col("rx_phone_calling").isNull,"").otherwise(col("rx_phone_calling")) as "rx_phone_calling",
        when(col("rx_phone_called").isNull,"").otherwise(col("rx_phone_called")) as "rx_phone_called",
        when(col("talk_duration").isNull,"").otherwise(col("talk_duration")) as "talk_duration",
        when(col("rx_call_type").isNull,"")
          .when(col("rx_call_type") === "0","呼入")
          .when(col("rx_call_type") === "1","呼出")
          .otherwise(col("rx_call_type")) as "rx_call_type")
      .withColumn("form_name",lit("录音收听下载历史"))
      .withColumn("business_line",lit(""))
      .dropDuplicates()
      .repartition(10)

    processDf
  }

  /**
   * 查询ES数据库字段
   * @param spark
   * @param startTimeStamp 开始时间
   * @param endTimeStamp 结束时间
   * @param templateId
   * @param templateGroupId 产品线id
   * @return
   */
  private def getEsData(spark: SparkSession,
                        loginUser: String,
                        startTimeStamp: Long,
                        endTimeStamp: Long): DataFrame = {

    val query =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "filter": [
         |        {
         |          "bool": {
         |            "should": [
         |              {
         |                "range": {
         |                  "createTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              },
         |              {
         |                "range": {
         |                  "modifiedTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        },
         |        {
         |          "prefix": {
         |            "entId": "${loginUser.toUpperCase()}"
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    val rdd = CommonUtils.ElasticSearchOperate(spark, indexName, PropertiesUtils.ESDukeProperties, query)
    // 定义模式（即Schema），这里只有一个名为"data"的字符串字段
    val schema: StructType = new StructType().add("data", DataTypes.StringType)
    // 使用SparkSession创建DataFrame
    val esDf: DataFrame = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)
    //获取ES中的字段数据
    val esFieldsDF = esDf
      // 热线编号
      .withColumn("rx_process_instance_id", getNullVal(get_json_object(col("data"), "$.processInstanceId").cast("String")))
      // 工单标题
      .withColumn("rx_temp_title", getNullVal(get_json_object(col("data"), "$.subject").cast("String")))
      // 工单内容
      .withColumn("rx_temp_desc", getNullVal(get_json_object(col("data"), "$.desc").cast("String")))
      .drop("data")

    esFieldsDF
  }
}
