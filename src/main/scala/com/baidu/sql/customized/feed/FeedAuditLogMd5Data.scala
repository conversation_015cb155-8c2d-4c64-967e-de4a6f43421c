package com.baidu.sql.customized.feed

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import com.baidu.sql.utils.SparkUtils._
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.commons.codec.digest.DigestUtils
import org.apache.http.HttpHost
import org.apache.http.client.config.RequestConfig

import java.util.concurrent.{Semaphore, TimeUnit}
import org.apache.http.client.methods.HttpHead
import org.apache.http.conn.ssl.TrustAllStrategy
import org.apache.http.impl.client.{HttpClientBuilder, HttpClients}
import org.apache.http.ssl.SSLContexts
import org.apache.http.util.EntityUtils

/**
 * @author: zhangrunjie
 * @date: 2025/7/1
 * @description: 数据获取人审表数据安全审核环节的数据，回写到MySQL和数仓表的同步逻辑
 */
object FeedAuditLogMd5Data {
  //运行日期
  var YesterDay: String = ""
  var day30: String = ""

  // 限流器（QPS不超过50）
  private val rateLimiter = new Semaphore(50)

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)
    day30 = calcnDate(YesterDay, -30)
    val YesterDayFormat = calcnDateFormat(YesterDay)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleConstrings", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    val logDf = spark.sql(
      s"""
        |with tmp_data as (
        |select
        |   article_id as nid,
        |   product,
        |   title,
        |   reject_reason,
        |   status,
        |   username,
        |   check_type,
        |   insert_time,
        |   receive_time,
        |   workflow_type,
        |   type,
        |   regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        | from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        | where
        | -- event_day between '${day30}' and '${YesterDay}'
        | event_day = '${YesterDay}'
        | and check_type in ('sf','sr','si')
        | and product in ('shortvideo', 'svideo', 'tinyvideo', 'microvideo')
        | and username not rlike '模型|智能体|m_system|AI'
        |),
        | bjh_base_data as (
        | select
        |   nid,
        |   url,
        |   rmb_self_build_url_http,
        |   rmb_video_source_url,
        |   status,
        |   secure_not_pass_reason,
        |   abstract,
        |   click_publish_time
        | from
        | bjh_data.bjh_feed_resource_rf
        | where event_day = '${YesterDay}'
        | and click_publish_time between '${YesterDayFormat} 00:00:00' and '${YesterDayFormat} 23:59:59'
        | and nid in (select distinct nid from tmp_data)
        | )
        | select
        |   t.nid,
        |   t.product,
        |   t.title,
        |   t.reject_reason,
        |   t.status,
        |   t.username,
        |   t.check_type,
        |   t.insert_time,
        |   t.receive_time,
        |   t.workflow_type,
        |   t.type,
        |   b.rmb_self_build_url_http,
        |   b.url,
        |   b.rmb_video_source_url,
        |   b.status as bjh_status,
        |   b.secure_not_pass_reason,
        |   b.abstract,
        |   b.click_publish_time
        | from tmp_data t left join bjh_base_data b on t.nid = b.nid
        |""".stripMargin)
      .withColumn("etag", getEtagUdf(col("rmb_self_build_url_http")))
      .repartition(10)
      .cache()
    println(s"bjh_ods_audit_auditlog_esdump_di获取${day30} 到 ${YesterDay}数据为：" + logDf.count())
    logDf.createOrReplaceTempView("log_tmp_data")
    logDf.show(5,false)

    //println("resDf:" + logDf.count())
    //resDf.show(10,false)
    logDf.repartition(1).write.mode("overwrite").parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_log.parquet")
    println("write parquet success!")
    //writeExcel(logDf,s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_log.xlsx")

    spark.close()
  }

  // 测试网络连通性的方法
  private def testNetworkConnectivity(host: String): Boolean = {
    import sys.process._
    try {
      // 执行 ping 命令，发送 4 个包，超时 2 秒
      val result = s"ping -c 4 -W 2 $host".!
      result == 0
    } catch {
      case e: Exception =>
        println(s"网络连通性测试失败: ${e.getMessage}")
        false
    }
  }

  // 获取ETag的自定义函数
  private def getEtag(url: String): String = {
    if (url == null || !url.endsWith(".mp4")) return null

    try {
      // 获取许可（控制QPS）
      if (!rateLimiter.tryAcquire(100, TimeUnit.MILLISECONDS)) return null

      val client = HttpClientBuilder.create().build()
      val request = new HttpHead(url.replace("vd3.bdstatic.com","bj.bcebos.com/bktvhjim1yg918f7y5ij"))

      // 设置请求超时
      val response = client.execute(request)
      try {
        val etagHeader = response.getFirstHeader("ETag")
        if (etagHeader != null) etagHeader.getValue.replace("\"", "") else null
      } finally {
        EntityUtils.consumeQuietly(response.getEntity)
        client.close()
      }
    } catch {
      case e: Exception =>
        println(s"Error fetching ETag for URL: $url. Error: ${e.getMessage}")
        null
    } finally {
      rateLimiter.release()
    }
  }

  // UDF注册获取Etag字段
  val getEtagUdf = udf((url: String) => getEtag(url))

  val getMd5Udf = udf((eTag: String,abStract:String,title:String) => {
    if (eTag != "" && abStract != "" && title != ""){

    }else{

    }
    val md5 = DigestUtils.md5Hex(url)
    md5
  })
}
