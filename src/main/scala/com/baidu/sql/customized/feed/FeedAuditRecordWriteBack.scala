package com.baidu.sql.customized.feed

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.SparkUtils.writeExcel
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.sql.{SaveMode, SparkSession}
import org.apache.spark.sql.functions.{col, encode, lit}

/**
 * @author: z<PERSON><PERSON><PERSON>e
 * @date: 2025/7/1
 * @description: feed模型（色情）数据关联flow审核表数据后，回写到MySQL的同步逻辑
 */
object FeedAuditRecordWriteBack {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleConstrings", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    //获取feed的T-1所有数据，nid为查询flow审核表的字段
    val feedSql =
      s"""
        |select
        |   *
        |from feed_audit_record
        |where DATE_FORMAT(call_back_time, '%Y%m%d' ) = '${YesterDay}'
        |-- and audit_type = '0'
        |""".stripMargin

    // 执行MySQL SQL查询
    val feedDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFeedAuditProperties,feedSql)
    feedDf.cache()
    println(s"feed_audit_record获取${YesterDay}数据为：" + feedDf.count())
    feedDf.createOrReplaceTempView("feed_audit_record")

    // 读取Hive表
    val hiveDf = spark.sql(
      s"""
         |select distinct
         |      nid,
         |      product,
         |      get_json_object (
         |        get_json_object (
         |          get_json_object (
         |            get_json_object (
         |              get_json_object (ext_info, '$$.sf_audit_info'),
         |              '$$.ext'
         |            ),
         |            '$$.service_mvideo_hit_llm'
         |          ),
         |          '$$.data'
         |        ),
         |        '$$.result'
         |      ) AS sf_mvideo_hit_llm_result,
         |      get_json_object (
         |        get_json_object (
         |          get_json_object (
         |            get_json_object (
         |              get_json_object (ext_info, '$$.sr_audit_info'),
         |              '$$.ext'
         |            ),
         |            '$$.service_mvideo_hit_llm'
         |          ),
         |          '$$.data'
         |        ),
         |        '$$.result'
         |      ) AS sr_mvideo_hit_llm_result,
         |      get_json_object (
         |        get_json_object (
         |          get_json_object (
         |            get_json_object (
         |              get_json_object (ext_info, '$$.sf_audit_info'),
         |              '$$.ext'
         |            ),
         |            '$$.service_svideo_hit_llm'
         |          ),
         |          '$$.data'
         |        ),
         |        '$$.result'
         |      ) AS sf_svideo_hit_llm_result,
         |      get_json_object (
         |        get_json_object (
         |          get_json_object (
         |            get_json_object (
         |              get_json_object (ext_info, '$$.sr_audit_info'),
         |              '$$.ext'
         |            ),
         |            '$$.service_svideo_hit_llm'
         |          ),
         |          '$$.data'
         |        ),
         |        '$$.result'
         |      ) AS sr_svideo_hit_llm_result,
         |      regexp_replace (ext_info, '\\\\s+', '') as ext_info
         |    from
         |      udw_ns.default.bjh_ods_auditflow_esdump_di
         |    where
         |      event_day = '${YesterDay}'
         |      and nid in (select distinct nid from feed_audit_record)
         |      and (ext_info like '%sf_audit_info%' or ext_info like '%sr_audit_info%')
         |""".stripMargin)
    println(s"bjh_ods_auditflow_esdump_di获取${YesterDay}数据为：" + hiveDf.count())
    hiveDf.createOrReplaceTempView("flow_tmp_data")

    val logDf = spark.sql(
      s"""
        |with tmp_data as (
        |select distinct
        |      article_id as nid,
        |      insert_time,
        |      row_number() OVER (
        |            PARTITION BY
        |              article_id,check_type
        |            ORDER BY
        |              insert_time DESC
        |          ) AS rn,
        |      status,
        |      reject_reason,
        |      check_type,
        |      regexp_replace (ext_json_info, '\\\\s+', '') as ext_json_info
        |    from
        |      udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        |    where
        |      event_day = '${YesterDay}'
        |      and article_id in (select distinct nid from feed_audit_record)
        |      and check_type not in ('sm','qm','rm')
        |)
        |  select
        |     nid,
        |     concat(
        |      '{',
        |      concat_ws(
        |        ',',
        |        collect_list(
        |          concat(
        |            '"', check_type, '":',
        |            '{',
        |              '"insert_time":', '"', cast(insert_time as string), '"', ',',
        |              '"status":', '"', status, '"', ',',
        |              '"reject_reason":', '"', reject_reason, '"',
        |            '}'
        |          )
        |        )
        |      ),
        |      '}'
        |    ) as ext_json_info
        |  from tmp_data
        |  where rn = 1
        |  group by nid
        |""".stripMargin)
    println(s"bjh_ods_audit_auditlog_esdump_di获取${YesterDay}数据为：" + logDf.count())
    logDf.createOrReplaceTempView("log_tmp_data")

    logDf.show(5,false)

    var resDf = spark.sql(
      """
        | select
        |     f.id as id,
        |     f.creator as creator,
        |     f.updator as updator,
        |     f.create_time as create_time,
        |     f.update_time as update_time,
        |     f.version as version,
        |     f.is_delete as is_delete,
        |     f.del_unique_key as del_unique_key,
        |     f.nid as nid,
        |     f.input_data as input_data,
        |     f.output_data as output_data,
        |     f.audit_tag as audit_tag,
        |     f.audit_task_id as audit_task_id,
        |     f.audit_type as audit_type,
        |     f.category_v4 as category_v4,
        |     f.sub_category_v4 as sub_category_v4,
        |     f.online_audit_status as online_audit_status,
        |     f.crcc_audit_status_info as crcc_audit_status_info,
        |     f.call_back_time as call_back_time,
        |     t.product as product,
        |     case when t.sf_mvideo_hit_llm_result != '' and t.sf_mvideo_hit_llm_result != 'null' then t.sf_mvideo_hit_llm_result
        |      when t.sr_mvideo_hit_llm_result != '' and t.sr_mvideo_hit_llm_result != 'null' then t.sr_mvideo_hit_llm_result
        |      when t.sf_svideo_hit_llm_result != '' and t.sf_svideo_hit_llm_result != 'null' then t.sf_svideo_hit_llm_result
        |      when t.sr_svideo_hit_llm_result != '' and t.sr_svideo_hit_llm_result != 'null' then t.sr_svideo_hit_llm_result
        |      else 'null' end as flow_hit_llm_result,
        |      t.ext_info as flow_ext_info,
        |      l.ext_json_info as log_ext_info
        |  from feed_audit_record f
        |  left join flow_tmp_data t on f.nid = t.nid
        |  left join log_tmp_data l on f.nid = l.nid
        |""".stripMargin)
      .cache()

    // 执行Hive SQL查询，类型转换
    resDf = resDf
      .select(
        $"id".cast("long") as "audit_id",
        $"creator".cast("long"),
        $"updator".cast("long"),
        $"create_time".cast("timestamp"),
        $"update_time".cast("timestamp"),
        $"version".cast("long"),
        $"is_delete".cast("int"),
        $"del_unique_key".cast("long"),
        $"nid".cast("string"),
        $"input_data".cast("string"),
        $"output_data".cast("string"),
        $"audit_tag".cast("string"),
        $"audit_task_id".cast("long"),
        $"audit_type".cast("int"),
        $"category_v4".cast("string"),
        $"sub_category_v4".cast("string"),
        $"online_audit_status".cast("int"),
        $"crcc_audit_status_info".cast("string"),
        $"call_back_time".cast("timestamp"),
        $"product".cast("string"),
        $"flow_hit_llm_result".cast("string"),
        $"flow_ext_info".cast("string"),
        $"log_ext_info".cast("string"),
      )
      .na.fill("null",Seq("flow_hit_llm_result","flow_ext_info","log_ext_info"))
      .na.fill("",Seq("nid","input_data","output_data","audit_tag","category_v4","sub_category_v4","crcc_audit_status_info","product"))
      .na.fill(0,Seq("audit_id","creator","updator","version","is_delete","del_unique_key","audit_task_id"))
      .withColumn("input_data",lit(""))
      .withColumn("output_data",lit(""))
      //.withColumn("input_data",encode($"input_data","utf-8"))
      .withColumn("audit_tag",encode($"audit_tag","utf-8"))
      .withColumn("flow_ext_info",encode($"flow_ext_info","utf-8"))
      .withColumn("log_ext_info",encode($"log_ext_info","utf-8"))
      .dropDuplicates()
      .repartition(50)
    println(s"获取feed审核数据${YesterDay}数据共：${resDf.count()}条数据")

    val properties = PropertiesUtils.MysqlFeedAuditProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入数据到MySQL数据库,覆盖写
    val deleteSql = s"delete from feed_audit_record_write_back where DATE_FORMAT(call_back_time, '%Y%m%d' ) = '${YesterDay}' "
    //将当天的数据先删除再写入
    CommonUtils.deleteMysql(properties,deleteSql)
    println(s"删除历史${YesterDay}数据成功")

    mysqlWriteOperate(spark,properties,resDf,"feed_audit_record_write_back",SaveMode.Append)

    spark.close()
  }
}
