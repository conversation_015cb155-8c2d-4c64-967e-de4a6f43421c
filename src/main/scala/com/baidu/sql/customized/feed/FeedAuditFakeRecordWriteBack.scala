package com.baidu.sql.customized.feed

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.sql.functions.{encode, lit}
import org.apache.spark.sql.{SaveMode, SparkSession}

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/9
 * @description: feed模型数据关联flow审核表数据后，回写到MySQL的同步逻辑
 */
object FeedAuditFakeRecordWriteBack {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleConstrings", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    //获取feed的T-1所有数据，nid为查询flow审核表的字段
    val feedSql =
      s"""
        |select
        |   *
        |from audit_face_fake_record
        |where DATE_FORMAT(create_time, '%Y%m%d' ) = '${YesterDay}'
        |""".stripMargin

    // 执行MySQL SQL查询
    val feedDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFeedAuditProperties,feedSql)
    feedDf.cache()
    println(s"audit_face_fake_record获取${YesterDay}数据为：" + feedDf.count())
    feedDf.createOrReplaceTempView("audit_face_fake_record")

    // 读取Hive表
    val hiveDf = spark.sql(
      s"""
         |select distinct
         |      nid,
         |      product,
         |      get_json_object (ext_info, '$$.sm_reason') as sm_reason,
         |      get_json_object (ext_info, '$$.sm_subreason') as sm_subreason,
         |      get_json_object (ext_info, '$$.sm_status') as sm_status,
         |      regexp_replace (ext_info, '\\\\s+', '') as ext_info
         |    from
         |      udw_ns.default.bjh_ods_auditflow_esdump_di
         |    where
         |      event_day = '${YesterDay}'
         |      and nid in (select distinct nid from audit_face_fake_record)
         |      and get_json_object (ext_info, '$$.sm_reason') like '%service_deep_fake%'
         |      and product = 'pgcnews'
         |""".stripMargin)
    println(s"bjh_ods_auditflow_esdump_di获取${YesterDay}数据为：" + hiveDf.count())
    //hiveDf.show(10,false)
    hiveDf.createOrReplaceTempView("flow_tmp_data")

    var resDf = spark.sql(
      """
        | select
        |     f.*,
        |     t.sm_reason,
        |     t.sm_subreason,
        |     t.sm_status,
        |     t.ext_info as flow_ext_info
        |  from audit_face_fake_record f
        |  left join flow_tmp_data t on f.nid = t.nid
        |""".stripMargin)
      .cache()

    // 执行Hive SQL查询，类型转换
    resDf = resDf
      .select(
        $"id".cast("long") as "audit_id",
        $"creator".cast("long"),
        $"updator".cast("long"),
        $"create_time".cast("timestamp"),
        $"update_time".cast("timestamp"),
        $"version".cast("long"),
        $"is_delete".cast("int"),
        $"del_unique_key".cast("long"),
        $"nid".cast("string"),
        $"input_data".cast("string"),
        $"output_data".cast("string"),
        $"image_url".cast("string"),
        $"model_input".cast("string"),
        $"model_output".cast("string"),
        $"is_face_fake".cast("int"),
        $"sm_reason".cast("string"),
        $"sm_subreason".cast("string"),
        $"sm_status".cast("string"),
        $"flow_ext_info".cast("string")
      )
      .na.fill("null",Seq("sm_reason","sm_subreason","sm_status","flow_ext_info"))
      .na.fill("",Seq("nid","input_data","output_data","image_url","model_input","model_output"))
      .na.fill(0,Seq("audit_id","creator","updator","version","is_delete","del_unique_key","is_face_fake"))
      .withColumn("input_data",encode($"input_data","utf-8"))
      .withColumn("output_data",encode($"output_data","utf-8"))
      .withColumn("flow_ext_info",encode($"flow_ext_info","utf-8"))
      .withColumn("model_input",encode($"model_input","utf-8"))
      .withColumn("model_output",encode($"model_output","utf-8"))
      .dropDuplicates()
      .repartition(20)
    println(s"获取feed数据${YesterDay}数据共：${resDf.count()}条数据")

    val properties = PropertiesUtils.MysqlFeedAuditProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入数据到MySQL数据库,覆盖写
    val deleteSql = s"delete from audit_face_fake_record_write_back where DATE_FORMAT(create_time, '%Y%m%d' ) = '${YesterDay}' "
    //将当天的数据先删除再写入
    CommonUtils.deleteMysql(properties,deleteSql)
    println(s"删除历史${YesterDay}数据成功")
    //追加写入
    mysqlWriteOperate(spark,properties,resDf,"audit_face_fake_record_write_back",SaveMode.Append)

    spark.close()
  }
}
