package com.baidu.sql.customized.feed

import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.sql.SparkSession
import org.apache.spark.storage.StorageLevel

/**
 * @author: z<PERSON><PERSON><PERSON>e
 * @date: 2025/7/10
 * @description: audit_record_log，audit_task，audit_sub_task，ad_audit_record，feed_audit_record 入审日志表数据，回写到数仓表的中
 */
object FeedAuditRecordLogToHive {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)
    //获取表名
    val tableName = args(1)
    //获取分区数
    val partitionNum = args(2).toInt

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleConstrings", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    //获取audit_record_log的T-1所有数据
    val feedSql =
      s"""
        |select
        |   *
        |from ${tableName}
        |where DATE_FORMAT(create_time, '%Y%m%d' ) = '${YesterDay}'
        |""".stripMargin

    //设置超时时间
    val properties = PropertiesUtils.MysqlFeedAuditReadProperties

    if (tableName.equals("audit_task")){
      // 执行MySQL SQL查询
      val feedDf = CommonUtils.mysqlOperatePartition(spark,properties,feedSql,tableName,partitionNum)
      val convertedFeedDf = feedDf.select(
          feedDf.columns.map { colName =>
            feedDf.col(colName).cast("string").as(colName)
          }: _*
        )
        .persist(StorageLevel.MEMORY_AND_DISK)
      //println("实际分区数:" + convertedFeedDf.rdd.partitions.size) // 实际分区数
      //println("各分区数据量:" + convertedFeedDf.rdd.glom().map(_.length).collect().mkString(",")) // 各分区数据量
      println(s"${tableName}获取${YesterDay}数据为：" + convertedFeedDf.count())
      convertedFeedDf.createOrReplaceTempView(tableName)

      spark.sql(
        s"""
           | insert overwrite table udw_ns.default.help_ods_${tableName} partition (event_day = $YesterDay)
           | select
           |   *,
           |   '' as buz_uniq_id
           | from ${tableName}
           |""".stripMargin)
    } else{
      // 执行MySQL SQL查询
      val feedDf = CommonUtils.mysqlOperatePartition(spark,properties,feedSql,tableName,partitionNum)
      val convertedFeedDf = feedDf.select(
          feedDf.columns.map { colName =>
            feedDf.col(colName).cast("string").as(colName)
          }: _*
        )
        .cache()
      //println("实际分区数:" + convertedFeedDf.rdd.partitions.size) // 实际分区数
      //println("各分区数据量:" + convertedFeedDf.rdd.glom().map(_.length).collect().mkString(",")) // 各分区数据量
      println(s"${tableName}获取${YesterDay}数据为：" + convertedFeedDf.count())
      convertedFeedDf.createOrReplaceTempView(tableName)

      spark.sql(
        s"""
           | insert overwrite table udw_ns.default.help_ods_${tableName} partition (event_day = $YesterDay)
           | select
           |   *
           | from ${tableName}
           |""".stripMargin)
    }
    //写入hive表数据
    println(s"写入${tableName}数据成功")

    spark.close()
  }
}
