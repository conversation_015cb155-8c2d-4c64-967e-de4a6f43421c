package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.UserRelation801.{getSqlData, ipList}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils.{cleanChars, isPrivateIPUDF}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.storage.StorageLevel


object UserRelation801Other {
  //`uid` STRING COMMENT '用户UID' SAMPLE '423435454',
  //`type` STRING COMMENT '关联类型' SAMPLE 'IP、CUID',
  //`value` STRING COMMENT '关联值' SAMPLE '*******',
  //`source` STRING COMMENT '数据来源表' SAMPLE 'bjh_feed_resource_rf',
  //`properties` STRING COMMENT '城市/设备数据值' SAMPLE '{"device": "apple"}'

  // 昨日日期
  var YesterDay = ""
  // 昨日计算日期 yyyy-MM-dd
  var YesterClacDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 今日计算日期 yyyy-MM-dd
  var ToClacDay = ""
  //回环IP（127.0.0.1）、无意义IP（0.0.0.0、***************、空）
  val ipList = List("127.0.0.1","0.0.0.0","***************","")

  /**
   * 用户关联表
   * @param args
   */
  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 昨日计算日期 yyyy-MM-dd
    YesterClacDay = calcnDateFormat(YesterDay)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 今日计算日期 yyyy-MM-dd
    ToClacDay = calcnDateFormat(ToDay)
    //单独获取某个表的数据写入
    //val tableName = args(1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("userRelation801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    // 读取百家号发文数据,每天数据到位时间不确定
    val bjhSql =
      s"""
         |select
         |  author_user_id as uid,
         |  concat(qm_user_ip,'-',commit_ip,'-',commit_ip_dual) as ips,
         |  concat(cuid,'-',ducut_info) as cuids,
         |  countryname as country,
         |  province,
         |  city,
         |  os,
         |  click_publish_time,
         |  unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') as click_publish_timestamp
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp}
         |""".stripMargin

    println("开始读取bjh_feed_resource_rf表")
    val bjhDf = spark.sql(bjhSql)
      .filter(col("ips").isNotNull or col("cuids").isNotNull)
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("百家号数据共：" + bjhDf.count())

    //将数据按照IP和cuid进行拆分
    bjhDf.createOrReplaceTempView("bjh_view")
    //百家号IP数据
    val bjhIpDf = spark.sql(
      """
        |select
        |   uid,
        |   explode(split(ips,'-')) as value,
        |   country,
        |   province,
        |   city
        |from bjh_view
        |""".stripMargin)
      //过滤内网IP
      .withColumn("is_private", isPrivateIPUDF(col("value")))
      //过滤无效IP
      .filter(!col("value").isin(ipList: _*))
      .filter(col("is_private") === false)
      //当存在多个ip时，获取不为空的随机一条数据
      .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("country").asc_nulls_last,col("province").asc_nulls_last,col("city").asc_nulls_last)))
      .filter(col("rn") === 1)
      .withColumn("value", cleanChars(col("value")))
      .withColumn("type",lit("IP"))
      .withColumn("source",lit("bjh_feed_resource_rf"))
      .withColumn("properties", concat(lit("{'country':'"), col("country"), lit("','province':'"), col("province"), lit("','city':'"), col("city"), lit("'}")))
      .drop("rn","is_private","country","province","city")
      .dropDuplicates()
      .repartition(100)

    println("IP数据共：" + bjhIpDf.count())
    //bjhIpDf.show(5,false)

    //百家号CUID数据
    val bjhCuidDf = spark.sql(
      """
        |select
        |   uid,
        |   explode(split(cuids,'-')) as value,
        |   os
        |from bjh_view
        |""".stripMargin)
      .filter(col("value") =!= "")
      .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("os").asc_nulls_last)))
      .filter(col("rn") === 1)
      .withColumn("value", cleanChars(col("value")))
      .withColumn("type",lit("CUID"))
      .withColumn("source",lit("bjh_feed_resource_rf"))
      .withColumn("properties",concat(lit("{'os':'"),col("os"),lit("'}")))
      .drop("rn","is_private","os")
      .dropDuplicates()
      .repartition(100)
    println("Cuid数据共：" + bjhCuidDf.count())
    //bjhCuidDf.show(5,false)
    //bjhDf.unpersist()

    // 读取用户属性维数据,	分区大小68.023GB
    val feedDimSql =
      s"""
        |select
        |    uid,
        |    ip_str as ip,
        |    cuid,
        |    os_name as os,
        |    device,
        |    country,
        |    province,
        |    city
        |from
        |    ubs_feed.feed_dim_user_dau_shoubai_expand_di
        |where
        |  event_day = '${YesterDay}'
        |  and uid > 0
        |  and (
        |    ip_str not in ('127.0.0.1','0.0.0.0','***************','')
        |    or cuid != ''
        |  )
        |""".stripMargin

    println("开始读取feed_dim_user_dau_shoubai_expand_di表")
    //最终返回的数据集
    var feedDimResDf = spark.emptyDataFrame
    //读取hive数据
    var feedDwdDf = spark.emptyDataFrame
    try{
      feedDwdDf = spark.sql(feedDimSql)
        .dropDuplicates()
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //println("数据共：" + feedDwdDf.count())
      feedDwdDf.createOrReplaceTempView("view_data")
    } catch {
      case e: Exception => println("数据读取失败,原因:" + e.getMessage)
    }

    //IP数据
    try {
      val IpDf = spark.sql(
          """
            |select
            |   uid,
            |   ip as value,
            |   country,
            |   province,
            |   city
            |from view_data
            |""".stripMargin)
        //过滤无效IP
        .filter(!col("value").isin(ipList: _*))
        .withColumn("is_private", isPrivateIPUDF(col("value")))
        .filter(col("is_private") === false)
        .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("country").asc_nulls_last,col("province").asc_nulls_last,col("city").asc_nulls_last)))
        .filter(col("rn") === 1)
        .withColumn("value", cleanChars(col("value")))
        .withColumn("type", lit("IP"))
        .withColumn("properties", concat(lit("{'country':'"), col("country"), lit("','province':'"), col("province"), lit("','city':'"), col("city"), lit("'}")))
        .drop("country", "province", "city","is_private","rn")
        .dropDuplicates()
      //.repartition(400)

      val dfCount = IpDf.count()
      println("IP数据共：" + dfCount)
      if (dfCount > 0 && feedDimResDf.isEmpty){
        feedDimResDf = IpDf
      }
    }catch {
      case e: Exception => println("IP数据读取失败,原因:" + e.getMessage)
    }

    //CUID数据
    try {
      val CuidDf = spark.sql(
          """
            |select
            |   uid,
            |   cuid as value,
            |   os,
            |   device
            |from view_data
            |""".stripMargin)
        .filter(col("value") =!= "")
        .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("os").asc_nulls_last,col("device").asc_nulls_last)))
        .filter(col("rn") === 1)
        .withColumn("value", cleanChars(col("value")))
        .withColumn("type", lit("CUID"))
        .withColumn("properties", concat(lit("{'os':'"), col("os"), lit("','device':'"), col("device"), lit("'}")))
        .drop("os", "device","rn")
        .dropDuplicates()

      val dfCount = CuidDf.count()
      println("CUID数据共：" + dfCount)
      if (dfCount > 0 && feedDimResDf.isEmpty){
        feedDimResDf = CuidDf
      }else if (dfCount > 0  && !feedDimResDf.isEmpty){
        feedDimResDf = feedDimResDf.union(CuidDf)
      }
    }catch {
      case e: Exception => println("CUID数据读取失败,原因:" + e.getMessage)
    }

    //feedDwdDf.unpersist()
    feedDimResDf = feedDimResDf.withColumn("source",lit("feed_dim_user_dau_shoubai_expand_di"))

    // 读取用户属性维数据，分区大小2.809GB
    val tiebaDimSql =
      s"""
         |select
         |    uid,
         |    cuid,
         |    os,
         |    brand_type as brand,
         |    device
         |from
         |    ubs_tieba.tieba_dim_user_active_di
         |where
         |  event_day = '${YesterDay}'
         |  and uid > 0
         |  and cuid != ''
         |""".stripMargin

    println("开始读取tieba_dim_user_active_di表")
    val tiebaDimResDf = getSqlData(spark,tiebaDimSql,"CUID")
      .withColumn("source",lit("tieba_dim_user_active_di"))

    // 读取用户属性维数据，分区大小350GB左右
    val eduDwdSql =
      s"""
         |select
         |    uid,
         |    ip_str as ip,
         |    cuid,
         |    os_name as os,
         |    brand,
         |    device,
         |    country,
         |    province,
         |    city
         |from
         |    edu_data.edu_dwd_log_hi
         |where
         |  event_day = '${YesterDay}'
         |  and uid != ''
         |  and cast(uid as long) > 0
         |  and (
         |    ip_str not in ('127.0.0.1','0.0.0.0','***************','')
         |    or cuid != ''
         |  )
         |""".stripMargin

    println("开始读取edu_dwd_log_hi表")
    val eduResDf = getSqlData(spark,eduDwdSql,"IP,CUID")
      .withColumn("source",lit("edu_dwd_log_hi"))

    // 读取search_pc_resultpage_query_click数据,分区大约4.3T
    val searchPcSql =
      s"""
         |select
         |    event_userid as uid,
         |    event_ip as ip,
         |    event_country as country,
         |    event_province as province,
         |    event_city as city
         |from
         |    ubs_search.search_pc_resultpage_query_click
         |where
         |  event_day = '${YesterDay}'
         |  and log_source = 'se'
         |  and event_userid > 0
         |  and event_ip not in ('127.0.0.1','0.0.0.0','***************','')
         |""".stripMargin

    println("开始读取search_pc_resultpage_query_click表")
    val searchResDf = getSqlData(spark,searchPcSql,"IP")
      .withColumn("source",lit("search_pc_resultpage_query_click"))

    //写入数据
    var totalResDf = bjhIpDf
    totalResDf = unionIfNotEmpty(totalResDf, bjhCuidDf)
    totalResDf = unionIfNotEmpty(totalResDf, feedDimResDf)
    totalResDf = unionIfNotEmpty(totalResDf, tiebaDimResDf)
    totalResDf = unionIfNotEmpty(totalResDf, eduResDf)
    totalResDf = unionIfNotEmpty(totalResDf, searchResDf)

    totalResDf = totalResDf
      .na.fill("")
      .dropDuplicates()
      .repartition(10)
    println("最终总的数据量为：" + totalResDf.count())
    totalResDf.createOrReplaceTempView("ResDf")

    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
        |select
        |   uid,
        |   type,
        |   value,
        |   source,
        |   properties
        |from ResDf
        |""".stripMargin)

    spark.stop()
  }

}

