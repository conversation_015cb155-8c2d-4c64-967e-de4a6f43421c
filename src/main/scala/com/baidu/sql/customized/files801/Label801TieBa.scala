package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{getTiebaMessage, mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.broadcast.Broadcast
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 *  @author: zhangrunjie 801风控用户标签表，贴吧用户标签表
 */
object Label801TieBa {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 贴吧封禁词表
  val blockedWords = Seq(
      //词表关键词，label_lv2
      Row("涉政","涉政"),
      Row("涉习","涉政"),
      Row("反动","涉政"),
      Row("政治","涉政"),
      Row("六四","涉政"),
      Row("台独","涉政"),
      Row("涉x","涉政"),
      Row("涉X","涉政"),
      Row("色情","色情"),
      Row("黄色","色情"),
      Row("涉黄","色情"),
      Row("漏点","色情"),
      Row("招嫖","色情"),
      Row("黑产","黑产"),
      Row("赌博","黑产"),
      Row("代孕","黑产"),
      Row("诈骗","黑产"),
      Row("涉毒","涉毒"),
      Row("未成年","未成年"),
      Row("违法","违反法规"),
      Row("违规","违反法规"),
      Row("机器发文","黑产"),
      Row("引流","黑产")
  )
  //封禁类型字典
  val tieba_block_type = Map(
    1 -> "单吧封禁",
    4 -> "全局封禁",
    16 -> "封禁屏蔽"
  )

  // 标签等级字典
  val labellevels = Seq(
    Row("黑","黑产","ODS日志脱端"),
    Row("黑","黑产","PC脱端实时"),
    Row("黑","黑产","apng"),
    Row("黑","黑产","cuid黑团伙"),
    Row("黑","黑产","cuid小黑团伙"),
    Row("黑","黑产","iphone异常"),
    Row("黑","黑产","ja3avg"),
    Row("黑","黑产","win8格式异常"),
    Row("黑","黑产","zhangqi_faketel"),
    Row("黑","黑产","黑cuid关联账户"),
    Row("黑","黑产","小米3g"),
    Row("黑","黑产","实时API脱端检测"),
    Row("黑","黑产","发帖机"),
    Row("黑","黑产","头像巡检"),
    Row("黑","黑产","批量注册"),
    Row("黑","黑产","机器刷帖"),
    Row("黑","色情","色情内容"),
    Row("黑","黑产","色情引流"),
    Row("黑","黑产","设备关联"),
    Row("黑","黑产","轨迹异常"),
    Row("黑","黑产","批量注册(UA异常)"),
    Row("黑","黑产","批量注册(云IP1)"),
    Row("黑","黑产","批量注册(云IP2)"),
    Row("黑","黑产","批量注册(用户名相似)"),
    Row("黑","黑产","发帖机引流"),
    Row("黑","黑产","地区吧作弊"),
    Row("黑","黑产","世界杯主题帖"),
    Row("黑","色情","色情相关图片"),
    Row("黑","色情","地区吧色情引流"),
    Row("黑","黑产","阳光发帖机拓展"),
    Row("黑","黑产","变体色情赌博引流"),
    Row("黑","黑产","实时流发帖机特征"),
    Row("黑","黑产","视频封面赌博引流"),
    Row("黑","黑产","解封账号重审封禁"),
    Row("黑","黑产","误解封账号再封禁"),
    Row("黑","黑产","楼中楼色情赌博引流"),
    Row("黑","黑产","贴吧视频封面赌博引流"),
    Row("黑","违反法规","违规用户短封"),
    Row("黑","其它lv1","quentin平台_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","mis_userpunish_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","pass_account_securit_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","mis_uegaudit_{封禁类型}"),
    Row("黑","黑产","mis_uegaudit_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","吧务&pm_{封禁类型}")
  )

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取贴吧数据
    val tiebaSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  tid, -- 发帖的id
         |  t_uid,
         |  floor_reply_pid,
         |  floor_reply_uid,
         |  floor_quote_post_uid,
         |  floor_quote_post_pid,
         |  fname,
         |  title,
         |  content,
         |  cast(create_ts as long) as timestamp
         |from
         |  ubs_tieba.tieba_dim_pub_info_hi
         |where
         |  event_day = '${YesterDay}'
         |  and cast(uid as long) > 0
         |order by uid
         |""".stripMargin

    val tiebaDf = spark.sql(tiebaSql)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧数据共：" + tiebaDf.count())
    tiebaDf.createOrReplaceTempView("tieba")
    //tiebaDf.show(5,false)

    //贴吧回贴进行拆分去重
    val commUidDf = spark.sql(
      """
        |with temp_table as
        |(select
        |     uid,
        |     concat_ws(',', COLLECT_SET(t_uid), COLLECT_SET(floor_reply_uid), COLLECT_SET(floor_quote_post_uid)) as con_uid
        | from tieba
        | group by uid
        |),
        |explode_table as
        |(select
        |  uid,
        |  explode(split(con_uid,',')) as comment_uid
        |from temp_table
        |)
        |select
        | uid,
        | comment_uid,
        | count(*) as com_cnt
        |from explode_table
        |where comment_uid != '' and comment_uid != '0'
        |and uid != comment_uid
        |group by uid,comment_uid
        |""".stripMargin)
    commUidDf.persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧用户拆分：" + commUidDf.count())
    commUidDf.createOrReplaceTempView("commUidDf")

    //读取贴吧用户关注明细维度表
    val tiebaDetailDf = spark.sql(
      s"""
        |select
        |   uid,
        |   followed_uid,
        |   count(*) as cnt
        |from ubs_tieba.tieba_dim_user_follow_detail_df
        |where event_day = '${YesterDay}'
        |and uid != followed_uid
        |group by uid,followed_uid
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧用户关注详情数据共：" + tiebaDetailDf.count())
    tiebaDetailDf.createOrReplaceTempView("tiebaDetailDf")

    //用户封禁操作日志明细数据
    val tiebaBlockUserDf = spark.sql(
      s"""
        |select
        |   uid,
        |   tieba_user_block_src,
        |   is_unblock,
        |   description,
        |   op_uname,
        |   tieba_block_type,
        |   fname
        |from ubs_tieba.tieba_dwd_operate_block_user_di
        |where event_day = '${YesterDay}'
        |and uid > 0
        |""".stripMargin)
      .repartition(5)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("用户封禁操作日志明细数据数据共：" + tiebaBlockUserDf.count())
    tiebaBlockUserDf.createOrReplaceTempView("tieba_block_user")
    val tiebaBlockDf = tiebaBlockUserDf.filter(col("is_unblock") === "0" and col("tieba_block_type").isin(1,4,16))
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //获取贴吧私信数据表
    val messageInfoDf = spark.sql(
      s"""
        |select
        |   uid,
        |   split(tag,':')[1] as send_uid,
        |   cast(create_time as long) as timestamp
        |from udw_ns.default.tieba_ods_dump_im_message_info
        |where event_day = '${YesterDay}'
        |and uid > 0
        |and tag != ''
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧私信数据共：" + messageInfoDf.count())
    messageInfoDf.createOrReplaceTempView("tiebaMessage")

    //贴吧私信数据表
    val messageSql =
      s"""
         |select
         |   uid,
         |   cast(send_uid as long) as send_uid
         |from tiebaMessage
         |where uid != send_uid
         |and cast(send_uid as long) > 0
         |group by uid,send_uid
         |""".stripMargin
    val messageUidDf = spark.sql(messageSql)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧私信过滤uid数据表共：" + messageUidDf.count())
    messageUidDf.createOrReplaceTempView("tieba_message_view")

    //贴吧类型词表匹配
    val tiebaTypeCsv = readCsv(spark,"贴吧类型词表")
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    val tiebaTypeBroad =  spark.sparkContext.broadcast(tiebaTypeCsv)
    tiebaTypeBroad.value.createOrReplaceTempView("tieba_type_view")

    //tieba_dim_pub_info_hi关联贴吧类型词表数据
    val tiebaTypeDf = spark.sql(
        """
          |select
          |   T.uid,
          |   B.post_type
          |from tieba T
          |inner join
          |(select post_name,post_type
          | from tieba_type_view
          | group by post_name,post_type) B
          |on T.fname = B.post_name
          |""".stripMargin)
      .withColumn("source", lit("贴吧"))
      .withColumn("type", lit("日常行为"))
      .withColumn("sub_type", lit("贴吧活跃"))
      .withColumn("label_lv1", lit("灰"))
      .withColumn("label_lv2", lit("其它lv3"))
      .withColumn("label_lv3", concat(lit("在"),col("post_type"),lit("相关吧发回帖")))
      .withColumn("score", lit("80"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //tiebaTypeJoinDf.createOrReplaceTempView("tiebaTypeJoinDf")
    println("tieba_dim_pub_info_hi关联贴吧类型词表数据共：" + tiebaTypeDf.count())

    //tieba_dwd_operate_block_user_di关联贴吧类型词表数据
    val tiebaBlockTypeDf = spark.sql(
        """
          |select
          |   T.uid,
          |   B.post_type
          |from tieba_block_user T
          |inner join
          |(select post_name,post_type
          | from tieba_type_view
          | group by post_name,post_type) B
          |on T.fname = B.post_name
          |""".stripMargin)
      .withColumn("source", lit("贴吧"))
      .withColumn("type", lit("日常行为"))
      .withColumn("sub_type", lit("贴吧活跃"))
      .withColumn("label_lv1", lit("灰"))
      .withColumn("label_lv2", lit("其它lv2"))
      .withColumn("label_lv3", concat(lit("在"),col("post_type"),lit("相关吧违规")))
      .withColumn("score", lit("80"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //tiebaTypeJoinDf.createOrReplaceTempView("tiebaTypeJoinDf")
    println("tieba_dwd_operate_block_user_di关联贴吧类型词表数据共：" + tiebaBlockTypeDf.count())

    //儿童色情相关吧与贴吧数据关联
    val ertongCsv = readCsv(spark,"儿童色情相关吧")
    val ertongBroad =  spark.sparkContext.broadcast(ertongCsv)
    ertongBroad.value.createOrReplaceTempView("ertong_view")
    val ertongJoinDf = spark.sql(
      """
        |select
        |   T.uid
        |from tieba T
        |inner join ertong_view B
        |on T.fname = B.post_name
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    ertongJoinDf.createOrReplaceTempView("ertongJoinDf")
    val ertongDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","未成年色情","儿童色情相关吧活跃","80",ertongJoinDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情相关吧数据关联共：" + ertongDf.count())

    //评论儿童色情种子用户
    val childrenCsv = readCsv(spark,"儿童色情种子用户")
      .withColumn("uid",col("uid").cast("long"))
    //小表做广播
    val childrenBroad = spark.sparkContext.broadcast(childrenCsv)
    childrenBroad.value.createOrReplaceTempView("children_view")

    //贴吧
    val tiebaChildrenJoinDf = spark.sql(
      """
        |select
        | T.uid
        |from commUidDf T
        |inner join children_view B
        |on T.comment_uid = B.uid
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("评论儿童色情种子用户数据关联贴吧共：" + tiebaChildrenJoinDf.count())
    tiebaChildrenJoinDf.createOrReplaceTempView("tiebaChildrenJoinDf")

    val tiebaChildrenDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","评论儿童色情种子用户","80",tiebaChildrenJoinDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("评论儿童色情种子用户数据关联贴吧共：" + tiebaChildrenDf.count())
    //tiebaChildrenDf.show(5,false)

    //儿童色情种子用户评论对象
    //贴吧
    val tiebaChildrenCommentDf = spark.sql(
      """
        |select
        |   T.comment_uid as uid
        |from commUidDf T
        |inner join children_view B
        |on T.uid = B.uid
        |where cast(T.comment_uid as long) > 0
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情种子用户评论对象数据关联贴吧共：" + tiebaChildrenCommentDf.count())
    tiebaChildrenCommentDf.createOrReplaceTempView("tiebaChildrenCommentDf")

    val explodeTiebaDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户评论对象","80",tiebaChildrenCommentDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情种子用户数据关联贴吧共：" + explodeTiebaDf.count())
    //explodeTiebaDf.show(5,false)

    //敏感吧内评论儿童色情种子用户
    val tiebaErtongDf = spark.sql(
      """
        |select
        | T.uid
        |from tiebaChildrenJoinDf T
        |inner join ertongJoinDf E
        |on T.uid = E.uid
        |""".stripMargin)
    //tiebaErtongDf.show(5,false)
    val tiebaErtongResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","敏感吧内评论儿童色情种子用户","80",tiebaErtongDf)
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("敏感吧内评论儿童色情种子用户数据关联贴吧共：" + tiebaErtongResDf.count())

    //敏感吧内儿童色情种子用户评论对象
    val tiebaErtongCommentDf = spark.sql(
        """
          |select
          | T.uid
          |from tiebaChildrenCommentDf T
          |inner join ertongJoinDf E
          |on T.uid = E.uid
          |""".stripMargin)

    //tiebaErtongCommentDf.show(5,false)
    val tiebaErtongCommentResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","敏感吧内儿童色情种子用户评论对象","80",tiebaErtongCommentDf)
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("敏感吧内儿童色情种子用户评论对象数据关联贴吧共：" + tiebaErtongCommentResDf.count())

    tiebaChildrenJoinDf.unpersist()
    ertongJoinDf.unpersist()
    tiebaChildrenCommentDf.unpersist()

    // 涉政敏感吧词表与贴吧数据关联
    val shezhenCsv = readCsv(spark,"涉政敏感吧")
    val shezhenJsonDf = tiebaDf.as("T").join(shezhenCsv.as("B"), col("T.fname") === col("B.post_name"))
    val shezhenDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","涉政","涉政敏感吧活跃","80",shezhenJsonDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("涉政敏感吧词表关联共：" + shezhenDf.count())
    //shezhenDf.show(5,false)

    //偷拍密录设备相关吧词表与贴吧数据关联
    val toupaiCsv = readCsv(spark,"偷拍密录设备相关吧")
    val heichanJoinDf = tiebaDf.as("T").join(toupaiCsv.as("B"), col("T.fname") === col("B.post_name"))
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    val heichanDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","黑产","偷拍密录设备相关吧活跃","80",heichanJoinDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("偷拍密录设备相关吧词表数据关联共：" + heichanDf.count())
    //heichanDf.show(5,false)

    //涉毒吧词表与贴吧数据关联
    val sheduCsv = readCsv(spark,"涉毒吧")
    val sheduJoinDf = tiebaDf.as("T").join(sheduCsv.as("B"), col("T.fname") === col("B.post_name"))
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    val sheduDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","涉毒","涉毒吧活跃","80",sheduJoinDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("涉毒吧词表数据关联共：" + sheduDf.count())

    //发布涉毒相关内容
    val sheduTextCsv = readCsv(spark,"涉毒暗语文本")
    val sheduTextBroad = spark.sparkContext.broadcast(sheduTextCsv)

    //标题
    val titleDf = sheduJoinDf.as("S").join(
      sheduTextBroad.value.filter(col("match_col") === "标题").as("T"),
      col("S.title") contains col("T.voc_content")
    )

    //内容 单词匹配
    val contentDf = sheduJoinDf.as("S").join(
      sheduTextBroad.value.filter(col("match_col") === "内容" && col("match_type") === "单词匹配").as("T"),
      col("S.content") contains col("T.voc_content")
    )

    //内容 双词匹配
    val contentDouDf = sheduJoinDf.as("S").join(
        sheduTextBroad.value.filter(col("match_col") === "内容" && col("match_type") === "双词匹配")
        .withColumn("voc_list",split(col("voc_content"),"&")).as("T"),
      (col("S.content").contains(col("T.voc_list")(0)))
        && (col("S.content").contains(col("T.voc_list")(1)))
    )
      .drop("voc_list")

    val tiebaSheduDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉毒","发布涉毒相关内容","80",titleDf,contentDf,contentDouDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("发布涉毒相关内容数据关联贴吧共：" + tiebaSheduDf.count())
    //tiebaSheduDf.show(5,false)

    tiebaChildrenCommentDf.unpersist()

    //拍密录设备相关文本
    val shebeiTextCsv = readCsv(spark,"偷拍密录设备相关文本")
    val shebeiTextBroad = spark.sparkContext.broadcast(shebeiTextCsv)

    //内容 单词匹配
    val contentShebeiDf = heichanJoinDf.as("S").join(
      shebeiTextBroad.value.filter(col("match_type") === "单词匹配").as("T"),
      col("S.content") contains col("T.voc_content")
    )

    //内容 双词匹配
    val contentShebeiDouDf = heichanJoinDf.as("S").join(
        shebeiTextBroad.value.filter(col("match_type") === "双词匹配").withColumn("voc_list",split(col("voc_content"),"&")).as("T"),
      (col("S.content").contains(col("T.voc_list")(0))) && (col("S.content").contains(col("T.voc_list")(1)))
    )
      .drop("voc_list")

    val tiebaTouPaiDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉毒","发布涉毒相关内容","80",contentShebeiDf,contentShebeiDouDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("发布偷拍密录设备相关文本数据关联贴吧共：" + tiebaTouPaiDf.count())
    //tiebaTouPaiDf.show(5,false)

    heichanJoinDf.unpersist()

    //贴吧发回帖计算时间差
    val tiebaDiffDf = spark.sql(
      """
        |WITH diff_time AS (
        |    SELECT
        |        uid,
        |        -- 计算与上一条记录的时间差（单位：秒）
        |        timestamp -
        |        LAG(timestamp, 1) OVER (PARTITION BY uid ORDER BY timestamp) AS time_diff_seconds
        |    FROM
        |        tieba
        |)
        |SELECT
        |    uid,
        |    -- 计算总体方差（分母n）
        |    VAR_POP(time_diff_seconds) AS variance_population
        |FROM
        |    diff_time
        |WHERE
        |    time_diff_seconds IS NOT NULL
        |GROUP BY
        |    uid
        |HAVING
        |    COUNT(*) >= 2
        |""".stripMargin)
      .filter(col("variance_population") <= "3")
      .withColumn("score",(when(col("variance_population") === "0.0","100")
        .when(col("variance_population") <= "0.25","90")
        .when(col("variance_population") <= "0.5","80")
        .when(col("variance_population") <= "1","70")
        .when(col("variance_population") <= "2","60")
        .when(col("variance_population") <= "3","50")).cast("float"))

    val tiebaVarResDf = mergeAndAddColumns("贴吧","日常行为","机器特征","灰","机器","固定步长发回帖","",tiebaDiffDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //tiebaVarResDf.show(5,false)
    println("贴吧评论方差计算数据：" + tiebaVarResDf.count())

    //贴吧高频发帖
    val tiebaCntDf = spark.sql(
        """
          |with cnt_tmp as (
          |SELECT
          |        uid,
          |        timestamp,
          |        -- 计算1秒内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1s,
          |        -- 计算1分钟内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 60 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1m,
          |        -- 计算1小时内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1h
          |    FROM tieba
          |    ORDER BY uid,timestamp
          | )
          | SELECT
          |    uid,
          |    MAX(
          |        CASE
          |            WHEN cnt_1s > 1 THEN 100     -- 1秒内发帖次数大于1
          |            WHEN cnt_1m > 3 THEN 90     -- 1分钟内发帖次数大于3
          |            WHEN cnt_1h > 5 THEN 80     -- 1小时内发帖次数大于5
          |            ELSE 0
          |        END
          |    ) AS score
          |FROM cnt_tmp
          |group by uid
          |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))
      .dropDuplicates()

    val tiebaResDf = mergeAndAddColumns("贴吧","日常行为","机器特征","灰","机器","高频发回帖","",tiebaCntDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧高频发文数据：" + tiebaResDf.count())

    //贴吧高频私信
    val tiebaMessageCntDf = spark.sql(
        """
          |with cnt_tmp as (
          |SELECT
          |        uid,
          |        timestamp,
          |        -- 5分钟内私信人数（去重）
          |        size(collect_set(send_uid) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 300 PRECEDING AND CURRENT ROW
          |        ))  AS cnt_5m,  -- 减去当前行
          |
          |        -- 1小时内私信人数（去重）
          |        size(collect_set(send_uid) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
          |        ))  AS cnt_1h,  -- 减去当前行
          |
          |        -- 1天内私信人数（无需时间窗口）
          |        size(collect_set(send_uid) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
          |        )) AS cnt_1d
          |    FROM tiebaMessage
          |    ORDER BY uid,timestamp
          | )
          | SELECT
          |    uid,
          |    MAX(
          |        CASE
          |            WHEN (cnt_5m >= 20 or cnt_1h >= 30 or cnt_1d > 80) THEN 100     -- 5分钟内私信>=20人或1小时>=30人或1天>80人
          |            WHEN (cnt_5m >= 10 or cnt_1h >= 20 or cnt_1d > 50) THEN 90     -- 5分钟内私信>=10人或1小时>=20人或1天>50人
          |            WHEN (cnt_5m >= 5 or cnt_1h >= 10 or cnt_1d > 25) THEN 80
          |            WHEN (cnt_5m >= 3 or cnt_1h >= 5 or cnt_1d > 10) THEN 70
          |            WHEN (cnt_1h >= 3 or cnt_1d >= 5) THEN 60
          |            WHEN (cnt_1d >= 3) THEN 50
          |            ELSE 0
          |        END
          |    ) AS score
          |FROM cnt_tmp
          |group by uid
          |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))
      .dropDuplicates()

    val tiebaMessageResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","黑产","高频私信多人","",tiebaMessageCntDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧高频私信多人数据：" + tiebaMessageResDf.count())

    //私信儿童色情种子用户
    val messageChildrenCommentDf = spark.sql(
      """
        |select
        |  A.uid
        |from tieba_message_view A
        |inner join children_view C
        |on A.send_uid = C.uid
        |""".stripMargin)

    val messageChildrenDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","私信儿童色情种子用户","80",messageChildrenCommentDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("私信儿童色情种子用户数据关联贴吧共：" + messageChildrenDf.count())
    //insChildrenDf.show(5,false)

    //儿童色情种子用户私信对象
    val ChildreninsCommentMessageDf = spark.sql(
      """
        |select
        |  A.send_uid as uid
        |from tieba_message_view A
        |inner join children_view C
        |on A.uid = C.uid
        |where cast(A.send_uid as long) > 0
        |""".stripMargin)

    val ChildreninsMessageDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户私信对象","80",ChildreninsCommentMessageDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情种子用户私信对象数据关联贴吧共：" + ChildreninsMessageDf.count())

    //关注儿童色情种子用户
    val insChildrenCommentDf = spark.sql(
      """
        |select
        |  A.uid
        |from tiebaDetailDf A
        |inner join children_view C
        |on A.followed_uid = C.uid
        |""".stripMargin)

    val insChildrenDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","关注儿童色情种子用户","80",insChildrenCommentDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("关注儿童色情种子用户数据关联贴吧共：" + insChildrenDf.count())
    //insChildrenDf.show(5,false)

    //儿童色情种子用户关注对象
    val ChildreninsCommentDf = spark.sql(
      """
        |select
        |  A.followed_uid as uid
        |from tiebaDetailDf A
        |inner join children_view C
        |on A.uid = C.uid
        |where cast(A.followed_uid as long) > 0
        |""".stripMargin)

    val ChildreninsDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户关注对象","80",ChildreninsCommentDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情种子用户关注对象数据关联贴吧共：" + ChildreninsDf.count())
    //ChildreninsDf.show(5,false)

    //与儿童色情用户互关,且至少一个用户为儿童色情
    val eachFollowChildrenDf = spark.sql(
      """
        |select
        |  A.uid,
        |  A.followed_uid,
        |  B.uid as Buid,
        |  B.followed_uid as Bfollowed_uid
        |from tiebaDetailDf A
        |inner join tiebaDetailDf B
        |on A.uid = B.followed_uid and A.followed_uid = B.uid
        |inner join children_view C
        |on A.uid = C.uid or A.followed_uid = C.uid
        |where A.uid > 0 and A.followed_uid > 0
        |""".stripMargin)

    val eachFollowResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情用户互关","80",eachFollowChildrenDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("与儿童色情用户互关数据贴吧共：" + eachFollowResDf.count())
    //eachFollowResDf.show(5,false)

    //与儿童色情用户私信,且至少一个用户为儿童色情
    val eachMessageChildrenDf = spark.sql(
      """
        |select
        |  A.uid,
        |  A.send_uid,
        |  B.uid as Buid,
        |  B.send_uid as Bsend_uid
        |from tieba_message_view A
        |inner join tieba_message_view B
        |on A.uid = B.send_uid and A.send_uid = B.uid
        |inner join children_view C
        |on A.uid = C.uid or A.send_uid = C.uid
        |where A.uid > 0 and A.send_uid > 0
        |""".stripMargin)

    val eachMessageResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情用户互相私信","80",eachMessageChildrenDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("与儿童色情用户互相私信数据贴吧共：" + eachMessageResDf.count())

    //贴吧运营域删贴维度表
    val tiebaDelpubDf = spark.sql(
      s"""
        |select
        | uid,
        | fid,
        | tid,
        | del_src,
        | ueg_strategy,
        | tieba_del_type,
        | ueg_strategy,
        | pid,
        | pub_type
        |from udw_ns.default.tieba_dim_operate_delpub_di
        |where event_day = '${YesterDay}'
        |and uid > 0
        |""".stripMargin)
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧运营域删贴维度表数据：" + tiebaDelpubDf.count())
    tiebaDelpubDf.createOrReplaceTempView("tiebaDelpubDf")

    //与贴吧类型词表关联
    //tieba_dim_pub_info_hi关联贴吧类型词表数据
    val tiebaDelTypeDf = spark.sql(
        """
          |select
          |   T.uid,
          |   B.post_type
          |from tiebaDelpubDf T
          |inner join tieba_type_view B
          |on T.fid = B.fid
          |""".stripMargin)
      .dropDuplicates()
      .withColumn("source", lit("贴吧"))
      .withColumn("type", lit("日常行为"))
      .withColumn("sub_type", lit("贴吧活跃"))
      .withColumn("label_lv1", lit("灰"))
      .withColumn("label_lv2", lit("其它lv2"))
      .withColumn("label_lv3", concat(lit("在"),col("post_type"),lit("相关吧违规")))
      .withColumn("score", lit("80"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //tiebaTypeJoinDf.createOrReplaceTempView("tiebaTypeJoinDf")
    println("tieba_dim_operate_delpub_di关联贴吧类型词表数据共：" + tiebaDelTypeDf.count())

    //与儿童色情种子用户同违规主题活跃
    val delpubDf = spark.sql(
      """
        |with tid_temp as (
        |select
        |   uid,
        |   tid,
        |   count(*) as cnt
        |from tieba
        |where tid > 0
        |group by uid,tid
        |),
        |delpub_temp as (
        |select
        |  T.tid
        |from tid_temp T
        |inner join children_view B on T.uid = B.uid
        |inner join tiebaDelpubDf C on T.tid = C.tid
        |)
        |select
        | T.uid
        |from tid_temp T
        |left anti join children_view B on T.uid = B.uid
        |inner join delpub_temp D on T.tid = D.tid
        |""".stripMargin)
      .dropDuplicates()

    val delpubDfResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情种子用户同违规主题活跃","80",delpubDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("与儿童色情种子用户同违规主题活跃数据：" + delpubDfResDf.count())

    tiebaDf.unpersist()

    //违规处罚标签,贴吧封禁============================================================
    // 定义DataFrame的模式
    val levelschema = StructType(Array(
      StructField("label_lv1", StringType, true),
      StructField("label_lv2", StringType, true),
      StructField("label_lv3", StringType, true)
    ))

    val blockschema = StructType(Array(
      StructField("keywords", StringType, true),
      StructField("label_lv2", StringType, true)
    ))

    //定义广播
    val labelDf = spark.createDataFrame(spark.sparkContext.parallelize(labellevels), levelschema)
    val labellevelsBroad = spark.sparkContext.broadcast(labelDf)
    val blockDf = spark.createDataFrame(spark.sparkContext.parallelize(blockedWords),blockschema)
    val blockedWordsBroad = spark.sparkContext.broadcast(blockDf)

    //贴吧封禁数据
    val tiebaBlockLabel = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "safe_mis" and col("op_uname") === "xteam").as("T")
      .join(labellevelsBroad.value.filter(!col("label_lv3").contains("{封禁类型}")).as("B"),
        col("T.description") contains col("B.label_lv3"))
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        $"label_lv1",
        $"label_lv2",
        $"label_lv3",
        lit("100") as "score"
      )
      .repartition(50)
    println("贴吧封禁数据：" + tiebaBlockLabel.count())
    //tiebaBlockLabel.show(5,false)

    //quentin平台
    val quentinDf = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "mis_quentin")
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        lit("黑") as "label_lv1",
        lit("其它lv1") as "label_lv2",
        lit("100") as "score"
      )
    var quentinResDf = spark.emptyDataFrame
    for (elem <- tieba_block_type.values) {
      if (quentinResDf.isEmpty){
        quentinResDf = quentinDf.withColumn("label_lv3",lit(s"quentin平台_${elem}"))
      }else{
        quentinResDf = quentinResDf.unionByName(quentinDf.withColumn("label_lv3",lit(s"quentin平台_${elem}")))
      }
    }
    println("quentin平台数据：" + quentinResDf.count())
    //quentinResDf.show(5,false)

    //黑产
    //mis_uegaudit_{封禁类型}
    val uegDf = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "ueg_handlelogic")
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        lit("黑") as "label_lv1",
        lit("黑产") as "label_lv2",
        lit("100") as "score"
      )
    var uegResDf = spark.emptyDataFrame
    for (elem <- tieba_block_type.values) {
      if (uegResDf.isEmpty){
        uegResDf = uegDf.withColumn("label_lv3",lit(s"mis_uegaudit_${elem}"))
      }else{
        uegResDf = uegResDf.unionByName(uegDf.withColumn("label_lv3",lit(s"mis_uegaudit_${elem}")))
      }
    }
    println("黑产mis_uegaudit数据：" + uegResDf.count())

    //mis_userpunish_{封禁类型}
    val misUserpunishResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"mis_userpunish")
    println("mis_userpunish_数据：" + misUserpunishResDf.count())

    //pass_account_securit_{封禁类型}
    val passAccountResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"pass_account_securit")
    println("pass_account_securit数据：" + passAccountResDf.count())

    //mis_uegaudit_{封禁类型}
    val misUegauditResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"mis_uegaudit","op_uname")
    println("mis_uegaudit数据：" + misUegauditResDf.count())

    //吧务&pm_{封禁类型}
    val pmResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"")
    println("吧务&pm数据：" + pmResDf.count())

    //合并贴吧封禁数据
    val tiebaBlockResDf = tiebaBlockLabel
      .unionByName(quentinResDf)
      .unionByName(uegResDf)
      .unionByName(misUserpunishResDf)
      .unionByName(passAccountResDf)
      .unionByName(misUegauditResDf)
      .unionByName(pmResDf)
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧封禁数据：" + tiebaBlockResDf.count())

    tiebaBlockDf.unpersist()

    //贴吧删帖违规规则
    val tiebaDelSrcDf = spark.sql(
      """
        |select
        | uid,
        | '黑' as label_lv1,
        | del_src,
        | ueg_strategy,
        | tieba_del_type
        |from tiebaDelpubDf
        |where del_src not in (100, 200)
        |""".stripMargin)
      .dropDuplicates()
      .withColumn("tieba_del_type",when(col("tieba_del_type") === "",lit("unknown")).otherwise(col("tieba_del_type")))
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧删帖数据：" + tiebaDelSrcDf.count())

    //读取贴吧删帖策略字典
    val tiebaDelwords = readCsv(spark,"贴吧删帖策略字典")
      .withColumnRenamed("ueg_strategy","keywords")
    val tiebaDelBorad = spark.sparkContext.broadcast(tiebaDelwords)

    val tiebaDel500Df = tiebaDelSrcDf
      .filter(col("del_src") === "500")
      .withColumn("label_lv2",lit("其它lv1"))
      .withColumn("label_lv3",concat(lit(s"在线管理员删贴_"),col("tieba_del_type")))
      .repartition(50)
    println("在线管理员删贴数据：" + tiebaDel500Df.count())
    //tiebaDel500Df.show(5,false)

    val tiebaDel400Df = tiebaDelSrcDf
      .filter(col("del_src") === "400" and col("ueg_strategy") === "0")
      .withColumn("label_lv2",lit("其它lv1"))
      .withColumn("label_lv3",concat(lit(s"机器删贴_"),col("tieba_del_type")))
      .repartition(50)
    println("机器删贴400数据：" + tiebaDel400Df.count())
    //tiebaDel400Df.show(5,false)

    val tiebaUegDf = tiebaDelSrcDf
      .filter(col("del_src") === "400" and col("ueg_strategy") =!= "0" and col("tieba_del_type") === "ueg_handlelogic")
      .as("T")
      .join(tiebaDelBorad.value.as("B"),col("T.ueg_strategy").contains(col("B.keywords")))
      .select(
        col("uid").as("uid"),
        col("del_src").as("del_src"),
        col("ueg_strategy").as("ueg_strategy"),
        //col("keywords").as("keywords"),
        col("tieba_del_type").as("tieba_del_type"),
        col("label_lv1").as("label_lv1"),
        col("label_lv2").as("label_lv2")
      )
      .withColumn("label_lv3",concat(lit(s"机器删贴_"),col("ueg_strategy")))
      .repartition(50)
    println("机器删贴ueg数据：" + tiebaUegDf.count())

    //合并贴吧删帖结果
    val tiebaDelResDf = tiebaDel500Df
      .unionByName(tiebaDel400Df)
      .unionByName(tiebaUegDf)
      .select("uid","label_lv1","label_lv2","label_lv3")
      .withColumn("source",lit("帖吧"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("贴吧删帖"))
      .withColumn("score",lit("100"))
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧删帖最终结果：" + tiebaDelResDf.count())

    //合并违规处罚标签数据，过滤涉政数据
    val tiebaBlockTotalDf = tiebaDelResDf.unionByName(tiebaBlockResDf)
      .filter(col("label_lv2") === "涉政" and col("label_lv1") === "黑")
      .select("uid")
      .dropDuplicates()
    tiebaBlockTotalDf.createOrReplaceTempView("tieba_block_total_data")

    //评论、回复涉政资源
    val tiebaSheZhengDf = spark.sql(
      """
        |with tmp_table as (
        |select
        | D.uid,
        | D.pid,
        | D.tid,
        | D.pub_type
        |from tieba_block_total_data T
        |join tiebaDelpubDf D
        |on T.uid = D.uid
        |),
        |tmp_type1 as (
        |select
        | T.uid,
        | '评论、回复涉政资源' as label_lv3
        |from tieba T
        |join (select tid,count(*) as tid_cnt from tmp_table where pub_type = '1' group by tid) B
        |on T.tid = B.tid
        |),
        |tmp_type2 as (
        |select
        | T.uid,
        | '评论、回复涉政资源' as label_lv3
        |from tieba T
        |join (select pid,count(*) as pid_cnt from tmp_table where pub_type = '2' group by pid) B
        |on T.floor_reply_pid = B.pid
        |),
        |tmp_type3 as (
        |select
        | T.uid,
        | '评论、回复涉政资源' as label_lv3
        |from tieba T
        |join (select pid,count(*) as pid_cnt from tmp_table where pub_type = '3' group by pid) B
        |on T.floor_quote_post_pid = B.pid
        |)
        |select
        |*
        |from tmp_type1
        |union all
        |select
        |*
        |from tmp_type2
        |union all
        |select
        |*
        |from tmp_type3
        |""".stripMargin)
      .dropDuplicates()
      .withColumn("label_lv1",lit("灰"))
    	.withColumn("label_lv2",lit("涉政"))
      .withColumn("source",lit("贴吧"))
      .withColumn("type",lit("日常行为"))
      .withColumn("sub_type",lit("发文、发帖、点赞、关注等行为"))
      .withColumn("score",lit("80"))
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("评论、回复涉政资源的数据：" + tiebaSheZhengDf.count())
    tiebaDelpubDf.unpersist()
    tiebaDelSrcDf.unpersist()

    //合并结果
    var resDf = //儿童色情相关吧数据
        unionIfNotEmpty(ertongDf,tiebaChildrenDf)  // 评论儿童色情种子用户数据
        resDf = unionIfNotEmpty(resDf,tiebaTypeDf)  // 贴吧类型数据1
        resDf = unionIfNotEmpty(resDf,tiebaBlockTypeDf)  // 贴吧类型数据2
        resDf = unionIfNotEmpty(resDf,tiebaDelTypeDf)  // 贴吧类型数据3
        resDf = unionIfNotEmpty(resDf,explodeTiebaDf)  // 儿童色情种子用户数据
        resDf = unionIfNotEmpty(resDf,tiebaErtongResDf)  // 敏感吧内评论儿童色情种子用户
        resDf = unionIfNotEmpty(resDf,tiebaErtongCommentResDf)  // 敏感吧内儿童色情种子用户评论对象
        resDf = unionIfNotEmpty(resDf,shezhenDf)  //涉政敏感吧词表
        resDf = unionIfNotEmpty(resDf,heichanDf)  //偷拍密录设备相关吧词表
        resDf = unionIfNotEmpty(resDf,sheduDf)    //涉毒吧词表数据
        resDf = unionIfNotEmpty(resDf,tiebaSheduDf)    //发布涉毒相关内容数据
        resDf = unionIfNotEmpty(resDf,tiebaTouPaiDf) //偷拍密录设备相关吧数据
        resDf = unionIfNotEmpty(resDf,tiebaVarResDf) //贴吧评论方差计算数据
        resDf = unionIfNotEmpty(resDf,tiebaResDf) //贴吧高频发文数据
        resDf = unionIfNotEmpty(resDf,tiebaMessageResDf) //贴吧高频私信多人数据
        resDf = unionIfNotEmpty(resDf,messageChildrenDf)  //私信儿童色情种子用户数据
        resDf = unionIfNotEmpty(resDf,ChildreninsMessageDf)  //儿童色情种子用户私信对象
        resDf = unionIfNotEmpty(resDf,insChildrenDf)  //关注儿童色情种子用户数据
        resDf = unionIfNotEmpty(resDf,ChildreninsDf)  //儿童色情种子用户关注对象
        resDf = unionIfNotEmpty(resDf,eachFollowResDf)  //与儿童色情用户互关数据
        resDf = unionIfNotEmpty(resDf,eachMessageResDf)  //与儿童色情用户互相私信数据
        resDf = unionIfNotEmpty(resDf,delpubDfResDf)  //与儿童色情种子用户同违规主题活跃数据
        resDf = unionIfNotEmpty(resDf,tiebaSheZhengDf) //评论、回复涉政资源
        .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
        .withColumn("score",col("score").cast("float"))
        .dropDuplicates()
        .coalesce(1)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("贴吧最终结果：" + resDf.count())
    resDf.createOrReplaceTempView("res_label_data")

    //释放资源
    ertongDf.unpersist()
    tiebaChildrenDf.unpersist()
    tiebaTypeDf.unpersist()
    tiebaBlockTypeDf.unpersist()
    tiebaDelTypeDf.unpersist()
    explodeTiebaDf.unpersist()
    tiebaErtongResDf.unpersist()
    tiebaErtongCommentResDf.unpersist()
    shezhenDf.unpersist()
    heichanDf.unpersist()
    sheduDf.unpersist()
    tiebaSheduDf.unpersist()
    tiebaTouPaiDf.unpersist()
    tiebaVarResDf.unpersist()
    tiebaResDf.unpersist()
    tiebaMessageResDf.unpersist()
    messageChildrenDf.unpersist()
    ChildreninsMessageDf.unpersist()
    insChildrenDf.unpersist()
    ChildreninsDf.unpersist()
    eachFollowResDf.unpersist()
    eachMessageResDf.unpersist()
    delpubDfResDf.unpersist()
    tiebaBlockResDf.unpersist()
    tiebaDelResDf.unpersist()
    tiebaSheZhengDf.unpersist()

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    resDf.unpersist()
    spark.stop()
  }

  // 定义一个UDF来解码obj_param3
  val decodeObjParam3UDF = udf((objParam3: String) => {
    try {
      java.net.URLDecoder.decode(objParam3, "utf8")
    } catch {
      case _: Exception => objParam3  // 如果解码失败，返回原始值
    }
  })

  /**
   * 获取封禁数据
   * @param spark
   * @param dataDf 数据集
   * @param broad  广播数据
   * @param label3  三级标签和tieba_user_block_src需要过滤的数据
   * @param colname 需要过滤的字段名，默认是description
   * @return
   */
  def getFengjinData(spark: SparkSession,dataDf:DataFrame,broad: Broadcast[DataFrame],label3:String,colname:String = "description"): DataFrame = {
    var labelDf = spark.emptyDataFrame
    val label_lv3 = if (label3 == "") "吧务&pm" else label3
    if (colname != "description"){
      labelDf = dataDf
        .filter(col("tieba_user_block_src") === label3).as("T")
        .join(broad.value.as("B"),
          col(s"T.${colname}") contains col("B.keywords")
        ,"left_outer")
        .select(
          col("uid"),
          lit("帖吧") as "source",
          lit("违规处罚标签") as "type",
          lit("贴吧封禁") as "sub_type",
          lit("黑") as "label_lv1",
          col("label_lv2"),
          lit("100") as "score"
        )
        .withColumn("label_lv2",when(col("label_lv2").isNull,lit("其它lv1")).otherwise(col("label_lv2")))
    }else{
      labelDf = dataDf
        .filter(col("tieba_user_block_src") === label3).as("T")
        .join(broad.value.as("B"),
          col(s"T.${colname}") contains col("B.keywords"))
        .select(
          col("uid"),
          lit("帖吧") as "source",
          lit("违规处罚标签") as "type",
          lit("贴吧封禁") as "sub_type",
          lit("黑") as "label_lv1",
          col("label_lv2"),
          lit("100") as "score"
        )
    }
    //封禁类型的三级标签
    var ResDf = spark.emptyDataFrame
    for (elem <- tieba_block_type.values) {
      if (ResDf.isEmpty){
        ResDf = labelDf.withColumn("label_lv3",lit(s"${label_lv3}_${elem}"))
      }else{
        ResDf = ResDf.unionByName(labelDf.withColumn("label_lv3",lit(s"${label_lv3}_${elem}")))
      }
    }
    ResDf
  }
}

