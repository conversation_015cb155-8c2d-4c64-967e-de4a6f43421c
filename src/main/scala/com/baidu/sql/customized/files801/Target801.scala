package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.getNonageData
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 * @author: zhangrunjie 801风控用户指标表
 */
object Target801 {

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 180天内日期
  var Days180 = ""
  //7天内日期
  var Days7 = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 180天内日期
    Days180 = calcnDate(ToDay, -180)
    // 7天内日期
    Days7 = calcnDate(ToDay, -7)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val Days180TimeStamp = getTimeStampTake(Days180,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .appName("TargetTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    val childrenCsv = readCsv(spark,"儿童色情种子用户")
      .withColumn("uid",col("uid").cast("long"))
      .cache()
    val childrenBro = spark.sparkContext.broadcast(childrenCsv)
    childrenBro.value.createOrReplaceTempView("childrenCsv")

    //用户画像-策略特征
    val crccUserDf = getNonageData(spark,YesterDay)

    crccUserDf.createOrReplaceTempView("crccUserDf")

    // 读取贴吧用户关注明细数据
    val tiebaUserSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(followed_uid as long) as followed_uid
         |from
         |  ubs_tieba.tieba_dim_user_follow_detail_df
         |where
         |  event_day = '${YesterDay}'
         |""".stripMargin

    val tiebaUserDf = spark.sql(tiebaUserSql)
      .dropDuplicates()
      .repartition(200)
      .cache()
    println("贴吧用户关注明细维度表数据共：" + tiebaUserDf.count())

    // 读取贴吧180天活跃用户数据
    val tiebaSql =
      s"""
         |select
         |  cast(uid as long) as uid
         |from
         |  ubs_tieba.tieba_dim_pub_info_hi
         |where
         |  event_day <= '${YesterDay}'
         |  and event_day >= '${Days180}'
         |  and uid != '0'
         |group by uid
         |""".stripMargin

    val tiebaDf = spark.sql(tiebaSql)
      .repartition(30)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("贴吧180天活跃用户共：" + tiebaDf.count())
    //tiebaDf.show(10,false)
    tiebaDf.createOrReplaceTempView("tieba")

    //贴吧私信数据表
    val messageSql =
      s"""
        |with tmp_table as (
        |select
        |   uid,
        |   split(tag,':')[1] as send_uid
        |from udw_ns.default.tieba_ods_dump_im_message_info
        |where event_day <= '${YesterDay}'
        |and event_day >= '${Days180}'
        |and uid > 0
        |and tag != ''
        |)
        |select
        |   uid,
        |   cast(send_uid as long) as send_uid
        |from tmp_table
        |where uid != send_uid
        |and cast(send_uid as long) > 0
        |group by uid,send_uid
        |""".stripMargin

    val messageDf = spark.sql(messageSql)
      .repartition(200)
      .cache()
    println("贴吧私信数据为:" + messageDf.count())
    messageDf.createOrReplaceTempView("message_view")

    //统计关注列表中未成年人的数量
    val minorRatio = calRatio(spark,tiebaDf, tiebaUserDf, crccUserDf,"followed_uid","关注")
      .withColumnRenamed("minor_uid_ratio","tieba_follow_minor_ratio")
      .withColumn("source",lit("贴吧关注"))
    println("贴吧关注列表中未成年人的比例数量：" + minorRatio.count())
    //minorRatio.show(5,false)

    //统计被关注列表中未成年人的数量
    val minorUidRatio = calRatio(spark,tiebaDf, tiebaUserDf, crccUserDf,"followed_uid","被关注")
      .withColumnRenamed("minor_uid_ratio","tieba_fans_minor_ratio")
      .withColumn("source",lit("贴吧关注"))
    println("贴吧被关注列表中未成年人的比例数量：" + minorUidRatio.count())
    //minorUidRatio.show(false)

    //帖吧近180天私信对象中未成年人比例
    val messageRatio = calRatioUid(spark,messageDf,crccUserDf,"send_uid","私信")
      .withColumnRenamed("minor_uid_ratio","tieba_minor_private_ratio")
      .withColumn("source",lit("贴吧私信"))
    println("帖吧近180天私信对象中未成年人比例数量：" + messageRatio.count())

    //帖吧近180天私信儿童色情种子用户次数
    //  贴吧
    val tiebaChildrenDf = spark.sql(
      """
        |select
        |   tmp.uid,
        |   count(*) as tieba_child_porn_private_target_cnt
        |from
        |(  select
        |     T.uid,
        |     T.send_uid,
        |     B.uid as Buid
        |   from message_view T
        |   left join childrenCsv B
        |   on T.send_uid = B.uid
        |)tmp
        |where tmp.Buid != tmp.uid
        |group by tmp.uid
        |""".stripMargin)
      .withColumn("source",lit("贴吧私信"))
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("帖吧近180天私信儿童色情种子用户次数共：" + tiebaChildrenDf.count())

    // 帖吧近180天儿童色情种子用户私信次数
    val explodeTiebaDf = spark.sql(
        """
          |select
          |   tmp.send_uid as uid,
          |   count(*) as tieba_child_porn_private_user_cnt
          |from
          |(  select
          |     T.uid,
          |     T.send_uid,
          |     B.uid as Buid
          |   from message_view T
          |   left join childrenCsv B
          |   on T.uid = B.uid
          |)tmp
          |where tmp.Buid != tmp.send_uid
          |group by tmp.send_uid
          |""".stripMargin)
      .withColumn("source",lit("贴吧私信"))
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("帖吧近180天儿童色情种子用户私信次数共：" + explodeTiebaDf.count())

    val combinedDf = minorRatio.as("R")
      .join(minorUidRatio.as("U"), $"R.uid" === $"U.uid", "full_outer")
      .join(messageRatio.as("M"), $"R.uid" === $"M.uid", "full_outer")
      .join(tiebaChildrenDf.as("C"), $"R.uid" === $"C.uid", "full_outer")
      .join(explodeTiebaDf.as("E"), $"R.uid" === $"E.uid", "full_outer")
      .select(
        coalesce($"R.uid", $"U.uid", $"M.uid", $"C.uid", $"E.uid").cast("string").as("uid"),
        $"R.tieba_follow_minor_ratio".cast("float").as("tieba_follow_minor_ratio"),
        $"U.tieba_fans_minor_ratio".cast("float").as("tieba_fans_minor_ratio"),
        $"M.tieba_minor_private_ratio".cast("float").as("tieba_minor_private_ratio"),
        $"C.tieba_child_porn_private_target_cnt".cast("int").as("tieba_child_porn_private_target_cnt"),
        $"E.tieba_child_porn_private_user_cnt".cast("int").as("tieba_child_porn_private_user_cnt"),
        array($"R.source".as("source_R"), $"U.source".as("source_U")).as("sources_RU"),
        array($"M.source".as("source_M"), $"C.source".as("source_C"), $"E.source".as("source_E")).as("sources_MCE")
      )

    // 对source字段进行去重和拼接
    val resultDf = combinedDf
      .withColumn("sources_RU_distinct", array_distinct($"sources_RU"))
      .withColumn("sources_MCE_distinct", array_distinct($"sources_MCE"))
      .withColumn("source", concat_ws(",", $"sources_RU_distinct", $"sources_MCE_distinct"))
      .select(
        "uid",
        "tieba_follow_minor_ratio",
        "tieba_fans_minor_ratio",
        "tieba_minor_private_ratio",
        "tieba_child_porn_private_target_cnt",
        "tieba_child_porn_private_user_cnt",
        "source"
      )
      .na.fill(0.0, Seq("tieba_follow_minor_ratio", "tieba_fans_minor_ratio", "tieba_minor_private_ratio"))
      .na.fill(0, Seq("tieba_child_porn_private_target_cnt", "tieba_child_porn_private_user_cnt"))
      .repartition(5)

    // 输出结果
    println("贴吧数量：" + resultDf.count())

    //写入afs结果
    resultDf.write
      .mode("overwrite")
      .parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/p801/zhibiao/tieba_attention_ratio/${YesterDay}")

    tiebaDf.unpersist()
    tiebaDf.unpersist()

    //==============================================手百================================================

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(article_uid as long) as article_uid,
         |  cast(parent_uid as long) as parent_uid,
         |  cast(ts as long) as timestamp
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${Days180TimeStamp}
         |  and ts < ${YesterTimeStamp}
         |  and cast(uid as long) > 0
         |  and ownner_type = 0
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .repartition(200)
      .cache()

    println("手百数据共：" + bdhdDf.count())
    //bdhdDf.show(5,false)
    bdhdDf.createOrReplaceTempView("bdhd_view")

    //用户评论进行拆分去重
    val paretUidDf = spark.sql(
      """
        |with temp_table as
        |(select
        |     uid,
        |     concat_ws(',', COLLECT_SET(article_uid), COLLECT_SET(parent_uid)) as con_uid
        | from bdhd_view
        | group by uid
        | ),
        |explode_table as
        |(select
        |  uid,
        |  explode(split(con_uid,',')) as comment_uid
        |from temp_table
        |)
        |select
        | uid,
        | comment_uid,
        | count(*) as com_cnt
        |from explode_table
        |where comment_uid != '' and comment_uid != '0'
        |and uid != comment_uid
        |group by uid,comment_uid
        |""".stripMargin)
    paretUidDf.cache()
    println("手百评论用户拆分：" + paretUidDf.count())
    paretUidDf.createOrReplaceTempView("paretUidDf")

    //统计手百评论用户评论列表中未成年人的比例
    val bdhdMinorUidRatio = calRatioUid(spark,paretUidDf,crccUserDf,"comment_uid","评论")
      .withColumnRenamed("minor_uid_ratio","bdhd_minor_comment_target_ratio")
    println("手百评论用户评论列表中未成年人的比例数量：" + bdhdMinorUidRatio.count())

    //统计未成年人的数量

    //统计手百评论用户被评论列表中未成年人的比例
    val bdhdMinorArtRatio = calRatioUid(spark,paretUidDf,crccUserDf,"comment_uid","被评论")
      .withColumnRenamed("minor_uid_ratio","bdhd_minor_comment_receiver_ratio")
    println("手百评论用户被评论列表中未成年人的比例数量：" + bdhdMinorArtRatio.count())

    //==========================================评论儿童色情种子用户==============================================

    // 手百
    val bdhdChildrenDf = spark.sql(
      """
        |select
        |   tmp.uid,
        |   count(*) as bdhd_child_porn_comment_target_cnt
        |from
        |(  select
        |     T.uid,
        |     T.comment_uid,
        |     B.uid as Buid
        |   from paretUidDf T
        |   left join childrenCsv B
        |   on T.comment_uid = B.uid
        |)tmp
        |where tmp.Buid != tmp.uid
        |group by tmp.uid
        |""".stripMargin)

    println("评论儿童色情种子用户数据关联手百共：" + bdhdChildrenDf.count())

    // 手百
    val explodeBdhdDf = spark.sql(
      """
        |select
        |   tmp.comment_uid as uid,
        |   count(*) as bdhd_child_porn_comment_user_cnt
        |from
        |(  select
        |     T.uid,
        |     T.comment_uid,
        |     B.uid as Buid
        |   from paretUidDf T
        |   left join childrenCsv B
        |   on T.uid = B.uid
        |)tmp
        |where tmp.Buid != tmp.comment_uid
        |group by tmp.comment_uid
        |""".stripMargin)
      .repartition(400)

    println("儿童色情种子用户数据关联手百共：" + explodeBdhdDf.count())

    val bdhdResDf = bdhdMinorUidRatio.as("A")
      .join(bdhdMinorArtRatio.as("B"),$"A.uid" === $"B.uid","full_outer")
      .join(bdhdChildrenDf.as("C"),$"A.uid" === $"C.uid","full_outer")
      .join(explodeBdhdDf.as("D"),$"A.uid" === $"D.uid","full_outer")
      .select(
        coalesce($"A.uid", $"B.uid",$"C.uid",$"D.uid").cast("string").as("uid"), // 合并连接键"uid",
        $"bdhd_minor_comment_target_ratio".cast("float"),
        $"bdhd_minor_comment_receiver_ratio".cast("float"),
        $"bdhd_child_porn_comment_target_cnt".cast("int"),
        $"bdhd_child_porn_comment_user_cnt".cast("int"))
      .withColumn("source",lit("手百评论"))
      .na.fill(0,Seq("bdhd_child_porn_comment_target_cnt","bdhd_child_porn_comment_user_cnt"))
      .na.fill(0.0,Seq("bdhd_minor_comment_target_ratio","bdhd_minor_comment_receiver_ratio"))
      .repartition(5)

    println("手百评论用户未成年比例数据合并后共：" + bdhdResDf.count())

    //保存结果
    bdhdResDf.write
      .mode("overwrite")
      .parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/p801/zhibiao/bdhd_attention_ratio/${YesterDay}")

    spark.stop()
  }


  /**
   * 计算未成年比例
   * @param uidDf  用户维度表，比如贴吧180天活跃用户数据
   * @param UserDf 用户关注明细数据，比如贴吧用户关注明细维度表数据
   * @param crccUserDf 用户画像-策略特征数据，比如贴吧用户关注明细维度表数据
   * @param followUidCol 用户被关注uid字段名，统计被关注列表中未成年人的比例
   * @param dataType 统计类型，关注或者被关注，"关注" 或者 "被关注"，默认为“关注”
   * @return
   */
  def calRatio(spark:SparkSession,uidDf: DataFrame, UserDf: DataFrame, crccUserDf: DataFrame,followUidCol:String,dataType:String): DataFrame = {
    var RatioDf: DataFrame = null
    uidDf.createOrReplaceTempView("uidDf")
    UserDf.createOrReplaceTempView("UserDf")
    crccUserDf.createOrReplaceTempView("crccUserDf")
    if (dataType == "关注" || dataType == "私信"){

      //统计关注列表中未成年人的数量
      val minorUidCount = spark.sql(
        s"""
          |SELECT
          |   T.uid,
          |   COALESCE(COUNT(DISTINCT valid_follows.${followUidCol}), 0) AS minor_uid_count
          |FROM uidDf T
          |LEFT JOIN (
          |  SELECT
          |     A.uid,
          |     A.${followUidCol},
          |     COUNT(*) AS cnt  -- 仅用于触发聚合
          |  FROM UserDf A
          |  INNER JOIN (
          |    SELECT /*+ MAPJOIN(B) */
          |      uid
          |    FROM crccUserDf
          |  )crcc ON A.${followUidCol} = crcc.uid
          |  GROUP BY A.uid, A.${followUidCol}
          |) valid_follows ON T.uid = valid_follows.uid
          |GROUP BY T.uid
          |""".stripMargin)
        .repartition(400)
      minorUidCount.count()
      minorUidCount.createOrReplaceTempView("minorUidCount")

      // 计算总关注者数量
      val totalUidCount = spark.sql(
        s"""
          |SELECT
          |  T.uid,
          |  COALESCE(COUNT(DISTINCT A.${followUidCol}), 0) AS total_uid_count
          |FROM uidDf T
          |LEFT JOIN (
          |  SELECT
          |    uid,
          |    ${followUidCol}
          |  FROM UserDf
          |  GROUP BY uid, ${followUidCol}
          |) A ON T.uid = A.uid
          |GROUP BY T.uid
          |""".stripMargin)
        .repartition(400)
      totalUidCount.count()
      totalUidCount.createOrReplaceTempView("totalUidCount")

      //计算未成年人比例
      RatioDf = spark.sql(
          s"""
             |SELECT
             |  tfc.uid,
             |  COALESCE(CAST(mfc.minor_uid_count AS FLOAT) /
             |  NULLIF(  -- 当分母为0时返回NULL触发COALESCE
             |    GREATEST(tfc.total_uid_count, 0),  -- 处理负值
             |    0
             |  ), 0.0) AS minor_uid_ratio
             |FROM totalUidCount tfc
             |LEFT JOIN minorUidCount mfc ON tfc.uid = mfc.uid
             |""".stripMargin)
        .repartition(400)

    }else{
      //统计被关注列表中未成年人的数量
      val minorUidCount = spark.sql(
          s"""
             |SELECT
             |  T.uid,
             |  COALESCE(COUNT(DISTINCT valid_follows.uid), 0) AS minor_uid_count
             |FROM uidDf T
             |LEFT JOIN (
             |  -- 层级1：预聚合有效关注关系
             |  SELECT
             |    A.uid,
             |    A.${followUidCol},
             |    COUNT(*) AS cnt  -- 仅用于触发聚合
             |  FROM UserDf A
             |  INNER JOIN (
             |    SELECT /*+ MAPJOIN(B) */
             |      uid
             |    FROM crccUserDf
             |  )crcc ON A.uid = crcc.uid
             |  GROUP BY A.uid, A.${followUidCol}  -- 去重关注关系
             |) valid_follows ON T.uid = valid_follows.${followUidCol}
             |GROUP BY T.uid
             |""".stripMargin)
        .repartition(400)
      minorUidCount.count()
      minorUidCount.createOrReplaceTempView("minorUidCount")

      // 计算总关注者数量
      val totalUidCount = spark.sql(
          s"""
             |SELECT
             |  T.uid,
             |  COALESCE(COUNT(DISTINCT A.uid), 0) AS total_uid_count
             |FROM uidDf T
             |LEFT JOIN (
             |  SELECT
             |    uid,
             |    ${followUidCol}
             |  FROM UserDf
             |  GROUP BY uid, ${followUidCol}
             |) A ON T.uid = A.${followUidCol}
             |GROUP BY T.uid
             |""".stripMargin)
        .repartition(400)
      totalUidCount.count()
      totalUidCount.createOrReplaceTempView("totalUidCount")

      //计算未成年人比例
      RatioDf = spark.sql(
          s"""
             |SELECT
             |  tfc.uid,
             |  COALESCE(CAST(mfc.minor_uid_count AS FLOAT) /
             |  NULLIF(  -- 当分母为0时返回NULL触发COALESCE
             |    GREATEST(tfc.total_uid_count, 0),
             |    0
             |  ), 0.0) AS minor_uid_ratio
             |FROM totalUidCount tfc
             |LEFT JOIN minorUidCount mfc ON tfc.uid = mfc.uid
             |""".stripMargin)
        .repartition(400)
    }
    RatioDf
  }

  /**
   * 计算未成年比例
   * @param uidDf  用户维度表，比如贴吧180天活跃用户数据
   * @param crccUserDf 用户画像-策略特征数据，比如贴吧用户关注明细维度表数据
   * @param followUidCol 用户被关注uid字段名，统计被关注列表中未成年人的比例
   * @param dataType 统计类型，关注或者被关注，"关注" 或者 "被关注"，默认为“关注”
   * @return
   */
  def calRatioUid(spark:SparkSession,uidDf: DataFrame, crccUserDf: DataFrame,followUidCol:String,dataType:String): DataFrame = {
    var RatioDf: DataFrame = null
    uidDf.createOrReplaceTempView("uidDf")
    crccUserDf.createOrReplaceTempView("crccUserDf")
    if (dataType == "关注" || dataType == "私信" || dataType == "评论"){
      //统计未成年人的数量
      val minorUidCount = spark.sql(
          s"""
             |  SELECT
             |    A.uid,
             |    COALESCE(COUNT(DISTINCT A.${followUidCol}), 0) AS minor_uid_count
             |  FROM uidDf A
             |  INNER JOIN (
             |    SELECT /*+ MAPJOIN(B) */
             |      uid
             |    FROM crccUserDf
             |  )crcc ON A.${followUidCol} = crcc.uid
             |  WHERE A.uid != 0
             |  GROUP BY A.uid
             |""".stripMargin)
        .repartition(400)
      minorUidCount.createOrReplaceTempView("minorUidCount")

      // 计算总评论数量
      val totalUidCount = spark.sql(
          s"""
             |SELECT
             |  uid,
             |  COALESCE(COUNT(DISTINCT ${followUidCol}), 0) AS total_uid_count
             |FROM uidDf
             |WHERE uid != 0
             |GROUP BY uid
             |""".stripMargin)
        .repartition(400)
      totalUidCount.createOrReplaceTempView("totalUidCount")

      //计算未成年人比例
      RatioDf = spark.sql(
          s"""
             |SELECT
             |  tfc.uid,
             |  COALESCE(CAST(mfc.minor_uid_count AS FLOAT) /
             |  NULLIF(  -- 当分母为0时返回NULL触发COALESCE
             |    GREATEST(tfc.total_uid_count, 0),  -- 防御性处理负值
             |    0
             |  ), 0.0) AS minor_uid_ratio
             |FROM totalUidCount tfc
             |LEFT JOIN minorUidCount mfc ON tfc.uid = mfc.uid
             |""".stripMargin)
        .repartition(400)

    }else{
      //统计手百评论用户被评论列表中未成年人的比例
      val bdhdminorUidCount = spark.sql(
          s"""
             |  SELECT
             |    A.${followUidCol} as uid,
             |    COALESCE(COUNT(DISTINCT A.uid), 0) AS minor_uid_count
             |  FROM uidDf A
             |  INNER JOIN (
             |    SELECT /*+ MAPJOIN(B) */
             |      uid
             |    FROM crccUserDf
             |  )crcc ON A.uid = crcc.uid
             |  WHERE A.${followUidCol} != 0
             |  GROUP BY A.${followUidCol}
             |""".stripMargin)
        .repartition(400)
      bdhdminorUidCount.createOrReplaceTempView("bdhdminorUidCount")

      // 计算总关注者数量
      val bdhdtotalUidCount = spark.sql(
          s"""
             |SELECT
             |  ${followUidCol} as uid,
             |  COALESCE(COUNT(DISTINCT uid), 0) AS total_uid_count
             |FROM uidDf
             |WHERE ${followUidCol} != 0
             |GROUP BY ${followUidCol}
             |""".stripMargin)
        .na.fill(0,Seq("total_uid_count"))
        .repartition(400)
      bdhdtotalUidCount.createOrReplaceTempView("bdhdtotalUidCount")

      //计算未成年人比例
      RatioDf = spark.sql(
          s"""
             |SELECT
             |  tfc.uid,
             |  COALESCE(CAST(mfc.minor_uid_count AS FLOAT) /
             |  NULLIF(  -- 当分母为0时返回NULL触发COALESCE
             |    GREATEST(tfc.total_uid_count, 0),  -- 防御性处理负值
             |    0
             |  ), 0.0) AS minor_uid_ratio
             |FROM bdhdtotalUidCount tfc
             |LEFT JOIN bdhdminorUidCount mfc ON tfc.uid = mfc.uid
             |""".stripMargin)
        .repartition(400)
    }
    RatioDf
  }


  /**
   * 读取词表
   * @param spark
   * @param csvName
   * @return
   */
  def readCsv(spark:SparkSession,csvName:String): DataFrame = {
    // 读取词表
    val tieba_toupai =
      spark.read
        .option("encoding", "UTF-8") // 这里假设文件是GBK编码，根据实际情况修改
        .option("header", "true") // 如果CSV文件包含表头，则设置为true
        .option("inferSchema", "true")
        .csv(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/${csvName}.csv")
    println(s"读取${csvName}共：" + tieba_toupai.count())
    tieba_toupai.show(5,false)
    tieba_toupai
  }


  /**
   * 合并多个DataFrame并添加新列
   * @param source       数据来源
   * @param source_type 标签类型
   * @param sub_type    标签子类型
   * @param label_lv1  标签一级分类
   * @param label_lv2  标签二级分类
   * @param label_lv3  标签三级分类
   * @param score  分数/置信
   * @param dataframes
   * @return
   */
  def mergeAndAddColumns(source: String,
                         source_type: String,
                         sub_type: String,
                         label_lv1: String,
                         label_lv2: String,
                         label_lv3: String,
                         score: String,
                         dataframes: DataFrame*): DataFrame = {
    // 合并多个DataFrame
    var mergedDF = dataframes.reduce(_ unionByName _)

    //如果参数分数不为空，则添加分数列，否则不添加score列
    if(!score.equals("")){
      mergedDF = mergedDF.withColumn("score", lit(score).cast("float"))
    }

    // 添加新列
    val finalDF = mergedDF
      .withColumn("source", lit(source))
      .withColumn("type", lit(source_type))
      .withColumn("sub_type", lit(sub_type))
      .withColumn("label_lv1", lit(label_lv1))
      .withColumn("label_lv2", lit(label_lv2))
      .withColumn("label_lv3", lit(label_lv3))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .repartition(400)

    finalDF
  }
}

