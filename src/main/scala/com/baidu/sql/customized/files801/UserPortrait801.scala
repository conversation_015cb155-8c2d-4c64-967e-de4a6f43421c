package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{getNonageData, readCsv}
import com.baidu.sql.utils.CommonUtils.mysqlOperate
import com.baidu.sql.utils.PropertiesUtils
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeFormat.{DAY_FORMAT, DAY_FORMAT_MYSQL}
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils.getNullVal
import org.apache.spark.Partition
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel

import scala.util.Try
import scala.util.matching.Regex

/**
 *  @author: zhangrunjie 801风控用户画像表
 */
object UserPortrait801 {
  //  uid	str	用户UID
  //  source	str	画像来源（platform：画像策略平台；rule：人工独立规则；manual：手动添加；model：模型；table：数据表；other：其他; ...）
  //  sub_source	str	画像子来源，一般是规则编号、名称等信息
  //  product	str	适用业务线（all、baijiahao、shoubai、feed、tieba、unknown...）
  //  label_lv1	str	画像标签一级分类
  //  label_lv2	str	画像标签二级分类
  //  label_lv3	float	画像标签三级分类/置信度
  //  score	str	画像置信度，取值范围：0-100
  //  remark STRING COMMENT '备注说明信息' SAMPLE '画像责任人：xxx'
  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  var Day30 = ""
  var Day90 = ""

  //综合敏感一级垂类
  val allCategory = Array("社会", "历史", "军事", "房产", "国际", "法律", "时事").mkString("','")
  //综合敏感二级垂类
  val entertainmentCategory = Array("K12教育", "创业", "民生", "中国现代史", "政法", "中国军情", "房产综合", "国际社会", "互联网", "财经综合", "案件解读", "法律综合", "高等教育", "互联网金融", "教育资讯", "纪录片", "中国古代史", "中国近代史", "国际时政", "军事历史", "台海时事", "军人风采", "金融市场", "国内时政", "环球军事", "历史综合", "警匪犯罪",  "军旅战争", "军事综合", "武器装备","农民工", "购房", "宏观经济", "股票", "宗教","国际关系", "国际经济", "理财投资", "政策法规", "世界历史","航空航天", "法律条例", "养生活动", "剧情", "影视资讯", "悬疑", "其他", "爱情", "网络小说", "综合游戏","秀颜值", "秀身材", "动作冒险", "时尚潮流", "青春偶像", "日韩动漫", "人际关系", "喜剧", "健身", "两性", "家庭伦理", "都市职场", "都市", "动漫周边", "国产动漫", "性感热舞", "车模", "美容美体", "街拍", "古风", "秀恩爱", "宅舞", "风尚大片", "孕产", "时尚综合","舞蹈综合", "艺术舞蹈").mkString("','")
  //低俗敏感一级垂类
  val vulgarFirstCategory = Array("动漫", "舞蹈", "情景剧", "健康养生", "艺术", "随手拍", "时尚").mkString("','")
  //低俗敏感二级垂类
  val vulgarSecondCategory = Array("两性", "性感热舞", "健康常识", "风尚大片", "亲子综艺", "综艺综合", "明星真人秀", "健身", "流行编舞", "养生活动", "情感综艺", "秀恩爱", "欧美动漫", "秀颜值", "健康综合", "时尚潮流", "美发", "摄影综合", "动画短片", "养护科普", "摄影作品", "日韩动漫", "艺术舞蹈", "游泳", "化妆", "艺术综合", "车模", "国产动漫", "综合游戏", "动漫周边", "探店", "秀身材", "生活vlog", "孕产", "儿童时尚", "摄影教学", "美容美体", "街拍", "广场舞", "恋爱", "挑战综艺", "街头采访", "艺术产业", "时尚综合", "宅舞", "古风", "动画电影", "舞蹈综合", "相亲综艺", "母婴用品", "花样滑冰", "杂技", "舞蹈综艺", "时尚综艺", "文化综艺").mkString("','")
  //涉政敏感一级垂类
  val politicsFirstCategory = Array("文化", "时事", "生活", "社会", "三农", "历史", "军事", "教育", "国际", "房产", "法律", "财经").mkString("','")
  //涉政敏感二级垂类
  val politicsSecondCategory = Array("互联网", "国际社会", "K12教育", "政法", "创业", "财经综合", "案件解读", "中国现代史", "民生", "中国军情", "房产综合", "法律综合", "中国近代史", "环球军事", "家庭伦理", "股票", "宗教", "政策法规", "高等教育", "互联网金融", "农民生活", "纪录片", "中国古代史", "人际关系", "国际时政", "科技综合", "军事历史", "台海时事", "科学综合", "军人风采", "金融市场", "国内时政", "历史综合", "警匪犯罪", "军旅战争", "军事综合", "家庭教育", "购房", "宏观经济", "现象普法", "党建", "国际关系", "国际经济", "理财投资", "世界历史", "航空航天", "法律条例", "农民工", "灾难").mkString("','")

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    //转成YYYY-MM-dd格式
    val Day90Format = calcnDateFormat(YesterDay,-90)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 30天前日期 yyyyMMdd
    Day30 = calcnDate(YesterDay, -30)
    Day90 = calcnDate(YesterDay, -90)
    println("昨日格式化日期：" + Day90Format)
    println("昨日日期：" + YesterDay)
    println("30天前日期：" + Day30)
    println("90天前日期：" + Day90)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val Day90TimeStamp = getTimeStampTake(Day90,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-90时间戳：" + Day90TimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .appName("userPortrait801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    //===========================================画像标签表-文心-全量表==========================================
    var event_day = getLatestPartitionDate(spark,"udw_ns.default.crcc_dwd_portrait_userlabel_df").get
    println("画像标签表-文心-全量表最新分区：" + event_day)

    val crccSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  label,
         |  score,
         |  event_label
         |from
         |  udw_ns.default.crcc_dwd_portrait_userlabel_df
         |where
         |  event_day = '20250515'
         |  and cast(uid as long) > 0
         |  and event_label != 'red'
         |""".stripMargin

    val crccUserDf = spark.sql(crccSql)
      .dropDuplicates()
      .withColumn("source",lit("table"))
      .withColumn("sub_source",lit("crcc_dwd_portrait_userlabel_df"))
      .withColumn("product",when(col("label").contains("white_harmless"),lit("tieba")).otherwise(lit("unknown")))
      .withColumn("label_lv1",when(col("event_label") === "black",lit("黑"))
        .when(col("event_label") === "gray",lit("灰"))
        .when(col("event_label") === "white",lit("白"))
        .otherwise(lit("")))
      .withColumn("label_lv2",when(col("label").contains("political"),lit("涉政"))
        .when(col("label").contains("product"),lit("黑产"))
        .when(col("label").contains("illegal"),lit("违法违规"))
        .when(col("label").contains("white_harmless"),lit("白名单"))
        .otherwise(lit("")))
      .withColumn("label_lv3",when(col("label").contains("political"),lit("其它涉政"))
        .when(col("label").contains("product"),lit("其它黑产"))
        .when(col("label").contains("illegal"),lit("其它违法违规"))
        .when(col("label").contains("white_harmless"),lit("帖吧白名单"))
        .otherwise(lit("")))
      .withColumn("score",when(col("label").contains("white_harmless"),col("score")).otherwise(lit("80")).cast("float"))
      .withColumn("remark",lit("表迁移灌入"))
      .select("uid","source","sub_source","product","label_lv1","label_lv2","label_lv3","score","remark")
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("画像标签表-文心-全量数据共：" + crccUserDf.count())
    //crccUserDf.show(10,false)
    crccUserDf.createOrReplaceTempView("crcc_user_view")

    //===========================================用户画像-策略特征==========================================
    //event_day = getLatestPartitionDate(spark,"udw_ns.default.crcc_dwd_portrait_strategy_user_df").get
    //println("用户画像-策略特征表最新分区：" + event_day)

    var portraitUserDf = getNonageData(spark,YesterDay)

    portraitUserDf = portraitUserDf
        .withColumn("source",lit("table"))
        .withColumn("sub_source",lit("crcc_dwd_portrait_strategy_user_df"))
        .withColumn("product",lit("all"))
        .withColumn("label_lv1",lit("绿"))
        .withColumn("label_lv2",lit("未成年"))
        .withColumn("label_lv3",lit("基础未成年用户"))
        .withColumn("score",lit("80").cast("float"))
        .withColumn("remark",lit("表迁移灌入"))
        .select("uid","source","sub_source","product","label_lv1","label_lv2","label_lv3","score","remark")
        .repartition(10)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("用户画像-策略特征未成年人数据共：" + portraitUserDf.count())

    portraitUserDf.createOrReplaceTempView("portrait_user_view")

    //=============================================风控策略引擎数据================================================
    val engineSql =
      """
        |select
        |   pass_id
        |from engine_profile_user
        |""".stripMargin
    //val engineDf = mysqlOperatePartition(spark,PropertiesUtils.MysqlFengKongProperties,fengkongSql,"engine_profile_user_classification")
    val engineUserDf = mysqlOperate(spark,PropertiesUtils.MysqlFengKongProperties,engineSql)
      .groupBy("pass_id")
      .agg(count("*").alias("cnt"))
      .select("pass_id")
      .repartition(10)
      .cache()
    println("风控策略引擎用户数据共：" + engineUserDf.count())
    engineUserDf.createOrReplaceTempView("engine_profile_user")

    //读取风控策略引擎数据
    val fengkongSql =
      """
        |select
        |   id,
        |   pass_id,
        |   engine_profile_class_id
        |from engine_profile_user_classification
        |where is_delete = 0
        |""".stripMargin
    //val engineDf = mysqlOperatePartition(spark,PropertiesUtils.MysqlFengKongProperties,fengkongSql,"engine_profile_user_classification")
    val engineDf = mysqlOperate(spark,PropertiesUtils.MysqlFengKongProperties,fengkongSql)
      .repartition(10)
      .cache()
    println("风控策略引擎数据共：" + engineDf.count())
    engineDf.createOrReplaceTempView("user_classification")

    val classSql =
      """
        |select a.id as a_id,
        |a.`name` as class_type1,
        |b.id as b_id,
        |b.`name` as class_type2,
        |c.id as c_id,
        |c.`name` as class_type3
        |from engine_profile_classification a
        |left join engine_profile_classification b on a.id = b.parent_id
        |left join engine_profile_classification c on b.id = c.parent_id
        |where a.parent_id = 0
        |""".stripMargin

    val classDf = mysqlOperate(spark,PropertiesUtils.MysqlFengKongProperties,classSql)
      .cache()
    println("风控策略引擎分类数据共：" + classDf.count())
    //classDf.show(10,false)
    spark.sparkContext.broadcast(classDf).value.createOrReplaceTempView("profile_classification")

    val resLabelData = spark.sql(
      """
        |with tmp_classification as(
        |select
        |  a.pass_id,
        |  a.engine_profile_class_id,
        |  b.a_id,
        |  b.class_type1 as label_lv1,
        |  b.b_id,
        |  b.class_type2 as label_lv2,
        |  b.c_id,
        |  b.class_type3 as label_lv3
        |from user_classification a
        |inner join profile_classification b on a.engine_profile_class_id = b.a_id
        |or a.engine_profile_class_id = b.b_id
        |or a.engine_profile_class_id = b.c_id
        |)
        |select
        |  a.pass_id as uid,
        |  'manual' as source,
        |  '风控策略引擎' as sub_source,
        |  'all' as product,
        |  c.label_lv1,
        |  c.label_lv2,
        |  c.label_lv3,
        |  80.0 as score,
        |  '初始化灌入' as remark
        |from engine_profile_user a
        |inner join tmp_classification c on a.pass_id = c.pass_id
        |where c.engine_profile_class_id not in (1,62,63)
        |  """.stripMargin)
      .dropDuplicates()
      .select("uid","source","sub_source","product","label_lv1","label_lv2","label_lv3","score","remark")
      //mysql数据库里字符有问题，需要重新修改一下，此处复制的mysql的字符，最好不要进行改动！
      .withColumn("label_lv1",when(col("label_lv1") === "⿊","黑").when(col("label_lv1") === "⽩","白").otherwise(col("label_lv1")))
      .repartition(10)
      .cache()
    println("风控策略引擎数据共：" + resLabelData.count())
    resLabelData.createOrReplaceTempView("label_data")

    //=========================================百家号307白名单==================================
    val bjhBaseDf = spark.sql(
      s"""
        |select
        |    author_user_id as uid,
        |    status,
        |    nid,
        |    first_category, --一级垂类
        |    second_category, --二级垂类
        |    secure_not_pass_reason,
        |    author_audit_at,  -- 注册时间
        |    click_publish_time,
        |    author_status
        |  from
        |    bjh_data.bjh_feed_resource_rf
        |  where
        |    event_day = '${YesterDay}'
        |    and author_status not in ('freeze')
        |    and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${Day90TimeStamp} --90天以内
        |    and is_bjh = '1'
        |    and type3 in ('news', 'shortvideo', 'littlevideo', 'gallery', 'dt_image_text', 'dt_text', 'dt_ugc_video')
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("获取百家号白名单90天内数据量共：" + bjhBaseDf.count())
    bjhBaseDf.createOrReplaceTempView("bjh_base_view")

    val tmp_over_data90 = spark.sql(
        s"""
           |    select
           |      author_user_id as uid,
           |      author_status
           |    from
           |      bjh_data.bjh_feed_resource_rf
           |    where
           |      event_day = '${YesterDay}'
           |      and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${getTimeStampTake(calcnDate(YesterDay, -89),10)} --90天以上
           |      and author_status not in ('freeze')
           |      and is_bjh = '1'
           |      and type3 in ('news', 'shortvideo', 'littlevideo', 'gallery', 'dt_image_text', 'dt_text', 'dt_ugc_video')
           |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("90天以上历史发文量共：" + tmp_over_data90.count())
    tmp_over_data90.createOrReplaceTempView("tmp_over_data90")

    val other_over_data90 = spark.sql(
      """
        |select
        |    uid,
        |    count(*) as up_90_publish  -- 用户90天以上历史发文数
        |  from tmp_over_data90
        |  group by uid
        |  having count(*) >= 1
        |""".stripMargin)
      .cache()
    println("低俗涉政用户90天以上历史发文数共：" + other_over_data90.count())
    other_over_data90.createOrReplaceTempView("other_over_data90")

    //=========================================百家号307综合白名单==================================

    val tmp_data90 = spark.sql(
      s"""
        |with tmp_data as (
        |select
        |    uid,
        |    status,
        |    first_category, --一级垂类f
        |    second_category, --二级垂类
        |    secure_not_pass_reason,
        |    click_publish_time
        |  from bjh_base_view
        |  where author_status not in ('freeze','unpass')
        |),
        |secure_data as ( -- 敏感词过滤
        |    select
        |       uid
        |    from tmp_data
        |    where secure_not_pass_reason RLIKE '涉x有害|涉x类|高危反动|时政有害|时政类|历史虚无主义|违反法律法规|淫秽色情|虚假谣言|涉及未成年|恶意营销'
        |    group by uid
        |)
        |    select
        |      t.uid,
        |      t.status,
        |      t.first_category,
        |      t.second_category,
        |      t.secure_not_pass_reason,
        |      t.click_publish_time
        |    from tmp_data t
        |    left anti join secure_data s on t.uid = s.uid
        |    """.stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("90天以内历史发文量共：" + tmp_data90.count())
    //tmp_data90.filter(col("uid") === "6539135568").show(50, false)
    tmp_data90.createOrReplaceTempView("tmp_data90")

    val pass_reason = spark.sql(
      """
        |select
        |    uid,
        |    round(avg(case when status = 'publish' then 1.0 else 0.0 end), 2) as ratio_user_publish   --用户90天以内发文成功率
        |  from
        |    tmp_data90
        |  group by uid
        |  having count(*) >= 3
        |""".stripMargin)
    println("用户90天以内发文成功率共：" + pass_reason.count())
    //pass_reason.show(5,false)
    pass_reason.createOrReplaceTempView("pass_reason")

    val sensitive_no_table = spark.sql(
      s"""
        |   select
        |      uid,
        |      '0' as is_sensitive, -- 是否为敏感垂类
        |      round(avg(case when status = 'publish' then 1.0 else 0.0 end), 2) as user_publish   --用户90天以内总发文成功率
        |    from tmp_data90
        |    where first_category not in ('${allCategory}')
        |    and second_category not in ('${entertainmentCategory}')
        |    group by uid
        |""".stripMargin)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("近90天用户无敏感垂类数据共：" + sensitive_no_table.count())
    //sensitive_no_table.show(5,false)
    sensitive_no_table.createOrReplaceTempView("sensitive_no_table")

    val sensitive_table = spark.sql(
      s"""
         |   select
         |      uid,
         |      first_category,
         |      second_category,
         |      status
         |    from tmp_data90
         |    where first_category in ('${allCategory}')
         |    or second_category in ('${entertainmentCategory}')
         |""".stripMargin)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("近90天用户有敏感垂类数据共：" + sensitive_table.count())
    //sensitive_table.filter(col("uid") === "6539135568").show(20, false)
    sensitive_table.createOrReplaceTempView("sensitive_table")

    //敏感垂类数据占比大于90%的用户
    val sensitive_ratio_table = spark.sql(
      s"""
         | select
         |   uid,
         |   '1' as is_sensitive, -- 是否为敏感垂类
         |   count(*) as sensitive_count,  -- 敏感垂类发文数
         |   round(avg(case when status = 'publish' then 1.0 else 0.0 end), 2) as user_publish   --用户90天以内垂类发文总成功率
         | from sensitive_table
         | group by uid
         | having count(*) > 3
         |""".stripMargin)
    println("近90天用户敏感垂类数据共：" + sensitive_ratio_table.count())
    //sensitive_ratio_table.filter(col("uid") === "6539135568").show(10, false)
    sensitive_ratio_table.createOrReplaceTempView("sensitive_ratio_table")

    val bjhResDf = spark.sql(
        s"""
           |with sensitive_view as (
           |  select
           |    s.uid,
           |    s.is_sensitive,
           |    s.user_publish
           |  from sensitive_no_table s
           |  left join (select uid from sensitive_table group by uid) n on s.uid = n.uid -- 先从无敏感用户中过滤掉有过发文敏感的用户
           |  where n.uid is null
           |  union all
           |  select
           |    uid,
           |    is_sensitive,
           |    user_publish
           |  from sensitive_ratio_table
           |  where user_publish > 0.9 -- 垂类发文成功率大于90%的用户
           |),
           |tmp_inner as (
           |select
           |  t.uid,
           |  s.is_sensitive,  -- 是否为敏感垂类
           |  s.user_publish,  -- 用户90天以内敏感垂类发文成功率
           |  p.ratio_user_publish  -- 用户90天以内总发文成功率
           |from
           |( select
           |    uid,
           |    count(*) as up_90_publish  -- 用户90天以上历史发文数
           |  from tmp_over_data90
           |  where author_status not in ('freeze','unpass')
           |  group by uid
           |  having count(*) >= 1) t
           |inner join (select uid,ratio_user_publish from pass_reason where ratio_user_publish > 0.5) p on t.uid = p.uid
           |inner join (select uid,is_sensitive,user_publish from sensitive_view) s on t.uid = s.uid
           |),
           |tmp_score as (
           |select
           |  t.uid,
           |  t.is_sensitive,
           |  t.user_publish,
           |  t.ratio_user_publish,
           |  case when t.is_sensitive = '1' then
           |   t.user_publish * t.ratio_user_publish * 100.0
           |   else t.ratio_user_publish * 100.0 end as score
           | from tmp_inner t
           |),
           |userlabel_data as (
           |  select
           |     uid
           |  from udw_ns.default.help_ods_crcc_userlabel_data
           |  where event_day >= '${calcnDate(YesterDay, -180)}' and event_day <= '${YesterDay}' and type='违规处罚标签'
           |  and cast(uid as long) > 0
           |  union all
           |  select
           |     uid
           |  from udw_ns.default.help_ods_crcc_userlabel_data
           |  where event_day >= '${calcnDate(YesterDay, -30)}' and event_day <= '${YesterDay}' and label_lv3='百家号疑似账号变更'
           |  and cast(uid as long) > 0
           |  union all
           |  select
           |     uid
           |  from udw_ns.default.help_ods_crcc_userlabel_data
           |  where event_day >= '${calcnDate(YesterDay, -15)}' and event_day <= '${YesterDay}' and label_lv3='发文垂类突变敏感垂类'
           |  and cast(uid as long) > 0
           |)
           |select
           |  uid,
           |  'manual' as source,
           |  'baijiahao_307_0' as sub_source,
           |  'baijiahao' as product,
           |  '白' as label_lv1,
           |  '白名单' as label_lv2,
           |  '百家号白名单_综合' as label_lv3,
           |  score,
           |  '' as remark
           | from tmp_score t
           | left anti join (select uid from crcc_user_view where label_lv1 = '黑' and label_lv2 in ('涉政','违法违规')) c
           | on t.uid = c.uid
           | left anti join (select uid from userlabel_data group by uid) u on t.uid = u.uid
           |""".stripMargin)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK)
    println("百家号综合白名单最终结果共：" + bjhResDf.count())
    bjhResDf.createOrReplaceTempView("bjh_res_data")

    //=========================================百家号307低俗白名单==================================

    val vulgar_data90 = spark.sql(
        s"""
           |with secure_data as ( -- 敏感词过滤
           |    select
           |       uid
           |    from bjh_base_view
           |    where secure_not_pass_reason RLIKE '违反法律法规|淫秽色情|虚假谣言|涉及未成年|恶意营销'
           |    group by uid
           |)
           |    select
           |      t.uid,
           |      t.nid,
           |      t.status,
           |      t.first_category,
           |      t.second_category,
           |      t.secure_not_pass_reason,
           |      t.click_publish_time
           |    from bjh_base_view t
           |    left anti join secure_data s on t.uid = s.uid
           |    where t.author_audit_at IS NOT NULL          -- 注册时间不为空
           |    and DATEDIFF(CURRENT_DATE, t.author_audit_at) > 90  -- 注册天数>90天
           |    and t.first_category not in ('${vulgarFirstCategory}') -- 排除低俗垂类
           |    and t.second_category not in ('${vulgarSecondCategory}') -- 排除低俗垂类
           |    """.stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("低俗90天以内历史发文量共：" + vulgar_data90.count())
    //vulgar_data90.show(10, false)
    vulgar_data90.createOrReplaceTempView("vulgar_data90")

    val vulgar_pass_reason = spark.sql(
      """
        |select
        |    uid,
        |    round(avg(case when status = 'publish' then 1.0 else 0.0 end), 2) as ratio_user_publish   --用户90天以内总发文成功率
        |  from
        |    vulgar_data90
        |  group by uid
        |""".stripMargin)
    println("用户低俗90天以内发文成功率共：" + vulgar_pass_reason.count())
    //vulgar_pass_reason.show(5,false)
    vulgar_pass_reason.createOrReplaceTempView("vulgar_pass_reason")

    //过滤评论色情低俗敏感垂类
    /*val esdumpDf = spark.sql(
      s"""
        |with esdump_data as (
        |select
        |  nid
        |from
        |  udw_ns.default.bjh_ods_auditflow_esdump_di
        |   WHERE
        |    event_day = '${YesterDay}'
        |    AND label_tag REGEXP '格调低俗|颜值两性|封面低质|清凉着装|弱低俗|低格调|必选-封面低俗|低俗行为|隐私部位|辱骂脏话|不当婚恋观|两性封面不可用|淫秽色情|P1消费女性|低俗|涉及未成年|违反法律法规|竖图不可用|低俗-非真人|P2两性科普|P1两性事件|横图不可用|P4清凉着装|暧昧'
        |)
        |select
        |  v.uid
        |from vulgar_data90 v
        |left anti join esdump_data e on v.nid = e.nid
        |group by v.uid
        |""".stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("过滤评论低俗敏感垂类共：" + esdumpDf.count())
    esdumpDf.show(5,false)
    esdumpDf.createOrReplaceTempView("esdump_data")*/

    val bjhVulgarResDf = spark.sql(
        s"""
           |with tmp_inner as (
           |select distinct
           |  t.uid,
           |  p.ratio_user_publish * 100.0 as score  -- 用户90天以内总发文成功率
           |from vulgar_data90 t
           |inner join (select uid,ratio_user_publish from vulgar_pass_reason where ratio_user_publish > 0.9) p on t.uid = p.uid
           |-- inner join (select uid from esdump_data) s on t.uid = s.uid
           |inner join other_over_data90 d on t.uid = d.uid
           |),
           |userlabel_data as (
           |  select
           |     uid
           |  from udw_ns.default.help_ods_crcc_userlabel_data
           |  where event_day >= '${calcnDate(YesterDay, -90)}'
           |  and event_day <= '${YesterDay}'
           |  and type='违规处罚标签'
           |  and label_lv2 in ('色情','违法违规')
           |  and cast(uid as long) > 0
           |  group by uid
           |)
           |select
           |  t.uid,
           |  'manual' as source,
           |  'baijiahao_307_2' as sub_source,
           |  'baijiahao' as product,
           |  '白' as label_lv1,
           |  '白名单' as label_lv2,
           |  '百家号白名单_低俗' as label_lv3,
           |  t.score,
           |  '' as remark
           | from tmp_inner t
           | left anti join (select uid from crcc_user_view where label_lv1 = '黑' and label_lv2 = '违法违规' group by uid) c
           | on t.uid = c.uid
           | left anti join userlabel_data u on t.uid = u.uid
           |""".stripMargin)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK)
    println("百家号低俗白名单最终结果共：" + bjhVulgarResDf.count())
    bjhVulgarResDf.createOrReplaceTempView("bjh_vulgar_data")

    //=========================================百家号307涉政白名单==================================

    val politics_data90 = spark.sql(
        s"""
           |with secure_data as ( -- 敏感词过滤
           |    select
           |       uid
           |    from bjh_base_view
           |    where secure_not_pass_reason RLIKE '涉x有害|涉x类|高危反动|时政有害|时政类|历史虚无主义|违反法律法规|淫秽色情|虚假谣言|涉及未成年|恶意营销'
           |    group by uid
           |)
           |    select
           |      t.uid,
           |      t.nid,
           |      t.status,
           |      t.first_category,
           |      t.second_category,
           |      t.secure_not_pass_reason,
           |      t.click_publish_time
           |    from bjh_base_view t
           |    left anti join secure_data s on t.uid = s.uid
           |    where t.author_audit_at IS NOT NULL          -- 注册时间不为空
           |    and DATEDIFF(CURRENT_DATE, t.author_audit_at) > 90  -- 注册天数>90天
           |    and t.first_category not in ('${politicsFirstCategory}') -- 排除涉政垂类
           |    and t.second_category not in ('${politicsSecondCategory}') -- 排除涉政垂类
           |    """.stripMargin)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("涉政90天以内历史发文量共：" + politics_data90.count())
    //politics_data90.show(10, false)
    politics_data90.createOrReplaceTempView("politics_data90")

    val politics_pass_reason = spark.sql(
      """
        |select
        |    uid,
        |    round(avg(case when status = 'publish' then 1.0 else 0.0 end), 2) as ratio_user_publish   --用户90天以内总发文成功率
        |  from
        |    politics_data90
        |  group by uid
        |""".stripMargin)
    println("用户涉政90天以内发文成功率共：" + politics_pass_reason.count())
    //politics_pass_reason.show(5,false)
    politics_pass_reason.createOrReplaceTempView("politics_pass_reason")

    val bjhPoliticsResDf = spark.sql(
        s"""
           |with tmp_inner as (
           |select distinct
           |  t.uid,
           |  p.ratio_user_publish * 100.0 as score  -- 用户90天以内总发文成功率
           |from politics_data90 t
           |inner join (select uid,ratio_user_publish from politics_pass_reason where ratio_user_publish > 0.9) p on t.uid = p.uid
           |inner join other_over_data90 d on t.uid = d.uid
           |),
           |userlabel_data as (
           |  select
           |     uid
           |  from udw_ns.default.help_ods_crcc_userlabel_data
           |  where event_day >= '${calcnDate(YesterDay, -90)}'
           |  and event_day <= '${YesterDay}'
           |  and type='违规处罚标签'
           |  and label_lv2 = '涉政'
           |  and cast(uid as long) > 0
           |  group by uid
           |)
           |select
           |  t.uid,
           |  'manual' as source,
           |  'baijiahao_307_1' as sub_source,
           |  'baijiahao' as product,
           |  '白' as label_lv1,
           |  '白名单' as label_lv2,
           |  '百家号白名单_涉政' as label_lv3,
           |  t.score,
           |  '' as remark
           | from tmp_inner t
           | left anti join (select uid from crcc_user_view where label_lv1 = '黑' and label_lv2 = '涉政' group by uid) c
           | on t.uid = c.uid
           | left anti join userlabel_data u on t.uid = u.uid
           |""".stripMargin)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK)
    println("百家号涉政白名单最终结果共：" + bjhPoliticsResDf.count())
    bjhPoliticsResDf.createOrReplaceTempView("bjh_politics_data")

    //=========================================新增afs数据==================================
    val afsDf = spark.read.parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/801/portrait_operator_data/${YesterDay}")
      .filter(col("uid") =!= "null" && col("uid") > "0")
    println("afs的portrait_operator_data数据共：" + afsDf.count())
    afsDf.createOrReplaceTempView("afs_data")
    //=========================================用户关联表近一个月的数据==================================
    val relationSql =
      s"""
        |select
        | uid,
        | count(*) as cnt
        |from udw_ns.default.help_ods_crcc_user_relation
        |where event_day > '${Day30}'
        |and event_day <= '${YesterDay}'
        |and cast(uid as long) > 0
        |group by uid
        |""".stripMargin

    val relationDf = spark.sql(relationSql)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("用户关联表近一个月的数据共：" + relationDf.count())
    relationDf.createOrReplaceTempView("relation_data")

    val relationOtherDf = spark.sql(
      """
        |select
        |   a.uid,
        |   'rule' as source,
        |   'unhit_user_portrait' as sub_source,
        |   'all' as product,
        |   '灰' as label_lv1,
        |   '不确定⽤户' as label_lv2,
        |   '其它' as label_lv3,
        |   80.0 as score,
        |   '' as remark
        |from relation_data a
        |left anti join label_data b on a.uid = b.uid
        |left anti join portrait_user_view c on a.uid = c.uid
        |left anti join bjh_res_data d on a.uid = d.uid
        |left anti join bjh_vulgar_data v on a.uid = v.uid
        |left anti join bjh_politics_data p on a.uid = p.uid
        |left anti join crcc_user_view e on a.uid = e.uid
        |left anti join afs_data f on a.uid = f.uid
        |""".stripMargin)
      .repartition(50)
      .cache()
    println("未命中用户关联表数据共：" + relationOtherDf.count())

    var resDf = unionIfNotEmpty(crccUserDf,portraitUserDf)
    resDf = unionIfNotEmpty(resDf,resLabelData)
    resDf = unionIfNotEmpty(resDf,bjhResDf)
    resDf = unionIfNotEmpty(resDf,bjhVulgarResDf)
    resDf = unionIfNotEmpty(resDf,bjhPoliticsResDf)
    resDf = unionIfNotEmpty(resDf,afsDf)
    resDf = unionIfNotEmpty(resDf,relationOtherDf)
      .repartition(50)
    println("画像表全量数据共：" + resDf.count())

    resDf.createOrReplaceTempView("res_label_data")

    //画像标签表-文心-全量表
    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert overwrite table udw_ns.default.help_ods_crcc_user_portrait partition(event_day = ${YesterDay})
        |select distinct
        | uid,
        | source,
        | sub_source,
        | product,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score,
        | remark
        |from res_label_data
        |""".stripMargin)

    spark.stop()
  }


  /**
   * 获取画像标签表-文心-全量表
   * @param spark
   * @param yesterDay 查询的日期分区
   * @return
   */
  def getUserLabelData(spark:SparkSession,yesterDay:String): DataFrame = {
    //画像标签表-文心-全量表
    val crccSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  count(*) as black_cnt
         |from
         |  udw_ns.default.crcc_dwd_portrait_userlabel_df
         |where
         |  event_day = '${yesterDay}'
         |  and event_label = 'black'
         |  and cast(uid as long) > 0
         |  and label = 'black_political'
         |group by uid
         |""".stripMargin

    val crccUserDf = spark.sql(crccSql)
      //.repartition(20)
      //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("画像标签表-文心-全量数据共：" + crccUserDf.count())
    crccUserDf
  }

  /**
   * 获取百度App关注用户历史存量明细数据表
   * @param spark
   * @param yesterDay
   * @return
   */
  def getInsightData(spark:SparkSession,yesterDay:String): DataFrame = {
    //百度App关注用户历史存量明细数据表，有权限
    val insightSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(third_id as long) as third_id,
         |  count(*) as follow_cnt
         |from
         |  udw_ns.default.insight_baiduapp_follow_history
         |where
         |  event_day = '${yesterDay}'
         |  and ext['fake'] = '0'
         |  and login_status!='0'
         |  and type in ('media', 'ugc', 'celebrity')
         |  and cast(uid as long) > 0
         |  and cast(third_id as long) > 0
         |  and uid != third_id
         |group by uid,third_id
         |""".stripMargin

    val insightDf = spark.sql(insightSql)
      //.repartition(1000)
      //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("百度App关注用户历史存量明细数据表共：" + insightDf.count())
    insightDf
  }

  /**
   * 获取指定 Hive 表的最新分区日期。
   * @param spark
   * @param fullTableName 表名，例如 "udw_ns.default.crcc_dwd_portrait_userlabel_df"
   * @param partitionColumn 分区字段名，例如 "event_day"
   * @return
   */
  def getLatestPartitionDate(
                              spark: SparkSession,
                              fullTableName: String,
                              partitionColumn: String = "event_day" // 默认分区字段名为 'event_day'，可根据实际情况修改
                            ): Option[String] = {

    // 1. 构建用于 SQL 查询的完整表名，并对各部分进行引用 (quoting)，以防名称中包含特殊字符
    // 例如：将 "udw_ns.default.crcc_dwd_portrait_userlabel_df" 转换为 "`udw_ns`.`default`.`crcc_dwd_portrait_userlabel_df`"
    val quotedTableName = fullTableName.split("\\.").map(part => s"`$part`").mkString(".")

    // 2. 构建 SHOW PARTITIONS SQL 查询语句
    val showPartitionsSql = s"SHOW PARTITIONS $quotedTableName"

    // 3. 执行 SQL 查询获取分区列表 DataFrame
    // 使用 Try 包装，以捕获执行 SQL 时可能发生的异常 (例如：表不存在，表未分区)
    val partitionsDfTry = Try {
      spark.sql(showPartitionsSql)
    }

    val partitionsDf = partitionsDfTry match {
      case scala.util.Success(df) => df
      case scala.util.Failure(e) =>
        // SHOW PARTITIONS 查询失败通常意味着表不存在或不是分区表
        println(s"错误：执行 SQL 查询 '$showPartitionsSql' 失败。原因: ${e.getMessage}")
        // 检查错误信息，给出更具体的提示
        val lowerCaseError = e.getMessage.toLowerCase
        if (lowerCaseError.contains("table or view not found") || lowerCaseError.contains("no such table")) {
          println(s"提示：表 '$fullTableName' 不存在或名称错误。")
        } else if (lowerCaseError.contains("is not partitioned")) {
          println(s"提示：表 '$fullTableName' 不是一个分区表。")
        }
        return None
    }

    // 4. 检查 SHOW PARTITIONS 是否返回了结果（即是否有分区）
    // 检查 DataFrame 是否为空，避免在无分区时拉取整个结果集
    if (partitionsDf.limit(1).collect().isEmpty) {
      println(s"提示：表 '$fullTableName' 没有找到任何分区。")
      return None
    }

    // 5. 从 DataFrame 中提取分区字符串，解析并找到指定分区列的最新值
    // SHOW PARTITIONS 返回的 DataFrame 包含一列，通常名为 "partition"，其值为分区路径字符串
    // 例如：对于 dt=2023-10-26/hour=10 的分区，这一列的值就是 "dt=2023-10-26/hour=10"

    // 构建正则表达式，用于从 "column=value" 格式的字符串中提取 value
    val partitionValueRegex: Regex = s"^$partitionColumn=(.*)".r // 匹配以 partitionColumn= 开头的部分并捕获值

    val latestPartitionValue: Option[String] = Try {
      partitionsDf.collect() // 将所有分区字符串拉取到驱动端
        .map(_.getString(0)) // 从 DataFrame 的第一列（即分区字符串列）获取 String 值
        .flatMap { partitionString => // partitionString 示例: "dt=2023-10-26/hour=10"
          // Spark 的 SHOW PARTITIONS 结果中，如果存在多个分区列，它们会用 '/' 分隔
          // 我们需要找到包含目标分区列的部分
          partitionString.split("/").find(_.startsWith(s"$partitionColumn="))
        }
        .flatMap { part => // part 示例: "dt=2023-10-26"
          // 使用正则表达式从 "column=value" 格式中提取 value
          part match {
            case partitionValueRegex(value) => Some(value)
            case _ => None // 如果正则表达式不匹配，则提取失败
          }
        }
        .max // 找到所有提取到的值中的最大值 (字符串比较适用于 YYYY-MM-DD 格式)
    }.toOption // 将 Try[String] 转换为 Option[String]

    latestPartitionValue match {
      case Some(date) =>
        // 成功找到并提取到最新分区列值
        Some(date)
      case None =>
        // 如果 latestPartitionValue 是 None，说明在 collect() 到的分区字符串中
        // 没有找到符合指定 partitionColumn=value 格式的部分，或者提取值失败
        println(s"错误：未能从表 '$fullTableName' 的分区列表中成功提取到指定分区列 '$partitionColumn' 的值。")
        // 打印一个示例分区字符串，帮助用户检查格式
        val firstPartitionString = partitionsDf.select("partition").collect().headOption.map(_.getString(0)).getOrElse("N/A")
        println(s"提示：请检查表的分区格式以及 partitionColumn 参数 '$partitionColumn' 是否正确。示例分区字符串: $firstPartitionString")
        None
    }
  }

}

