package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{getInsightData, getNonageData}
import com.baidu.sql.customized.files801.Target801.calRatio
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel
import org.apache.spark.sql.functions._


object TargetBdhd801 {

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 180天内日期
  var Days180 = ""

  def main(args: Array[String]): Unit = {

    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 180天内日期
    Days180 = calcnDate(ToDay, -180)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val Days180TimeStamp = getTimeStampTake(Days180,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .appName("TargetTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${Days180TimeStamp}
         |  and ts < ${YesterTimeStamp}
         |  and uid != 0
         |  and ownner_type = 0
         |group by uid
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .repartition(1000)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("手百数据共：" + bdhdDf.count())
    //bdhdDf.show(5,false)
    bdhdDf.createOrReplaceTempView("bdhd_view")

    //百度App关注用户历史存量明细数据表，有权限
    val insightDf = getInsightData(spark,YesterDay)

    println("百度App关注用户历史存量明细数据表共：" + insightDf.count())
    insightDf.createOrReplaceTempView("insight_view")

    //用户画像-策略特征
    val crccUserBroad = getNonageData(spark,YesterDay)

    val minorUidCount = bdhdDf.as("A")
      .join(broadcast(insightDf).as("B"), $"A.uid" === $"B.uid","left_outer")
      .join(broadcast(crccUserBroad).as("C"), $"B.third_id" === $"C.uid","inner")
      .groupBy($"A.uid")
      .agg(coalesce(countDistinct($"B.third_id"),lit(0)).as("minor_uid_count"))
      .repartition(400)
    minorUidCount.createOrReplaceTempView("minorUidCount")

    val totalUidCount = bdhdDf.as("A")
      .join(broadcast(insightDf).as("B"), $"A.uid" === $"B.uid","left_outer")
      .groupBy($"A.uid")
      .agg(coalesce(countDistinct($"B.third_id"),lit(0)).as("total_uid_count"))
      .repartition(400)
    totalUidCount.createOrReplaceTempView("totalUidCount")

    bdhdDf.createOrReplaceTempView("uidDf")
    insightDf.createOrReplaceTempView("UserDf")
    crccUserBroad.createOrReplaceTempView("crccUserDf")

    //统计关注列表中未成年人的数量
    /*val minorUidCount = spark.sql(
        s"""
           |SELECT
           |   T.uid,
           |   COALESCE(COUNT(DISTINCT valid_follows.third_id), 0) AS minor_uid_count
           |FROM uidDf T
           |LEFT JOIN (
           |  SELECT
           |     A.uid,
           |     A.third_id,
           |     COUNT(*) AS cnt  -- 仅用于触发聚合
           |  FROM UserDf A
           |  INNER JOIN (
           |    SELECT /*+ MAPJOIN(B) */
           |      uid
           |    FROM crccUserDf
           |  )crcc ON A.third_id = crcc.uid
           |  GROUP BY A.uid, A.third_id
           |) valid_follows ON T.uid = valid_follows.uid
           |GROUP BY T.uid
           |""".stripMargin)
      .repartition(400)
    minorUidCount.count()
    minorUidCount.persist(StorageLevel.MEMORY_AND_DISK_SER)
    minorUidCount.createOrReplaceTempView("minorUidCount")*/

    // 计算总关注者数量
    /*val totalUidCount = spark.sql(
        s"""
           |SELECT
           |  T.uid,
           |  COALESCE(COUNT(DISTINCT A.third_id), 0) AS total_uid_count
           |FROM uidDf T
           |LEFT JOIN (
           |  SELECT
           |    uid,
           |    third_id
           |  FROM UserDf
           |  GROUP BY uid, third_id
           |) A ON T.uid = A.uid
           |GROUP BY T.uid
           |""".stripMargin)
      .repartition(400)
    totalUidCount.count()
    totalUidCount.persist(StorageLevel.MEMORY_AND_DISK_SER)
    totalUidCount.createOrReplaceTempView("totalUidCount")*/

    //计算未成年人比例
    val shoubaiMinorRatio = spark.sql(
        s"""
           |SELECT
           |  tfc.uid,
           |  COALESCE(CAST(mfc.minor_uid_count AS FLOAT) /
           |  NULLIF(  -- 当分母为0时返回NULL触发COALESCE
           |    GREATEST(tfc.total_uid_count, 0),  -- 处理负值
           |    0
           |  ), 0.0) AS minor_uid_ratio
           |FROM totalUidCount tfc
           |LEFT JOIN minorUidCount mfc ON tfc.uid = mfc.uid
           |""".stripMargin)
      .repartition(400)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    
    println("手百用户关注列表中未成年人的比例数量：" + shoubaiMinorRatio.count())
    shoubaiMinorRatio.show(5,false)
    //minorUidCount.unpersist()
    //totalUidCount.unpersist()

    //统计手百用户被关注列表中未成年人的比例
    val shoubaiMinorUidRatio = calRatio(spark,bdhdDf, insightDf, crccUserBroad,"third_id","被关注")
      .withColumnRenamed("minor_uid_ratio","bdhd_app_fans_minor_ratio")
    println("手百用户被关注列表中未成年人的比例数量：" + shoubaiMinorUidRatio.count())
    shoubaiMinorUidRatio.show(5,false)

    bdhdDf.unpersist()
    insightDf.unpersist()

    spark.stop()
  }

}
