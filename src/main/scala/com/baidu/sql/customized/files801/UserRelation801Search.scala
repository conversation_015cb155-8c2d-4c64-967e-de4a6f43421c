package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.UserRelation801.getSqlData
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._


object UserRelation801Search {
  //`uid` STRING COMMENT '用户UID' SAMPLE '423435454',
  //`type` STRING COMMENT '关联类型' SAMPLE 'IP、CUID',
  //`value` STRING COMMENT '关联值' SAMPLE '*******',
  //`source` STRING COMMENT '数据来源表' SAMPLE 'bjh_feed_resource_rf',
  //`properties` STRING COMMENT '城市/设备数据值' SAMPLE '{"device": "apple"}'

  // 昨日日期
  var YesterDay = ""
  // 昨日计算日期 yyyy-MM-dd
  var YesterClacDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 今日计算日期 yyyy-MM-dd
  var ToClacDay = ""
  //回环IP（127.0.0.1）、无意义IP（0.0.0.0、***************、空）
  val ipList = List("127.0.0.1","0.0.0.0","***************","")

  /**
   * 用户关联表
   * @param args
   */
  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 昨日计算日期 yyyy-MM-dd
    YesterClacDay = calcnDateFormat(YesterDay)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 今日计算日期 yyyy-MM-dd
    ToClacDay = calcnDateFormat(ToDay)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("userRelation801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    // 读取search_pc_resultpage_query_click数据，分区36T左右
    val searchWiseSql =
      s"""
         |select
         |    uid,
         |    ip,
         |    cuid,
         |    os_name as os,
         |    brand,
         |    device,
         |    country,
         |    province,
         |    city
         |from
         |    things_turing_ns.ubs_search.view_search_wise_resultpage_query_click_crcdata
         |where
         |  event_day = '${YesterDay}'
         |  and log_source in ('se','tabse_se','secraft_se')
         |  and uid > 0
         |  and (
         |    ip not in ('127.0.0.1','0.0.0.0','***************','')
         |    or cuid != ''
         |  )
         |""".stripMargin

    println("开始读取search_wise_resultpage_query_click_crcdata表")
    var searchWiseResDf = getSqlData(spark,searchWiseSql,"IP,CUID")
    if(searchWiseResDf.count() > 0){
      searchWiseResDf = searchWiseResDf
        .withColumn("source",lit("search_wise_resultpage_query_click"))
        .repartition(10)
    }else{
      println("search_wise_resultpage_query_click表为空")
      sys.exit(1)
    }

    searchWiseResDf.createOrReplaceTempView("search_wise_data")

    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
         |select
         |   uid,
         |   type,
         |   value,
         |   source,
         |   properties
         |from search_wise_data
         |""".stripMargin)

    spark.stop()
  }
}

