package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 *  @author: zhangrunjie 801风控用户标签表，搜索相关标签
 */
object Label801Search {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""

  //搜索、特征词表
  val labelList = Seq(
    ("灰","涉政","涉政敏感"),
    ("灰","黑产","黑产相关"),
    ("灰","其它lv3","离婚相关"),
    ("灰","其它lv3","失业相关"),
    ("灰","其它lv3","失信被执行相关"),
    ("灰","其它lv3","诉讼/败诉相关"),
    ("灰","其它lv3","借贷相关"),
    ("灰","其它lv3","犯罪记录相关"),
    ("灰","其它lv3","破产相关"),
    ("绿","未成年","未成年作弊"),
    ("绿","未成年","未成年游戏"),
    ("绿","未成年","未成年人喜好内容"),
    ("灰","未成年色情","儿童色情相关"),
  )

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 数据源Wise,Pc
    val datasource = args(1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .appName("labelTable801search")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    import spark.implicits._

    labelList.toDF("label_lv1","label_lv2","label_lv3")
      .createOrReplaceTempView("label_list_view")

    //搜索、特征词表数据读取
    val searchFeature = readCsv(spark,"搜索、输入法特征词表")
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    // 疆藏敏感词表与搜索数据关联
    val jiangzangCsv = readCsv(spark,"福临疆藏敏感词")
      .unionByName(readCsv(spark,"自建疆藏敏感词"))
      .unionByName(readCsv(spark,"贴吧疆藏敏感词"))

    //多词匹配
    val multiwords = jiangzangCsv.filter(col("match_type").contains("多词"))
      .withColumn("voc_list",split(col("content"),"&"))
    .persist(StorageLevel.MEMORY_AND_DISK_SER)  // 缓存多词匹配表

    //单词匹配
    val singlewords = jiangzangCsv.filter(col("match_type").contains("单词"))
    .persist(StorageLevel.MEMORY_AND_DISK_SER)  // 缓存单词匹配表

    //多词匹配
    val multiSearchwords = searchFeature.filter(col("match_type") === "组合匹配")
      .withColumn("voc_list",split(col("content"),"&&"))
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    // 使用broadcast join优化小表join
    val searchBroad = spark.sparkContext.broadcast(searchFeature.filter(col("match_type").contains("包含匹配")))
    val searchBroadMulite = spark.sparkContext.broadcast(multiSearchwords)
    val multiBroad = spark.sparkContext.broadcast(multiwords)
    val singleBroad = spark.sparkContext.broadcast(singlewords)

    // 定义连接条件：检查 query 是否包含 voc_list 中的每一个元素
    val condition = expr("forall(T.voc_list, x -> instr(S.query, x) > 0)")
    //结果数据集
    var searchResDf: DataFrame = spark.emptyDataFrame

    if (datasource == "wise") {
      // 读取search_pc_resultpage_query_click数据，分区36T左右
      val searchWiseSql =
        s"""
           |select
           |    uid,
           |    original_query as query
           |from
           |    things_turing_ns.ubs_search.view_search_wise_resultpage_query_click_crcdata
           |where
           |  event_day = '${YesterDay}'
           |  and log_source in ('se','tabse_se','secraft_se')
           |  and uid > 0
           |  and original_query != ''
           |""".stripMargin

      val searchWiseDf =  spark.sql(searchWiseSql)
      println("searchWiseDf count:" + searchWiseDf.count())
      searchWiseDf.createOrReplaceTempView("searchWise")

      //搜索特征内容双词匹配
      val searchFeatureDouWiseDf = searchWiseDf.as("S").join(
          searchBroadMulite.value.as("T"),
          condition,"inner"
        )
        .drop("voc_list")

      //搜索特征内容单词匹配
      val searchFeatureWiseSingleDf = searchWiseDf.as("S").join(
        searchBroad.value.as("T"),
        col("S.query") contains col("T.content")
      )

      val searchFeatureWiseDf = searchFeatureDouWiseDf.unionByName(searchFeatureWiseSingleDf)
        .withColumn("source",lit("搜索"))
        .withColumn("type",lit("日常行为"))
        .withColumn("sub_type",lit("搜索特征"))
        .withColumn("score",lit("80"))
        .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .dropDuplicates()
        .repartition(10)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("searchFeatureWiseDf count:" + searchFeatureWiseDf.count())

      //内容 单词匹配
      val contentWiseDf = searchWiseDf.as("S").join(
        singleBroad.value.as("T"),
        col("S.query") contains col("T.content")
      )

      //内容 双词匹配
      val contentDouWiseDf = searchWiseDf.as("S").join(
          multiBroad.value.as("T"),
          condition,"inner"
        )
        .drop("voc_list")

      searchResDf = mergeAndAddColumns("搜索","日常行为","搜索特征","灰","涉政","疆藏涉政敏感内容","80",contentWiseDf,contentDouWiseDf)
        .unionByName(searchFeatureWiseDf)
        .repartition(10)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

    }else if (datasource == "pc") {
      // 读取search_pc_resultpage_query_click数据,分区大约4.3T
      val searchPcSql =
        s"""
           |select
           |    event_userid as uid,
           |    query
           |from
           |    ubs_search.search_pc_resultpage_query_click
           |where
           |  event_day = '${YesterDay}'
           |  and log_source = 'se'
           |  and event_userid > 0
           |  and query != ''
           |""".stripMargin

      val searchPcDf =  spark.sql(searchPcSql)
      println("searchPcDf count:" + searchPcDf.count())
      searchPcDf.createOrReplaceTempView("searchPc")

      //搜索特征内容双词匹配
      val searchFeatureDouPcDf = searchPcDf.as("S").join(
          searchBroadMulite.value.as("T"),
          condition,"inner"
        )
        .drop("voc_list")

      //搜索特征内容单词匹配
      val searchFeaturePcSingleDf = searchPcDf.as("S").join(
        searchBroad.value.as("T"),
        col("S.query") contains col("T.content")
      )

      val searchFeaturePcDf = searchFeatureDouPcDf.unionByName(searchFeaturePcSingleDf)
        .withColumn("source",lit("搜索"))
        .withColumn("type",lit("日常行为"))
        .withColumn("sub_type",lit("搜索特征"))
        .withColumn("score",lit("80"))
        .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .dropDuplicates()
        .repartition(10)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("searchFeaturePcDf count:" + searchFeaturePcDf.count())

      //内容 单词匹配
      val contentPcDf = searchPcDf.as("S").join(
        singleBroad.value.as("T"),
        col("S.query") contains col("T.content")
      )

      //内容 双词匹配
      val contentDouPcDf = searchPcDf.as("S")
        .join(
          multiBroad.value.as("T"),
          condition,"inner"
        )
        .drop("voc_list")

      searchResDf = mergeAndAddColumns("搜索","日常行为","搜索特征","灰","涉政","疆藏涉政敏感内容","80",contentPcDf,contentDouPcDf)
        .unionByName(searchFeaturePcDf)
        .repartition(10)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

      searchPcDf.unpersist()
    }

    // 合并结果
    val resDf = searchResDf
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .withColumn("score",col("score").cast("float"))
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("最终写入数据共" + resDf.count())
    resDf.createOrReplaceTempView("res_label_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
         |select
         | uid,
         | source,
         | type,
         | sub_type,
         | label_lv1,
         | label_lv2,
         | label_lv3,
         | score
         |from res_label_data
         |""".stripMargin)
    resDf.unpersist()

    spark.stop()
  }
}

