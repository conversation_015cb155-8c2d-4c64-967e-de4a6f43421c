package com.baidu.sql.customized.files801

import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.SparkSession

object UserRelation801OverWrite {
  //`uid` STRING COMMENT '用户UID' SAMPLE '423435454',
  //`type` STRING COMMENT '关联类型' SAMPLE 'IP、CUID',
  //`value` STRING COMMENT '关联值' SAMPLE '*******',
  //`source` STRING COMMENT '数据来源表' SAMPLE 'bjh_feed_resource_rf',
  //`properties` STRING COMMENT '城市/设备数据值' SAMPLE '{"device": "apple"}'

  // 昨日日期
  var YesterDay = ""
  // 昨日计算日期 yyyy-MM-dd
  var YesterClacDay = ""
  // 今日日期 yyyyMMdd
  var BeforeDay = ""

  /**
   * 用户关联表
   * @param args
   */
  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 昨日计算日期 yyyy-MM-dd
    YesterClacDay = calcnDateFormat(YesterDay)
    // 今日日期 yyyyMMdd
    BeforeDay = calcnDate(YesterDay, -1)

    println(s"开始覆盖写入${YesterDay}用户关联表")

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer","org.apache.spark.serializer.KryoSerializer")
      .config("spark.kryoserializer.buffer.max", "256m")
      .config("spark.sql.files.maxPartitionBytes", "512MB")
      .config("spark.sql.files.openCostInBytes", "4194304")
      .appName("userRelation801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val tmpDf = spark.sql(
      s"""
        |select
        |   uid,
        |   type,
        |   value,
        |   concat_ws(",",collect_set(source)) as source,
        |   concat_ws(",",collect_set(properties)) as properties
        |from udw_ns.default.help_ods_crcc_user_relation
        |where event_day = ${YesterDay}
        |group by uid,type,value
        |""".stripMargin)
    println(s"查询${YesterDay}用户关联表数据量为：" + tmpDf.count())
    tmpDf.createOrReplaceTempView("view_data")

    spark.sql(
      s"""
         |insert overwrite table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
         |select
         |   uid,
         |   type,
         |   value,
         |   source,
         |   properties
         |from view_data
         |""".stripMargin)

    println(s"覆盖写入${YesterDay}用户关联表成功}")

    spark.stop()
  }
}

