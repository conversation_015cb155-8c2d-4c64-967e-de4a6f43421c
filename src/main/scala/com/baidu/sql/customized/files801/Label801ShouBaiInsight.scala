package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{getInsightData, getNonageData, getTiebaMessage, getUserLabelData, mergeAndAddColumns, readCsv}
import com.baidu.sql.customized.files801.Target801.{calRatio, calRatioUid}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.storage.StorageLevel
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 *  @author: zhangrunjie 801风控用户标签表，手百评论关注数据标签
 */
object Label801ShouBaiInsight {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""

  //APP安装列表特征
  val labelList = Seq(
  ("灰","其它lv2","翻墙VPN软件"),
  ("灰","其它lv1","境外敏感社交软件"),
  ("灰","其它lv3","境外支付类软件"),
  ("灰","其它lv3","境外生活类软件"),
  ("灰","其它lv3","境外常用软件"),
  ("灰","其它lv3","虚拟货币类软件"),
  ("灰","其它lv3","律师常用APP"),
  ("红","其它lv3","政务APP"),
  ("灰","其它lv3","出境/境外人员相关软件"),
  ("灰","其它lv3","警务APP"),
  ("灰","其它lv3","借贷类APP"),
  ("绿","未成年","K12教育类软件"),
  ("绿","未成年","早教类APP"),
  ("绿","未成年","儿童手表相关"),
  ("灰","未成年色情","儿童色情APP"),
  ("灰","色情","色情类APP"),
  ("灰","色情","偷拍类APP"),
  ("灰","其它lv2","博彩游戏类APP"),
  ("绿","未成年","未成年人游戏"),
  ("灰","黑产","网赚软件"),
  ("灰","其它lv3","外卖从业者软件"),
  ("灰","其它lv3","网约车从业者软件"),
  ("灰","其它lv3","股票类软件"),
  ("灰","其它lv3","外贸行业软件"),
  ("灰","其它lv3","考公软件"),
  ("灰","其它lv3","男同交友软件"),
  ("灰","其它lv3","约炮交友软件"),
  ("灰","其他lv3","台湾地区软件"),
  ("灰","其他lv2","邪教相关APP"),
  ("灰","其他lv3","境内网盘APP"),
  ("灰","其他lv3","境外网盘APP"),
  ("灰","其他lv3","成人常用APP"),
  ("灰","其他lv3","快递从业者软件"),
  ("灰","其他lv3","货车司机软件"),
  ("灰","其他lv3","台湾社交类APP"),
  ("灰","其他lv3","台湾新闻类APP"),
  ("灰","其他lv3","台湾报刊杂志类APP"),
  ("灰","其他lv3","台湾健康运动类APP"),
  ("灰","其他lv3","台湾娱乐类APP"),
  ("灰","其他lv3","台湾照片影片类APP"),
  ("灰","其他lv3","台湾地图类APP"),
  ("灰","其他lv3","台湾生活类APP"),
  ("灰","其他lv3","台湾医药类APP"),
  ("灰","其他lv3","台湾参考类APP"),
  ("灰","其他lv3","台湾工具类APP"),
  ("灰","其他lv3","台湾商业类APP"),
  ("灰","其他lv3","台湾教育类APP"),
  ("灰","其他lv3","台湾音乐类APP"),
  ("灰","其他lv3","台湾图像与设计类APP"),
  ("灰","其他lv3","台湾美食类APP"),
  ("灰","其他lv3","台湾旅游类APP"),
  ("灰","其他lv3","台湾财经类APP"),
  ("灰","其他lv3","台湾天气类APP"),
  ("灰","其他lv3","台湾书籍类APP"),
  ("灰","其他lv3","台湾购物类APP"),
  ("灰","其它lv3","ROOT改机软件")
  )

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    //0是获取所有数据，1是获取上半部分数据，2是获取下半部分数据
    val datatype = args(1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    // 判断是否为特殊日期（1号、10号、20号）,是则返回true，否则返回false
    val isAppLabelWrite = isSpecialDay(YesterDay)
    if (isAppLabelWrite) {
      println("当前日期是特殊日期（1/10/20号）")
      // 执行特殊日期的业务逻辑
    } else {
      println("当前日期不是特殊日期")
    }

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.kryoserializer.buffer.max","256m")
      .config("spark.serializer","org.apache.spark.serializer.KryoSerializer")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    import spark.implicits._

    // 读取贴吧数据
    val tiebaSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  t_uid,
         |  floor_reply_uid,
         |  floor_quote_post_uid
         |from
         |  ubs_tieba.tieba_dim_pub_info_hi
         |where
         |  event_day = '${YesterDay}'
         |  and cast(uid as long) > 0
         |""".stripMargin

    val tiebaDf = spark.sql(tiebaSql)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧数据共：" + tiebaDf.count())
    tiebaDf.createOrReplaceTempView("tieba")

    //贴吧回贴进行拆分去重
    val commUidDf = spark.sql(
      """
        |with temp_table as
        |(select
        |     uid,
        |     concat_ws(',', COLLECT_SET(t_uid), COLLECT_SET(floor_reply_uid), COLLECT_SET(floor_quote_post_uid)) as con_uid
        | from tieba
        | group by uid
        |),
        |explode_table as
        |(select
        |  uid,
        |  explode(split(con_uid,',')) as comment_uid
        |from temp_table
        |)
        |select
        | uid,
        | comment_uid,
        | count(*) as com_cnt
        |from explode_table
        |where comment_uid != '' and comment_uid != '0'
        |and uid != comment_uid
        |group by uid,comment_uid
        |""".stripMargin)
    commUidDf.persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧用户拆分：" + commUidDf.count())
    commUidDf.createOrReplaceTempView("commUidDf")

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(article_uid as long) as article_uid,
         |  cast(parent_uid as long) as parent_uid
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${YesterTimeStamp}
         |  and ts < ${ToDayTimeStamp}
         |  and cast(uid as long) > 0
         |  and ownner_type = 0
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("手百数据共：" + bdhdDf.count())
    bdhdDf.createOrReplaceTempView("bdhd_view")

    //用户评论进行拆分去重
    val paretUidDf = spark.sql(
        """
          |with temp_table as
          |(select
          |     uid,
          |     concat_ws(',', COLLECT_SET(article_uid), COLLECT_SET(parent_uid)) as con_uid
          | from bdhd_view
          | group by uid
          | ),
          |explode_table as
          |(select
          |  uid,
          |  explode(split(con_uid,',')) as comment_uid
          |from temp_table
          |)
          |select
          | uid,
          | comment_uid,
          | count(*) as com_cnt
          |from explode_table
          |where comment_uid != '' and comment_uid != '0'
          |and uid != comment_uid
          |group by uid,comment_uid
          |""".stripMargin)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("手百评论用户拆分：" + paretUidDf.count())
    paretUidDf.createOrReplaceTempView("paretUidDf")

    //读取贴吧用户关注明细维度表
    val tiebaDetailDf = spark.sql(
      s"""
         |select
         |   uid,
         |   followed_uid,
         |   count(*) as cnt
         |from ubs_tieba.tieba_dim_user_follow_detail_df
         |where event_day = '${YesterDay}'
         |and uid != followed_uid
         |group by uid,followed_uid
         |""".stripMargin)
    .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧用户关注详情数据共：" + tiebaDetailDf.count())
    tiebaDetailDf.createOrReplaceTempView("tiebaDetailDf")

    //========================================关注涉政黑用户=========================================
    //合并违规处罚标签数据，过滤涉政数据 用户标签数据
    val tiebaBlockTotalDf = spark.sql(
        s"""
           |select
           |   uid
           |from udw_ns.default.help_ods_crcc_userlabel_data
           |where event_day = '${YesterDay}'
           |and type = '违规处罚标签'
           |and label_lv1 = '黑'
           |and label_lv2 = '涉政'
           |""".stripMargin)
      .dropDuplicates()
    //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("涉政黑用户数：" + tiebaBlockTotalDf.count())

    //涉政黑用户词表
    val userLabelDf = getUserLabelData(spark,YesterDay)
    val totalUserBlockDf = userLabelDf
      .select("uid")
      .unionByName(tiebaBlockTotalDf)
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("涉政黑用户数：" + totalUserBlockDf.count())

    spark.sparkContext.broadcast(totalUserBlockDf).value.createOrReplaceTempView("user_label_view")

    if (datatype.equals("0") || datatype.equals("1")){
      //关注涉政黑用户 贴吧
      val tiebaShezhengBlockDf = spark.sql(
          """
            |select
            | T.uid
            |from tiebaDetailDf T
            |inner join user_label_view U
            |on T.followed_uid = U.uid
            |""".stripMargin)
        .dropDuplicates()

      val tiebaShezhengBlockResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","关注涉政黑用户","80",tiebaShezhengBlockDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //println("贴吧关注涉政黑用户数据：" + tiebaShezhengBlockResDf.count())

      //========================================评论与被评论涉政黑用户发布的资源=========================================

      //评论涉政黑用户发布的资源 手百
      val ShezhengBlockDf = spark.sql(
          """
            |select
            | T.uid
            |from paretUidDf T
            |inner join user_label_view U
            |on T.comment_uid = U.uid
            |""".stripMargin)
        .dropDuplicates()

      val shezhengBlockResDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","评论涉政黑用户发布的资源","80",ShezhengBlockDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //println("评论涉政黑用户发布的资源数据：" + shezhengBlockResDf.count())

      //发布的资源被涉政黑用户评论 手百
      val BlockShezhengDf = spark.sql(
          """
            |select
            | T.comment_uid as uid
            |from paretUidDf T
            |inner join user_label_view U
            |on T.uid = U.uid
            |""".stripMargin)
        .dropDuplicates()

      val BlockshezhengResDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","发布的资源被涉政黑用户评论","80",BlockShezhengDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //println("发布的资源被涉政黑用户评论数据：" + BlockshezhengResDf.count())

      //评论涉政黑用户发布的资源 贴吧
      val commentBlockDf =  spark.sql(
        """
          |select
          | T.uid
          |from commUidDf T
          |inner join user_label_view U
          |on T.comment_uid = U.uid
          |""".stripMargin)

      val commentBlockResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","评论涉政黑用户发布的资源","80",commentBlockDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //println("评论涉政黑用户发布的资源数据：" + commentBlockResDf.count())

      //发布的资源被涉政黑用户评论 贴吧
      val BlockcommentDf =  spark.sql(
        """
          |with tmp_table as (
          |select
          | case when T.floor_quote_post_uid > 0 and T.floor_reply_uid > 0
          | then concat('-',T.floor_quote_post_uid,T.floor_reply_uid)
          | when T.floor_reply_uid > 0 then T.floor_reply_uid
          | when T.floor_quote_post_uid > 0 then T.floor_quote_post_uid
          | else T.t_uid end as uid
          |from tieba T
          |inner join user_label_view U
          |on T.uid = U.uid
          |)
          |select
          | explode(split(uid,'-')) as uid
          |from tmp_table
          |""".stripMargin)

      val BlockcommentResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","发布的资源被涉政黑用户评论","80",BlockcommentDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

      //私信涉政黑用户
      val messageDf = getTiebaMessage(spark,YesterDay)
      val messageBroad = spark.sparkContext.broadcast(messageDf)
      messageBroad.value.createOrReplaceTempView("messageInfoDf")
      println("私信数据共：" + messageDf.count())

      val BlockMessageDf = spark.sql(
        """
          |select
          |  T.uid
          |from messageInfoDf T
          |inner join user_label_view U
          |on T.send_uid = U.uid
          |""".stripMargin)

      val BlockMessageResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","私信涉政黑用户","80",BlockMessageDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

      //涉政黑用户私信对象
      val BlockMessageSendDf = spark.sql(
        """
          |select
          |  T.send_uid as uid
          |from messageInfoDf T
          |inner join user_label_view U
          |on T.uid = U.uid
          |""".stripMargin)

      val BlockMessageSendResDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","涉政黑用户私信对象","80",BlockMessageSendDf)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      //================================================未成年人比例数据================================================
      //未成年人画像数据
      val crccDf = getNonageData(spark,YesterDay)
      val crccUserBroad = spark.sparkContext.broadcast(crccDf)
      crccUserBroad.value.createOrReplaceTempView("crccUserDf")

      //读取指标表的未成年人比例数据,和app_list
      val targtDf = spark.sql(
          s"""
             |select
             |  uid,
             |  tieba_minor_private_ratio,
             |  bdhd_minor_comment_target_ratio,
             |  app_list
             |from udw_ns.default.help_ods_crcc_usertarget_data
             |where event_day = ${YesterDay}
             |""".stripMargin)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("指标表的未成年人比例数据共：" + targtDf.count())
      targtDf.createOrReplaceTempView("applist_view")

      //未成年私信聚集 贴吧
      val tiebaMessageMinorUidCount = spark.sql(
        s"""
           |WITH
           |  -- 计算未成年用户
           |  B AS (
           |    SELECT
           |      A.uid,
           |      COALESCE(COUNT(A.send_uid), 0) AS minor_uid_count
           |    FROM messageInfoDf A
           |    INNER JOIN crccUserDf crcc
           |      ON A.uid = crcc.uid
           |    GROUP BY A.uid
           |  ),
           |  -- 未成年人比例大于70%
           |  T AS (
           |    SELECT
           |      uid,
           |      tieba_minor_private_ratio
           |    FROM applist_view
           |    WHERE tieba_minor_private_ratio > 0.7
           |  ),
           |  -- 互动对象大于5个
           |  V AS (
           |    SELECT
           |      uid,
           |      COUNT(*) AS com_cnt
           |    FROM messageInfoDf
           |    GROUP BY uid
           |    HAVING COUNT(distinct send_uid) > 5
           |  )
           |
           |SELECT
           |  B.uid,
           |  T.tieba_minor_private_ratio,
           |  V.com_cnt
           |FROM B
           |INNER JOIN T ON B.uid = T.uid
           |INNER JOIN V ON B.uid = V.uid
           |""".stripMargin)

      val tiebaMessageMinorUidDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","绿","未成年","未成年私信聚集","80",tiebaMessageMinorUidCount)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("贴吧未成年人私信聚集总数：" + tiebaMessageMinorUidDf.count())

      //未成年评论聚集 贴吧
      //统计贴吧被回帖中未成年人的数量
      val minorUidRatio = calRatioUid(spark,commUidDf, crccDf,"comment_uid","关注")
      println("贴吧被回帖中未成年人的比例数量：" + minorUidRatio.count())
      minorUidRatio.createOrReplaceTempView("minorUidRatioDf")

      val tiebaMinorUidCount = spark.sql(
        s"""
           |WITH
           |  -- 计算未成年用户
           |  B AS (
           |    SELECT
           |      A.uid,
           |      COALESCE(COUNT(A.comment_uid), 0) AS minor_uid_count
           |    FROM commUidDf A
           |    INNER JOIN crccUserDf crcc
           |      ON A.uid = crcc.uid
           |    GROUP BY A.uid
           |  ),
           |  -- 回帖中未成年人比例大于70%
           |  T AS (
           |    SELECT
           |      uid,
           |      minor_uid_ratio
           |    FROM minorUidRatioDf
           |    WHERE minor_uid_ratio > 0.7
           |  ),
           |  -- 互动对象大于5个
           |  V AS (
           |    SELECT
           |      uid,
           |      COUNT(*) AS com_cnt
           |    FROM commUidDf
           |    GROUP BY uid
           |    HAVING COUNT(distinct comment_uid) > 5
           |  )
           |
           |SELECT
           |  B.uid,
           |  T.minor_uid_ratio,
           |  V.com_cnt
           |FROM B
           |INNER JOIN T ON B.uid = T.uid
           |INNER JOIN V ON B.uid = V.uid
           |""".stripMargin)

      val tiebaMinorUidDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","绿","未成年","未成年评论聚集","80",tiebaMinorUidCount)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("贴吧未成年人评论聚集总数：" + tiebaMinorUidDf.count())
      //minorUidCount.show(5,false)

      //未成年评论聚集 手百
      val shoubaiMinorUidCount = spark.sql(
        s"""
           |WITH
           |  -- 计算未成年用户
           |  B AS (
           |    SELECT
           |      A.uid,
           |      COALESCE(COUNT(A.comment_uid), 0) AS minor_uid_count
           |    FROM paretUidDf A
           |    INNER JOIN crccUserDf crcc
           |      ON A.uid = crcc.uid
           |    GROUP BY A.uid
           |  ),
           |  -- 评论未成年人比例大于70%
           |  T AS (
           |    SELECT
           |      uid,
           |      bdhd_minor_comment_target_ratio
           |    FROM applist_view
           |    WHERE bdhd_minor_comment_target_ratio > 0.7
           |  ),
           |  -- 评论互动对象大于5个
           |  V AS (
           |    SELECT
           |      uid,
           |      COUNT(*) AS com_cnt
           |    FROM paretUidDf
           |    GROUP BY uid
           |    HAVING COUNT(distinct comment_uid) > 5
           |  )
           |
           |SELECT
           |  B.uid,
           |  T.bdhd_minor_comment_target_ratio,
           |  V.com_cnt
           |FROM B
           |INNER JOIN T ON B.uid = T.uid
           |INNER JOIN V ON B.uid = V.uid
           |""".stripMargin)

      val shoubaiMinorUidDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","绿","未成年","未成年评论聚集","80",shoubaiMinorUidCount)
        .repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("手百未成年人评论聚集总数：" + shoubaiMinorUidDf.count())

      //==============================================================APP_LIST标签匹配===============================================
      //满足特定日期后开始实现这部分逻辑
      var appListDf = spark.emptyDataFrame
      if (isAppLabelWrite){
        val app_lables = readCsv(spark,"APP安装列表特征")
          .persist(StorageLevel.MEMORY_AND_DISK_SER)
        println("APP安装列表特征：" + app_lables.count())
        spark.sparkContext.broadcast(app_lables).value.createOrReplaceTempView("app_lables_view")

        var userPortraitDf = spark.emptyDataFrame
        //获取全量画像表数据
        try{
          userPortraitDf = spark.read.parquet(s"afs://kunpeng.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian/user_portrait_strategy/${YesterDay}")
        } catch {
          case e: Exception =>
            println(s"[ERROR] 读取用户画像数据失败，日期分区：${YesterDay}，错误信息：${e.getMessage}")
            // 定义返回DF的schema（即使数据为空也保持相同结构）
            val resultSchema = StructType(Array(StructField("uid", LongType, nullable = false),StructField("app_list", StringType, nullable = true)))
            // 返回带schema的空DataFrame
            userPortraitDf = spark.createDataFrame(spark.sparkContext.emptyRDD[Row], resultSchema)
        }
        userPortraitDf.createOrReplaceTempView("user_portrait_view")

        labelList.toDF("label_lv1","label_lv2","label_lv3")
          .createOrReplaceTempView("label_list_view")
        appListDf = spark.sql(
            """
              |WITH exploded_apps AS (
              |   SELECT
              |     uid,
              |     trim(exploded_app) AS app_name  -- 添加trim处理空格
              |   FROM (
              |     SELECT distinct
              |       uid, explode(split(app_list, ' ')) AS exploded_app
              |     FROM user_portrait_view
              |     WHERE app_list IS NOT NULL  -- 过滤空值
              |   )
              | ),
              | matched_apps AS (
              |   SELECT
              |     e.uid,
              |     l.app_type
              |   FROM exploded_apps e
              |   JOIN app_lables_view l
              |     ON e.app_name = l.app_name
              |   GROUP BY e.uid, l.app_type  -- 提前去重
              | )
              | SELECT
              |   m.uid,
              |   l.label_lv1,
              |   l.label_lv2,
              |   l.label_lv3
              | FROM matched_apps m
              | JOIN label_list_view l
              |   ON m.app_type = l.label_lv3
              |""".stripMargin)
          .withColumn("source", lit("画像部委"))
          .withColumn("type", lit("日常行为"))
          .withColumn("sub_type", lit("APP安装列表特征"))
          .withColumn("score",lit("80").cast("float"))
          .repartition(50)
          .persist(StorageLevel.MEMORY_AND_DISK_SER)
        println("APP_LIST标签匹配：" + appListDf.count())
      }else{
        appListDf
      }

      var resDf = unionIfNotEmpty(tiebaShezhengBlockResDf,shezhengBlockResDf) //贴吧关注涉政黑用户数据,评论涉政黑用户发布的资源数据 手百
      resDf = unionIfNotEmpty(resDf,tiebaShezhengBlockResDf) //贴吧关注涉政黑用户数据
      resDf = unionIfNotEmpty(resDf,shezhengBlockResDf) //评论涉政黑用户发布的资源数据 手百
      resDf = unionIfNotEmpty(resDf,BlockshezhengResDf) //发布的资源被涉政黑用户评论数据 手百
      resDf = unionIfNotEmpty(resDf,commentBlockResDf) //评论涉政黑用户发布的资源数据 贴吧
      resDf = unionIfNotEmpty(resDf,BlockcommentResDf) //发布的资源被涉政黑用户评论 贴吧
      resDf = unionIfNotEmpty(resDf,BlockMessageResDf) //私信涉政黑用户 贴吧
      resDf = unionIfNotEmpty(resDf,BlockMessageSendResDf) //涉政黑用户私信对象 贴吧
      resDf = unionIfNotEmpty(resDf,appListDf)    //APP安装列表特征
      resDf = unionIfNotEmpty(resDf,tiebaMessageMinorUidDf)    //贴吧私信未成年人数据
      resDf = unionIfNotEmpty(resDf,tiebaMinorUidDf)    //贴吧未成年人评论回帖数据
      resDf = unionIfNotEmpty(resDf,shoubaiMinorUidDf)    //手百评论未成年人数据
        .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
        .withColumn("score",col("score").cast("float"))
        .dropDuplicates()
        .coalesce(20)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

      println("最终结果：" + resDf.count())
      resDf.createOrReplaceTempView("res_label_data")

      //写入表help_ods_crcc_userlabel_data
      spark.sql(
        s"""
           |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
           |select
           | uid,
           | source,
           | type,
           | sub_type,
           | label_lv1,
           | label_lv2,
           | label_lv3,
           | score
           |from res_label_data
           |""".stripMargin)

      resDf.unpersist()
    }

    //================================================儿童色情种子================================================
    if (datatype.equals("0") || datatype.equals("2")){
      //百度App关注用户历史存量明细数据表，有权限
      val insightDf = getInsightData(spark,YesterDay)
      insightDf.createOrReplaceTempView("insight_view")

      //评论儿童色情种子用户
      val childrenCsv = readCsv(spark,"儿童色情种子用户")
        .withColumn("uid",col("uid").cast("long"))
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      childrenCsv.createOrReplaceTempView("children_view")

      //关注儿童色情种子用户
      val insChildrenCommentDf = spark.sql(
        """
          |select /*+ BROADCAST(children_view) */
          |  A.uid
          |from insight_view A
          |inner join children_view C
          |on A.third_id = C.uid
          |""".stripMargin)

      val insChildrenDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","关注儿童色情种子用户","80",insChildrenCommentDf)
        //.repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("关注儿童色情种子用户数据关联手百共：" + insChildrenDf.count())
      //insChildrenDf.show(5,false)

      //儿童色情种子用户关注对象
      val ChildreninsCommentDf = spark.sql(
        """
          |select /*+ BROADCAST(children_view) */
          |  A.third_id as uid
          |from insight_view A
          |inner join children_view C
          |on A.uid = C.uid
          |""".stripMargin)

      val ChildreninsDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户关注对象","80",ChildreninsCommentDf)
        //.repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("儿童色情种子用户关注对象数据关联手百共：" + ChildreninsDf.count())
      //ChildreninsDf.show(5,false)

      //与儿童色情用户互关,且至少一个用户为儿童色情
      val eachFollowChildrenDf = spark.sql(
        """
          |with tmp_view as(
          |select /*+ BROADCAST(children_view) */
          |   A.uid,
          |   A.third_id
          |from insight_view A
          |inner join children_view C
          |on A.uid = C.uid or A.third_id = C.uid
          |)
          |select
          |   A.uid
          |from tmp_view A
          |inner join tmp_view B
          |on A.uid = B.third_id and A.third_id = B.uid
          |group by A.uid
          |""".stripMargin)

      val eachFollowResDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情用户互关","80",eachFollowChildrenDf)
        //.repartition(50)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("与儿童色情用户互关数据手百共：" + eachFollowResDf.count())

      childrenCsv.unpersist()

      //关注涉政黑用户 手百
      val shoubaiShezhengBlockDf = spark.sql(
          """
            |select /*+ BROADCAST(user_label_view) */
            | T.uid
            |from insight_view T
            |inner join user_label_view U
            |on T.third_id = U.uid
            |""".stripMargin)
        .dropDuplicates()

      val shoubaiShezhengBlockResDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","涉政","关注涉政黑用户","80",shoubaiShezhengBlockDf)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)
      println("手百关注涉政黑用户数据：" + shoubaiShezhengBlockResDf.count())

      var resDf2 = unionIfNotEmpty(insChildrenDf,ChildreninsDf) //关注儿童色情种子用户数据
      resDf2 = unionIfNotEmpty(resDf2,eachFollowResDf) //与儿童色情用户互关数据
      resDf2 = unionIfNotEmpty(resDf2,shoubaiShezhengBlockResDf) //手百关注涉政黑用户
       .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .withColumn("score",col("score").cast("float"))
        .dropDuplicates()
        .coalesce(2)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

      println("最终结果：" + resDf2.count())
      resDf2.createOrReplaceTempView("res_label_data2")

      tiebaDf.unpersist()
      commUidDf.unpersist()
      paretUidDf.unpersist()
      totalUserBlockDf.unpersist()

      //写入表help_ods_crcc_userlabel_data
      spark.sql(
        s"""
           |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
           |select
           | uid,
           | source,
           | type,
           | sub_type,
           | label_lv1,
           | label_lv2,
           | label_lv3,
           | score
           |from res_label_data2
           |""".stripMargin)

      resDf2.unpersist()
    }
    spark.stop()
  }

  /**
   * 判断是否为特殊日期，每月1，10，20号则运行app_list的标签逻辑写入表中
   * @param dateStr
   * @return
   */
  def isSpecialDay(dateStr: String): Boolean = {
    // 定义日期格式解析器
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")

    try {
      // 解析字符串为LocalDate对象
      val date = LocalDate.parse(dateStr, formatter)
      // 获取月份中的日
      val dayOfMonth = date.getDayOfMonth
      // 检查是否为目标日期
      dayOfMonth match {
        case 1 | 10 | 20 => true
        case _ => false
      }
    } catch {
      case e: Exception =>
        // 日期格式错误时返回false
        println(s"错误：无效的日期格式 '$dateStr'，需要yyyyMMdd格式")
        false
    }
  }
}

