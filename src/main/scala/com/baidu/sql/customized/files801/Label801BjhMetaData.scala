package com.baidu.sql.customized.files801

import com.alibaba.fastjson.JSON
import com.baidu.sql.customized.files801.Label801BjhAppId.IpBasedTokenBucketController
import com.baidu.sql.utils.SparkUtils.QpsController
import com.baidu.sql.utils.TimeOperateUtil._
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.SparkSession
import org.apache.spark.storage.StorageLevel
import scala.util.Random
import java.util.concurrent.TimeUnit
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import sys.process._

/**
 *  @author: zhangrunjie 801风控用户标签表，百家号元数据获取
 */
object Label801BjhMetaData {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""

  val RELEVANCE_STATUS = Map(
    "0" -> "间接关联",
    "1" -> "作者关联-自主",
    "2" -> "作者关联-代绑",
    "3" -> "作者关联-私信",
    "4" -> "运营关联"
  )

  val PLATFORM_SOURCE = Map(
    "1" -> "抖音",
    "2" -> "B站",
    "3" -> "快手",
    "4" -> "头条",
    "5" -> "微博",
    "6" -> "微信公众号",
    "7" -> "爱奇艺",
    "8" -> "小红书",
    "9" -> "企鹅号",
    "10" -> "知乎",
    "11" -> "逛逛",
    "12" -> "视频号"
  )

  val DOUYIN_STATUS = Map(
    "1" -> "可用",
    "-1" -> "注销",
    "-2" -> "封禁",
    "-3" -> "禁言",
    "" -> "未抓到"
  )

  val okHttpClient = new OkHttpClient.Builder()
    .callTimeout(40, TimeUnit.SECONDS)
    .connectTimeout(40, TimeUnit.SECONDS)
    .readTimeout(40, TimeUnit.SECONDS)
    .build()

  var httpUrl = ""

  // 添加分布式QPS控制的广播变量
  @transient lazy val qpsController = new QpsController(10) // 每秒5个请求

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    // 并发数
    val asyncNum = args(1).toInt

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val BeForeDayTimeStamp = getTimeStampTake(BeForeDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 1. 执行命令行获取服务实例信息
    val commandOutput = "get_instance_by_service -i group.mcs-in.superpage.all".!!
    println(s"命令输出: $commandOutput")

    // 2. 解析IP和端口（根据实际输出格式调整正则）
    val ipPortPattern = """(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})""".r
    val serviceInstances = ipPortPattern.findAllMatchIn(commandOutput).map { m =>
      m.group(1) // ip
    }.toList
    println(s"服务实例: $serviceInstances")
    // 根据IP数量动态调整分区数和QPS
    val availableIpCount = serviceInstances.length
    val maxQpsPerIp = args(2).toInt // 每个IP最大QPS
    val totalMaxQps = availableIpCount * maxQpsPerIp
    val safetyBuffer = 0.9 // 90%安全系数
    val targetGlobalQps = (totalMaxQps * safetyBuffer).toInt

    // 分区数设置：建议为IP数量的1-2倍，确保负载均衡
    val optimalPartitionCount = Math.min(availableIpCount * 2, asyncNum) // 最多40个分区

    println(s"目标全局QPS: $targetGlobalQps, 分区数: $optimalPartitionCount")

    // 创建基于IP的QPS控制器
    val ipBasedQpsController = new IpBasedTokenBucketController(serviceInstances)
    val broadcastQpsController = spark.sparkContext.broadcast(ipBasedQpsController)

    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(author_user_id as long) as uid,
         |  rmb_self_build_url_http,
         |  coalesce(
         |    regexp_extract(
         |      regexp_replace(rmb_self_build_url_http, '/+$$', ''),
         |      '.*/([^/\\.]+)\\.mp4$$',
         |      1
         |    ),
         |    'default_mid'
         |  ) as mid
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${BeForeDayTimeStamp}
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${YesterTimeStamp}
         |  and rmb_self_build_url_http is not null
         |  and rmb_self_build_url_http != ''
         |""".stripMargin

    val bjhDf = spark.sql(bjhSql)
      //调整并发数
      .repartition(asyncNum)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号一天内数据共：" + bjhDf.count())
    bjhDf.createOrReplaceTempView("bjh_data")
    //bjhDf.show(10,false)

    //接口数据获取 - 使用mapPartitions进行分布式处理
    val sessionDf = bjhDf
      .filter($"mid" =!= "")
      .select(
        col("mid").cast("string"),
        col("uid").cast("string")
      )
      .repartition(optimalPartitionCount) // 动态分区数
      .mapPartitions { partition =>
        val qpsCtrl = broadcastQpsController.value
        partition.map { row =>
          val mid = row.getString(0)
          val uid = row.getString(1)
          val responseJson = processRequestWithTokenControl(mid, qpsCtrl)
          (mid, uid, responseJson)
        }
      }
      .toDF("mid","uid", "responseJson")
      .filter($"responseJson" =!= "" && $"responseJson".isNotNull)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("接口请求数据共：" + sessionDf.count())

    val resLabelDf = sessionDf
      .withColumn("data_source",
        when(col("responseJson").contains("www.youtube.com"),"YOUTUBE")
        .when(col("responseJson").contains("Twitter-vork muxer"),"TWITTER")
        .when(col("responseJson").contains("Packed by Bilibili XCoder"),"BILIBILI")
        .when(col("responseJson").contains("PDDLiveEdit"),"PDDLIVE")
        .when(col("responseJson").contains("GIFSHOW"),"GIFSHOW")
        .when(col("responseJson").contains("douyin_beauty_me") || col("responseJson").contains("com.bytedance.info"),"DOUYIN")
        .when(col("responseJson").contains("dreamina"),"DREAMINA_AI")
        .when(col("responseJson").contains("Vidu"),"VIDU_AI")
        .when(col("responseJson").contains("zhipuqingyan"),"ZHIPUQINGYAN_AI")
        .otherwise("")
      )
      .withColumn("source",lit("百家号"))
      .withColumn("type",lit("日常行为"))
      .withColumn("sub_type",lit("发文、发帖、点赞、关注等行为"))
      .withColumn("label_lv1",lit("灰"))
      .withColumn("label_lv2",lit("其它lv3"))
      .withColumn("label_lv3",concat(lit("视频发文具有元数据特征_"),col("data_source")))
      .withColumn("score",lit("80").cast("float"))
      .filter(col("data_source") =!= "")
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("最终数据共：" + resLabelDf.count())
    //resLabelDf.show(10,false)
    resLabelDf.createOrReplaceTempView("res_label_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    resLabelDf.unpersist()
    spark.stop()
  }

  //获取datalist字段
  val getDatalist = udf((data: mutable.WrappedArray[String]) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      val res = ListBuffer[String]()
      data.foreach(row => {
        //获取json数据
        val json = JSON.parseObject(row)
        //关联状态
        val relevance_status = json.getOrDefault("relevance_status","").toString
        //平台来源
        val platform_source = json.getOrDefault("platform_source","").toString
        //状态
        val status = json.getOrDefault("status","").toString
        if (platform_source.equals("1")){
          res += Seq(platform_source,relevance_status,status).mkString("=>")
        }
      })
      res.mkString("<=>")
    }
  })

  //获取关联状态字段
  val getrelevanceStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      RELEVANCE_STATUS.getOrElse(data,"")
    }
  })

  //获取状态字段
  val getStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      DOUYIN_STATUS.getOrElse(data,"")
    }
  })

  /**
   * 发送请求获取数据
   * @param mid
   * @param maxRetries
   * @return
   */
  def postRequestTest(mid: String, maxRetries: Int = 3): String = {
    var result = ""
    // 初始化重试次数
    var attempts = 0

    // 循环尝试获取数据
    while (attempts < maxRetries && result.isEmpty) {
      attempts += 1

      val httpurl = httpUrl + mid
      //println(s"请求接口：$httpurl")

      // 口令获取
      var response: Response = null

      val request = new Request.Builder()
        .url(httpurl)
        .get()
        .build

      try {
        response = okHttpClient.newCall(request).execute
        if (response.isSuccessful) {
          result = response.body.string
        } else {
          result = "Empty response"
        }
      } catch {
        case _: Exception =>
          result = "Empty response"
      } finally {
        if (response != null) response.close()
      }
    }
    result
  }

  /**
   * 带令牌桶控制的请求处理函数
   */
  def processRequestWithTokenControl(mid: String, ipTokenController: IpBasedTokenBucketController): String = {
    var result = ""
    var attempts = 0
    val maxRetries = 3

    while (attempts < maxRetries && result.isEmpty) {
      attempts += 1

      // 获取可用IP和对应的令牌桶控制器
      val (ip, tokenController) = ipTokenController.getAvailableIpWithTokenControl()

      try {
        // 获取令牌（会自动等待直到有可用令牌）
        tokenController.acquire()

        val httpUrlLocal = s"http://${ip}:9912/xvision/tradition/mediainfo/detail?mediaid="
        val httpurl = httpUrlLocal + mid

        var response: Response = null

        try {
          val request = new Request.Builder()
            .url(httpurl)
            .get()
            .build()

          response = okHttpClient.newCall(request).execute()

          if (response.isSuccessful) {
            result = response.body.string
          }
        } catch {
          case _: Exception => // 忽略异常，继续重试
        } finally {
          if (response != null) response.close()
        }

        // 响应结果处理
        if (result.nonEmpty) {
          // 成功获取到响应内容，保持result
        } else {
          // 空响应，重置结果继续重试
          result = ""
          // 指数退避策略 + 随机抖动，避免重试集中
          val baseDelay = 100 * Math.pow(2, attempts).toLong
          val jitter = Random.nextInt(50) // 0-50ms随机抖动
          Thread.sleep(baseDelay + jitter)
        }
      }
    }
    result
  }
}

