package com.baidu.sql.customized.files801

import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 *  @author: zhangrunjie 801风控用户标签表
 */
object Label801 {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取贴吧数据
    val tiebaSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(t_uid as long) as t_uid,
         |  cast(floor_reply_uid as long) as floor_reply_uid,
         |  fname,
         |  title,
         |  content,
         |  cast(create_ts as long) as timestamp
         |from
         |  ubs_tieba.tieba_dim_pub_info_hi
         |where
         |  event_day = '${YesterDay}'
         |order by uid
         |""".stripMargin

    val tiebaDf = spark.sql(tiebaSql)
      .repartition(60)
      .cache()
    //一天大概500w数据
    println("贴吧数据共：" + tiebaDf.count())
    tiebaDf.createOrReplaceTempView("tieba")
    //tiebaDf.show(5,false)

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(article_uid as long) as article_uid,
         |  cast(parent_uid as long) as parent_uid,
         |  cast(ts as long) as timestamp
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${YesterTimeStamp}
         |  and ts < ${ToDayTimeStamp}
         |  and ownner_type = 0
         |order by uid,timestamp
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .repartition(200)
      .cache()
    //一天大概700w数据
    println("手百数据共：" + bdhdDf.count())
    //bdhdDf.show(5,false)
    bdhdDf.createOrReplaceTempView("bdhd")

    // 涉政敏感吧词表与贴吧数据关联
    val shezhenCsv = readCsv(spark,"涉政敏感吧")
    val shezhenJsonDf = tiebaDf.as("T").join(shezhenCsv.as("B"), col("T.fname") === col("B.post_name"))
    val shezhenDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","涉政","涉政敏感吧活跃","80",shezhenJsonDf)
    println("涉政敏感吧词表关联共：" + shezhenDf.count())
    //shezhenDf.show(5,false)

    //偷拍密录设备相关吧词表与贴吧数据关联
    val toupaiCsv = readCsv(spark,"偷拍密录设备相关吧")
    val heichanJoinDf = tiebaDf.as("T").join(toupaiCsv.as("B"), col("T.fname") === col("B.post_name"))
    val heichanDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","黑产","偷拍密录设备相关吧活跃","80",heichanJoinDf)
    println("偷拍密录设备相关吧词表数据关联共：" + heichanDf.count())
    //heichanDf.show(5,false)

    //涉毒吧词表与贴吧数据关联
    val sheduCsv = readCsv(spark,"涉毒吧")
    val sheduJoinDf = tiebaDf.as("T").join(sheduCsv.as("B"), col("T.fname") === col("B.post_name"))
    val sheduDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","涉毒","涉毒吧活跃","80",sheduJoinDf)
    println("涉毒吧词表数据关联共：" + sheduDf.count())
    //sheduDf.show(5,false)

    //儿童色情相关吧与贴吧数据关联
    val ertongCsv = readCsv(spark,"儿童色情相关吧")
    val ertongJoinDf = tiebaDf.as("T").join(ertongCsv.as("B"), col("T.fname") === col("B.post_name"))
    val ertongDf = mergeAndAddColumns("贴吧","日常行为","贴吧活跃","灰","未成年色情","儿童色情相关吧活跃","80",ertongJoinDf)
    println("儿童色情相关吧数据关联共：" + ertongDf.count())
    //ertongDf.show(5,false)

    //评论儿童色情种子用户
    val childrenCsv = readCsv(spark,"儿童色情种子用户")
      .withColumn("uid",col("uid").cast("long"))

    //贴吧
    val tiebaChildrenJoinDf = tiebaDf.as("T").join(childrenCsv.as("B"),
      //只要其中一个匹配到了就对uid打标签
        col("T.t_uid") === col("B.uid") || col("T.floor_reply_uid") === col("B.uid"))
      .select(
        col("T.uid") as "uid",
        col("T.t_uid") as "t_uid",
        col("T.floor_reply_uid") as "floor_reply_uid",
        col("B.uid") as "Buid"
      )
      // 过滤掉发帖者本人，自己评论自己
      .filter(col("uid") =!= col("Buid"))

    val tiebaChildrenDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","评论儿童色情种子用户","80",tiebaChildrenJoinDf)
    println("评论儿童色情种子用户数据关联贴吧共：" + tiebaChildrenDf.count())
    //tiebaChildrenDf.show(5,false)

    // 手百
    val bdhdChildrenJoinDf = bdhdDf.as("T").join(childrenCsv.as("B"),
        //只要其中一个匹配到了就对uid打标签
        col("T.article_uid") === col("B.uid") || col("T.parent_uid") === col("B.uid"))
      .select(
        col("T.uid") as "uid",
        col("T.article_uid") as "article_uid",
        col("T.parent_uid") as "parent_uid",
        col("B.uid") as "Buid"
      )
      // 过滤掉发帖者本人，自己评论自己
      .filter(col("uid") =!= col("Buid"))

    val bdhdChildrenDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","评论儿童色情种子用户","80",bdhdChildrenJoinDf)
    println("评论儿童色情种子用户数据关联手百共：" + bdhdChildrenDf.count())
    //bdhdChildrenDf.show(5,false)

    //儿童色情种子用户评论对象
    //贴吧
    val tiebaChildrenCommentDf = tiebaDf.as("T").join(childrenCsv.as("B"), col("T.uid") === col("B.uid"))
      .select(
        col("T.uid") as "old_uid",
        col("T.t_uid") as "t_uid",
        col("T.floor_reply_uid") as "floor_reply_uid",
        col("B.uid") as "Buid"
      )
      .withColumn("uid",concat(col("t_uid"),lit(","),col("floor_reply_uid")))
      .repartition(200)

    tiebaChildrenCommentDf.createOrReplaceTempView("tiebaChildrenCommentDf")
    // 拆分uid字段，使之变成多行
    val explodeTiebaTmpDf = spark.sql(
      """
        |select
        |  old_uid,
        |  explode(split(uid,',')) as uid
        |from tiebaChildrenCommentDf
        |""".stripMargin)
      // 过滤掉发帖者本人，自己评论自己
      .filter(col("old_uid") =!= col("uid"))
      .filter(col("uid") =!= "0")

    val explodeTiebaDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户评论对象","80",explodeTiebaTmpDf)
    println("儿童色情种子用户数据关联贴吧共：" + explodeTiebaDf.count())
    //explodeTiebaDf.show(5,false)

    // 手百
    val bdhdChildrenCommentDf = bdhdDf.as("T").join(childrenCsv.as("B"), col("T.uid") === col("B.uid"))
      .select(
        col("T.uid") as "old_uid",
        col("T.article_uid") as "article_uid",
        col("T.parent_uid") as "parent_uid",
        col("B.uid") as "Buid"
      )
      .withColumn("uid",concat(col("article_uid"),lit(","),col("parent_uid")))

    bdhdChildrenCommentDf.createOrReplaceTempView("bdhdChildrenCommentDf")
    // 拆分uid字段，使之变成多行
    val explodeBdhdTmpDf = spark.sql(
        """
          |select
          |  old_uid,
          |  explode(split(uid,',')) as uid
          |from bdhdChildrenCommentDf
          |""".stripMargin)
      // 过滤掉发帖者本人，自己评论自己
      .filter(col("old_uid") =!= col("uid"))
      .filter(col("uid") =!= "0")

    val explodeBdhdDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户评论对象","80",explodeBdhdTmpDf)
    println("儿童色情种子用户数据关联手百共：" + explodeBdhdDf.count())
    //explodeBdhdDf.show(5,false)

    //发布涉毒相关内容
    val sheduTextCsv = readCsv(spark,"涉毒暗语文本")

    //标题
    val titleDf = sheduJoinDf.as("S").join(
      sheduTextCsv.filter(col("match_col") === "标题").as("T"),
      col("S.title") contains col("T.voc_content")
    )
    //titleDf.show(5,false)
    println("涉毒标题数据：" + titleDf.count())

    //内容 单词匹配
    val contentDf = sheduJoinDf.as("S").join(
      sheduTextCsv.filter(col("match_col") === "内容" && col("match_type") === "单词匹配").as("T"),
      col("S.content") contains col("T.voc_content")
    )
    //contentDf.show(5,false)
    println("涉毒内容单词匹配数据：" + contentDf.count())

    //内容 双词匹配
    val contentDouDf = sheduJoinDf.as("S").join(
      sheduTextCsv.filter(col("match_col") === "内容" && col("match_type") === "双词匹配")
        .withColumn("voc_list",split(col("voc_content"),"&")).as("T"),
      (col("S.content").contains(col("T.voc_list")(0)))
        && (col("S.content").contains(col("T.voc_list")(1)))
    )
      .drop("voc_list")
    //contentDouDf.show(5,false)
    println("涉毒内容双词匹配数据：" + contentDouDf.count())

    val tiebaSheduDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉毒","发布涉毒相关内容","80",titleDf,contentDf,contentDouDf)
    println("发布涉毒相关内容数据关联贴吧共：" + tiebaSheduDf.count())
    tiebaSheduDf.show(5,false)

    //拍密录设备相关文本
    val shebeiTextCsv = readCsv(spark,"偷拍密录设备相关文本")

    //内容 单词匹配
    val contentShebeiDf = heichanJoinDf.as("S").join(
      shebeiTextCsv.filter(col("match_type") === "单词匹配").as("T"),
      col("S.content") contains col("T.voc_content")
    )
    //contentShebeiDf.show(5,false)
    println("偷拍密录设备相关文本内容单词匹配数据：" + contentShebeiDf.count())

    //内容 双词匹配
    val contentShebeiDouDf = heichanJoinDf.as("S").join(
      shebeiTextCsv.filter(col("match_type") === "双词匹配").withColumn("voc_list",split(col("voc_content"),"&")).as("T"),
      (col("S.content").contains(col("T.voc_list")(0))) && (col("S.content").contains(col("T.voc_list")(1)))
    )
      .drop("voc_list")
    //contentShebeiDouDf.show(5,false)
    println("偷拍密录设备相关文本内容双词匹配数据：" + contentShebeiDouDf.count())
    val tiebaTouPaiDf = mergeAndAddColumns("贴吧","日常行为","发文、发帖、点赞、关注等行为","灰","涉毒","发布涉毒相关内容","80",contentShebeiDf,contentShebeiDouDf)
    println("发布偷拍密录设备相关文本数据关联贴吧共：" + tiebaTouPaiDf.count())
    tiebaTouPaiDf.show(5,false)

    //机器特征
    //手百评论时间差计算
    val bdhdDiffDf = spark.sql(
      """
        |WITH diff_time AS (
        |    SELECT
        |        uid,
        |        timestamp,
        |        -- 计算与上一条记录的时间差（单位：秒）
        |        timestamp -
        |        LAG(timestamp, 1) OVER (PARTITION BY uid ORDER BY timestamp) AS time_diff_seconds
        |    FROM
        |        bdhd
        |)
        |SELECT
        |    uid,
        |    -- 计算总体方差（分母n）
        |    VAR_POP(time_diff_seconds) AS variance_population
        |FROM
        |    diff_time
        |WHERE
        |    time_diff_seconds IS NOT NULL
        |GROUP BY
        |    uid
        |HAVING
        |    COUNT(*) >= 2
        |""".stripMargin)
      .filter(col("variance_population") <= "3")
      .withColumn("score",(when(col("variance_population") === "0.0","100")
        .when(col("variance_population") <= "0.25","90")
        .when(col("variance_population") <= "0.5","80")
        .when(col("variance_population") <= "1","70")
        .when(col("variance_population") <= "2","60")
        .when(col("variance_population") <= "3","50")).cast("float"))

    val bdhdVarResDf = mergeAndAddColumns("手百","日常行为","机器特征","灰","机器","固定步长发评","",bdhdDiffDf)

    bdhdVarResDf.show(5,false)
    println("手百评论方差计算数据：" + bdhdVarResDf.count())

    //贴吧发回帖计算时间差
    val tiebaDiffDf = spark.sql(
      """
        |WITH diff_time AS (
        |    SELECT
        |        uid,
        |        -- 计算与上一条记录的时间差（单位：秒）
        |        timestamp -
        |        LAG(timestamp, 1) OVER (PARTITION BY uid ORDER BY timestamp) AS time_diff_seconds
        |    FROM
        |        tieba
        |)
        |SELECT
        |    uid,
        |    -- 计算总体方差（分母n）
        |    VAR_POP(time_diff_seconds) AS variance_population
        |FROM
        |    diff_time
        |WHERE
        |    time_diff_seconds IS NOT NULL
        |GROUP BY
        |    uid
        |HAVING
        |    COUNT(*) >= 2
        |""".stripMargin)
      .filter(col("variance_population") <= "3")
      .withColumn("score",(when(col("variance_population") === "0.0","100")
        .when(col("variance_population") <= "0.25","90")
        .when(col("variance_population") <= "0.5","80")
        .when(col("variance_population") <= "1","70")
        .when(col("variance_population") <= "2","60")
        .when(col("variance_population") <= "3","50")).cast("float"))

    val tiebaVarResDf = mergeAndAddColumns("贴吧","日常行为","机器特征","灰","机器","固定步长发回帖","",tiebaDiffDf)

    tiebaVarResDf.show(5,false)
    println("贴吧评论方差计算数据：" + tiebaVarResDf.count())


    //手百高频发文
    val bdhdCntDf = spark.sql(
      """
        |with cnt_tmp as (
        |SELECT
        |    uid,
        |    timestamp,
        |    -- 计算1秒内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1s,
        |    -- 计算1分钟内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 60 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1m,
        |    -- 计算1小时内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1h
        |FROM bdhd
        |ORDER BY uid,timestamp
        |)
        |SELECT
        |    uid,
        |    MAX(
        |        CASE
        |            WHEN cnt_1s > 1 THEN 100     -- 1秒内发帖次数大于1
        |            WHEN cnt_1m > 3 THEN 90     -- 1分钟内发帖次数大于3
        |            WHEN cnt_1h > 5 THEN 80     -- 1小时内发帖次数大于5
        |            ELSE 0
        |        END
        |    ) AS score
        |FROM cnt_tmp
        |group by uid
        |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))

    bdhdCntDf.count()
    val bdhdResDf = mergeAndAddColumns("手百","日常行为","机器特征","灰","机器","高频发评","",bdhdCntDf)

    bdhdResDf.show(5,false)
    println("手百高频发文数据：" + bdhdResDf.count())

    //贴吧高频发帖
    val tiebaCntDf = spark.sql(
        """
          |with cnt_tmp as (
          |SELECT
          |        uid,
          |        timestamp,
          |        -- 计算1秒内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1s,
          |        -- 计算1分钟内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 60 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1m,
          |        -- 计算1小时内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1h
          |    FROM tieba
          |    ORDER BY uid,timestamp
          | )
          | SELECT
          |    uid,
          |    MAX(
          |        CASE
          |            WHEN cnt_1s > 1 THEN 100     -- 1秒内发帖次数大于1
          |            WHEN cnt_1m > 3 THEN 90     -- 1分钟内发帖次数大于3
          |            WHEN cnt_1h > 5 THEN 80     -- 1小时内发帖次数大于5
          |            ELSE 0
          |        END
          |    ) AS score
          |FROM cnt_tmp
          |group by uid
          |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))

    tiebaCntDf.count()
    val tiebaResDf = mergeAndAddColumns("贴吧","日常行为","机器特征","灰","机器","高频发回帖","",tiebaCntDf)

    tiebaResDf.show(5,false)
    println("贴吧高频发文数据：" + tiebaResDf.count())

    //合并结果
    val resDf = shezhenDf
      .unionByName(heichanDf)
      .unionByName(sheduDf)
      .unionByName(ertongDf)
      .unionByName(tiebaChildrenDf)
      .unionByName(bdhdChildrenDf)
      .unionByName(explodeTiebaDf)
      .unionByName(explodeBdhdDf)
      .unionByName(tiebaSheduDf)
      .unionByName(tiebaTouPaiDf)
      .unionByName(bdhdVarResDf)
      .unionByName(tiebaVarResDf)
      .unionByName(bdhdResDf)
      .unionByName(tiebaResDf)
      .dropDuplicates()
      .repartition(200)

    println("最终结果：" + resDf.count())
    resDf.show(5,false)
    resDf.createOrReplaceTempView("res_label_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert overwrite table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    spark.stop()
  }


  /**
   * 读取词表
   * @param spark
   * @param csvName
   * @return
   */
  def readCsv(spark:SparkSession,csvName:String): DataFrame = {
    // 读取词表
    val wordlist =
      spark.read
        .option("encoding", "UTF-8") // 这里假设文件是GBK编码，根据实际情况修改
        .option("header", "true") // 如果CSV文件包含表头，则设置为true
        .option("inferSchema", "true")
        .csv(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/${csvName}.csv")
    println(s"读取${csvName}共：" + wordlist.count())
    //tieba_toupai.show(5,false)
    wordlist
  }


  /**
   * 合并多个DataFrame并添加新列
   * @param source       数据来源
   * @param source_type 标签类型
   * @param sub_type    标签子类型
   * @param label_lv1  标签一级分类
   * @param label_lv2  标签二级分类
   * @param label_lv3  标签三级分类
   * @param score  分数/置信
   * @param dataframes
   * @return
   */
  def mergeAndAddColumns(source: String,
                         source_type: String,
                         sub_type: String,
                         label_lv1: String,
                         label_lv2: String,
                         label_lv3: String,
                         score: String,
                         dataframes: DataFrame*): DataFrame = {
    // 合并多个DataFrame
    var mergedDF = dataframes.reduce(_ unionByName _)

    //如果参数分数不为空，则添加分数列，否则不添加score列
    if(!score.equals("")){
      mergedDF = mergedDF.withColumn("score", lit(score).cast("float"))
    }

    // 添加新列
    val finalDF = mergedDF
      .withColumn("source", lit(source))
      .withColumn("type", lit(source_type))
      .withColumn("sub_type", lit(sub_type))
      .withColumn("label_lv1", lit(label_lv1))
      .withColumn("label_lv2", lit(label_lv2))
      .withColumn("label_lv3", lit(label_lv3))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()

    finalDF
  }

  /**
   * 获取未成年人画像数据
   * @param spark
   * @param yesterDay 查询的日期分区
   * @return
   */
  def getNonageData(spark: SparkSession, yesterDay: String): DataFrame = {
    // 定义返回DF的schema（即使数据为空也保持相同结构）
    val resultSchema = StructType(Array(StructField("uid", LongType, nullable = false)))
    try {
      // 获取最新的日期分区
      val userPortraitDf = spark.read.parquet(s"afs://kunpeng.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian/user_portrait_strategy/${yesterDay}")
      if (userPortraitDf.isEmpty) {
        // 返回带schema的空DataFrame
        spark.createDataFrame(spark.sparkContext.emptyRDD[Row], resultSchema)
      } else {
        userPortraitDf.createOrReplaceTempView("user_portrait_view")

        val crccSql =
          s"""
             |WITH tmp_data AS (
             |  SELECT
             |    CAST(uid AS LONG) AS uid,
             |    life_stage,
             |    SPLIT(life_stage, '\\\\|')[1] AS life_part2,
             |    SPLIT(life_stage, '\\\\|')[2] AS life_part3,
             |    age,
             |    SPLIT(age, '\\\\|')[1] AS age_part2,
             |    SPLIT(age, '\\\\|')[2] AS age_part3,
             |    age_point,
             |    SPLIT(age_point, '\\\\|')[1] AS age_point_part2,
             |    SPLIT(age_point, '\\\\|')[2] AS age_point_part3
             |  FROM user_portrait_view
             |  WHERE
             |    age_point != ''
             |    AND age != ''
             |    AND life_stage != ''
             |    AND CAST(uid AS LONG) > 0
             |    AND SIZE(SPLIT(life_stage, '\\\\|')) >= 3
             |    AND SIZE(SPLIT(age, '\\\\|')) >= 3
             |    AND SIZE(SPLIT(age_point, '\\\\|')) >= 3
             |)
             |SELECT uid
             |FROM tmp_data
             |WHERE
             |  (CAST(life_part3 AS INT) >= 60 AND life_part2 IN ('初中生', '高中生'))
             |  OR (CAST(age_point_part3 AS INT) >= 60 AND age_point_part2 IN ('14岁', '15岁', '16岁', '17岁'))
             |  OR (CAST(age_part3 AS INT) >= 60 AND age_part2 = '18以下')
             |""".stripMargin

        val crccUserDf = spark.sql(crccSql)
          .repartition(50)
          .persist(StorageLevel.MEMORY_AND_DISK_SER)

        println(s"用户画像-策略特征未成年人数据量：${crccUserDf.count()}")
        crccUserDf
      }
    } catch {
      case e: Exception =>
        println(s"[ERROR] 读取用户画像数据失败，日期分区：${yesterDay}，错误信息：${e.getMessage}")
        // 返回带schema的空DataFrame
        spark.createDataFrame(spark.sparkContext.emptyRDD[Row], resultSchema)
    }
  }

  /**
   * 获取画像标签表-文心-全量表
   * @param spark
   * @param yesterDay 查询的日期分区
   * @return
   */
  def getUserLabelData(spark:SparkSession,yesterDay:String): DataFrame = {
    //画像标签表-文心-全量表
    val crccSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  count(*) as black_cnt
         |from
         |  udw_ns.default.crcc_dwd_portrait_userlabel_df
         |where
         |  event_day = '${yesterDay}'
         |  and event_label = 'black'
         |  and cast(uid as long) > 0
         |  and label = 'black_political'
         |group by uid
         |""".stripMargin

    val crccUserDf = spark.sql(crccSql)
      //.repartition(20)
      //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("画像标签表-文心-全量数据共：" + crccUserDf.count())
    crccUserDf
  }

  /**
   * 获取百度App关注用户历史存量明细数据表
   * @param spark
   * @param yesterDay
   * @return
   */
  def getInsightData(spark:SparkSession,yesterDay:String): DataFrame = {
    //百度App关注用户历史存量明细数据表，有权限
    val insightSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(third_id as long) as third_id,
         |  count(*) as follow_cnt
         |from
         |  udw_ns.default.insight_baiduapp_follow_history
         |where
         |  event_day = '${yesterDay}'
         |  and ext['fake'] = '0'
         |  and login_status!='0'
         |  and type in ('media', 'ugc', 'celebrity')
         |  and cast(uid as long) > 0
         |  and cast(third_id as long) > 0
         |  and uid != third_id
         |group by uid,third_id
         |""".stripMargin

    val insightDf = spark.sql(insightSql)
      //.repartition(1000)
      //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("百度App关注用户历史存量明细数据表共：" + insightDf.count())
    insightDf
  }

  /**
   * 获取贴吧私信数据表
   * @param spark
   * @param yesterDay
   * @return
   */
  def getTiebaMessage(spark: SparkSession, yesterDay: String): DataFrame = {
    //贴吧私信数据表
    val messageSql =
      s"""
         |with tmp_table as (
         |select
         |   uid,
         |   split(tag,':')[1] as send_uid
         |from udw_ns.default.tieba_ods_dump_im_message_info
         |where event_day = '${yesterDay}'
         |and uid > 0
         |and tag != ''
         |)
         |select
         |   uid,
         |   cast(send_uid as long) as send_uid
         |from tmp_table
         |where uid != send_uid
         |and cast(send_uid as long) > 0
         |group by uid,send_uid
         |""".stripMargin

    val messageDf = spark.sql(messageSql)

    messageDf
  }
}

