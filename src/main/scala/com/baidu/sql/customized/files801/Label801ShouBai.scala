package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 *  @author: zhangrunjie 801风控用户标签表，手百评论数据标签
 */
object Label801ShouBai {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  //敏感词表字典
  val blockedWords = Seq(
    //词表关键词，label_lv2
    Row("政治敏感","涉政"),
    Row("广告作弊","黑产"),
    Row("色情低俗","色情"),
    Row("领导人","涉政"),
    Row("违法信息","违法违规"),
    Row("突发专项","涉政"),
    Row("涉恐涉暴","涉恐涉暴"),
    Row("涉一号首长","涉政"),
    Row("境外媒体","涉政"),
    Row("历史虚无","涉政"),
    Row("宗教民族","涉政"),
    Row("涉政","涉政"),
    Row("色情","色情")
  )

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  cast(article_uid as long) as article_uid,
         |  cast(parent_uid as long) as parent_uid,
         |  cast(ts as long) as timestamp,
         |  nid,
         |  status,
         |  ownner_type
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${YesterTimeStamp}
         |  and ts < ${ToDayTimeStamp}
         |  and cast(uid as long) > 0
         |  and ownner_type = 0
         |order by uid,timestamp
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //一天大概700w数据
    println("手百数据共：" + bdhdDf.count())
    //bdhdDf.show(5,false)
    bdhdDf.createOrReplaceTempView("bdhd_view")

    //用户评论进行拆分去重
    val paretUidDf = spark.sql(
      """
        |with temp_table as
        |(select
        |     uid,
        |     concat_ws(',', COLLECT_SET(article_uid), COLLECT_SET(parent_uid)) as con_uid
        | from bdhd_view
        | group by uid
        | ),
        |explode_table as
        |(select
        |  uid,
        |  explode(split(con_uid,',')) as comment_uid
        |from temp_table
        |)
        |select
        | uid,
        | comment_uid,
        | count(*) as com_cnt
        |from explode_table
        |where comment_uid != '' and comment_uid != '0'
        |and uid != comment_uid
        |group by uid,comment_uid
        |""".stripMargin)
    paretUidDf.persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("手百评论用户拆分：" + paretUidDf.count())
    paretUidDf.createOrReplaceTempView("paretUidDf")

    //评论儿童色情种子用户
    val childrenCsv = readCsv(spark,"儿童色情种子用户")
      .withColumn("uid",col("uid").cast("long"))
    val chindrenBroad = spark.sparkContext.broadcast(childrenCsv)
    chindrenBroad.value.createOrReplaceTempView("children_view")

    // 评论儿童色情种子用户
    val bdhdChildrenJoinDf = spark.sql(
      """
        |select
        | T.uid
        |from paretUidDf T
        |inner join children_view B
        |on T.comment_uid = B.uid
        |""".stripMargin)

    val bdhdChildrenDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","评论儿童色情种子用户","80",bdhdChildrenJoinDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("评论儿童色情种子用户数据关联手百共：" + bdhdChildrenDf.count())

    //儿童色情种子用户评论对象
    val bdhdChildrenCommentDf = spark.sql(
      """
        |select
        |   T.comment_uid as uid
        |from paretUidDf T
        |inner join children_view B
        |on T.uid = B.uid
        |where cast(T.comment_uid as long) > 0
        |""".stripMargin)
    val explodeBdhdDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","儿童色情种子用户评论对象","80",bdhdChildrenCommentDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("儿童色情种子用户数据关联手百共：" + explodeBdhdDf.count())
    //explodeBdhdDf.show(5,false)

    //机器特征
    //手百评论时间差计算
    val bdhdDiffDf = spark.sql(
      """
        |WITH diff_time AS (
        |    SELECT
        |        uid,
        |        timestamp,
        |        -- 计算与上一条记录的时间差（单位：秒）
        |        timestamp -
        |        LAG(timestamp, 1) OVER (PARTITION BY uid ORDER BY timestamp) AS time_diff_seconds
        |    FROM
        |        bdhd_view
        |)
        |SELECT
        |    uid,
        |    -- 计算总体方差（分母n）
        |    VAR_POP(time_diff_seconds) AS variance_population
        |FROM
        |    diff_time
        |WHERE
        |    time_diff_seconds IS NOT NULL
        |GROUP BY
        |    uid
        |HAVING
        |    COUNT(*) >= 2
        |""".stripMargin)
      .filter(col("variance_population") <= "3")
      .withColumn("score",(when(col("variance_population") === "0.0","100")
        .when(col("variance_population") <= "0.25","90")
        .when(col("variance_population") <= "0.5","80")
        .when(col("variance_population") <= "1","70")
        .when(col("variance_population") <= "2","60")
        .when(col("variance_population") <= "3","50")).cast("float"))

    val bdhdVarResDf = mergeAndAddColumns("手百","日常行为","机器特征","灰","机器","固定步长发评","",bdhdDiffDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //bdhdVarResDf.show(5,false)
    println("手百评论方差计算数据：" + bdhdVarResDf.count())

    //手百高频发文
    val bdhdCntDf = spark.sql(
      """
        |with cnt_tmp as (
        |SELECT
        |    uid,
        |    timestamp,
        |    -- 计算1秒内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1s,
        |    -- 计算1分钟内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 60 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1m,
        |    -- 计算1小时内发帖次数
        |    COUNT(*) OVER (
        |        PARTITION BY uid
        |        ORDER BY timestamp
        |        RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
        |    )  AS cnt_1h
        |FROM bdhd_view
        |ORDER BY uid,timestamp
        |)
        |SELECT
        |    uid,
        |    MAX(
        |        CASE
        |            WHEN cnt_1s > 1 THEN 100     -- 1秒内发帖次数大于1
        |            WHEN cnt_1m > 3 THEN 90     -- 1分钟内发帖次数大于3
        |            WHEN cnt_1h > 5 THEN 80     -- 1小时内发帖次数大于5
        |            ELSE 0
        |        END
        |    ) AS score
        |FROM cnt_tmp
        |group by uid
        |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))

    //bdhdCntDf.count()
    val bdhdResDf = mergeAndAddColumns("手百","日常行为","机器特征","灰","机器","高频发评","",bdhdCntDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //bdhdResDf.show(5,false)
    println("手百高频发文数据：" + bdhdResDf.count())

    paretUidDf.unpersist()

    //与儿童色情种子用户同违规主题活跃==========================
    val illegalDf = spark.sql(
      """
        |with temp_table as(
        |select
        |  T.nid
        |from (select uid,nid from bdhd_view where status = 0) T
        |inner join children_view B on T.uid = B.uid
        |)
        |select
        | T.uid
        |from (select uid,nid from bdhd_view where ownner_type != 1) T
        |left anti join children_view C on T.uid = C.uid
        |inner join temp_table TT on T.nid = TT.nid
        |""".stripMargin)

    val illegalResDf = mergeAndAddColumns("手百","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情种子用户同违规主题活跃","80",illegalDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("与儿童色情种子用户同违规主题活跃手百共：" + illegalResDf.count())
    //illegalResDf.show(5,false)

    //离婚相关资源评论==================================================================
    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(nid as long) as nid
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(nid as long) > 0
         |  and author_user_id != ''
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp}
         |  and second_category = '现象普法'
         |  and title like '%离婚%'
         |""".stripMargin

    // 筛选两天内数据
    val bjhDf = spark.sql(bjhSql)
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号数据共：" + bjhDf.count())
    bjhDf.createOrReplaceTempView("bjh_data")

    // 筛选出评论过离婚相关资源用户
    val divorceDf = spark.sql(
      """
        |with temp_table as (
        |select
        |  uid,
        |  split(nid,'_')[1] as nid
        |from bdhd_view
        |where ownner_type = 0
        |)
        |select
        | t.uid
        |from temp_table t
        |inner join bjh_data t1 on t.nid = t1.nid
        |""".stripMargin)
    val divorceResDf = mergeAndAddColumns("手百","日常行为","账号行为特征","灰","其它lv3","离婚相关资源评论","80",divorceDf)
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("离婚相关资源评论手百共：" + divorceResDf.count())

    bdhdDf.unpersist()
    bjhDf.unpersist()

    //合并结果  评论儿童色情种子用户
    var resDf = unionIfNotEmpty(bdhdChildrenDf,explodeBdhdDf) //儿童色情种子用户数据
      resDf = unionIfNotEmpty(resDf,bdhdVarResDf) //手百评论方差计算
      resDf = unionIfNotEmpty(resDf,bdhdResDf)  //手百高频发文数据
      resDf = unionIfNotEmpty(resDf,illegalResDf) //与儿童色情种子用户同违规主题活跃
      resDf = unionIfNotEmpty(resDf,divorceResDf) //离婚相关资源评论
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .withColumn("score",col("score").cast("float"))
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("最终结果：" + resDf.count())
    resDf.createOrReplaceTempView("res_label_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    resDf.unpersist()
    spark.stop()
  }

  // 定义一个UDF来转换sm_reason和sm_subreason到secure_reject_reason格式
  val secureRejectReasonUDF = udf((sm_reason: String, sm_subreason: String) => {
    if ((sm_reason == "" || sm_reason == null) && (sm_subreason != "" && sm_subreason != null)){
      sm_subreason
    }else if ((sm_reason != "" && sm_reason != null)  && (sm_subreason == "" || sm_subreason == null)){
      sm_reason
    }else if(sm_reason != "" && sm_reason != null && sm_subreason != "" && sm_subreason != null && !sm_reason.contains("&&") && !sm_subreason.contains("&&")){
      sm_reason + " && " + sm_subreason
    } else if (sm_reason != "" && sm_reason != null && sm_subreason != "" && sm_subreason != null && sm_reason.contains("&&") && sm_subreason.contains("&&")){
      val reasons = sm_reason.split("&&")
      val subreasons = sm_subreason.split("&&")
      reasons.zip(subreasons)
        .map { case (reason, subreason) => s"${reason}$$${subreason}" }
        .mkString(" && ")
    }else{
      ""
    }
  })
}

