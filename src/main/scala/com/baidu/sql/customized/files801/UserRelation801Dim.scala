package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.UserRelation801.getSqlData
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.SparkSession


object UserRelation801Dim {
  //`uid` STRING COMMENT '用户UID' SAMPLE '423435454',
  //`type` STRING COMMENT '关联类型' SAMPLE 'IP、CUID',
  //`value` STRING COMMENT '关联值' SAMPLE '*******',
  //`source` STRING COMMENT '数据来源表' SAMPLE 'bjh_feed_resource_rf',
  //`properties` STRING COMMENT '城市/设备数据值' SAMPLE '{"device": "apple"}'

  // 昨日日期
  var YesterDay = ""
  // 昨日计算日期 yyyy-MM-dd
  var YesterClacDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 今日计算日期 yyyy-MM-dd
  var ToClacDay = ""
  //回环IP（127.0.0.1）、无意义IP（0.0.0.0、***************、空）
  val ipList = List("127.0.0.1","0.0.0.0","***************","")

  /**
   * 用户关联表
   * @param args
   */
  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 昨日计算日期 yyyy-MM-dd
    YesterClacDay = calcnDateFormat(YesterDay)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 今日计算日期 yyyy-MM-dd
    ToClacDay = calcnDateFormat(ToDay)
    //单独获取某个表的数据写入
    //val tableName = args(1)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("userRelation801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    // 读取用户属性维数据,	分区大小3.2T左右
    val tiebaDwdSql =
      s"""
         |select
         |    uid,
         |    ip,
         |    cuid,
         |    os,
         |    brand,
         |    device,
         |    country,
         |    province,
         |    city
         |from
         |    things_turing_ns.ubs_tieba.view_tieba_dwd_log_qi_crc_data
         |where
         |  event_day = '${YesterDay}'
         |  and bhv_key in ('cforumapi/pb/page', 'cforumapi/pb/picpage', 'forum/Pb' , '6707')
         |  and uid > 0
         |  and (
         |    ip not in ('127.0.0.1','0.0.0.0','***************','')
         |    or cuid != ''
         |  )
         |""".stripMargin

    println("开始读取tieba_dwd_log_qi表")
    var tiebaResDf = getSqlData(spark,tiebaDwdSql,"IP,CUID")

    val tiebaCount = tiebaResDf.count()
    if(tiebaCount > 0){
      tiebaResDf = tiebaResDf
        .withColumn("source",lit("tieba_dwd_log_qi"))
        .repartition(10)
    }else{
      println("tieba_dwd_log_qi表无数据")
    }

    //全业务线cuid、did、uid映射关系及活跃信息，分区5T左右数据
    val odDimSql =
      s"""
         |select
         |    uid,
         |    cuid,
         |    os,
         |    brand,
         |    device
         |from
         |    things_turing_ns.od_data.view_od_dim_user_active_state_df_crcc
         |where
         |  event_day = '${YesterDay}'
         |  and last_uid_active_day = '${YesterDay}'
         |  and uid > 0
         |  and cuid != ''
         |""".stripMargin

    println("开始读取od_dim_user_active_state_df表")
    var odResDf = getSqlData(spark,odDimSql,"CUID")
    val odCount = odResDf.count()
    if(odCount > 0){
      odResDf = odResDf
        .withColumn("source",lit("od_dim_user_active_state_df"))
        .repartition(10)
    }else{
      println("od_dim_user_active_state_df表无数据")
      sys.exit(0)
    }

    var totalResDf = tiebaResDf
    totalResDf = unionIfNotEmpty(totalResDf, odResDf)

    totalResDf.createOrReplaceTempView("res_data")

    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
         |select
         |   uid,
         |   type,
         |   value,
         |   source,
         |   properties
         |from res_data
         |""".stripMargin)

    spark.stop()
  }
}

