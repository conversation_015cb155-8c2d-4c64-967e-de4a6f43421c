package com.baidu.sql.customized.files801

import com.alibaba.fastjson.JSON
import com.baidu.sql.customized.files801.Label801BjhAppId.IpBasedTokenBucketController
import com.baidu.sql.utils.SparkUtils.QpsController
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils.{convertToStandardJson, json_format_valid}
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, IntegerType, StringType, StructField, StructType}
import org.apache.spark.storage.StorageLevel

import java.util.concurrent.TimeUnit
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.sys.process._
import scala.util.Random

/**
 *  @author: zhangrunjie 801风控用户标签表，百家号元数据获取
 */
object UserRelation801MetaData {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""

  val RELEVANCE_STATUS = Map(
    "0" -> "间接关联",
    "1" -> "作者关联-自主",
    "2" -> "作者关联-代绑",
    "3" -> "作者关联-私信",
    "4" -> "运营关联"
  )

  val PLATFORM_SOURCE = Map(
    "1" -> "抖音",
    "2" -> "B站",
    "3" -> "快手",
    "4" -> "头条",
    "5" -> "微博",
    "6" -> "微信公众号",
    "7" -> "爱奇艺",
    "8" -> "小红书",
    "9" -> "企鹅号",
    "10" -> "知乎",
    "11" -> "逛逛",
    "12" -> "视频号"
  )

  val DOUYIN_STATUS = Map(
    "1" -> "可用",
    "-1" -> "注销",
    "-2" -> "封禁",
    "-3" -> "禁言",
    "" -> "未抓到"
  )

  val okHttpClient = new OkHttpClient.Builder()
    .callTimeout(40, TimeUnit.SECONDS)
    .connectTimeout(40, TimeUnit.SECONDS)
    .readTimeout(40, TimeUnit.SECONDS)
    .build()

  var httpUrl = ""

  // 添加分布式QPS控制的广播变量
  @transient lazy val qpsController = new QpsController(10) // 每秒5个请求

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 并发数
    val asyncNum = args(1).toInt

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 1. 执行命令行获取服务实例信息
    val commandOutput = "get_instance_by_service -i group.mcs-in.superpage.all".!!
    println(s"命令输出: $commandOutput")

    // 2. 解析IP和端口（根据实际输出格式调整正则）
    val ipPortPattern = """(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})""".r
    val serviceInstances = ipPortPattern.findAllMatchIn(commandOutput).map { m =>
      m.group(1) // ip
    }.toList
    println(s"服务实例: $serviceInstances")
    // 根据IP数量动态调整分区数和QPS
    val availableIpCount = serviceInstances.length
    val maxQpsPerIp = args(2).toInt // 每个IP最大QPS
    val totalMaxQps = availableIpCount * maxQpsPerIp
    val safetyBuffer = 0.9 // 90%安全系数
    val targetGlobalQps = (totalMaxQps * safetyBuffer).toInt

    // 分区数设置：建议为IP数量的1-2倍，确保负载均衡
    val optimalPartitionCount = Math.min(availableIpCount * 2, asyncNum) // 最多40个分区

    println(s"目标全局QPS: $targetGlobalQps, 分区数: $optimalPartitionCount")

    // 创建基于IP的QPS控制器
    val ipBasedQpsController = new IpBasedTokenBucketController(serviceInstances)
    val broadcastQpsController = spark.sparkContext.broadcast(ipBasedQpsController)

    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(author_user_id as long) as uid,
         |  rmb_self_build_url_http,
         |  coalesce(
         |    regexp_extract(
         |      regexp_replace(rmb_self_build_url_http, '/+$$', ''),
         |      '.*/([^/\\.]+)\\.mp4$$',
         |      1
         |    ),
         |    'default_mid'
         |  ) as mid
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp}
         |  and rmb_self_build_url_http is not null
         |  and rmb_self_build_url_http != ''
         |  and type3 in ('littlevideo', 'shortvideo')
         |""".stripMargin

    val bjhDf = spark.sql(bjhSql)
      //调整并发数
      .repartition(asyncNum)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号一天内数据共：" + bjhDf.count())
    bjhDf.createOrReplaceTempView("bjh_data")
    //bjhDf.show(10,false)

    /*val sessionDf = Seq(
      ("6890647005","bjh_feed_resource_rf","VEDIO_METADATA_UID","pddlive","""{"logid":"1964287330","status":1,"data":{"videoInfoExt":{"default":{"vodMoovSize":15183,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/360p/h264/**********162337858/mda-rgvcvfpxbj96wz3w.mp4","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/360p/h264/**********162337858/mda-rgvcvfpxbj96wz3w.mp4","vodVideoSize":1531173,"vodVideoBps":408309,"vodVideoRate":25,"vodVideoHW":"640$$360"},"hd":{"vodMoovSize":15222,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/576p/h264/**********823844157/mda-rgvcvfpxbj96wz3w.mp4","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/576p/h264/**********823844157/mda-rgvcvfpxbj96wz3w.mp4","vodVideoSize":2988869,"vodVideoBps":797030,"vodVideoRate":25,"vodVideoHW":"1024$$576"},"sc":{"vodMoovSize":15302,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p/h264/**********173896007/mda-rgvcvfpxbj96wz3w.mp4","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p/h264/**********173896007/mda-rgvcvfpxbj96wz3w.mp4","vodVideoSize":5071973,"vodVideoBps":1352529,"vodVideoRate":25,"vodVideoHW":"1280$$720"},"audio_mp3":{"vodMoovSize":0,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/audio/1753866307379326806/mda-rgvcvfpxbj96wz3w.mp3","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/audio/1753866307379326806/mda-rgvcvfpxbj96wz3w.mp3","vodVideoSize":360355,"vodVideoBps":96092,"vodVideoRate":0,"vodVideoHW":"0$$0"}},"downloadVideoInfoExt":{"shoubai":{"vodMoovSize":15302,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p_shoubai/h264_sblogo/**********117790068/mda-rgvcvfpxbj96wz3w.mp4","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p_shoubai/h264_sblogo/**********117790068/mda-rgvcvfpxbj96wz3w.mp4","vodVideoSize":5083162,"vodVideoBps":1355509,"vodVideoRate":25,"vodVideoHW":"1280$$720"},"haokan":{"vodMoovSize":15302,"vodVideoDuration":30,"urlHttps":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p_haokan/h264_hklogo/**********141495935/mda-rgvcvfpxbj96wz3w.mp4","urlHttp":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p_haokan/h264_hklogo/**********141495935/mda-rgvcvfpxbj96wz3w.mp4","vodVideoSize":5054241,"vodVideoBps":1347799,"vodVideoRate":25,"vodVideoHW":"1280$$720"}},"playableUrlList":{"default":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/360p/h264/**********162337858/mda-rgvcvfpxbj96wz3w.mp4","hd":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/576p/h264/**********823844157/mda-rgvcvfpxbj96wz3w.mp4","sc":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/720p/h264/**********173896007/mda-rgvcvfpxbj96wz3w.mp4"},"thumbnailList":["http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00000000.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00001041.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00002082.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00003122.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00004163.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00005204.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00006245.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00007286.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00008326.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00009367.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00010408.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00011449.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00012490.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00013530.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00014571.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00015612.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00016653.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00017693.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00018734.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00019775.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00020816.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00021857.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00022897.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00023938.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00024979.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00026020.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00027061.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00028101.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00029142.jpg","http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w00030183.jpg"],"sourceUrl":"http://vod-gechgi84v43uhfhp.bj.bcebos.com/src/mda-rgvcvfpxbj96wz3w?authorization=bce-auth-v1%2F0eac9da97d094ea5b9f06f6441c7a652%2F2025-07-30T09%3A05%3A24Z%2F-1%2Fhost%2F5adc855e37f7a0395cdc1b3187551af4aeb191a693cf1df6d04781221dffbd87","sourceMeta":{"bucket":"vod-gechgi84v43uhfhp","key":"src/mda-rgvcvfpxbj96wz3w","fileSizeInByte":22908978,"type":"video","container":"mov,mp4,m4a,3gp,3g2,mj2","durationInSecond":30,"durationInMillisecond":30180,"video":{"codec":"h264","heightInPixel":1280,"widthInPixel":720,"bitRateInBps":5969450,"frameRate":16000.0,"bitDepth":8,"colorPrimaries":"bt470bg","colorTrc":"unknown","colorSpace":"unknown"},"audio":{"codec":"aac","channels":2,"sampleRateInHz":44100,"bitRateInBps":96074},"userDefinedMetaData":"major_brand:isom,minor_version:512,compatible_brands:isomiso2avc1mp41,comment:PDDLiveEdit,title:{"uid"\:"7222165943214"},encoder:Lavf57.83.100","etag":"-2503ec6d2af24514e034a6bf37f0c2e9"},"http":{"file":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/360p/h264/**********162337858/mda-rgvcvfpxbj96wz3w.mp4","cover":"http://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w.jpg"},"https":{"file":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/360p/h264/**********162337858/mda-rgvcvfpxbj96wz3w.mp4","cover":"https://vd3.bdstatic.com/mda-rgvcvfpxbj96wz3w/**********/mda-rgvcvfpxbj96wz3w.jpg"},"meta":{"durationInSeconds":30,"sourceSizeInBytes":22908978,"sizeInBytes":42998751},"aiContent":{},"attributes":{"description":"","title":""},"codecStartTime":1753866307,"codecEndTime":1753866324,"error":{"code":"","message":"","err_no":0,"service_name":"","mct_code":""},"extraInfo":"{\"blackBorderDetect\":{\"x\":0,\"y\":0,\"width\":720,\"height\":1280,\"type\":4,\"is_vertical\":1,\"code\":0,\"msg\":\"OK\"}}","template":"online_template_watermark","status":"PUBLISHED","subStatus":"SUB_READY"},"request_params":{"nid":"mda-rgvcvfpxbj96wz3w","mediaId":"mda-rgvcvfpxbj96wz3w","sourceBucket":"vod-gechgi84v43uhfhp","sourceKey":"vod-gechgi84v43uhfhp/mda-rgvcvfpxbj96wz3w","business":"baijiahao_process","token":"xxxxxxxxxxxxxxxxxxxxx","description":"\"{\\\"media_name\\\":\\\"\\\\\\/storage\\\\\\/emulated\\\\\\/0\\\\\\/Android\\\\\\/data\\\\\\/com.baidu.searchbox\\\\\\/files\\\\\\/ugc\\\\\\/video\\\\\\/1753866295210.mp4\\\",\\\"author_id\\\":\\\"y3sbJAMLLtAuY-3Ikr2qng\\\",\\\"author_name\\\":\\\"\\u660e\\u5feb\\u4e14\\u7075\\u79c0\\u7684\\u6807\\u5175\\\",\\\"producer\\\":\\\"\\\"}\"","priority":4,"title":"baijiahao","transcodingPresetGroupName":"online_template_watermark"},"codec_time":{"codec_start_time":1753866307,"codec_sub_time":1753866321,"codec_end_time":1753866324}}"""),
      ("6837053836","bjh_feed_resource_rf","VEDIO_METADATA_UID","gifshow","""{"logid":"1078275789","status":1,"data":{"videoInfoExt":{"watermark":{"vodMoovSize":23313,"vodVideoDuration":43,"urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p_watermark/h264_cae/**********457098438/mda-rguvs3t00um2c3gb.mp4","urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p_watermark/h264_cae/**********457098438/mda-rguvs3t00um2c3gb.mp4","vodVideoSize":4503095,"vodVideoBps":837785,"vodVideoRate":30,"vodVideoHW":"960$$540"},"audio_mp3":{"vodMoovSize":0,"vodVideoDuration":43,"urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/audio_mp3/**********200155252/mda-rguvs3t00um2c3gb.m4a","urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/audio_mp3/**********200155252/mda-rguvs3t00um2c3gb.m4a","vodVideoSize":556890,"vodVideoBps":103608,"vodVideoRate":0,"vodVideoHW":"0$$0"},"default":{"vodMoovSize":23329,"vodVideoDuration":43,"urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p/mv_cae264_backtrack_540p_normal/1753822906164304640/mda-rguvs3t00um2c3gb.mp4","urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p/mv_cae264_backtrack_540p_normal/1753822906164304640/mda-rguvs3t00um2c3gb.mp4","vodVideoSize":4503473,"vodVideoBps":837857,"vodVideoRate":30,"vodVideoHW":"960$$540"},"sc":{"urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p/mv_cae264_backtrack_720p_normal/1753822906241005506/mda-rguvs3t00um2c3gb.mp4","urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p/mv_cae264_backtrack_720p_normal/1753822906241005506/mda-rguvs3t00um2c3gb.mp4","vodVideoSize":6487438,"vodVideoBps":1206968,"vodVideoRate":30,"vodVideoHW":"1280$$720","vodVideoDuration":43,"vodMoovSize":23845}},"downloadVideoInfoExt":{"shoubai":{"vodMoovSize":23789,"vodVideoDuration":43,"urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p_shoubai/h264_sblogo/**********449273910/mda-rguvs3t00um2c3gb.mp4","urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p_shoubai/h264_sblogo/**********449273910/mda-rguvs3t00um2c3gb.mp4","vodVideoSize":6364554,"vodVideoBps":1184102,"vodVideoRate":30,"vodVideoHW":"1280$$720"},"haokan":{"vodMoovSize":23789,"vodVideoDuration":43,"urlHttps":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p_haokan/h264_hklogo/**********286697909/mda-rguvs3t00um2c3gb.mp4","urlHttp":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p_haokan/h264_hklogo/**********286697909/mda-rguvs3t00um2c3gb.mp4","vodVideoSize":6240408,"vodVideoBps":1161011,"vodVideoRate":30,"vodVideoHW":"1280$$720"}},"playableUrlList":{"watermark":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p_watermark/h264_cae/**********457098438/mda-rguvs3t00um2c3gb.mp4","default":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p/mv_cae264_backtrack_540p_normal/1753822906164304640/mda-rguvs3t00um2c3gb.mp4","sc":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/720p/mv_cae264_backtrack_720p_normal/1753822906241005506/mda-rguvs3t00um2c3gb.mp4"},"thumbnailList":["http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00000000.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00001498.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00002996.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00004494.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00005992.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00007490.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00008988.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00010486.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00011984.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00013482.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00014980.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00016478.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00017976.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00019474.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00020972.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00022470.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00023968.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00025466.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00026964.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00028462.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00029960.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00031458.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00032956.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00034454.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00035952.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00037450.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00038948.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00040446.jpg","http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb00041944.jpg"],"sourceUrl":"http://vod-magic-pen.bj.bcebos.com/src/mda-rguvs3t00um2c3gb.mp4?authorization=bce-auth-v1%2F0eac9da97d094ea5b9f06f6441c7a652%2F2025-07-29T21%3A01%3A33Z%2F-1%2Fhost%2F89362bad6341db80af1afdc546d324f6497e8bce65404902c284a39713716372","sourceMeta":{"bucket":"vod-magic-pen","key":"src/mda-rguvs3t00um2c3gb.mp4","fileSizeInByte":11140792,"type":"video","container":"mov,mp4,m4a,3gp,3g2,mj2","durationInSecond":43,"durationInMillisecond":43440,"video":{"codec":"h264","heightInPixel":1280,"widthInPixel":720,"bitRateInBps":1914080,"frameRate":29.67,"bitDepth":8,"colorPrimaries":"unknown","colorTrc":"unknown","colorSpace":"unknown"},"audio":{"codec":"aac","channels":2,"sampleRateInHz":44100,"bitRateInBps":131390},"userDefinedMetaData":"major_brand:isom,minor_version:512,compatible_brands:isomiso2avc1mp41,encoder:Lavf61.4.100,comment:GIFSHOW [524849321][HUAWEI|VCE-AL00][10|29][HWVCE][7.6.50.15529][Camera\\:b][RealFps\\:29.8][DaenerysVersion\\:r4.7.296.4b65d1ded[Encode\\:hardware_encode][EditorVer\\:fullScreen3][BeautyType\\:ks][0\\:0.87][pipeline]#[1080x1920][29.67fps][10957k][173k][C][7][tv=5d4f_0509][audioGain=1.2211][uploadSource=APP][p=hAFR6roQNgzxASGll17Ktg==]","etag":"-35a61375b790400f551d9aedbbe3beab"},"http":{"file":"http://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p/mv_cae264_backtrack_540p_normal/1753822906164304640/mda-rguvs3t00um2c3gb.mp4","cover":"http://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb.jpg"},"https":{"file":"https://vd9.bdstatic.com/mda-rguvs3t00um2c3gb/mb/540p/mv_cae264_backtrack_540p_normal/1753822906164304640/mda-rguvs3t00um2c3gb.mp4","cover":"https://vd9.bdstatic.com//mda-rguvs3t00um2c3gb/mb/**********/mda-rguvs3t00um2c3gb.jpg"},"meta":{"durationInSeconds":43,"sourceSizeInBytes":11140792,"sizeInBytes":39796650},"aiContent":{},"attributes":{"description":"","title":""},"codecStartTime":1753822846,"codecEndTime":1753822893,"error":{"code":"","message":"","err_no":0,"service_name":"","mct_code":""},"extraInfo":"","template":"online_sbqm_sv_clip_720p","status":"PUBLISHED","subStatus":"SUB_READY"},"request_params":{"nid":"mda-rguvs3t00um2c3gb","mediaId":"mda-rguvs3t00um2c3gb","sourceBucket":"vod-magic-pen","sourceKey":"vod-magic-pen/mda-rguvs3t00um2c3gb.mp4","business":"quanmin_process","token":"xxxxxxxxxxxxxxxxxxxxx","description":"{\"download_source_name\":5,\"pub_source_from\":\"magic_pen_taihang_video\",\"transport_key\":\"{\\\"third_source_id\\\":\\\"8b859ae6fcd89428a6cd\\\",\\\"commit_ip\\\":\\\"***************\\\",\\\"search_share\\\":2,\\\"api_transport_info\\\":\\\"{\\\\\\\"baike_article_id\\\\\\\":0,\\\\\\\"search_query\\\\\\\":\\\\\\\"\\\\u4e0d\\\\u8bc6\\\\u5e90\\\\u5c71\\\\\\\",\\\\\\\"search_source_from\\\\\\\":\\\\\\\"magic_pen_taihang_video\\\\\\\",\\\\\\\"search_url\\\\\\\":\\\\\\\"www.kuaishou.com\\\\/short-video\\\\/3x6itsychkmza32\\\\\\\"}\\\",\\\"third_publish_at\\\":1598913711}\",\"upload_time\":\"2025-07-30 05:00:45 AM\",\"user_id\":\"6837053836\"}","title":"miaobimv_10.176.84.164_mp4","transcodingPresetGroupName":"online_sbqm_sv_clip_720p","mode":"miaobi","source_name":"magic_pen"},"codec_time":{"codec_start_time":1753822846,"codec_sub_time":1753822878,"codec_end_time":1753822893}}"""),
      ("4674264150","bjh_feed_resource_rf","douyin","douyin","{'logid':'1006425517','status':1,'data':{'downloadVideoInfoExt':{'shoubai':{'vodMoovSize':13266,'vodVideoDuration':26,'urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/720p_shoubai/h264_sblogo/1753848884046938183/mda-rgv609gqgrv6mm3q.mp4','urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/720p_shoubai/h264_sblogo/1753848884046938183/mda-rgv609gqgrv6mm3q.mp4','vodVideoSize':6426892,'vodVideoBps':1977507,'vodVideoRate':25,'vodVideoHW':'720$$1280'},'haokan':{'vodMoovSize':13338,'vodVideoDuration':26,'urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/720p_haokan/h264_hklogo/1753848883937058387/mda-rgv609gqgrv6mm3q.mp4','urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/720p_haokan/h264_hklogo/1753848883937058387/mda-rgv609gqgrv6mm3q.mp4','vodVideoSize':6458974,'vodVideoBps':1987379,'vodVideoRate':25,'vodVideoHW':'720$$1280'}},'videoInfoExt':{'default':{'gopAlign':true,'vodMoovSize':13172,'urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/cae_h264/1753848931210283782/mda-rgv609gqgrv6mm3q.mp4','urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/cae_h264/1753848931210283782/mda-rgv609gqgrv6mm3q.mp4','vodVideoSize':2547162,'vodVideoBps':783738,'vodVideoRate':25,'vodVideoHW':'360$$640','vodVideoDuration':26},'hd':{'vodMoovSize':13266,'vodVideoDuration':26,'vodVideoSize':3506124,'vodVideoBps':1078804,'vodVideoRate':25,'vodVideoHW':'576$$1024','urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/576p/h264/1753848884133030777/mda-rgv609gqgrv6mm3q.mp4','urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/576p/h264/1753848884133030777/mda-rgv609gqgrv6mm3q.mp4'},'sc':{'urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/sc/cae_h264/1753848931247149833/mda-rgv609gqgrv6mm3q.mp4','urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/sc/cae_h264/1753848931247149833/mda-rgv609gqgrv6mm3q.mp4','vodVideoSize':4675025,'vodVideoBps':1438474,'vodVideoRate':25,'vodVideoHW':'720$$1280','vodVideoDuration':26,'vodMoovSize':13371,'gopAlign':true},'1080p':{'urlHttp':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1080p/cae_h264/1753848931253742373/mda-rgv609gqgrv6mm3q.mp4','urlHttps':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1080p/cae_h264/1753848931253742373/mda-rgv609gqgrv6mm3q.mp4','vodVideoSize':9432675,'vodVideoBps':2902364,'vodVideoRate':25,'vodVideoHW':'1080$$1920','vodVideoDuration':26,'vodMoovSize':13414,'gopAlign':true}},'playableUrlList':{'default':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/cae_h264/1753848931210283782/mda-rgv609gqgrv6mm3q.mp4','hd':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/576p/h264/1753848884133030777/mda-rgv609gqgrv6mm3q.mp4','sc':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/sc/cae_h264/1753848931247149833/mda-rgv609gqgrv6mm3q.mp4','1080p':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1080p/cae_h264/1753848931253742373/mda-rgv609gqgrv6mm3q.mp4'},'thumbnailList':['http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848883/mda-rgv609gqgrv6mm3q.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00000000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00001000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00002000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00003000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00004000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00005000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00006000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00007000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00008000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00009000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00010000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00011000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00012000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00013000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00014000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00015000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00016000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00017000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00018000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00019000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00020000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00021000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00022000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00023000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00024000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00025000.jpg','http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848884/mda-rgv609gqgrv6mm3q00026000.jpg'],'sourceUrl':'http://vod-gechgi84v43uhfhp.bj.bcebos.com/src/mda-rgv609gqgrv6mm3q?authorization=bce-auth-v1%2F0eac9da97d094ea5b9f06f6441c7a652%2F2025-07-30T04%3A14%3A58Z%2F-1%2Fhost%2F6232dba55b61cb5ebc4778a3a265147775534289898eea156180fce6f0402636','sourceMeta':{'bucket':'vod-gechgi84v43uhfhp','key':'src/mda-rgv609gqgrv6mm3q','fileSizeInByte':21360987,'type':'video','container':'mov,mp4,m4a,3gp,3g2,mj2','durationInSecond':26,'durationInMillisecond':26030,'video':{'codec':'hevc','heightInPixel':1080,'widthInPixel':1920,'bitRateInBps':6429891,'frameRate':30.0,'bitDepth':8,'colorPrimaries':'bt709','colorTrc':'bt709','colorSpace':'bt709'},'audio':{'codec':'aac','channels':2,'sampleRateInHz':44100,'bitRateInBps':128126},'userDefinedMetaData':'major_brand:isom,minor_version:512,compatible_brands:isomiso2hvc1mp41,DreaminaMetaInfo:{},Hw:1,LvMetaInfo:{\\\"data\\\"\\\\:{\\\"appVersion\\\"\\\\:\\\"16.7.0\\\"\\\\,\\\"capabilityName\\\"\\\\:\\\"general_1\\\"\\\\,\\\"did\\\"\\\\:\\\"uetwtrequqouioui\\\"\\\\,\\\"editSource\\\"\\\\:\\\"\\\"\\\\,\\\"editType\\\"\\\\:\\\"edit\\\"\\\\,\\\"effect_id\\\"\\\\:\\\"general_1\\\"\\\\,\\\"effect_type\\\"\\\\:\\\"tool\\\"\\\\,\\\"enterFrom\\\"\\\\:\\\"\\\"\\\\,\\\"exportType\\\"\\\\:\\\"export\\\"\\\\,\\\"extendTemplateId\\\"\\\\:\\\"\\\"\\\\,\\\"extendTemplateType\\\"\\\\:0\\\\,\\\"firstLaunchMethod\\\"\\\\:\\\"enter_launch\\\"\\\\,\\\"global_data_for_metadata_write\\\"\\\\:\\\"\\\"\\\\,\\\"infoStickerId\\\"\\\\:\\\"\\\"\\\\,\\\"is_use_audio_separation\\\"\\\\:1\\\\,\\\"launchMode\\\"\\\\:\\\"launch\\\"\\\\,\\\"lock_cnt_list\\\"\\\\:\\\"\\\"\\\\,\\\"movie3dTextTemplateId\\\"\\\\:\\\"\\\"\\\\,\\\"os\\\"\\\\:\\\"android\\\"\\\\,\\\"product\\\"\\\\:\\\"lv\\\"\\\\,\\\"room_id\\\"\\\\:\\\"\\\"\\\\,\\\"slowMotion\\\"\\\\:\\\"none\\\"\\\\,\\\"source_capabilityName\\\"\\\\:[\\\"filter\\\\,intelligent_edit\\\\,music\\\"]\\\\,\\\"source_templateId\\\"\\\\:[\\\"7420290809708334360\\\"]\\\\,\\\"stickerId\\\"\\\\:\\\"\\\"\\\\,\\\"templateId\\\"\\\\:\\\"\\\"\\\\,\\\"theme_params\\\"\\\\:\\\"\\\"\\\\,\\\"transferMethod\\\"\\\\:\\\"\\\"\\\\,\\\"uid\\\"\\\\:\\\"ioqtpetwitrwirq\\\"\\\\,\\\"videoEffectId\\\"\\\\:\\\"\\\"\\\\,\\\"videoId\\\"\\\\:\\\"b06e852f-b1ff-481d-ba75-2b9664fcf9c7\\\"\\\\,\\\"videoParams\\\"\\\\:{\\\"st\\\"\\\\:1\\\\,\\\"ef\\\"\\\\:0\\\\,\\\"be\\\"\\\\:0\\\\,\\\"tx\\\"\\\\:0\\\\,\\\"mu\\\"\\\\:0\\\\,\\\"ft\\\"\\\\:0\\\\,\\\"te\\\"\\\\:0\\\\,\\\"re\\\"\\\\:0\\\\,\\\"ma\\\"\\\\:0\\\\,\\\"v\\\"\\\\:0\\\\,\\\"me\\\"\\\\:0\\\\,\\\"vs\\\"\\\\:0\\\\,\\\"sp\\\"\\\\:0}}\\\\,\\\"source_type\\\"\\\\:\\\"douyin_beauty_me\\\"},bitrate:12000000,maxrate:30000000,te_is_reencode:1,encoder:Lavf57.71.100','etag':'-********************************'},'http':{'file':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/cae_h264/1753848931210283782/mda-rgv609gqgrv6mm3q.mp4','cover':'http://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848883/mda-rgv609gqgrv6mm3q.jpg'},'https':{'file':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/cae_h264/1753848931210283782/mda-rgv609gqgrv6mm3q.mp4','cover':'https://vd3.bdstatic.com/mda-rgv609gqgrv6mm3q/1753848883/mda-rgv609gqgrv6mm3q.jpg'},'meta':{'durationInSeconds':26,'sourceSizeInBytes':21360987,'sizeInBytes':54407839},'aiContent':{},'attributes':{'description':'','title':''},'codecStartTime':1753848870,'codecEndTime':1753848898,'error':{'code':'','message':'','err_no':0,'service_name':'','mct_code':''},'extraInfo':'{\\\"blackBorderDetect\\\":{\\\"x\\\":0,\\\"y\\\":0,\\\"width\\\":1920,\\\"height\\\":1080,\\\"type\\\":4,\\\"is_vertical\\\":0,\\\"code\\\":0,\\\"msg\\\":\\\"OK\\\"},\\\"bizUser\\\":\\\"svCae\\\",\\\"trans_type\\\":\\\"cae\\\",\\\"nid\\\":\\\"15792546091960987499\\\"}','template':'online_template_haokan_cae264_backtrack_normal','status':'PUBLISHED','subStatus':'SUB_READY'},'request_params':{'nid':'mda-rgv609gqgrv6mm3q','mediaId':'mda-rgv609gqgrv6mm3q','sourceBucket':'vod-gechgi84v43uhfhp','sourceKey':'vod-gechgi84v43uhfhp/mda-rgv609gqgrv6mm3q','business':'baijiahao_process','token':'xxxxxxxxxxxxxxxxxxxxx','description':'\\\"{\\\\n  \\\\\\\"author_name\\\\\\\" : \\\\\\\"\\\\u597d\\\\u8fd0\\\\u6613\\\\u7f18\\\\\\\",\\\\n  \\\\\\\"author_id\\\\\\\" : \\\\\\\"-AZ0eA4myC6NVdUAMOG45w\\\\\\\",\\\\n  \\\\\\\"producer\\\\\\\" : \\\\\\\"\\\\\\\",\\\\n  \\\\\\\"media_name\\\\\\\" : \\\\\\\"\\\\\\\"\\\\n}\\\"','priority':4,'title':'baijiahao','transcodingPresetGroupName':'online_template_watermark'},'codec_time':{'codec_start_time':1753848870,'codec_sub_time':1753848898,'codec_end_time':1753848898}}"),
    ).toDF("uid","source","type","properties","responseJson")*/
    //sessionDf.show(false)

    //接口数据获取 - 使用mapPartitions进行分布式处理
    val sessionDf = bjhDf
      .filter($"mid" =!= "")
      .select(
        col("mid").cast("string"),
        col("uid").cast("string")
      )
      .repartition(optimalPartitionCount) // 动态分区数
      .mapPartitions { partition =>
        val qpsCtrl = broadcastQpsController.value
        partition.map { row =>
          val mid = row.getString(0)
          val uid = row.getString(1)
          val responseJson = processRequestWithTokenControl(mid, qpsCtrl)
          (mid, uid, responseJson)
        }
      }
      .toDF("mid","uid", "responseJson")
      .filter($"responseJson" =!= "" && $"responseJson".isNotNull)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("接口请求数据共：" + sessionDf.count())
    sessionDf.show(5,false)

    // 先定义schema
    val metaSchema = StructType(Seq(
      StructField("data", StructType(Seq(
        StructField("sourceMeta", StructType(Seq(
          StructField("userDefinedMetaData", StringType)
        )))
      ))
      ))
    )

    val resLabelDf = sessionDf
      .withColumn("data_source",
        when(col("responseJson").contains("PDDLiveEdit"),"PDDLIVE")
        .when(col("responseJson").contains("GIFSHOW"),"GIFSHOW")
        .when(col("responseJson").contains("douyin_beauty_me") || col("responseJson").contains("com.bytedance.info"),"DOUYIN")
        .otherwise("")
      )
      .filter(col("data_source") =!= "")
      .withColumn("properties",
        when(col("data_source") === "PDDLIVE","pddlive")
          .when(col("data_source") === "GIFSHOW","gifshow")
          .when(col("data_source") === "DOUYIN","douyin")
          .otherwise("")
      )
      .withColumn("type",
        when(col("data_source") === "PDDLIVE","VEDIO_METADATA_UID")
          .when(col("data_source") === "GIFSHOW","VEDIO_METADATA_UID")
          .when(col("data_source") === "DOUYIN","douyin")
          .otherwise("")
      )
      .withColumn("source",lit("bjh_feed_resource_rf"))
      .withColumn("jsonObj",
        when($"responseJson".isNotNull && $"responseJson" =!= "" &&
          json_format_valid($"responseJson"),
          from_json($"responseJson", metaSchema)
        ).otherwise(
          lit(null).cast(metaSchema)  // 关键修改：返回空结构体而非字符串
        ))
      .withColumn("meta_data",
        when($"responseJson".isNotNull && $"responseJson" =!= "" &&
          json_format_valid($"responseJson"),  // 验证JSON格式
          get_json_object($"responseJson", "$.data.sourceMeta.userDefinedMetaData").cast("string")
          ).otherwise(lit("")))
      .withColumn("value",
        when(col("properties") === "douyin",extractDIDandUID(col("meta_data")))
        .when(col("properties") === "gifshow",extractGifshowData(col("meta_data")))
        .when(col("properties") === "pddlive",extractUID(col("meta_data")))
        .otherwise(lit("")))
      .select("uid","source","type","properties","meta_data","value")
      .filter($"value" =!= "")
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    resLabelDf.createOrReplaceTempView("res_label_data")
    //resLabelDf.show(false)

    val resDf = spark.sql(
      """
        |with tmp_data as (
        |select
        | uid,
        | source,
        | type,
        | properties,
        | meta_data,
        | explode(split(value,',')) as value
        |from res_label_data
        |)
        |select
        |   uid,
        |   source,
        |   case when value like 'uid%' then 'VEDIO_METADATA_UID'
        |   when value like 'did%' then 'VEDIO_METADATA_DID'
        |   else type end as type,
        |   properties,
        |   case when value like '%:%' then split(value,':')[1]
        |   else value end as value
        |from tmp_data
        |""".stripMargin)
      .filter($"value" =!= "")

    println("最终数据共：" + resLabelDf.count())
    resDf.show(5,false)
    resLabelDf.createOrReplaceTempView("res_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
         |select
         |   uid,
         |   type,
         |   value,
         |   source,
         |   properties
         |from res_data
         |""".stripMargin)

    resLabelDf.unpersist()
    spark.stop()
  }

  //获取datalist字段
  val getDatalist = udf((data: mutable.WrappedArray[String]) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      val res = ListBuffer[String]()
      data.foreach(row => {
        //获取json数据
        val json = JSON.parseObject(row)
        //关联状态
        val relevance_status = json.getOrDefault("relevance_status","").toString
        //平台来源
        val platform_source = json.getOrDefault("platform_source","").toString
        //状态
        val status = json.getOrDefault("status","").toString
        if (platform_source.equals("1")){
          res += Seq(platform_source,relevance_status,status).mkString("=>")
        }
      })
      res.mkString("<=>")
    }
  })

  //获取关联状态字段
  val getrelevanceStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      RELEVANCE_STATUS.getOrElse(data,"")
    }
  })

  //获取状态字段
  val getStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      DOUYIN_STATUS.getOrElse(data,"")
    }
  })

  // 定义UDF函数
  val extractDIDandUID = udf((inputStr: String) => {
    // 步骤1：提取LvMetaInfo字段中的JSON块
    val lvMetaPattern = """LvMetaInfo:(\{.*?}),""".r
    val lvMetaJson = lvMetaPattern.findFirstMatchIn(inputStr) match {
      case Some(m) => m.group(1).replace("\\\"", "\"").replace("\\:", ":") // 清理转义字符
      case None    => ""
    }

    // 步骤2：正则直接提取目标字段（高效且避免全量JSON解析）
    val didPattern = """\"did\"\:\"([^\"]+)\"""".r
    val uidPattern = """\"uid\"\:\"([^\"]+)\"""".r

    val did = didPattern.findFirstMatchIn(lvMetaJson).map(_.group(1)).getOrElse("")
    val uid = uidPattern.findFirstMatchIn(lvMetaJson).map(_.group(1)).getOrElse("")

    s"did:$did,uid:$uid" // 返回组合格式
  })

  // 定义提取GIFSHOW数据的UDF
  val extractGifshowData = udf { (metaData: String) =>

    // 1. 查找"GIFSHOW"关键字的起始位置
    val gifshowIndex = metaData.indexOf("GIFSHOW")
    if (gifshowIndex == -1) ""
    else {
      val afterGifshow = metaData.substring(gifshowIndex + 7) // 跳过"GIFSHOW"长度(7字符)

      // 2. 定位第一个左括号的位置
      val startIndex = afterGifshow.indexOf('[')
      if (startIndex == -1) ""
      else {
        // 3. 定位匹配的右括号位置
        var openBrackets = 1
        var currentIndex = startIndex + 1
        var endIndex = -1

        while (openBrackets > 0 && currentIndex < afterGifshow.length) {
          afterGifshow.charAt(currentIndex) match {
            case '[' => openBrackets += 1
            case ']' =>
              openBrackets -= 1
              if (openBrackets == 0) endIndex = currentIndex
            case _ => // 继续遍历
          }
          currentIndex += 1
        }

        // 4. 提取括号内容并验证数字
        if (endIndex == -1) ""
        else {
          val content = afterGifshow.substring(startIndex + 1, endIndex)
          if (content.forall(_.isDigit)) "uid:" + content else ""
        }
      }
    }
  }

  /**
   * 提取PDDLIVE中的uid字段数据
   */
  val extractUID = udf((inputStr: String) => {
    // 1. 提取title字段的JSON值
    val titlePattern = """title:(\{.*?})""".r
    val titleJson = titlePattern.findFirstMatchIn(inputStr) match {
      case Some(m) => m.group(1)
      case None => ""
    }

    // 2. 清理转义字符（处理\"和\:）
    val cleanedJson = titleJson.replace("\\\"", "\"").replace("\\:", ":")

    // 3. 从清理后的JSON中提取uid值
    val uidPattern = """"uid":"([^"]+)"""".r
    val uid = uidPattern.findFirstMatchIn(cleanedJson) match {
      case Some(m) => m.group(1)
      case None => ""
    }

    // 4. 返回指定格式
    s"uid:$uid"
  })

  /**
   * 发送请求获取数据
   * @param mid
   * @param maxRetries
   * @return
   */
  def postRequestTest(mid: String, maxRetries: Int = 3): String = {
    var result = ""
    // 初始化重试次数
    var attempts = 0

    // 循环尝试获取数据
    while (attempts < maxRetries && result.isEmpty) {
      attempts += 1

      val httpurl = httpUrl + mid
      //println(s"请求接口：$httpurl")

      // 口令获取
      var response: Response = null

      val request = new Request.Builder()
        .url(httpurl)
        .get()
        .build

      try {
        response = okHttpClient.newCall(request).execute
        if (response.isSuccessful) {
          result = response.body.string
        } else {
          result = "Empty response"
        }
      } catch {
        case _: Exception =>
          result = "Empty response"
      } finally {
        if (response != null) response.close()
      }
    }
    result
  }

  /**
   * 带令牌桶控制的请求处理函数
   */
  def processRequestWithTokenControl(mid: String, ipTokenController: IpBasedTokenBucketController): String = {
    var result = ""
    var attempts = 0
    val maxRetries = 3

    while (attempts < maxRetries && result.isEmpty) {
      attempts += 1

      // 获取可用IP和对应的令牌桶控制器
      val (ip, tokenController) = ipTokenController.getAvailableIpWithTokenControl()

      try {
        // 获取令牌（会自动等待直到有可用令牌）
        tokenController.acquire()

        val httpUrlLocal = s"http://${ip}:9912/xvision/tradition/mediainfo/detail?mediaid="
        val httpurl = httpUrlLocal + mid

        var response: Response = null

        try {
          val request = new Request.Builder()
            .url(httpurl)
            .get()
            .build()

          response = okHttpClient.newCall(request).execute()

          if (response.isSuccessful) {
            result = response.body.string
          }
        } catch {
          case _: Exception => // 忽略异常，继续重试
        } finally {
          if (response != null) response.close()
        }

        // 响应结果处理
        if (result.nonEmpty) {
          // 成功获取到响应内容，保持result
        } else {
          // 空响应，重置结果继续重试
          result = ""
          // 指数退避策略 + 随机抖动，避免重试集中
          val baseDelay = 100 * Math.pow(2, attempts).toLong
          val jitter = Random.nextInt(50) // 0-50ms随机抖动
          Thread.sleep(baseDelay + jitter)
        }
      }
    }
    result
  }
}

