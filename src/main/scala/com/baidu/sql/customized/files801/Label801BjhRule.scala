package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.mergeAndAddColumns
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.spark.sql.functions.{col, regexp_replace, when}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel

import java.io.{BufferedReader, ByteArrayInputStream, InputStreamReader}
import java.net.URLDecoder
import java.util.concurrent.TimeUnit
import java.util.zip.GZIPInputStream

/**
 *  @author: zhangrunjie 801风控用户标签表，百家号规则数据标签
 */
object Label801BjhRule {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""
  var day2 = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    day2 = calcnDate(YesterDay, -2)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    val bjhCateDf = spark.sql(
        s"""
          |WITH base_data AS (
          |    -- 一次性读取所有需要的数据（包含时间范围内、内容类型、敏感标签）
          |    SELECT
          |        author_user_id AS uid,
          |        first_category,
          |        second_category,
          |        click_publish_time,
          |        -- 判断该条记录是否属于“敏感垂类”
          |        CASE
          |            WHEN first_category IN ('社会', '历史', '国际', '时事') THEN 1
          |            WHEN second_category IN (
          |                '民生', '中国现代史', '政法', '国际社会','财经综合','中国古代史',
          |                '中国近代史','国际时政','军事历史','台海时事','金融市场','国内时政',
          |                '环球军事','历史综合','军事综合','武器装备','国际关系','国际经济',
          |                '政策法规','世界历史','法律条例','秀颜值','秀身材','两性','性感热舞',
          |                '舞蹈综合','艺术舞蹈'
          |            ) THEN 1
          |            ELSE 0
          |        END AS is_sensitive,
          |        -- 计算发文距离今天的天数
          |        datediff(current_date, click_publish_time) AS publish_gap_days
          |    FROM
          |        bjh_data.bjh_feed_resource_rf
          |    WHERE
          |        event_day = '${YesterDay}'
          |        AND type3 IN ('news', 'shortvideo', 'littlevideo', 'gallery', 'dt_image_text', 'dt_text', 'dt_ugc_video')
          |        AND datediff(current_date, click_publish_time) <= 45  -- 统一限制为近 45 天
          |),
          |
          |-- 提取每个用户在【近 45 天】内的唯一垂类集合（一级+二级）
          |category_all AS (
          |    SELECT
          |        uid,
          |        count(distinct category) category_num
          |    FROM (
          |        SELECT
          |            uid,
          |            first_category AS category
          |        FROM
          |            base_data
          |        WHERE
          |            first_category IS NOT NULL
          |            and is_sensitive = 1
          |        GROUP BY 1, 2
          |
          |        UNION ALL
          |
          |        SELECT
          |            uid,
          |            second_category AS category
          |        FROM
          |            base_data
          |        WHERE
          |            second_category IS NOT NULL
          |            and is_sensitive = 1
          |        GROUP BY 1, 2
          |    ) tmp
          |    GROUP BY 1
          |),
          |
          |-- 提取每个用户在【15-45 天】区间内的唯一垂类集合（一级+二级）
          |category_15_45 AS (
          |    SELECT
          |        uid,
          |        count(distinct category) category_num
          |    FROM (
          |        SELECT
          |            uid,
          |            first_category AS category
          |        FROM
          |            base_data
          |        WHERE
          |            first_category IS NOT NULL
          |            AND is_sensitive = 1
          |            AND publish_gap_days BETWEEN 15 AND 45
          |        GROUP BY 1, 2
          |
          |        UNION ALL
          |
          |        SELECT
          |            uid,
          |            second_category AS category
          |        FROM
          |            base_data
          |        WHERE
          |            second_category IS NOT NULL
          |            AND is_sensitive = 1
          |            AND publish_gap_days BETWEEN 15 AND 45
          |        GROUP BY 1, 2
          |    ) tmp
          |    GROUP BY 1
          |),
          |
          |-- 提取每个用户的【近 45 天内敏感垂类发文数量】
          |sensitive_post_count AS (
          |    SELECT
          |        uid,
          |        COUNT(1) AS mingan_num
          |    FROM
          |        base_data
          |    WHERE
          |        is_sensitive = 1
          |    GROUP BY
          |        1
          |)
          |
          |-- 主查询：整合统计信息并筛选
          |SELECT
          |    t1.uid
          |    -- t1.category_num AS all_category_num,  -- 近 45 天总垂类数
          |    -- t2.category_num AS category_15_45_num,       -- 15-45 天垂类数
          |    -- t3.mingan_num                                     -- 敏感垂类发文数量
          |FROM
          |    category_all t1
          |    LEFT JOIN category_15_45 t2 ON t1.uid = t2.uid
          |    LEFT JOIN sensitive_post_count t3 ON t1.uid = t3.uid
          |WHERE
          |    t3.mingan_num >= 3
          |    and t1.category_num > t2.category_num
          |""".stripMargin)
    val bjhCateResDf = mergeAndAddColumns("百家号","日常行为","发文、发帖、点赞、关注等行为","灰","其它lv3","发文垂类突变敏感垂类","80",bjhCateDf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("发文垂类突变敏感垂类百家号共：" + bjhCateResDf.count())

    val bjhRateDf = spark.sql(
      s"""
         |WITH user_data AS (
         |  SELECT
         |    author_user_id AS uid,
         |    event_day,
         |    status,
         |    click_publish_time,
         |    -- 标记是否为删除状态
         |    (status IN ('deleted', 'withdraw')) AS is_deleted,
         |    -- 标记当天活跃用户
         |    CASE WHEN event_day = '${YesterDay}'
         |          AND from_unixtime(unix_timestamp(click_publish_time), 'yyyyMMdd') = '${YesterDay}'
         |         THEN 1 ELSE 0 END AS is_active_today
         |  FROM bjh_data.bjh_feed_resource_rf
         |  WHERE event_day IN ('${YesterDay}', '${BeForeDay}', '${day2}')
         |    AND from_unixtime(unix_timestamp(click_publish_time), 'yyyyMMdd') = event_day  -- 确保时间对齐
         |    AND is_bjh = '1'
         |    AND type3 IN ('news', 'shortvideo', 'littlevideo', 'gallery', 'dt_image_text', 'dt_text', 'dt_ugc_video')
         |),
         |user_metrics AS (
         |  SELECT
         |    uid,
         |    -- 聚合三天的指标
         |    SUM(IF(event_day = '${YesterDay}', 1, 0)) AS num,
         |    SUM(IF(event_day = '${YesterDay}' AND status = 'publish', 1, 0)) AS publish_num,
         |    SUM(IF(event_day = '${YesterDay}' AND status IN ('deleted', 'withdraw'), 1, 0)) AS deleted_num,
         |
         |    SUM(IF(event_day = '${BeForeDay}', 1, 0)) AS num_2,
         |    SUM(IF(event_day = '${BeForeDay}' AND status = 'publish', 1, 0)) AS publish_num_2,
         |    SUM(IF(event_day = '${BeForeDay}' AND status IN ('deleted', 'withdraw'), 1, 0)) AS deleted_num_2,
         |
         |    SUM(IF(event_day = '${day2}', 1, 0)) AS num_3,
         |    SUM(IF(event_day = '${day2}' AND status = 'publish', 1, 0)) AS publish_num_3,
         |    SUM(IF(event_day = '${day2}' AND status IN ('deleted', 'withdraw'), 1, 0)) AS deleted_num_3,
         |
         |    -- 计算最大删除时间
         |    MAX(IF(status IN ('deleted', 'withdraw'), click_publish_time, NULL)) AS max_time
         |  FROM user_data
         |  GROUP BY uid
         |  HAVING SUM(is_active_today) > 0  -- 仅保留当天活跃用户
         |  and SUM(IF(event_day = '${YesterDay}', 1, 0)) > 10 -- 历史发文数大于10
         |  and SUM(IF(event_day = '${YesterDay}' AND status = 'publish', 1, 0)) <= 50 -- 历史成功发文数小于等于50
         |),
         |delete_stats AS (
         |  SELECT
         |    d.uid,
         |    SUM(IF(u.click_publish_time < d.max_time AND u.status IN ('deleted', 'withdraw'), 1, 0)) AS number,
         |    SUM(IF(u.click_publish_time < d.max_time, 1, 0)) AS denominator
         |  FROM user_metrics d
         |  JOIN user_data u ON d.uid = u.uid
         |  WHERE d.max_time IS NOT NULL  -- 仅处理有删除记录的用户
         |  GROUP BY d.uid
         |  HAVING SUM(IF(u.click_publish_time < d.max_time AND u.status IN ('deleted', 'withdraw'), 1, 0)) >= 10
         |),
         |delete_rate as (
         |SELECT
         |  '${YesterDay}' AS yimai_partition_col,
         |  m.uid,
         |  m.num AS history_posts,
         |  m.publish_num AS success_posts,
         |  m.deleted_num / NULLIF(m.num, 0) AS day1_delete_ratio,
         |  m.deleted_num_2 / NULLIF(m.num_2, 0) AS day2_delete_ratio,
         |  m.deleted_num_3 / NULLIF(m.num_3, 0) AS day3_delete_ratio,
         |  m.max_time AS last_delete_time,
         |  datediff(current_date, m.max_time) AS gap_days,
         |  COALESCE(d.number, 0) AS pre_last_delete_num,
         |  COALESCE(d.denominator, 0) AS pre_last_total_posts,
         |  COALESCE(d.number / NULLIF(d.denominator, 0), 0) AS pre_delete_rate
         |FROM user_metrics m
         |LEFT JOIN delete_stats d ON m.uid = d.uid
         |)
         |select
         |*
         |from delete_rate
         |where pre_delete_rate >= 0.8
         |""".stripMargin)

    val bjhRateResDf = mergeAndAddColumns("百家号","日常行为","账号行为特征","灰","账号变更","百家号疑似账号变更","80",bjhRateDf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号疑似账号变更共：" + bjhRateResDf.count())

    //合并数据
    var resTotalDf = unionIfNotEmpty(bjhCateResDf,bjhRateResDf)
      .withColumn("score",col("score").cast("float"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("合并数据量：" + resTotalDf.count())
    resTotalDf.createOrReplaceTempView("res_label_data")
    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    spark.stop()
  }

  /**
   * 读取词表
   * @param spark
   * @param csvName
   * @return
   */
  def readGz(spark:SparkSession,gzName:String): DataFrame = {
    // 读取词表
    val wordlist =
      spark.read.format("text")
        .option("encoding", "utf-8")
        .option("compression", "gzip")  // 显式指定压缩格式
        .load(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/${gzName}.gz")
    println(s"读取${gzName}共：" + wordlist.count())
    //tieba_toupai.show(5,false)
    wordlist
  }

  /**
   * 直接内存解压方案（适用于小文件）
   * @param spark SparkSession实例
   * @param url 文件下载URL
   * @return 解压后的DataFrame
   */
  def readGzFromUrl(spark: SparkSession, url: String): DataFrame = {
    import spark.implicits._
    val client = new OkHttpClient.Builder()
      .connectTimeout(30, TimeUnit.MINUTES)  // 连接超时时间
      .readTimeout(30, TimeUnit.MINUTES)     // 读取超时时间
      .build()
    val request = new Request
      .Builder()
      .addHeader("Cookie", "UUAP_TRACE_TOKEN=1a38310549ad4083cb9bbd88701d8dd2; BIDUPSID=A3EA8EBF676C184191B0952CF97E782B; PSTM=1727577040; BAIDUID=6676C5CCA96091B6109E86A323345ACC:SL=0:NR=10:FG=1; MAWEBCUID=web_rGyRACydJfOxSQuGbFovVwNTOFkHDCPmdlyMYpoBXaluDEBjJM; BDSFRCVID=hKCOJexroGWgbiOJgx5cu8KF12KK0gOTDY66TR5qJ04BtyCVvB0JEG0PtOpvTLPhlDMeogKK0mOTHv-F_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF=JR-e_CDytIK3fP36q45HMt00qxby26nCKD39aJ5nQI5nSUJN0U6kbtTWKq_OBM5X0CLHbJj8QUbmjRO206oay6O3LlO83h5b-I6IKl0MLPbM_p49Xq3s-4CEDxnMBMPjamOnaPLE3fAKftnOM46JehL3346-35543bRTLnLy5KJYMDFRDTLMejOyDNR22Pc22-oKWnTXbRu_Hn7zeT0WQM4pbt-qJqTHfRv-bqcmWT6pOP36jnbVyDuAL4nnBT5KaarPbx--Qpb_EDO9XnJY-jtkQN3T-RkO5bRiLRo4MlctDn3oyT3VXp0n5mcly5jtMgOBBJ0yQ4b4OR5JjxonDh83bG7MJnktfJIOoKKKfIt2bP365ITSMtDjMfKX5RjJfK5aKq7F5l8-hl0wWhJnyxI7jxo4-6vi3JCH2njJLIOxOKQphPRIhjcb34TCtpbMHm3L5JnN3KJmEqC9bT3v5tkV0M5k2-biWbRL2Mbdbj6P_IoG2Mn8M4bb3qOpBtQmJeTxoUJ25DnJh-PGe6D-D5byjNteqbbfb-oa3RTeb6rjDnCr0fkWXUI8Ln8t0hbMKD59aJ3yJROhbhvDDj5vyT8sXnOnQPT2QNutQlFMJpoMJqOK5xK2yxL1Db3JWbOqQgJTBnRgBR5oepvoD-cc3MkBqtjdJJQOBKQB0KnGbUQkeq8CQft20b0EeMtjW6LEJJFj_K85JK83fP36q45HMt00qxby26n3W279aJ5y-J7nSn3PMfnkbnv0-p_OBMKDte3ion3vQpbZMCQ206oay6O3LlO83h5q0JRZKl0MLPbcq-Q2Xh3shM0IbfnMBMPjamOnaPLE3fAKftnOM46JehL3346-35543bRTLnLy5KJYMDcnK4-XjjQ-DajP; H_WISE_SIDS_BFESS=61027_62325_62338_62372_62639_62642_62673_62705_62619_62330_62693_62753; MCITY=-%3A; SECURE_ZT_EXTRA_INFO=oGGxP2nfM_r4mZcQMta8lA7IMcrKHdLGC6YMUI5Ipzk0duuo4zuCFuJ6pJItyThFgCXzvx_XWpdsSSf6vy9PaS6_UJH6Ug9lAPqqCmbChbk; ZT_EXTRA_INFO=oGGxP2nfM_r4mZcQMta8lA7IMcrKHdLGC6YMUI5Ipzk0duuo4zuCFuJ6pJItyThFgCXzvx_XWpdsSSf6vy9PaS6_UJH6Ug9lAPqqCmbChbk; UUAP_P_TOKEN=PT-1117482775059394560-1EtzjOQdgD9tRkORyEtj-uuap; SECURE_UUAP_P_TOKEN=PT-1117482775059394560-1EtzjOQdgD9tRkORyEtj-uuap; BDUSS=Fh6V2FlSGJudHVHQUtELWduM1RZM29QR3ZwczVtdjVYODQzNENVdUVrTEEyQ1ZvRVFBQUFBJCQAAAAAAAAAAAEAAAAHkvCgY2hlbnpodW9fMDYxOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMBL~mfAS~5nS; BDUSS_BFESS=Fh6V2FlSGJudHVHQUtELWduM1RZM29QR3ZwczVtdjVYODQzNENVdUVrTEEyQ1ZvRVFBQUFBJCQAAAAAAAAAAAEAAAAHkvCgY2hlbnpodW9fMDYxOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMBL~mfAS~5nS; bce-sessionid=00128fb63675b294430bce51941779ba2a7; PHPSESSID=ST1119220967156539393qxn3y3OUjVlxrGjeyevduuap; BA_HECTOR=0lah24ak2h05a004ak0k2005053l7j1jvu92023; BAIDUID_BFESS=6676C5CCA96091B6109E86A323345ACC:SL=0:NR=10:FG=1; BDSFRCVID_BFESS=hKCOJexroGWgbiOJgx5cu8KF12KK0gOTDY66TR5qJ04BtyCVvB0JEG0PtOpvTLPhlDMeogKK0mOTHv-F_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF_BFESS=JR-e_CDytIK3fP36q45HMt00qxby26nCKD39aJ5nQI5nSUJN0U6kbtTWKq_OBM5X0CLHbJj8QUbmjRO206oay6O3LlO83h5b-I6IKl0MLPbM_p49Xq3s-4CEDxnMBMPjamOnaPLE3fAKftnOM46JehL3346-35543bRTLnLy5KJYMDFRDTLMejOyDNR22Pc22-oKWnTXbRu_Hn7zeT0WQM4pbt-qJqTHfRv-bqcmWT6pOP36jnbVyDuAL4nnBT5KaarPbx--Qpb_EDO9XnJY-jtkQN3T-RkO5bRiLRo4MlctDn3oyT3VXp0n5mcly5jtMgOBBJ0yQ4b4OR5JjxonDh83bG7MJnktfJIOoKKKfIt2bP365ITSMtDjMfKX5RjJfK5aKq7F5l8-hl0wWhJnyxI7jxo4-6vi3JCH2njJLIOxOKQphPRIhjcb34TCtpbMHm3L5JnN3KJmEqC9bT3v5tkV0M5k2-biWbRL2Mbdbj6P_IoG2Mn8M4bb3qOpBtQmJeTxoUJ25DnJh-PGe6D-D5byjNteqbbfb-oa3RTeb6rjDnCr0fkWXUI8Ln8t0hbMKD59aJ3yJROhbhvDDj5vyT8sXnOnQPT2QNutQlFMJpoMJqOK5xK2yxL1Db3JWbOqQgJTBnRgBR5oepvoD-cc3MkBqtjdJJQOBKQB0KnGbUQkeq8CQft20b0EeMtjW6LEJJFj_K85JK83fP36q45HMt00qxby26n3W279aJ5y-J7nSn3PMfnkbnv0-p_OBMKDte3ion3vQpbZMCQ206oay6O3LlO83h5q0JRZKl0MLPbcq-Q2Xh3shM0IbfnMBMPjamOnaPLE3fAKftnOM46JehL3346-35543bRTLnLy5KJYMDcnK4-XjjQ-DajP; ZFY=JJduTPIYqNlzXawb7uZ5iUFNUOWGAvFBirQcSvSu1gM:C; delPer=0; BDRCVFR[feWj1Vr5u3D]=I67x6TjHwwYf0; PSINO=1; BDRCVFR[tox4WRQ4-Km]=mk3SLVN4HKm; userFrom=null; BDRCVFR[-pGxjrCMryR]=mk3SLVN4HKm; arialoadData=false; jsdk-uuid=f1c8d94d-85b6-4991-92ae-6c1bb08a6893; jsdk-user=JkwEXPHabwHgi/kcFB5p0w==; wmtoken=jPANDax8FunlJ30R7hsGNPr6yRlgD1XBJZxn8xIzupvKnOBtQ1ooJAkTbLTMYg1BesF%2FgElbFw6Abzbu6IsG%2BD%2BpZ9tQMD8CT5k1i3%2FRoxthW9BBNfjv5k3Owrt3KmM4bAx2d1PM9uvxP0X5kdhDmQ%3D%3D; H_PS_PSSID=61027_62325_62338_62639_62842_62867_62878_62879_62888_62918_62928_62921_62967; H_WISE_SIDS=61027_62325_62338_62639_62842_62867_62878_62879_62888_62918_62928_62921_62967; BDORZ=FFFB88E999055A3F8A630C64834BD6D0; RT=\\\"z=1&dm=baidu.com&si=0cfca642-ef23-4968-a4e8-0b6fade43132&ss=m96srtx9&sl=unk&tt=65oku&ld=4edc0l&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf\\\"")
      .url(url)
      .build()

    var call: okhttp3.Call = null
    var response: Response = null
    var inputStream: GZIPInputStream = null
    var reader: BufferedReader = null

    try {
      // 1. 执行HTTP请求
      call = client.newCall(request)
      response = call.execute()

      if (!response.isSuccessful) {
        throw new RuntimeException(s"HTTP请求失败，状态码: ${response.code}, 原因: ${response.message}")
      }

      // 2. 创建解压流
      inputStream = new GZIPInputStream(new ByteArrayInputStream(response.body.bytes()))
      reader = new BufferedReader(new InputStreamReader(inputStream))

      // 3. 读取解压后的内容
      val lines = Iterator.continually(reader.readLine()).takeWhile(_ != null).toSeq

      // 4. 转换为DataFrame
      spark.sparkContext.parallelize(lines).toDF("log_line").limit(5)

    } catch {
      case e: Exception =>
        throw new RuntimeException(s"读取GZ文件失败: ${e.getMessage}", e)
    } finally {
      // 5. 清理资源（注意关闭顺序）
      if (reader != null) reader.close()
      if (inputStream != null) inputStream.close()
      if (response != null) response.close()
      if (call != null) call.cancel()
    }
  }

  /**
   * 使用OkHttp下载gz文件到HDFS（优化版）
   *
   * @param spark    SparkSession实例
   * @param url      文件下载URL
   * @param hdfsPath HDFS存储路径（包含文件名）
   */
  def downloadGzFileToHdfs(spark: SparkSession, url: String): DataFrame = {
    val client = new OkHttpClient()
    val request = new Request.Builder().url(url).build()

    var call: okhttp3.Call = null
    var response: Response = null
    var outputStream: org.apache.hadoop.fs.FSDataOutputStream = null

    // 提取文件名
    val fileName = extractGzFilename(url)

    try {
      // 1. 执行HTTP请求
      call = client.newCall(request)
      response = call.execute()

      if (!response.isSuccessful) {
        throw new RuntimeException(s"HTTP请求失败，状态码: ${response.code}, 原因: ${response.message}")
      }

      println(s"成功下载文件: ${fileName}")

      readGz(spark, fileName)

    } catch {
      case e: Exception =>
        throw new RuntimeException(s"文件下载失败: ${e.getMessage}", e)
    } finally {
      // 4. 清理资源
      if (response != null) response.close()
      if (call != null) call.cancel()
      if (outputStream != null) outputStream.close()
    }

  }

  /**
   * 从URL中提取gz文件名
   * @param url
   * @return
   */
  def extractGzFilename(url: String): String = {
    val pattern = """/([^/]+\.gz)(?=\?|$)|[?&]filename=([^&]+)""".r

    pattern.findFirstMatchIn(url) match {
      case Some(m) =>
        m.group(1) match {
          case null =>
            // 处理URL编码的filename参数
            URLDecoder.decode(m.group(2), "UTF-8")
              .split("""[;=]""")
              .last
          case filename => filename
        }
      case None => ""
    }
  }
}

