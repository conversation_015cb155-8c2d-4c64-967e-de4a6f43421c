package com.baidu.sql.customized.files801

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import com.baidu.sql.customized.files801.Label801.readCsv
import com.baidu.sql.utils.SparkUtils._
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils.getNullVal
import okhttp3.{MediaType, OkHttpClient, Request, RequestBody, Response}
import org.apache.spark.sql.functions.{col, expr, length, lit, regexp_replace, substring, when}
import org.apache.spark.sql.{DataFrame, SparkSession, functions}
import com.crealytics.spark.excel._
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.storage.StorageLevel
import org.apache.spark.sql.types._
import sys.process._

import java.io.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ByteArrayInputStream, InputStreamReader}
import java.net.URLDecoder
import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDateTime, ZoneId}
import java.util.concurrent.TimeUnit
import java.util.zip.GZIPInputStream


/**
 *  @author: zhangrunjie 801风控用户标签表，百家号数据标签
 */
object Label801BjhTest {
  //  source  str 数据来源
  //  type  str 标签类型
  //  sub_type  str 标签子类型
  //  label_lv1 str 标签一级分类
  //  label_lv2 str 标签二级分类
  //  label_lv3 str 标签三级分类
  //  score float 分数/置信度
  //  event_day str 时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""
  var day2 = ""
  var day7 = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    day2 = calcnDate(YesterDay, -2)
    day7 = calcnDate(YesterDay, -7)
    val day180 = calcnDate(YesterDay, -180)

    val YesterDayFormat = calcnDateFormat(YesterDay)
    val Day180Format = calcnDateFormat(day180)
    println("yyyy-MM-dd日期格式：" + YesterDayFormat)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val Day7TimeStamp = getTimeStampTake(day7,10)
    val BeForeDayTimeStamp = getTimeStampTake(BeForeDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)
    val Day90TimeStamp = getTimeStampTake(calcnDate(YesterDay, -90),10)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)
    import spark.implicits._

    val resDf = spark.sql(
      s"""
        |select
        |        author_user_id,
        |        '百家号发文' as activate_resource,
        |        bid, -- 用户bid
        |        app_id, -- 用户app_id
        |        author_name, -- 用户名
        |        author_audit_at, -- 作者注册时间
        |        author_status, -- 作者状态
        |        author_mcn_org_name, -- 作者mcn组织名称
        |        author_mcn_source_type, -- 作者mcn具体类型
        |        -- datediff(CURRENT_DATE(), author_audit_at) as author_login_day,-- 用户注册天数
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 3), 1, 0)) as  author_publish_3_day, -- 近3天发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 7), 1, 0)) as author_publish_7_day, -- 近7天发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 30) , 1, 0)) as author_publish_30_day, -- 近30天发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 90), 1, 0)) as author_publish_90_day, -- 近90天发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 180), 1, 0)) as  author_publish_180_day, -- 近180天发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 3) and status = 'publish', 1, 0)) as  author_publish_pass_3_day, -- 近3天成功发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 7) and status = 'publish', 1, 0)) as  author_publish_pass_7_day, -- 近7天成功发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 30)  and status = 'publish', 1, 0)) as  author_publish_pass_30_day, -- 近30天成功发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 90) and status = 'publish', 1, 0)) as author_publish_pass_90_day, -- 近90天成功发文量
        |        sum(if(click_publish_time	 >= date_sub(CURRENT_DATE(), 180) and status = 'publish', 1, 0)) as author_publish_pass_180_day, -- 近180天成功发文量
        |        -- if(author_mcn_org_name is null, 1, 0) as is_mcn, -- 是否MCN
        |        first_public_time, -- 百家号首次发文时间
        |        max(publish_time) as bjh_last_public_time -- 百家号最后发文时间
        |    from bjh_data.bjh_feed_resource_rf
        |    where event_day = '${YesterDay}'
        |    and publish_time between '${Day180Format}' and '${YesterDayFormat}'
        |    and author_user_id not like '%-%'
        |    and author_user_id = '6510695796'
        |    group by author_user_id,
        |        bid, -- 用户bid
        |        app_id, -- 用户app_id
        |        author_name, -- 用户名
        |        author_audit_at, -- 作者注册时间
        |        author_status, -- 作者状态
        |        author_mcn_org_name, -- 作者mcn组织名称
        |        author_mcn_source_type, -- 作者mcn具体类型
        |        first_public_time
        |
        |""".stripMargin)
      .cache()
    resDf.show(10,false)

    /*val feed_resource = spark.sql(
      s"""
        |select
        |   nid
        |from bjh_data.bjh_feed_resource_rf
        |where event_day = '${YesterDay}'
        |and click_publish_time between '${YesterDayFormat} 00:00:00' and '${YesterDayFormat} 23:59:59' -- 全量表取昨天日期数据过滤
        |and aigc_resource != 1
        |and type3 in ('shortvideo', 'svideo', 'tinyvideo', 'microvideo')
        |group by nid
        |""".stripMargin)
      .cache()
    println(s"获取发文表短小视频数据量：${feed_resource.count()}")
    feed_resource.createOrReplaceTempView("feed_resource_data")*/

    /*val baseDf = spark.sql(
      s"""
        |select
        |   article_id as nid,
        |   product,
        |   title,
        |   reject_reason,
        |   status,
        |   username,
        |   check_type,
        |   insert_time,
        |   receive_time,
        |   workflow_type,
        |   type,
        |   regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        | from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        | where
        | event_day = '${YesterDay}'
        | and check_type in ('sf','sm','qm','qf')
        | and product in ('shortvideo', 'svideo', 'tinyvideo', 'microvideo')
        | and username not rlike '模型|智能体|AI'
        |""".stripMargin)
      .cache()
    println(s"获取审核表数据量：${baseDf.count()}")
    baseDf.createOrReplaceTempView("base_data")

    val resDf = spark.sql(
        s"""
           |-- 安全人审
           | select
           |   'sf安全人审' as type,
           |   count(distinct nid) as cnt
           | from base_data
           | where status = 0 and check_type = 'sf'
           | union all
           |-- 安全机审
           | select
           |   'sm安全机审' as type,
           |   count(distinct nid) as cnt
           | from base_data
           | where status = 0 and check_type = 'sm'
           | union all
           |-- 质量人审
           |select
           |   'qf质量人审' as type,
           |   count(distinct nid) as cnt
           | from base_data
           | where status = 0 and check_type = 'qf'
           | union all
           |-- 质量机审
           |select
           |   'qm质量机审' as type,
           |   count(distinct nid) as cnt
           | from base_data
           | where status = 0 and check_type = 'qm'
           |""".stripMargin)
      .cache()
    resDf.show()

    val resGroupDf = spark.sql(
      s"""
        |-- 安全人审
        | select
        |   'sf' as type,
        |   reject_reason,
        |   count(distinct nid) as cnt
        | from base_data
        | where status = 0 and check_type = 'sf'
        | group by reject_reason
        | union all
        |-- 安全机审
        | select
        |   'sm' as type,
        |   reject_reason,
        |   count(distinct nid) as cnt
        | from base_data
        | where status = 0 and check_type = 'sm'
        | group by reject_reason
        | union all
        | -- 质量人审
        | select
        |   'qf质量人审' as type,
        |   reject_reason,
        |   count(distinct nid) as cnt
        | from base_data
        | where status = 0 and check_type = 'qf'
        | group by reject_reason
        | union all
        |-- 质量机审
        |select
        |   'qm质量机审' as type,
        |   reject_reason,
        |   count(distinct nid) as cnt
        | from base_data
        | where status = 0 and check_type = 'qm'
        | group by reject_reason
        |""".stripMargin)
      .cache()

    println("resGroupDf:" + resGroupDf.count())
    resGroupDf.show(10,false)
    resGroupDf.repartition(1).write.mode("overwrite").parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_质量审核数据.parquet")
    println("write parquet success!")
    writeExcel(resGroupDf,s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_质量审核数据.xlsx")
*/

    /*val resDf = spark.sql(
      s"""
        |with tmp_data as (
        |select
        |   nid,
        |   get_json_object(ext_info,'$$.sf_reason') as sf_reason,
        |   get_json_object(ext_info,'$$.sf_subreason') as sf_subreason,
        |   get_json_object(ext_info,'$$.sf_username') as sf_username,
        |   get_json_object(ext_info,'$$.pass_strategy_detail') as pass_strategy_detail,
        |   get_json_object(ext_info,'$$.sf_status') as sf_status,
        |   get_json_object(ext_info,'$$.sm_status') as sm_status
        | from
        |   udw_ns.default.bjh_ods_auditflow_esdump_di
        | where
        |   event_day = '${YesterDay}'
        |   and product in ('shortvideo', 'svideo', 'tinyvideo', 'microvideo')
        |   and nid in (select nid from feed_resource_data)
        |)
        |-- 安全人审数量
        |select
        |'sf' as type,
        |count(distinct nid) as cnt
        |from tmp_data
        |where sf_status = 0
        |union all
        |-- 安全机审数量
        |select
        |'sm' as type,
        |count(distinct nid) as cnt
        |from tmp_data
        |where sm_status = 0
        |""".stripMargin)
      .cache()
    println(s"一共读取到${resDf.count()}条数据")
    resDf.show()*/



    /*val resDf2 = spark.sql(
      """
        |SELECT
        |  regexp_replace (title, '\\s+', '') as title,
        |  url,    -- 内容url
        |  type3,  -- 分类
        |  author_user_id, -- 用户id
        |  nid,
        |  status,
        |  read_pv,
        |  click_publish_time,
        |  view_count, -- 真实阅读量
        |  fake_view_count, -- 包装阅读量
        |  real_reprint_view_count, -- 真实再分发阅读量
        |  publish_view_count_15day, -- 发布15天累计阅读量
        |  add_comment, --  互动中台累计评论量（互动中台，不止手百）
        |  comment_pv, -- 不建议使用-评论量
        |  haokan_total_comment, -- 好看端累计评论量
        |  channel_baijia_pinglun_all, -- 分渠道百+运营投放累计评论量
        |  pinglun_all, -- 手百分客户端累计评论量
        |  haokan_total_zan, --  好看端累计点赞量
        |  article_zan_all, -- 手百分客户端累计点赞量
        |  channel_baijia_article_zan_all, -- 分渠道百+运营投放累计点赞量
        |  add_praise, --  互动中台累计真实新增点赞量（互动中台，不止手百）
        |  channel_baijia_share_pv_all, -- 分渠道百+运营投放累计转发量
        |  real_reprint_recommend_count, -- 真实转发推荐
        |  add_share -- 互动中台累计真实新增转发量（互动中台，不止手百）
        |from bjh_data.bjh_feed_resource_rf
        |where event_day = '20250811'
        |  and click_publish_time between '2025-08-05 00:00:00' and '2025-08-05 23:59:59' -- 全量表取昨天日期数据过滤
        |  and nid in (
        |   '4022147559572242176',
        |   '11990983100238845668',
        |   '16471175285748261825',
        |   '4331700825122452692',
        |   '3856616625176597695',
        |   '5102871630583495273',
        |   '4395333948113571346',
        |   '5329974687791232532'
        |  )  -- nid查询过滤
        |""".stripMargin)
    resDf2.show(false)

    println("resDf:" + resDf2.count())
    //resDf.show(10,false)
    resDf2.repartition(1).write.mode("overwrite").parquet("afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250811_nid.parquet")
    println("write parquet success!")
    writeExcel(resDf2,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250811_nid.xlsx")
*/

    /*val nidDf = readCsv(spark,"20250814短小视频")
      .withColumn("nid",regexp_replace(col("nid"), "[^0-9]", ""))
      .filter(col("nid") =!= "")
      .cache()
    println("nidDf.count:"+nidDf.count())
    nidDf.createOrReplaceTempView("nid_data")

    val resDf2 = spark.sql(
      s"""
        |select
        | article_id as nid,
        | product,
        | title,
        | reject_reason,
        | status,
        | username,
        | event_day,
        | insert_time,
        | receive_time,
        | workflow_type,
        | type,
        | regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        |from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        |where event_day = '20250811'
        |and article_id in (select distinct nid from nid_data)
        |""".stripMargin)

    resDf2.repartition(1).write.mode("overwrite").parquet("afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250811_2000nid.parquet")
    println("write parquet success!")
    writeExcel(resDf2,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250811_2000nid.xlsx")
*/
    /*val resDf = spark.sql(
      """
        |select distinct
        |  nid,
        |  bid,
        |  type,
        |  regexp_replace (title, '\\s+', '') as title,
        |  regexp_replace (subtitle, '\\s+', '') as subtitle,
        |  publish_time,
        |  publish_ip,
        |  app_id,
        |  user_name,
        |  user_level,
        |  sub_type,
        |  source_type,
        |  feed_content,
        |  product,
        |  subproduct,
        |  property,
        |  status,
        |  audit_category,
        |  sf_trigger_reason,
        |  qf_trigger_reason,
        |  get_json_object (ext_info, '$.sm_reason') as sm_reason,
        |  get_json_object (ext_info, '$.sm_subreason') as sm_subreason,
        |  get_json_object (ext_info, '$.sf_username') as sf_username,
        |  get_json_object (ext_info, '$.sm_status') as sm_status,
        |  get_json_object (ext_info, '$.sf_status') as sf_status,
        |  regexp_replace (ext_info, '\\s+', '') as ext_info
        |from
        |  udw_ns.default.bjh_ods_auditflow_esdump_di
        |where
        |  event_day between '20250701' and '20250730'
        |  and nid in (select distinct nid from nid_data)
        |""".stripMargin)
    println("resDf.count:"+resDf.count())
    //resDf.show(10,false)
    resDf.repartition(1).write.mode("overwrite").parquet("afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250730_nid.parquet")
    println("write parquet success!")
    writeExcel(resDf,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250730_nid.xlsx")
*/
    // /Users/<USER>/Desktop/zrj_files/801/导数据文件
    /*val inputPath = "/Users/<USER>/Desktop/zrj_files/801/导数据文件/20250807uid.parquet"
    val outputPath = "/Users/<USER>/Desktop/zrj_files/801/导数据文件/20250807uid.xlsx"
    println("inputPath:"+inputPath)
    println("outputPath:"+outputPath)
    convertParquetToExcel(spark,inputPath,outputPath)*/

    /*val inputPath = "afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_flow/20250624/20250624_result.parquet"
    val outputPath = "afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_flow/20250624/20250624_result3.xlsx"
    println("inputPath:"+inputPath)
    println("outputPath:"+outputPath)
    convertParquetToExcel(spark,inputPath,outputPath)*/

    /*val nidDf = readExcel(spark,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250804全模态nid.xlsx")
      .withColumn("nid",regexp_replace(col("nid"), "[^0-9]", ""))
      .filter(col("nid") =!= "")
      .cache()
    val nidDfBroad = spark.sparkContext.broadcast(nidDf)
    println("nidDf如下：" + nidDf.count())
    //nidDf.show(10,false)
    nidDfBroad.value.createOrReplaceTempView("bjh_tmp_data")*/

    /*val resDf = spark.sql(
      s"""
        |SELECT
        |  mis_url,
        |  rmb_self_build_url_http,
        |  nid,
        |  secure_status,
        |  secure_not_pass_reason,
        |  m_content,
        |  url,
        |  img_str,
        |  author_user_id
        |from bjh_data.bjh_feed_resource_rf
        |where event_day = '${YesterDay}'
        |and click_publish_time between '2025-07-06 00:00:00' and '2025-08-01 23:59:59'
        |and nid in (select distinct nid from nid_data)
        |and type3 in ('news','dt_image_text')
        |""".stripMargin)*/

    /*val resDf = spark.sql(
      s"""
         |with tmp_data as (
         |select
         | article_id as nid,
         | product,
         | title,
         | reject_reason,
         | status,
         | username,
         | regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
         |from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
         |where event_day between '20250101' and '20250617'
         |  and status = 0
         |  and username = '短小视频涉政智能体'
         |),
         |bjh_tmp_data as (
         |SELECT
         |  mis_url,
         |  rmb_self_build_url_http,
         |  nid,
         |  secure_status,
         |  secure_not_pass_reason,
         |  m_content,
         |  url,
         |  author_user_id
         |from bjh_data.bjh_feed_resource_rf
         |where event_day = '${YesterDay}'
         |and nid in (select distinct nid from tmp_data)
         |)
         |select
         |   t1.*,
         |   t2.mis_url,
         |   t2.rmb_self_build_url_http,
         |   t2.secure_status,
         |   t2.secure_not_pass_reason,
         |   t2.m_content,
         |   t2.url,
         |   t2.author_user_id
         |from tmp_data t1
         |left join bjh_tmp_data t2 on t1.nid = t2.nid
         |""".stripMargin)*/

    /*println("resDf:" + resDf.count())
    //resDf.show(10,false)
    resDf.repartition(1).write.mode("overwrite").parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_uid.parquet")
    println("write parquet success!")
    writeExcel(resDf,s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_uid.xlsx")
*/
    /*val excelDf = readExcel(spark,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/0704目标人物得分0.5以上.xlsx")
    println("excelDf:" + excelDf.count())
    excelDf.createOrReplaceTempView("base_excel")

    val resDf = spark.sql(
      """
        |with tmp_data as (
        |select
        |     es_id,
        |     article_id as nid,
        |     product,
        |     title,
        |     reject_reason,
        |     status,
        |     username,
        |     check_type,
        |     insert_time,
        |     receive_time,
        |     workflow_type,
        |     type,
        |     regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        |   from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        |   where event_day = '20250704'
        |   and article_id in (select distinct nid from base_excel)
        |)
        |select distinct
        |   a.*,
        |   t.es_id,
        |   t.product,
        |   t.title,
        |   t.reject_reason,
        |   t.status,
        |   t.username,
        |   t.check_type,
        |   t.insert_time,
        |   t.receive_time,
        |   t.workflow_type,
        |   t.type,
        |   t.ext_json_info
        |from base_excel a
        |left join tmp_data t on a.nid = t.nid
        |""".stripMargin)*/
    /*println("resDf:" + resDf.count())
    //resDf.show(10,false)
    resDf.repartition(1).write.mode("overwrite").parquet("afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250716_author_url.parquet")
    println("write parquet success!")
    writeExcel(resDf,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/20250716_author_url.xlsx")
*/

    //6.20词表
    /*val wordsDf = Seq(
      "3s词表策略-政治敏感",
      "3s词表策略-领导人",
      "新版涉政有害模型",
      "涉习变体策略",
      "涉习内容策略",
      "3s词表策略-涉一号首长",
      "涉习称谓错误",
      "业务线词表策略-政治敏感",
      "业务线词表策略-涉一号首长"
    )
    val smReason = Seq("word_3s","sync_word","leader_variant","leader_content","involved_political","sensitive_hardface","figure","sensitive_cartoon","mingan_renwumanhua","shexi_titai","pgc_video_political","ocr_porn")
    val smSubReason = Seq("3s词表策略-涉一号首长","3s词表策略-领导人","3s词表策略-政治敏感","3s词表策略-色情低俗","3s词表策略-违法违规","业务线词表策略-领导人","业务线词表策略-涉一号首长","业务线词表策略-政治敏感","业务线词表策略-违法违规","业务线词表策略-色情低俗")

    val smReasonArray = smReason.map(w => s"'${w.replace("'", "''")}'").mkString(",")
    val smReasonLikeArray = smReason.mkString("|")
    val smSubReasonArray = smSubReason.map(w => s"'${w.replace("'", "''")}'").mkString(",")*/

    /*val bjhBaseDf = spark.sql(
        s"""
           |select
           |      nid,
           |      bid,
           |      type,
           |      regexp_replace (title, '\\\\s+', '') as title,
           |      regexp_replace (subtitle, '\\\\s+', '') as subtitle,
           |      publish_time,
           |      publish_ip,
           |      app_id,
           |      user_name,
           |      user_level,
           |      sub_type,
           |      source_type,
           |      feed_content,
           |      product,
           |      subproduct,
           |      property,
           |      status,
           |      audit_category,
           |      sf_trigger_reason,
           |      qf_trigger_reason,
           |      get_json_object (ext_info, '$$.sm_reason') as sm_reason,
           |      get_json_object (ext_info, '$$.sm_subreason') as sm_subreason,
           |      get_json_object (ext_info, '$$.sf_username') as sf_username,
           |      get_json_object (ext_info, '$$.sr_reason') as sr_reason,
           |      get_json_object (ext_info, '$$.pass_strategy_detail') as pass_strategy_detail,
           |      get_json_object (ext_info, '$$.sm_status') as sm_status,
           |      get_json_object (ext_info, '$$.sf_status') as sf_status,
           |      -- ext.service_video_intelligent_agent.data.result.result
           |      -- 提取最外层的 result (整数值)
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sf_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_dynamicnew_political'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sf_political_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sf_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_video_intelligent_agent'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sf_video_intelligent_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sf_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_video_porn_llm'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sf_porn_llm_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sf_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_pgc_intelligent_agent'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sf_pgc_intelligent_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sf_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_ugc_hit_llm'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sf_ugc_hit_llm_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sr_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_dynamicnew_political'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sr_political_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sr_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_video_intelligent_agent'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sr_video_intelligent_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sr_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_video_porn_llm'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sr_porn_llm_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sr_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_pgc_intelligent_agent'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sr_pgc_intelligent_agent_result,
           |      get_json_object (
           |        get_json_object (
           |          get_json_object (
           |            get_json_object (
           |              get_json_object (ext_info, '$$.sr_audit_info'),
           |              '$$.ext'
           |            ),
           |            '$$.service_ugc_hit_llm'
           |          ),
           |          '$$.data.result'
           |        ),
           |        '$$.result'
           |      ) AS sr_ugc_hit_llm_result,
           |      regexp_replace (ext_info, '\\\\s+', '') as ext_info
           |    from
           |      udw_ns.default.bjh_ods_auditflow_esdump_di
           |    where
           |      event_day = '20250624'
           |      and product = 'pgcnews'
           |      and get_json_object (ext_info, '$$.sm_reason') RLIKE '${smReasonLikeArray}'
           |""".stripMargin)
      .cache()
    println(s"获取流程表数据：${bjhBaseDf.count()}")
    bjhBaseDf.createOrReplaceTempView("bjh_base_data")*/

    /*val smReasonDf = spark.sql(
      s"""
        |select t.* from
        |(select
        |  nid,
        |  min(array_contains(array(${smReasonArray}), sm_reasons)) AS all_contained
        |from (select nid,explode(split(sm_reason,'&&')) as sm_reasons from bjh_base_data)
        |group by nid
        |)t where t.all_contained = true
        |""".stripMargin)
    println(s"sm_reason 过滤：${smReasonDf.count()}")
    smReasonDf.show(5,false)
    smReasonDf.createOrReplaceTempView("smReason_view")*/

    /*val smSubReasonDf = spark.sql(
      s"""
        |select
        |   nid,
        |   regexp_replace(sm_subreasons, '\\\\(.*?\\\\)|:.*', '') as sm_subreason
        |from
        |(select
        | t2.nid,
        | explode(split(sm_subreason,'&&')) as sm_subreasons
        |from
        |smReason_view t
        |inner join bjh_base_data t2 on t.nid = t2.nid
        |)
        |where sm_subreasons like '%3s词表策略%' or sm_subreasons like '%业务线词表策略%'
        |""".stripMargin)
    println(s"sm_subreason 基础数据：${smSubReasonDf.count()}")
    smSubReasonDf.show(5,false)
    smSubReasonDf.createOrReplaceTempView("smSubReason_base_data")

    val smSubReasonFilterDf = spark.sql(
      s"""
        |select
        |t.*
        |from
        |(select
        |  nid,
        |  min(array_contains(array(${smSubReasonArray}), sm_subreason)) AS all_contained
        |from smSubReason_base_data
        |group by nid)t where t.all_contained = true
        |""".stripMargin)
    println(s"sm_subreason 过滤：${smSubReasonFilterDf.count()}")
    //smSubReasonFilterDf.show(5,false)
    smSubReasonFilterDf.createOrReplaceTempView("smSubReason_filter_view")*/

    /*val resDf = spark.sql(
      """
        |select
        | t2.*
        |from
        |smReason_view t
        |inner join bjh_base_data t2 on t.nid = t2.nid
        |""".stripMargin)
    println(s"最终结果：${resDf.count()}")
    resDf.show(5,false)*/

    /*val resDf = spark.sql(
      s"""
         |-- with bjh_base_data as ( -- 基础数据表
         |--
         |-- ),
         |smReason_view as ( -- sm_reason 过滤
         |select
         |  nid,
         |  min(array_contains(array(${smReasonArray}), sm_reasons)) AS all_contained
         |from (select nid,explode(split(sm_reason,'&&')) as sm_reasons from bjh_base_data)
         |group by nid
         |),
         |smSubReason_base_data as ( -- 筛选出符合的nid数据后，取sm_subreason字段
         |select
         | t2.nid,
         | explode(split(sm_subreason,'&&')) as sm_subreasons
         |from
         |(select nid from smReason_view where all_contained = true) t -- true是sm_reason有包含smReasonArray中的元素
         |inner join bjh_base_data t2 on t.nid = t2.nid
         |),
         |smSubReason_view as ( -- sm_subreason 去除括号和冒号数据
         |select
         | nid,
         | regexp_replace(sm_subreasons, '\\\\(.*?\\\\)|:.*', '') as sm_subreason
         |from smSubReason_base_data
         |),
         |smSubReason_filter_view as ( -- sm_subreason 过滤
         |select
         |  nid,
         |  min(array_contains(array(${smSubReasonArray}), sm_subreason)) AS all_contained
         |from smSubReason_view
         |group by nid
         |) -- 最终筛选出符合sm_subreason的nid数据
         |select
         | t2.*
         |from
         |(select nid from smSubReason_filter_view where all_contained = true) t
         |inner join bjh_base_data t2 on t.nid = t2.nid
         |limit 3000
         |""".stripMargin)*/

    /*val resDf = spark.sql(
      """
        |with tmp_data as (
        |    select
        |      nid,
        |      bid,
        |      type,
        |      regexp_replace (title, '\\s+', '') as title,
        |      regexp_replace (subtitle, '\\s+', '') as subtitle,
        |      publish_time,
        |      publish_ip,
        |      app_id,
        |      user_name,
        |      user_level,
        |      sub_type,
        |      source_type,
        |      feed_content,
        |      product,
        |      subproduct,
        |      property,
        |      status,
        |      audit_category,
        |      sf_trigger_reason,
        |      qf_trigger_reason,
        |      get_json_object (ext_info, '$.sm_reason') as sm_reason,
        |      get_json_object (ext_info, '$.sm_subreason') as sm_subreason,
        |      get_json_object (ext_info, '$.sf_username') as sf_username,
        |      get_json_object (ext_info, '$.sr_reason') as sr_reason,
        |      get_json_object (ext_info, '$.pass_strategy_detail') as pass_strategy_detail,
        |      get_json_object (ext_info, '$.sm_status') as sm_status,
        |      get_json_object (ext_info, '$.sf_status') as sf_status,
        |      -- ext.service_video_intelligent_agent.data.result.result
        |      -- 提取最外层的 result (整数值)
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sf_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_dynamicnew_political'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sf_political_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sf_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_video_intelligent_agent'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sf_video_intelligent_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sf_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_video_porn_llm'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sf_porn_llm_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sf_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_pgc_intelligent_agent'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sf_pgc_intelligent_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sf_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_ugc_hit_llm'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sf_ugc_hit_llm_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sr_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_dynamicnew_political'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sr_political_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sr_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_video_intelligent_agent'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sr_video_intelligent_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sr_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_video_porn_llm'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sr_porn_llm_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sr_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_pgc_intelligent_agent'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sr_pgc_intelligent_agent_result,
        |      get_json_object (
        |        get_json_object (
        |          get_json_object (
        |            get_json_object (
        |              get_json_object (ext_info, '$.sr_audit_info'),
        |              '$.ext'
        |            ),
        |            '$.service_ugc_hit_llm'
        |          ),
        |          '$.data.result'
        |        ),
        |        '$.result'
        |      ) AS sr_ugc_hit_llm_result,
        |      regexp_replace (ext_info, '\\s+', '') as ext_info
        |    from
        |      udw_ns.default.bjh_ods_auditflow_esdump_di
        |    where
        |      event_day = '20250620'
        |      and product = 'dynamicnew'
        |      and get_json_object (ext_info, '$.sm_subreason') RLIKE '3s词表策略-政治敏感|3s词表策略-领导人|新版涉政有害模型|涉习变体策略|涉习内容策略|3s词表策略-涉一号首长|涉习称谓错误|业务线词表策略-政治敏感|业务线词表策略-涉一号首长'
        |)
        |-- select * from tmp_data
        |select
        |b.nid as tmp_nid,
        |t.*
        |from base_excel b
        |left join tmp_data t on b.nid = t.nid
        |""".stripMargin)*/
    /*println("resDf:"+bjhBaseDf.count())
    bjhBaseDf.show(10,false)
    bjhBaseDf.repartition(1).write.mode("overwrite").parquet("afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_flow/20250625/20250625_result.parquet")
    println("write parquet success!")
    writeExcel(bjhBaseDf,"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_flow/20250625/20250625_result.xlsx")

*/


    /*val excelPath = "afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/feed-rcv-624.xlsx"

    val excelDf = readExcel(spark,excelPath)
      .cache()
    println("excelDf: " + excelDf.count())
    //excelDf.show(5,false)
    excelDf.createOrReplaceTempView("base_excel")*/

    /*val resDf = spark.sql(
      """
        |select
        |     article_id as nid,
        |     product,
        |     title,
        |     reject_reason,
        |     status,
        |     username,
        |     check_type,
        |     insert_time,
        |     receive_time,
        |     workflow_type,
        |     type,
        |     regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        |   from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        |   where event_day = '20250620'
        |   and article_id in (select distinct nid from base_excel)
        |""".stripMargin)
      .cache()*/


    /*val resDf = readExcel(spark, "afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_words_feed/content_20250620_resolved.xlsx",
      Map(
        "sheetName" -> "DataSheet",
        "header" -> "true",
        "inferSchema" -> "false"
      )
    )

    resDf
      .filter(col("nid").isin("5712814616274238609","5338451336975324546","12543474586880168921","2831987816168556182"))
      .dropDuplicates("nid").createOrReplaceTempView("bjh_tmp_data")

    val resExcelDf = spark.sql(
      s"""
        |with tmp_data as (
        |select
        |   article_id as nid,
        |   product,
        |   title,
        |   reject_reason,
        |   status,
        |   username,
        |   check_type,
        |   insert_time,
        |   receive_time,
        |   workflow_type,
        |   type,
        |   regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
        | from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
        | where event_day between '20250619' and '20250621'
        | and article_id in (select nid from bjh_tmp_data)
        |)
        | select
        |   b.nid,
        |   b.result,
        |   t.product,
        |   t.title,
        |   t.reject_reason,
        |   t.status,
        |   t.username,
        |   t.check_type,
        |   t.insert_time,
        |   t.receive_time,
        |   t.workflow_type,
        |   t.type,
        |   t.ext_json_info
        | from bjh_tmp_data b
        | left join tmp_data t on b.nid = t.nid
        |""".stripMargin)
    resExcelDf.show(false)

    println("resExcelDf写入成功")

    val resExcelDf2 = spark.sql(
      s"""
         |with tmp_data as (
         |select
         |   nid,
         |   bid,
         |   type,
         |   regexp_replace(title, '\\\\s+', '')  as title,
         |   regexp_replace(subtitle, '\\\\s+', '')  as subtitle,
         |   publish_time,
         |   publish_ip,
         |   app_id,
         |   user_name,
         |   user_level,
         |   sub_type,
         |   source_type,
         |   feed_content,
         |   product,
         |   subproduct,
         |   property,
         |   status,
         |   audit_category,
         |   sf_trigger_reason,
         |   qf_trigger_reason,
         |   get_json_object(ext_info,'$$.sm_reason') as sm_reason,
         |   get_json_object(ext_info,'$$.sm_subreason') as sm_subreason,
         |   get_json_object(ext_info,'$$.sf_username') as sf_username,
         |   get_json_object(ext_info,'$$.sr_reason') as sr_reason,
         |   get_json_object(ext_info,'$$.pass_strategy_detail') as pass_strategy_detail,
         |   get_json_object(ext_info,'$$.sm_status') as sm_status,
         |   get_json_object(ext_info,'$$.sf_status') as sf_status,
         |   regexp_replace(ext_info, '\\\\s+', '')  as ext_info
         | from
         |   udw_ns.default.bjh_ods_auditflow_esdump_di
         | where
         |   event_day between '20250619' and '20250621'
         |   and nid in (select nid from bjh_tmp_data)
         |)
         | select
         |   b.nid,
         |   b.result,
         |   t.publish_time,
         |   t.publish_ip,
         |   t.title,
         |   t.subtitle,
         |   t.app_id,
         |   t.user_name,
         |   t.user_level,
         |   t.sub_type,
         |   t.source_type,
         |   t.feed_content,
         |   t.product,
         |   t.subproduct,
         |   t.property,
         |   t.status,
         |   t.audit_category,
         |   t.sf_trigger_reason,
         |   t.qf_trigger_reason,
         |   t.sm_reason,
         |   t.sm_subreason,
         |   t.sf_username,
         |   t.sr_reason,
         |   t.pass_strategy_detail,
         |   t.sm_status,
         |   t.sf_status,
         |   t.ext_info
         | from bjh_tmp_data b
         | left join tmp_data t on b.nid = t.nid
         |""".stripMargin)
    resExcelDf2.show(false)

    println("resExcelDf2写入成功")*/

    spark.stop()
  }
}

