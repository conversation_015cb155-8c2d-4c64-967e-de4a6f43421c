package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.storage.StorageLevel
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.Row

import java.util.regex.Pattern


/**
 *  @author: zhangrunjie 801风控用户标签表，百家号数据标签
 */
object Label801Bjh {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""
  var Day30 = ""
  //判断重复内容的关键词
  val dupicateContent = Seq(
    "check_better$$similar",
    "视频择优去重",
    "择优判重模型",
    "自消重判重",
    "安全审核通过",
    "文章相似",
    "图文自消重",
    "自消重判重",
    "命中指纹去重策略",
    "安全机审判重拒绝自消")

  // 敏感词表
  val blockedWords = Seq(
      //词表关键词，label_lv2
      Row("涉政","涉政"),
      Row("政治敏感","涉政"),
      Row("广告作弊","黑产"),
      Row("色情低俗","色情"),
      Row("领导人","涉政"),
      Row("违法信息","违法违规"),
      Row("突发专项","涉政"),
      Row("涉恐涉暴","涉恐涉暴"),
      Row("涉一号首长","涉政"),
      Row("境外媒体","涉政"),
      Row("历史虚无","涉政"),
      Row("宗教民族","涉政")
  )

  // 评论、回复涉政资源类型映射
  val type_dict = Map(
    "dt_image_text"->"dt",
    "dt_text"->"dt",
    "gallery"->"news",
    "littlevideo"->"sv",
    "news"->"news",
    "shortvideo"->"sv"
  )

  // 匹配中文字符的正则表达式
  val chineseRegex = Pattern.compile(".*[\\u4e00-\\u9FFF].*")

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    Day30 = calcnDate(YesterDay, -30)
    println("30天前日期：" + Day30)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val BeForeDayTimeStamp = getTimeStampTake(BeForeDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(author_user_id as long) as uid,
         |  city,
         |  secure_not_pass_reason,
         |  nid,
         |  type3,
         |  status,
         |  author_status,
         |  secure_status,
         |  quality_status,
         |  unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') as secure_trial_timestamp,
         |  unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') as secure_machine_timestamp,
         |  unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') as timestamp
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and
         |  (
         |    (unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${BeForeDayTimeStamp}
         |    and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |    or (unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |    and unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |    or (unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |    and unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |  )
         |""".stripMargin

    // 筛选两天内数据
    val bjhDf = spark.sql(bjhSql)
      .filter(col("timestamp") >= BeForeDayTimeStamp && col("timestamp") < ToDayTimeStamp)
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号两天内数据共：" + bjhDf.count())
    bjhDf.dropDuplicates("uid","city").createOrReplaceTempView("bjh_data")

    //筛选一天内数据
    val bjhOneDf = bjhDf.filter(col("timestamp") >= YesterTimeStamp)
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内数据共：" + bjhOneDf.count())
    bjhOneDf.createOrReplaceTempView("bjh_one_data")

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  cast(uid as long) as uid,
         |  nid,
         |  status,
         |  ownner_type
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${YesterTimeStamp}
         |  and ts < ${ToDayTimeStamp}
         |  and cast(uid as long) > 0
         |  and ownner_type = 0
         |""".stripMargin

    val bdhdDf = spark.sql(bdhdSql)
      .withColumnRenamed("nid","old_nid")
      .withColumn("nid",split(col("old_nid"),("_"))(1))
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //一天大概700w数据
    println("手百数据共：" + bdhdDf.count())
    //bdhdDf.show(5,false)
    bdhdDf.createOrReplaceTempView("bdhd_view")

    //两天内百家号发文IP城市变化
    val cityChangeIPDf = spark.sql(
      """
        |select
        |   uid,
        |   collect_list(city) as city_list
        |from bjh_data
        |where city != ''
        |group by uid
        |""".stripMargin)
      .withColumn("score",(when(size(col("city_list")) >= 6,"100")
        .when(size(col("city_list")) === 6,"90")
        .when(size(col("city_list")) === 5,"80")
        .when(size(col("city_list")) === 4,"70")
        .when(size(col("city_list")) === 3,"60")
        .when(size(col("city_list")) === 2,"50")).cast("float"))
      .filter(col("score") >= 50)

    val cityIpResDf = mergeAndAddColumns("百家号","IP特征标签","IP特征","灰","其它lv3","两天内百家号发文IP城市变化","",cityChangeIPDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("一天内切换使用境内、境外IP的数据共" + cityIpResDf.count())
    //cityIpResDf.show(5,false)

    //贴吧发回帖计算时间差
    val bjhDiffDf = spark.sql(
        """
          |WITH diff_time AS (
          |    SELECT
          |        uid,
          |        -- 计算与上一条记录的时间差（单位：秒）
          |        timestamp -
          |        LAG(timestamp, 1) OVER (PARTITION BY uid ORDER BY timestamp) AS time_diff_seconds
          |    FROM
          |        bjh_one_data
          |)
          |SELECT
          |    uid,
          |    -- 计算总体方差（分母n）
          |    VAR_POP(time_diff_seconds) AS variance_population
          |FROM
          |    diff_time
          |WHERE
          |    time_diff_seconds IS NOT NULL
          |GROUP BY
          |    uid
          |HAVING
          |    COUNT(*) >= 2
          |""".stripMargin)
      .filter(col("variance_population") <= "3")
      .withColumn("score",(when(col("variance_population") === "0.0","100")
        .when(col("variance_population") <= "0.25","90")
        .when(col("variance_population") <= "0.5","80")
        .when(col("variance_population") <= "1","70")
        .when(col("variance_population") <= "2","60")
        .when(col("variance_population") <= "3","50")).cast("float"))

    val bjhVarResDf = mergeAndAddColumns("百家号","日常行为","机器特征","灰","机器","固定步长发文","",bjhDiffDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    //bjhVarResDf.show(5,false)
    println("百家号评论方差计算数据：" + bjhVarResDf.count())

    //百家号高频发文
    val bjhCntDf = spark.sql(
        """
          |with cnt_tmp as (
          |SELECT
          |        uid,
          |        timestamp,
          |        -- 计算1秒内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1s,
          |        -- 计算1分钟内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 60 PRECEDING AND CURRENT ROW
          |        )  AS cnt_1m,
          |        -- 计算1小时内发帖次数
          |        COUNT(*) OVER (
          |            PARTITION BY uid
          |            ORDER BY timestamp
          |            RANGE BETWEEN 3600 PRECEDING AND CURRENT ROW
          |        ) AS cnt_1h
          |    FROM bjh_one_data
          |    ORDER BY uid,timestamp
          | )
          | SELECT
          |    uid,
          |    MAX(
          |        CASE
          |            WHEN cnt_1s > 1 THEN 100     -- 1秒内发帖次数大于1
          |            WHEN cnt_1m > 3 THEN 90     -- 1分钟内发帖次数大于3
          |            WHEN cnt_1h > 5 THEN 80     -- 1小时内发帖次数大于5
          |            ELSE 0
          |        END
          |    ) AS score
          |FROM cnt_tmp
          |group by uid
          |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))

    val bjhResDf = mergeAndAddColumns("百家号","日常行为","机器特征","灰","机器","高频发文","",bjhCntDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号高频发文数据：" + bjhResDf.count())
    bjhResDf.createOrReplaceTempView("bjh_high_freq_view")

    //高频违规发文
    val bjhIllegalDf = spark.sql(
      """
        |select
        |  b.uid,
        |  round(avg(case when b.status in ('forbidden','rejected') then 1.0 else 0.0 end), 2) as ratio_illegal   -- 发文违规率
        |from bjh_one_data b
        |inner join bjh_high_freq_view h on b.uid = h.uid
        |group by b.uid
        |""".stripMargin)
      .withColumn("score",(when(col("ratio_illegal") >= 0.9,"100")
        .when(col("ratio_illegal") >= 0.8,"90")
        .when(col("ratio_illegal") >= 0.7,"80")
        .when(col("ratio_illegal") >= 0.6,"70")
        .when(col("ratio_illegal") >= 0.5,"60")
        .otherwise("0")).cast("float"))
      .filter(col("score") > 0)
      .drop("ratio_illegal")

    val bjhIllegalResDf = mergeAndAddColumns("百家号","日常行为","机器特征","灰","机器","高频违规发文","",bjhIllegalDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号高频违规发文数据：" + bjhIllegalResDf.count())
    bjhIllegalResDf.show(5,false)

    //重复内容发布
    val contentDf = dupicateContent.toDF("content")
    val bjhDupDf = bjhOneDf.as("T")
      .join(contentDf.as("C"),col("T.secure_not_pass_reason").contains(col("C.content")))
      .dropDuplicates("uid")
    val bjhDupResDf = mergeAndAddColumns("百家号","日常行为","发文、发帖、点赞、关注等行为","灰","其它lv3","重复内容发布","80",bjhDupDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("重复内容发布数据量：" + bjhDupResDf.count())

    //与儿童色情种子用户同违规主题活跃
    //评论儿童色情种子用户
     val childrenCsv = readCsv(spark,"儿童色情种子用户")
      .withColumn("uid",col("uid").cast("long"))
    val chindrenBroad = spark.sparkContext.broadcast(childrenCsv)
    chindrenBroad.value.createOrReplaceTempView("children_view")

    val illegalDf = spark.sql(
      """
        |with temp_table as(
        |select
        |  T.nid as nid
        |from bdhd_view T
        |inner join children_view B on T.uid = B.uid
        |inner join (select nid from bjh_one_data where secure_not_pass_reason != '') D on T.nid = D.nid
        |)
        |select
        | T.uid
        |from (select uid,nid from bdhd_view where ownner_type != 1) T
        |left anti join children_view C on T.uid = C.uid
        |inner join temp_table TT on T.nid = TT.nid
        |""".stripMargin)

    val illegalResDf = mergeAndAddColumns("百家号","日常行为","发文、发帖、点赞、关注等行为","灰","未成年色情","与儿童色情种子用户同违规主题活跃","80",illegalDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("与儿童色情种子用户同违规主题活跃百家号共：" + illegalResDf.count())

    //百家号发文违规标签规则=======================================
    //百家号敏感词读取
    val blockschema = StructType(Array(
      StructField("keywords", StringType, true),
      StructField("label_lv2", StringType, true)
    ))
    val blockDf = spark.createDataFrame(spark.sparkContext.parallelize(blockedWords),blockschema)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    val blockedWordsBroad = spark.sparkContext.broadcast(blockDf)
    blockedWordsBroad.value.createOrReplaceTempView("block_words")

    //读取违规标签规则文件,并拆分label_lv3字段为中文和英文部分
    val bdhdillegalDf = readCsv(spark,"百家号发文违规标签规则")
      .withColumn("index",monotonically_increasing_id())
      .withColumn("label_part", splitEnZhUDF(col("label_lv3")))
    val bdhdillegalBroad = spark.sparkContext.broadcast(bdhdillegalDf)
    bdhdillegalBroad.value.createOrReplaceTempView("bjh_illegal_words")

    //筛选一天内数据，包含secure_trial_time和secure_machine_last_time
    val bjhOneSecureDf = bjhDf
      .filter(col("timestamp") >= YesterTimeStamp || col("secure_trial_timestamp") >= YesterTimeStamp || col("secure_machine_timestamp") >= YesterTimeStamp)
      .filter(col("status").isin("deleted", "delete", "forbidden", "rejected", "withdraw", "freeze",  "frozen"))
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内数据包含secure数据共：" + bjhOneSecureDf.count())
    bjhOneSecureDf.createOrReplaceTempView("bjh_one_secure_data")

    val explodeDf = spark.sql(
      """
        |select
        | T.*
        |from
        |(select
        |   uid,
        |   nid,
        |   type3,
        |   explode(split(secure_not_pass_reason, '&&')) as reject_reason
        | from bjh_one_secure_data) T
        | where T.reject_reason != ''
        | """.stripMargin)
    explodeDf.createOrReplaceTempView("explode_bjh_secure_data")

    val illegalTable = spark.sql(
      """
        |with temp_table as (
        |select
        |   T.uid,
        |   E.label_lv1,
        |   E.label_lv2,
        |   FIRST(E.label_lv3) OVER (PARTITION BY T.uid ORDER BY E.index) as label_lv3,
        |   FIRST(E.label_part) OVER (PARTITION BY T.uid ORDER BY E.index) as label_part,
        |   E.score,
        |   T.reject_reason,
        |   nid,
        |   type3,
        |   ROW_NUMBER() OVER (PARTITION BY T.uid ORDER BY 1) as rn
        | from explode_bjh_secure_data T
        | inner join bjh_illegal_words E
        | ON forall(E.label_part, x -> instr(T.reject_reason, x) > 0)
        |)
        |select
        | uid,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | label_part,
        | score,
        | reject_reason,
        | nid,
        | type3
        |from temp_table
        |where rn = 1
        |""".stripMargin)
      .repartition(20)
      .dropDuplicates()
      //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("illegalTable数据量：" + illegalTable.count())
    //illegalTable.show(10,false)
    illegalTable.createOrReplaceTempView("illegal_table_view")

    val illegalWordsTable = spark.sql(
      """
        |with temp_table as (
        |select
        |   T.uid,
        |   T.label_lv1,
        |   B.label_lv2,
        |   T.label_lv3,
        |   label_part,
        |   T.score,
        |   B.keywords,
        |   T.reject_reason,
        |   T.nid,
        |   T.type3
        | from (select * from illegal_table_view where label_lv2 = '通过敏感词表字典获取') T
        | inner join block_words B
        | ON T.reject_reason LIKE CONCAT('%', B.keywords, '%')
        |)
        |select
        | *
        |from temp_table
        |union all
        |select
        | uid,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | label_part,
        | score,
        | '' as keywords,
        | reject_reason,
        | nid,
        | type3
        |from illegal_table_view
        |where label_lv2 != '通过敏感词表字典获取'
        |""".stripMargin)
      .repartition(20)
      .dropDuplicates()
      .drop("label_part","keywords","reject_reason")
      .withColumn("source",lit("百家号"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("百家号发文违规"))
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("illegalWordsTable数据量：" + illegalWordsTable.count())
    illegalWordsTable.createOrReplaceTempView("illegal_words_table")

    //评论、回复涉政资源，需要用到违规处罚标签数据，但是违规处罚标签不在这里写入表中，在illegal中写入
    spark.udf.register("get_type_dict",getTypeDict)

    val bjhSheZhengDf = spark.sql(
      """
        |with tmp_table as (
        |select
        | nid,
        | get_type_dict(type3) as type3
        |from illegal_words_table
        |where label_lv2 = '涉政' and label_lv1 = '黑'
        |),
        |tmp_nid as (
        |select
        |  concat_ws("_",type3,nid) as type_nid
        |from tmp_table
        |where type3 != ''
        |)
        |select
        | B.uid,
        | '评论、回复涉政资源' as label_lv3
        |from tmp_nid T
        |inner join bdhd_view B on T.type_nid = B.old_nid
        |""".stripMargin)
      .dropDuplicates("uid")
      .withColumn("label_lv1",lit("灰"))
      .withColumn("label_lv2",lit("涉政"))
      .withColumn("source",lit("百家号"))
      .withColumn("type",lit("日常行为"))
      .withColumn("sub_type",lit("发文、发帖、点赞、关注等行为"))
      .withColumn("score",lit("80"))
      .dropDuplicates()
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("评论、回复涉政资源的数据：" + bjhSheZhengDf.count())
    //bjhSheZhengDf.show(10,false)

    //百家号色情反感数据===================================================================
    val bjhEroticDf = spark.sql(
      s"""
        |select
        |  userid as uid,
        |  resource_id,
        |  split(resource_id, '_')[1] as nid,
        |  description,
        |  submit_time,
        |  unix_timestamp(submit_time, 'yyyy-MM-dd HH:mm:ss') as timestamp,
        |  event_day
        |from udw_ns.default.help_ods_ufo_feed_report_di
        |where event_day >= '${Day30}'
        |and event_day <= '${YesterDay}'
        |and cast(userid as long) > 0
        |and submit_time >= from_unixtime(
        |                    unix_timestamp('${Day30}', 'yyyyMMdd'),
        |                    'yyyy-MM-dd 00:00:00'
        |                  )
        |""".stripMargin)
      .dropDuplicates()
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("help_ods_ufo_feed_report_di 30天数据量：" + bjhEroticDf.count())
    bjhEroticDf.createOrReplaceTempView("bjh_erotic_total_data")
    //过滤当天数据
    val bjhDayDf = bjhEroticDf.filter(col("event_day") === YesterDay)
    println("bjh_erotic_data数据量：" + bjhDayDf.count())
    bjhDayDf.createOrReplaceTempView("bjh_erotic_data")

    val bjhJuBaoResDf = spark.sql(
      """
        |with tmp_data as ( -- 被拒绝的nid
        |   select
        |      nid
        |   from bjh_one_data
        |   where secure_status = 'rejected'
        |   or quality_status = 'rejected'
        |),
        |erotic_table as ( -- 有举报行为
        |select
        |   uid,
        |   '百家号举报表' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '灰' as label_lv1,
        |   '其它lv2' as label_lv2,
        |   '有举报行为' as label_lv3,
        |   80 as score
        |from bjh_erotic_data
        |),
        |jubao_table as ( -- 举报色情资源
        |select
        |   uid,
        |   '百家号举报表' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '绿' as label_lv1,
        |   '色情反感' as label_lv2,
        |   '举报色情资源' as label_lv3,
        |   80 as score
        |from bjh_erotic_data
        |where description like '%色情低俗%'
        |),
        |sub_standard_table as ( -- 举报色情低标准敏感资源
        | select
        |   uid,
        |   '百家号举报表' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '绿' as label_lv1,
        |   '色情反感' as label_lv2,
        |   '举报色情低标准敏感资源' as label_lv3,
        |   80 as score
        | from bjh_erotic_data
        | where description LIKE '%未成年%' or
        |    description LIKE '%青少年%' or
        |    description LIKE '%儿童%' or
        |    description LIKE '%少年%' or
        |    description LIKE '%低龄用户%' or
        |    description LIKE '%未满18岁%' or
        |    description LIKE '%孩子%' or
        |    description LIKE '%家中有小孩%' or
        |    description LIKE '%wcn%' or
        |    description LIKE '%一直推%' or
        |    description LIKE '%多次推%' or
        |    description LIKE '%重复推%' or
        |    description LIKE '%刷屏%' or
        |    description LIKE '%不喜欢还推%'
        |),
        |choice_table as ( --  举报色情选择性资源
        | SELECT
        |    uid,
        |    '百家号举报表' AS source,
        |    '日常行为' AS type,
        |    '举报' AS sub_type,
        |    '绿' AS label_lv1,
        |    '色情反感' AS label_lv2,
        |    '举报色情选择性资源' AS label_lv3,
        |    80 AS score
        |FROM bjh_erotic_data
        |WHERE
        |    description LIKE '%男同%' OR
        |    description LIKE '%gay%' OR
        |    description LIKE '%同性恋%' OR
        |    description LIKE '%同志%' OR
        |    description LIKE '%txl%' OR
        |    description LIKE '%基佬%' OR
        |    description LIKE '%通讯录%' OR
        |    description LIKE '%男酮%' OR
        |    description LIKE '%广告%' OR
        |    description LIKE '%推广%' OR
        |    description LIKE '%营销%' OR
        |    description LIKE '%软文%' OR
        |    description LIKE '%弹窗广告%' OR
        |    description LIKE '%硬广%' OR
        |    description LIKE '%卖丝袜%' OR
        |    description LIKE '%丝袜促销%' OR
        |    description LIKE '%擦边营销%' OR
        |    description LIKE '%软色情带货%' OR
        |    description LIKE '%黑丝推广%' OR
        |    description LIKE '%乱伦%' OR
        |    description LIKE '%近亲性行为%' OR
        |    description LIKE '%伦理失序%' OR
        |    description LIKE '%母子%'
        |),
        |success_jubao_table as ( --  成功举报色情资源
        | select
        |   t.uid,
        |   '百家号举报表，百家号' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '绿' as label_lv1,
        |   '色情反感' as label_lv2,
        |   '成功举报色情资源' as label_lv3,
        |   80 as score
        | from bjh_erotic_data t
        | inner join jubao_table j on t.uid = j.uid
        | inner join tmp_data b on t.nid = b.nid
        |),
        |success_lowlevel_table as ( --  成功举报色情低标准敏感资源
        | select
        |   t.uid,
        |   '百家号举报表，百家号' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '绿' as label_lv1,
        |   '色情反感' as label_lv2,
        |   '成功举报色情低标准敏感资源' as label_lv3,
        |   80 as score
        | from bjh_erotic_data t
        | inner join sub_standard_table j on t.uid = j.uid
        | inner join tmp_data b on t.nid = b.nid
        |),
        |success_choice_table as ( --  成功举报色情选择性资源
        | select
        |   t.uid,
        |   '百家号举报表，百家号' as source,
        |   '日常行为' as type,
        |   '举报' as sub_type,
        |   '绿' as label_lv1,
        |   '色情反感' as label_lv2,
        |   '成功举报色情选择性资源' as label_lv3,
        |   80 as score
        | from bjh_erotic_data t
        | inner join choice_table j on t.uid = j.uid
        | inner join tmp_data b on t.nid = b.nid
        |)
        |select * from erotic_table
        |union all
        |select * from jubao_table
        |union all
        |select * from sub_standard_table
        |union all
        |select * from choice_table
        |union all
        |select * from success_jubao_table
        |union all
        |select * from success_lowlevel_table
        |union all
        |select * from success_choice_table
        |""".stripMargin)
      .dropDuplicates()
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("bjhJuBaoResDf色情反感数据量：" + bjhJuBaoResDf.count())

    //高频举报
    val bjhHighReportDf = spark.sql(
      """
        |with cnt_tmp as (
        |SELECT
        |        uid,
        |        timestamp,
        |        -- 计算0秒内举报次数
        |        COUNT(*) OVER (
        |            PARTITION BY uid
        |            ORDER BY timestamp
        |            RANGE BETWEEN CURRENT ROW AND CURRENT ROW
        |        )  AS cnt_0s,
        |        -- 计算1秒内举报次数
        |        COUNT(*) OVER (
        |            PARTITION BY uid
        |            ORDER BY timestamp
        |            RANGE BETWEEN 1 PRECEDING AND CURRENT ROW
        |        )  AS cnt_1s,
        |        -- 计算2秒内举报次数
        |        COUNT(*) OVER (
        |            PARTITION BY uid
        |            ORDER BY timestamp
        |            RANGE BETWEEN 2 PRECEDING AND CURRENT ROW
        |        )  AS cnt_2s,
        |        -- 计算3秒内举报次数
        |        COUNT(*) OVER (
        |            PARTITION BY uid
        |            ORDER BY timestamp
        |            RANGE BETWEEN 3 PRECEDING AND CURRENT ROW
        |        )  AS cnt_3s
        |    from bjh_erotic_data
        |    where event_day = date_format(submit_time, 'yyyyMMdd')
        |    ORDER BY uid,timestamp
        | )
        | SELECT
        |    uid,
        |    MAX(
        |        CASE
        |            WHEN cnt_0s >= 2 THEN 100    -- 0秒内发帖次数大于等于2
        |            WHEN cnt_1s >= 2 THEN 90     -- 1秒内发帖次数大于等于2
        |            WHEN cnt_2s >= 3 THEN 80     -- 2秒内发帖次数大于等于3
        |            WHEN cnt_3s >= 4 THEN 70     -- 3秒内发帖次数大于等于4
        |            ELSE 0
        |        END
        |    ) AS score
        |FROM cnt_tmp
        |group by uid
        |""".stripMargin)
      .filter(col("score") =!= "0")
      .withColumn("score",col("score").cast("float"))

    val bjhHighReportResDf = mergeAndAddColumns("百家号举报表","日常行为","举报","灰","机器","高频举报","",bjhHighReportDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号高频举报数据：" + bjhHighReportResDf.count())
    //bjhHighReportResDf.show(5,false)

    //针对性多次举报
    val bjhMangReportDf = spark.sql(
      """
        |select
        | uid,
        | round(
        |    count(*) / nullif(count(distinct resource_id), 0),  -- 防止除数为0
        |    2
        |  ) as avg_reports_resource
        |from bjh_erotic_total_data
        |group by uid
        |""".stripMargin)
      .withColumn("score",(when(col("avg_reports_resource") >= 7.0,"100")
        .when(col("avg_reports_resource") >= 6.0,"90")
        .when(col("avg_reports_resource") >= 5.0,"80")
        .when(col("avg_reports_resource") >= 4.0,"70")
        .when(col("avg_reports_resource") >= 3.0,"60")
        .otherwise("0")).cast("float"))
      .filter(col("score") > 0)

    val bjhMangReportResDf = mergeAndAddColumns("百家号举报表","日常行为","举报","灰","其它lv3","针对性多次举报","",bjhMangReportDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("百家号针对性多次举报数据：" + bjhMangReportResDf.count())
    //bjhMangReportResDf.show(5,false)

    //获取case_id列表
    val idBaseList = bjhDayDf
      .filter(date_format(col("submit_time"),"yyyyMMdd") === YesterDay)
      .select("uid")
      .distinct()
      .rdd
      .map(row => row.getString(0))
      .collect()
    println("uidBaseList：" + idBaseList.length)
    // 创建广播变量并保留引用
    val broadcastVar = spark.sparkContext.broadcast(idBaseList)
    // 使用广播变量
    val uidString = broadcastVar.value.mkString("'", "','", "'")

    // 读取用户行为明细数据
    val feedDwdDf = spark.sql(
      s"""
         |with tmp_data as (
         |select
         |  nid
         |from bjh_erotic_data
         |where event_day = date_format(submit_time, 'yyyyMMdd')
         |)
         |select
         | t.uid,
         | t.rid,
         | t.ext_log_pd
         |from
         |(select
         |  uid,
         |  rid,
         |  ext_log_pd
         |from
         |  ubs_feed.feed_dwd_pub_log_hi
         |where
         |  event_day = '${YesterDay}'
         |  and event_minute = '00'
         |  and event_action in ('display_list', 'display_land', 'click')
         |  and uid != ''
         |  and cast(uid as long) > 0
         |  and uid in (${uidString})
         |) t
         |inner join tmp_data d on t.rid = d.nid
         |group by
         |  t.uid,
         |  t.rid,
         |  t.ext_log_pd
         |""".stripMargin)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("feedDwdDf数据量：" + feedDwdDf.count())
    feedDwdDf.createOrReplaceTempView("feed_dwd_pub_log_hi")
    //feedDwdDf.show(5,false)

    //================================================搜索目的性举报==================================================

    val searchReportDf = spark.sql(
      """
        |SELECT
        |    uid,
        |    COUNT(DISTINCT rid) AS nid_cnt  -- 搜索次数
        |FROM feed_dwd_pub_log_hi
        |where ext_log_pd LIKE '%search%'
        |GROUP BY uid
        |""".stripMargin)
      .withColumn("score",(when(col("nid_cnt") >= 7,"100")
        .when(col("nid_cnt") >= 6,"90")
        .when(col("nid_cnt") >= 5,"80")
        .when(col("nid_cnt") >= 4,"70")
        .when(col("nid_cnt") >= 3,"60")
        .otherwise("0")).cast("float"))
      .filter(col("score") > 0)

    val searchReportResDf = mergeAndAddColumns("百家号","日常行为","举报","灰","其它lv3","搜索目的性举报","",searchReportDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号搜索目的性举报数据：" + searchReportResDf.count())
    //searchReportResDf.show(5,false)

    //================================================举报分享资源==================================================
    val shareReportDf = spark.sql(
        """
          |SELECT
          |    uid,
          |    COUNT(DISTINCT rid) AS nid_cnt  -- 分享次数
          |FROM feed_dwd_pub_log_hi
          |where ext_log_pd = 'share'
          |GROUP BY uid
          |""".stripMargin)
      .withColumn("score",(when(col("nid_cnt") >= 7,"100")
        .when(col("nid_cnt") >= 6,"90")
        .when(col("nid_cnt") >= 5,"80")
        .when(col("nid_cnt") >= 4,"70")
        .when(col("nid_cnt") >= 3,"60")
        .otherwise("0")).cast("float"))
      .filter(col("score") > 0)

    val shareReportResDf = mergeAndAddColumns("百家号","日常行为","举报","灰","其它lv3","举报分享资源","",shareReportDf)
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号举报分享资源数据：" + shareReportResDf.count())
    //shareReportResDf.show(5,false)

    //合并数据
    var resTotalDf = unionIfNotEmpty(cityIpResDf,bjhVarResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhVarResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhIllegalResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhDupResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,illegalResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhSheZhengDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhJuBaoResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhHighReportResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,bjhMangReportResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,searchReportResDf)
      resTotalDf = unionIfNotEmpty(resTotalDf,shareReportResDf)
      .withColumn("score",col("score").cast("float"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("合并数据量：" + resTotalDf.count())
    resTotalDf.createOrReplaceTempView("res_label_data")

    //释放资源
    bjhDf.unpersist()
    bdhdDf.unpersist()
    cityIpResDf.unpersist()
    bjhVarResDf.unpersist()
    bjhResDf.unpersist()
    bjhIllegalResDf.unpersist()
    bjhDupResDf.unpersist()
    bjhOneSecureDf.unpersist()
    illegalTable.unpersist()
    illegalWordsTable.unpersist()
    illegalResDf.unpersist()
    resTotalDf.unpersist()
    bjhVarResDf.unpersist()
    bjhHighReportResDf.unpersist()
    bjhMangReportResDf.unpersist()
    feedDwdDf.unpersist()
    searchReportResDf.unpersist()
    shareReportResDf.unpersist()

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
        |select
        | uid,
        | source,
        | type,
        | sub_type,
        | label_lv1,
        | label_lv2,
        | label_lv3,
        | score
        |from res_label_data
        |""".stripMargin)

    resTotalDf.unpersist()
    spark.stop()
  }

  //按照中文和英文单词切分的udf
  val splitEnZhUDF: UserDefinedFunction = udf { (str: String) =>
    if (str == null) {
      Array("")
    } else if (str.contains("_")) {
      val tokens = str.split("_")
      val index = tokens.indexWhere(token => chineseRegex.matcher(token).matches())
      if (index == -1) Array(str)
      else Array(tokens.take(index).mkString("_")) ++ tokens.drop(index)
    }else Array(str)
  }

  //获取type的中文描述
  val getTypeDict = udf((str: String) => {
    if (str == null && str == "") {
      ""
    } else {
      type_dict.getOrElse(str, "")
    }
  })
}

