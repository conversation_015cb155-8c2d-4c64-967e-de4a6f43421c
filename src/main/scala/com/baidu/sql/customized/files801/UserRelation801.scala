package com.baidu.sql.customized.files801

import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import com.baidu.sql.utils.UdfUtils._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.storage.StorageLevel


object UserRelation801 {
  //`uid` STRING COMMENT '用户UID' SAMPLE '423435454',
  //`type` STRING COMMENT '关联类型' SAMPLE 'IP、CUID',
  //`value` STRING COMMENT '关联值' SAMPLE '*******',
  //`source` STRING COMMENT '数据来源表' SAMPLE 'bjh_feed_resource_rf',
  //`properties` STRING COMMENT '城市/设备数据值' SAMPLE '{"device": "apple"}'

  // 昨日日期
  var YesterDay = ""
  // 昨日计算日期 yyyy-MM-dd
  var YesterClacDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 今日计算日期 yyyy-MM-dd
  var ToClacDay = ""
  //回环IP（127.0.0.1）、无意义IP（0.0.0.0、***************、空）
  val ipList = List("127.0.0.1","0.0.0.0","***************","")

  /**
   * 用户关联表
   * @param args
   */
  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 昨日计算日期 yyyy-MM-dd
    YesterClacDay = calcnDateFormat(YesterDay)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    // 今日计算日期 yyyy-MM-dd
    ToClacDay = calcnDateFormat(ToDay)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer","org.apache.spark.serializer.KryoSerializer")
      .config("spark.kryoserializer.buffer.max", "256m")
      .config("spark.sql.files.maxPartitionBytes", "512MB")
      .config("spark.sql.files.openCostInBytes", "4194304")
      .appName("userRelation801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    // 读取手百数据
    val bdhdSql =
      s"""
         |select
         |  uid,
         |  cuid as value
         |from
         |  udw_ns.default.bdhd_comment_info
         |where
         |  event_day = '${YesterDay}'
         |  and ts >= ${YesterTimeStamp}
         |  and ts < ${ToDayTimeStamp}
         |  and uid > 0
         |  and cuid != ''
         |  and ownner_type = 0
         |group by uid,cuid
         |""".stripMargin

    var bdhdDf = spark.emptyDataFrame
    try{
      bdhdDf = spark.sql(bdhdSql)
        .withColumn("type",lit("CUID"))
        .withColumn("source",lit("bdhd_comment_info"))
        .withColumn("properties",lit(""))
        .dropDuplicates()
        .repartition(200)
      //一天大概700w数据
      println("手百数据共：" + bdhdDf.count())
      bdhdDf.show(5,false)
    }catch {
      case e: Exception => println("手百数据读取失败,原因:" + e.getMessage)
    }

    // 读取贴吧数据,country,province,city字段没权限
    val tiebaSql =
      s"""
         |select
         |  uid,
         |  ip as value
         |  -- country,
         |  -- province,
         |  -- city
         |from
         |  ubs_tieba.tieba_dim_pub_info_hi
         |where
         |  event_day = '${YesterDay}'
         |  and uid > 0
         |  and ip not in ('127.0.0.1','0.0.0.0','***************','')
         |""".stripMargin

    var tiebaResDf = spark.emptyDataFrame

    try {
      tiebaResDf = spark.sql(tiebaSql)
        //过滤无效IP
        .filter(!col("value").isin(ipList: _*))
        .withColumn("is_private", isPrivateIPUDF(col("value")))
        .filter(col("is_private") === false)
        //.withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("country").asc_nulls_last,col("province").asc_nulls_last,col("city").asc_nulls_last)))
        //.filter(col("rn") === 1)
        .withColumn("type", lit("IP"))
        .withColumn("source",lit("tieba_dim_pub_info_hi"))
        .withColumn("properties", lit(""))
        //.withColumn("properties", concat(lit("{'country':'"), col("country"), lit("','province':'"), col("province"), lit("','city':'"), col("city"), lit("'}")))
        //.drop("country", "province", "city","is_private","rn")
        .drop("is_private")
        .dropDuplicates()
    }catch {
      case e: Exception => println("IP数据读取失败,原因:" + e.getMessage)
    }


    // 读取用户行为明细数据
    val feedDwdSql =
      s"""
         |select
         |  uid,
         |  ip,
         |  cuid,
         |  country,
         |  province,
         |  city,
         |  os,
         |  brand,
         |  device
         |from
         |  ubs_feed.feed_dwd_pub_log_hi
         |where
         |  event_day = '${YesterDay}'
         |  and event_minute = '00'
         |  and event_action in ('click','display_list','dura','other','dau')
         |  and uid != ''
         |  and cast(uid as long) > 0
         |  and (
         |    ip not in ('127.0.0.1','0.0.0.0','***************','')
         |    or cuid != ''
         |  )
         |""".stripMargin

    var feedResDf = getSqlData(spark,feedDwdSql,"IP,CUID")
      if (feedResDf.count() > 0){
        feedResDf = feedResDf
          .withColumn("source",lit("feed_dwd_pub_log_hi"))
          .repartition(10)
      }else{
        println("feed_dwd_pub_log_hi数据为空")
        sys.exit(1)
      }

    //写入数据
    var totalResDf = feedResDf
    totalResDf = unionIfNotEmpty(totalResDf, tiebaResDf)

    totalResDf = totalResDf
      .na.fill("")
      .dropDuplicates()
      .repartition(10)
    println("最终总的数据量为：" + totalResDf.count())
    totalResDf.createOrReplaceTempView("ResDf")

    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_user_relation partition (event_day = ${YesterDay})
         |select
         |   uid,
         |   type,
         |   value,
         |   source,
         |   properties
         |from ResDf
         |""".stripMargin)

    spark.stop()
  }

  /**
   * 读取数据并处理IP和CUID数据
   * @param spark
   * @param HiveSql hive sql
   * @param dataType 数据类型:IP,CUID
   * @return DataFrame
   */
  def getSqlData(spark: SparkSession,HiveSql: String,dataType:String,persist:Boolean = true): DataFrame = {
    //最终返回的数据集
    var resultDf = spark.emptyDataFrame
    //读取hive数据
    var feedDwdDf = spark.emptyDataFrame
      try{
      feedDwdDf = spark.sql(HiveSql)
        .dropDuplicates()
      if(persist) {
        feedDwdDf = feedDwdDf.persist(StorageLevel.MEMORY_AND_DISK_SER)
      }
      println("数据共：" + feedDwdDf.count())
    } catch {
      case e: Exception => println("数据读取失败,原因:" + e.getMessage)
    }
    feedDwdDf.createOrReplaceTempView("view_data")


    if (dataType.contains("IP")){
      try {
        val IpDf = spark.sql(
            """
              |select
              |   uid,
              |   ip as value,
              |   country,
              |   province,
              |   city
              |from view_data
              |""".stripMargin)
          //过滤无效IP
          .filter(!col("value").isin(ipList: _*))
          .withColumn("is_private", isPrivateIPUDF(col("value")))
          .filter(col("is_private") === false)
          .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("country").asc_nulls_last,col("province").asc_nulls_last,col("city").asc_nulls_last)))
          .filter(col("rn") === 1)
          .withColumn("type", lit("IP"))
          .withColumn("properties", concat(lit("{'country':'"), col("country"), lit("','province':'"), col("province"), lit("','city':'"), col("city"), lit("'}")))
          .drop("country", "province", "city","is_private","rn")
          .dropDuplicates()
          //.repartition(400)

        val dfCount = IpDf.count()
        println("IP数据共：" + dfCount)
        //tiebaDf.createOrReplaceTempView("tieba")
        //IpDf.show(5,false)
        if (dfCount > 0 && resultDf.isEmpty){
          resultDf = IpDf
        }
      }catch {
        case e: Exception => println("IP数据读取失败,原因:" + e.getMessage)
      }
    }

    if(dataType.contains("CUID")){
      try {
        val CuidDf = spark.sql(
            """
              |select
              |   uid,
              |   cuid as value,
              |   os,
              |   brand,
              |   device
              |from view_data
              |""".stripMargin)
          .filter(col("value") =!= "")
          .withColumn("rn",row_number().over(Window.partitionBy("uid").orderBy(col("value").asc_nulls_last,col("os").asc_nulls_last,col("brand").asc_nulls_last,col("device").asc_nulls_last)))
          .filter(col("rn") === 1)
          .withColumn("value", cleanChars(col("value")))
          .withColumn("type", lit("CUID"))
          .withColumn("properties", concat(lit("{'os':'"), col("os"), lit("','brand':'"), col("brand"), lit("','device':'"), col("device"), lit("'}")))
          .drop("os", "brand", "device","rn")
          .dropDuplicates()
          //.repartition(400)

        val dfCount = CuidDf.count()
        println("CUID数据共：" + dfCount)
        //tiebaDf.createOrReplaceTempView("tieba")
        //CuidDf.show(5, false)
        if (dfCount > 0 && resultDf.isEmpty){
          resultDf = CuidDf
        }else if (dfCount > 0  && !resultDf.isEmpty){
          resultDf = resultDf.union(CuidDf)
        }
      }catch {
        case e: Exception => println("CUID数据读取失败,原因:" + e.getMessage)
      }
    }
    //feedDwdDf.unpersist()
    resultDf
  }

}

