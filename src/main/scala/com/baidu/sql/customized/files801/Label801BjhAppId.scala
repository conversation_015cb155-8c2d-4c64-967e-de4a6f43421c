package com.baidu.sql.customized.files801

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONArray}
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.types.{ArrayType, IntegerType, StringType, StructField, StructType}
import org.apache.spark.storage.StorageLevel
import scala.util.Random
import java.security.MessageDigest
import java.util.concurrent.TimeUnit
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import sys.process._
import java.util.concurrent.atomic.AtomicLong

/**
 *  @author: zhangrunjie 801风控用户标签表，百家号数据标签
 */
object Label801BjhAppId {
  //  source    str    数据来源
  //  type    str    标签类型
  //  sub_type    str    标签子类型
  //  label_lv1    str    标签一级分类
  //  label_lv2    str    标签二级分类
  //  label_lv3    str    标签三级分类
  //  score    float    分数/置信度
  //  event_day    str    时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""

  val RELEVANCE_STATUS = Map(
    "0" -> "间接关联",
    "1" -> "作者关联-自主",
    "2" -> "作者关联-代绑",
    "3" -> "作者关联-私信",
    "4" -> "运营关联"
  )

  val PLATFORM_SOURCE = Map(
    "1" -> "抖音",
    "2" -> "B站",
    "3" -> "快手",
    "4" -> "头条",
    "5" -> "微博",
    "6" -> "微信公众号",
    "7" -> "爱奇艺",
    "8" -> "小红书",
    "9" -> "企鹅号",
    "10" -> "知乎",
    "11" -> "逛逛",
    "12" -> "视频号"
  )

  val DOUYIN_STATUS = Map(
    "1" -> "可用",
    "-1" -> "注销",
    "-2" -> "封禁",
    "-3" -> "禁言",
    "" -> "未抓到"
  )

  val AK = "f89e27198c65547afee36c6bed76413c"
  val SK = "m23U5c5WyuXQ"

  // 实例ip和端口获取命令
  val ipShell = "get_instance_by_service -ips group.bjh-gateway.searchbox.all"

  val okHttpClient = new OkHttpClient.Builder()
    .callTimeout(40, TimeUnit.SECONDS)
    .connectTimeout(40, TimeUnit.SECONDS)
    .readTimeout(40, TimeUnit.SECONDS)
    .build()

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    // 并发数
    val asyncNum = args(1).toInt

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    val BeForeDayTimeStamp = getTimeStampTake(BeForeDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 1. 执行命令行获取服务实例信息
    val commandOutput = ipShell.!!
    println(s"命令输出: $commandOutput")

    // 2. 按行分割并解析每行数据
    val serviceInstances = commandOutput.split("\n").flatMap { line =>
      val parts = line.trim.split("\\s+")  // 按空格分割每行
      if (parts.length >= 4) {
        // 格式：IP1 IP2 端口 状态 → 我们需要第二个IP和端口
        val ip = parts(1)   // 第二个IP地址
        val port = parts(2) // 端口号
        Some(ip + ":" + port)   // 返回(ip:port)字符串
      } else {
        None  // 忽略不符合格式的行
      }
    }.toSeq
    println(s"可用服务实例数量: ${serviceInstances.length}")

    val maxQpsPerIp = args(2).toInt // 每个IP最大QPS
    val totalMaxQps = serviceInstances.length * maxQpsPerIp
    val safetyBuffer = 0.9 // 90%安全系数
    val targetGlobalQps = (totalMaxQps * safetyBuffer).toInt

    // 分区数设置：建议为IP数量的1-2倍，确保负载均衡
    val optimalPartitionCount = Math.min(serviceInstances.length * 2, asyncNum) // 最多40个分区

    println(s"目标全局QPS: $targetGlobalQps, 分区数: $optimalPartitionCount")

    // 创建基于IP的令牌桶控制器
    val ipBasedTokenController = new IpBasedTokenBucketController(serviceInstances)
    val broadcastTokenController = spark.sparkContext.broadcast(ipBasedTokenController)

    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(author_user_id as long) as uid,
         |  app_id,
         |  count(*) as bjh_count
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and app_id != ''
         |  and cast(app_id as long) > 0
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |  and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp}
         |group by uid,app_id
         |""".stripMargin

    val bjhDf = spark.sql(bjhSql)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号一天内数据共：" + bjhDf.count())
    bjhDf.createOrReplaceTempView("bjh_data")
    bjhDf.show(5,false)

    //接口数据获取 - 使用mapPartitions进行分布式处理
    val sessionArray = bjhDf
      .filter($"app_id" =!= "")
      .select("app_id")
      .repartition(optimalPartitionCount) // 动态分区数
      .mapPartitions { partition =>
        val tokenCtrl = broadcastTokenController.value

        // 分区启动时添加随机延迟，避免所有分区同时发起请求
        Thread.sleep(Random.nextInt(100)) // 0-100ms随机延迟

        partition.map { row =>
          val appId = row.getString(0)
          val responseJson = processRequestWithTokenControl(appId, tokenCtrl)
          (appId, responseJson)
        }
      }
      .toDF("app_id", "responseJson")
      .filter($"responseJson" =!= "" && $"responseJson".isNotNull)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("接口请求数据共：" + sessionArray.count())

    // 转换为DataFrame操作
    val processedDF = sessionArray
      // 解析JSON响应
      .withColumn("jsonObj",
        when($"responseJson".isNotNull && $"responseJson" =!= "" &&
          json_format_valid($"responseJson"),  // 验证JSON格式
          from_json($"responseJson", StructType(Seq(
            StructField("errno", IntegerType),
            StructField("data", StructType(Seq(
              StructField("list", ArrayType(StringType))
            ))
            )))
          )
        ).otherwise(lit(null)))
      // 提取errno和data.list
      .withColumn("errno", $"jsonObj.errno")
      .withColumn("dataArray",
        when($"errno" === 0, $"jsonObj.data.list")
          .otherwise(array().cast(ArrayType(StringType)))
      )
      // 将数组元素转换为字符串序列
      .withColumn("resArray",
        when(size($"dataArray") > 0, $"dataArray")
          .otherwise(array().cast(ArrayType(StringType)))
      )
      .drop("jsonObj", "dataArray", "responseJson")  // 清理中间列

    val operatorDF = processedDF
      .filter(size($"resArray") =!= 0)  // 过滤空数组
      .withColumn("data", getDatalist($"resArray"))  // 使用UDF处理
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("接口数据共：" + operatorDF.count())
    operatorDF.createOrReplaceTempView("operator_data")

    //按照<=>分割data字段
    val operDeailDf = spark.sql(
      """
        |select
        |   app_id,
        |   explode(split(data,'<=>')) as data_details
        |from operator_data
        |where data != ''
        |""".stripMargin)

    val sourceDataDf = operDeailDf
      .withColumn("platform_source",getNullVal(split(col("data_details"),"=>").getItem(0).cast("string")))
      .withColumn("relevance_status",getNullVal(split(col("data_details"),"=>").getItem(1).cast("string")))
      .withColumn("status",getNullVal(split(col("data_details"),"=>").getItem(2).cast("string")))
      //过滤掉非抖音的数据和状态不为-1,-2,-3的数据
      .filter(col("status").isin("-1","-2","-3") && col("platform_source") === "1")
      .repartition(20)
    sourceDataDf.show(10,false)

    val resLabelDf = bjhDf.as("T")
      .join(sourceDataDf.as("S"),$"T.app_id" === $"S.app_id")
      .withColumn("source",lit("百家号_竞品情报"))
      .withColumn("type",lit("竞品侧违规"))
      .withColumn("sub_type",lit("竞品侧违规"))
      .withColumn("label_lv1",lit("灰"))
      .withColumn("label_lv2",lit("其它lv1"))
      .withColumn("label_lv3",concat(lit("douyin_block_"),getrelevanceStatus(col("relevance_status")),lit("_"),getStatus(col("status"))))
      .withColumn("score",when(col("relevance_status") === "0",lit("60")).otherwise(lit("100")).cast("float"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .dropDuplicates()
      .coalesce(1)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("最终数据共：" + resLabelDf.count())
    resLabelDf.createOrReplaceTempView("res_label_data")

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
         |select
         | uid,
         | source,
         | type,
         | sub_type,
         | label_lv1,
         | label_lv2,
         | label_lv3,
         | score
         |from res_label_data
         |""".stripMargin)

    resLabelDf.unpersist()
    spark.stop()
  }

  //获取datalist字段
  val getDatalist = udf((data: mutable.WrappedArray[String]) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      val res = ListBuffer[String]()
      data.foreach(row => {
        //获取json数据
        val json = JSON.parseObject(row)
        //关联状态
        val relevance_status = json.getOrDefault("relevance_status","").toString
        //平台来源
        val platform_source = json.getOrDefault("platform_source","").toString
        //状态
        val status = json.getOrDefault("status","").toString
        if (platform_source.equals("1")){
          res += Seq(platform_source,relevance_status,status).mkString("=>")
        }
      })
      res.mkString("<=>")
    }
  })

  //获取关联状态字段
  val getrelevanceStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      RELEVANCE_STATUS.getOrElse(data,"")
    }
  })

  //获取状态字段
  val getStatus = udf((data: String) => {
    if(data == null || data.isEmpty) {
      ""
    }else{
      DOUYIN_STATUS.getOrElse(data,"")
    }
  })

  /**
   * 带令牌桶控制的请求处理函数
   */
  def processRequestWithTokenControl(app_id: String, ipTokenController: IpBasedTokenBucketController): String = {
    var result = ""
    var attempts = 0
    val maxRetries = 3

    while (attempts < maxRetries && result.isEmpty) {
      attempts += 1

      // 获取可用IP和对应的令牌桶控制器
      val (ip, tokenController) = ipTokenController.getAvailableIpWithTokenControl()

      try {
        // 获取令牌（会自动等待直到有可用令牌）
        tokenController.acquire()

        val httpUrlLocal = s"http://${ip}/gateway/aiop_operation/aiop-relevance-api/direct_relation_list?"

        // 生成时间戳和令牌
        val ts = currentTimestampMillis(10)
        val input = s"$AK$ts$SK"
        val tk = MessageDigest
          .getInstance("MD5")
          .digest(input.getBytes("UTF-8"))
          .map("%02x".format(_)).mkString

        // 构造URL
        val encodedReq = java.net.URLEncoder.encode(
          s"""{"app_id":"$app_id","platform_source":0}""",
          "UTF-8"
        )
        val httpurl = httpUrlLocal + s"AK=${AK}&TK=${tk}&TS=${ts}&req=$encodedReq"

        var response: Response = null

        try {
          val request = new Request.Builder().url(httpurl).get().build()
          response = okHttpClient.newCall(request).execute()

          if (response.isSuccessful) {
            result = response.body.string
          } else {
            // 添加429超限错误码处理
            if (response.code() == 429) {
              // 指数退避策略 + 随机抖动
              val baseDelay = 100 * Math.pow(2, attempts).toLong
              val jitter = Random.nextInt(50)
              Thread.sleep(baseDelay + jitter)
              result = ""  // 重置结果以触发重试
            }
          }
        } catch {
          case _: Exception => // 忽略异常，继续重试
        } finally {
          if (response != null) response.close()
        }

        // 响应结果处理
        if (result.nonEmpty && JSONUtil.isJson(result)) {
          val jsonObject = JSON.parseObject(result)
          if (!jsonObject.isEmpty) {
            val errno = Option(jsonObject.getInteger("errno")).getOrElse(-1)
            errno match {
              case 0 => // 成功返回，保持result
              // 添加429错误码处理（针对JSON返回中的错误码）
              case 100101007 | 429 => // 令牌不足错误或超限错误
                // 指数退避策略 + 随机抖动，避免重试集中
                val baseDelay = 100 * Math.pow(2, attempts).toLong
                val jitter = Random.nextInt(50) // 0-50ms随机抖动
                Thread.sleep(baseDelay + jitter)
                result = "" // 重置结果继续重试
              case _ => result = "" // 其他错误码
            }
          }
        } else {
          result = "" // 非JSON或空响应
        }
      }
    }
    result
  }

  /**
   * 基于IP的令牌桶控制器
   */
  class IpBasedTokenBucketController(serviceInstances: Seq[String]) extends Serializable {
    // 每个IP对应一个令牌桶控制器，100ms生成一个令牌
    @transient lazy val ipTokenControllers = serviceInstances.map(ip =>
      ip -> new TokenBucketController(1, 100) // 1个令牌，100ms填充间隔
    ).toMap

    def getAvailableIpWithTokenControl(): (String, TokenBucketController) = {
      // 随机选择一个IP及其对应的令牌桶控制器
      val randomIp = serviceInstances(Random.nextInt(serviceInstances.length))
      val tokenController = ipTokenControllers(randomIp)
      (randomIp, tokenController)
    }
  }

  /**
   * 令牌桶控制器，适配外部服务的100ms令牌填充机制
   * @param maxTokens 最大令牌数
   * @param tokenIntervalMs 令牌填充间隔(ms)
   */
  class TokenBucketController(maxTokens: Int, tokenIntervalMs: Long) extends Serializable {
    @transient private val tokens = new AtomicLong(maxTokens) // 当前可用令牌数
    @transient private val lastRefillTime = new AtomicLong(System.currentTimeMillis())

    /**
     * 获取令牌，如果没有可用令牌则等待
     */
    def acquire(): Unit = {
      while (true) {
        val now = System.currentTimeMillis()
        val elapsed = now - lastRefillTime.get()

        // 计算这段时间内应该补充的令牌数
        val newTokens = (elapsed / tokenIntervalMs).toInt

        if (newTokens > 0) {
          // 补充令牌，不超过最大令牌数
          lastRefillTime.addAndGet(newTokens * tokenIntervalMs)
          tokens.updateAndGet(current =>
            Math.min(current + newTokens, maxTokens).toLong
          )
        }

        // 尝试获取令牌
        if (tokens.get() > 0 && tokens.decrementAndGet() >= 0) {
          return // 获取成功
        } else {
          // 没有可用令牌，等待到下一个令牌生成
          val waitTime = tokenIntervalMs - (elapsed % tokenIntervalMs)
          Thread.sleep(waitTime)
        }
      }
    }
  }
}