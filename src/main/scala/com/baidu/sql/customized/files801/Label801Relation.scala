package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.storage.StorageLevel
import com.baidu.sql.utils.UdfUtils._

/**
 *  @author: zhangrunjie 801风控用户标签表，用户关联表数据标签
 */
object Label801Relation {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  var day30 = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    day30 = calcnDate(YesterDay, -30)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801Relation")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取help_ods_crcc_user_relation用户关联表
    val userRelationSql =
      s"""
         |select
         |    uid,
         |    type,
         |    properties,
         |    event_day,
         |    value
         |from
         |    udw_ns.default.help_ods_crcc_user_relation
         |where
         |  event_day >= '${day30}'
         |  and event_day <= '${YesterDay}'
         |  and type = 'IP'
         |union all
         |select
         |    uid,
         |    type,
         |    properties,
         |    event_day,
         |    value
         |from
         |    udw_ns.default.help_ods_crcc_user_relation
         |where
         |  event_day = '${YesterDay}' and type = 'CUID'
         |""".stripMargin
    //读取用户关联表
    val userRelation30Df = spark.sql(userRelationSql)
      .withColumn("country", getNullVal(get_json_object(col("properties"), "$.country")).cast("string"))

    //完全境外IP,需要30天内数据
    val completeOverseasIPdf = userRelation30Df
      .filter(col("type") === "IP" and !col("country").isin("CN","中国"))
      .dropDuplicates("uid")
      .repartition(200)

    val completeOverseasIPResdf = mergeAndAddColumns("用户关联表","IP特征标签","IP特征","灰","其它lv3","完全境外IP","80",completeOverseasIPdf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("完全境外IP的数据共" + completeOverseasIPResdf.count())

    val userRelationDf =  userRelation30Df.filter(col("event_day") === YesterDay and col("type") === "IP")
      .withColumn("province", getNullVal(get_json_object(col("properties"), "$.province")).cast("string"))
      .drop("properties")
      .repartition(500)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("用户关联表数据共" + userRelationDf.count())
    userRelationDf.createOrReplaceTempView("userRelation")

    //使用境外IP
    val overseasIPdf = userRelationDf
      .filter(!col("country").isin("CN","中国"))
      .dropDuplicates("uid")
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("境外IP筛选的数据共" + overseasIPdf.count())
    overseasIPdf.createOrReplaceTempView("overseasIP")

    val overseasIPResdf = mergeAndAddColumns("用户关联表","IP特征标签","IP特征","灰","其它lv3","使用境外IP","80",overseasIPdf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("使用境外IP的数据共" + overseasIPResdf.count())
    //overseasIPdf.show(5,false)

    //使用港澳台IP
    var gangaoIPdf = userRelationDf
      .filter(
        col("province").contains("香港")
        || col("province").contains("澳门")
        || col("province").contains("台湾")
        || col("country").contains("TW")
        || col("country").contains("台湾")
      )
      .dropDuplicates("uid")
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("港澳台IP筛选的数据共" + gangaoIPdf.count())
    gangaoIPdf.createOrReplaceTempView("gangaoIP")
    //gangaoIPdf.show(5,false)

    val gangaoIPResdf = mergeAndAddColumns("用户关联表","IP特征标签","IP特征","灰","其它lv3","使用港澳台IP","80",gangaoIPdf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("使用港澳台IP的数据共" + gangaoIPResdf.count())
    //gangaoIPdf.show(5,false)

    //使用台湾IP
    val taiwanIPdf = userRelationDf
      .filter(
          col("province").contains("台湾")
          || col("country").contains("TW")
          || col("country").contains("台湾")
      )
      .dropDuplicates("uid")
      .repartition(200)

    val taiwanIPResdf = mergeAndAddColumns("用户关联表","IP特征标签","IP特征","灰","其它lv3","使用台湾IP","80",taiwanIPdf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("使用台湾IP的数据共" + taiwanIPResdf.count())
    //taiwanIPResdf.show(5,false)
    // 释放中间数据
    overseasIPdf.unpersist()
    gangaoIPdf.unpersist()

    //一天内切换使用境内、境外IP
    val ipChangeDf =
      spark.sql(
          """
            |select
            |   t.uid as uid,
            |   t.country as country1,
            |   t1.country as country2
            |from
            |(select uid,country from userRelation where country in ('CN','中国')) t
            |join (
            |select uid,country from overseasIP
            |union all
            |select uid,country from gangaoIP
            |) t1 on t.uid = t1.uid
            |""".stripMargin)
        .dropDuplicates("uid")
        .repartition(100)

    val ipChangeResDf = mergeAndAddColumns("用户关联表","IP特征标签","IP特征","灰","其它lv3","一天内切换使用境内、境外IP","80",ipChangeDf)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内切换使用境内、境外IP的数据共" + ipChangeResDf.count())

    //CUID特征标签
    val userCuidDf = userRelation30Df.filter(col("type") === "CUID")
      .dropDuplicates()
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    userCuidDf.createOrReplaceTempView("cuid_view")

    //设备同一天内被多人使用
    val cuidMultiUseDf = spark.sql(
      s"""
         | with cuid_use_count as(
         | select
         |    value,
         |    count(distinct uid) as uid_count
         | from
         |    cuid_view
         | where
         |    cast(uid as long) > 0
         | group by value
         | having count(distinct uid) > 1
         | )
         |select
         |   t.uid,
         |   u.uid_count
         |from cuid_use_count u
         |inner join (select value,uid from cuid_view group by value,uid) t on u.value = t.value
         |""".stripMargin)
      .withColumn("score",(when(col("uid_count") > 6,"100")
        .when(col("uid_count") === 6,"90")
        .when(col("uid_count") === 5,"80")
        .when(col("uid_count") === 4,"70")
        .when(col("uid_count") === 3,"60")
        .when(col("uid_count") === 2,"50")).cast("float"))
      .dropDuplicates()
    val cuidMultiResDf = mergeAndAddColumns("用户关联表","CUID特征标签","CUID特征","灰","黑产","设备同一天内被多人使用","",cuidMultiUseDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //cuidMultiUseDf.filter(col("score") >= 5).show(20,false)

    //tiebaVarResDf.show(5,false)
    println("设备同一天内被多人使用数据：" + cuidMultiResDf.count())

    //同一天使用多个设备
    val multiCuidUseDf = spark.sql(
        s"""
           |select
           |   uid,
           |   count(distinct value) as cuid_count
           |from
           |   cuid_view
           |where
           |   cast(uid as long) > 0
           |group by uid
           |having count(distinct value) > 1
           | """.stripMargin)
      .withColumn("score",(when(col("cuid_count") > 6,"100")
        .when(col("cuid_count") === 6,"90")
        .when(col("cuid_count") === 5,"80")
        .when(col("cuid_count") === 4,"70")
        .when(col("cuid_count") === 3,"60")
        .when(col("cuid_count") === 2,"50")).cast("float"))
      .dropDuplicates()
    val multiCuidResDf = mergeAndAddColumns("用户关联表","CUID特征标签","CUID特征","灰","黑产","同一天使用多个设备","",multiCuidUseDf)
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    //multiCuidResDf.filter(col("score") === 100).show(5,false)
    println("同一天使用多个设备数据：" + multiCuidResDf.count())

    // 合并结果
    var resDf = unionIfNotEmpty(overseasIPResdf,gangaoIPResdf)
      resDf = unionIfNotEmpty(resDf,gangaoIPResdf)
      resDf = unionIfNotEmpty(resDf,completeOverseasIPResdf)
      resDf = unionIfNotEmpty(resDf,taiwanIPResdf)
      resDf = unionIfNotEmpty(resDf,ipChangeResDf)
      resDf = unionIfNotEmpty(resDf,cuidMultiResDf)
      resDf = unionIfNotEmpty(resDf,multiCuidResDf)
      .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
      .dropDuplicates()
      .coalesce(5)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("合并后的数据共" + resDf.count())
    resDf.createOrReplaceTempView("res_label_data")

    //释放缓存资源
    userRelationDf.unpersist()
    overseasIPResdf.unpersist()
    gangaoIPResdf.unpersist()
    completeOverseasIPResdf.unpersist()
    taiwanIPResdf.unpersist()
    ipChangeResDf.unpersist()
    cuidMultiResDf.unpersist()
    multiCuidResDf.unpersist()

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
         |select
         | uid,
         | source,
         | type,
         | sub_type,
         | label_lv1,
         | label_lv2,
         | label_lv3,
         | score
         |from res_label_data
         |""".stripMargin)
    resDf.unpersist()
    spark.stop()
  }
}

