package com.baidu.sql.customized.files801

import com.baidu.sql.utils.SparkUtils._
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.SparkSession


/**
 *  @author: zhangrunjie 801风控用户标签表，百家号4个uid例行获取发文数据
 */
object Label801BjhCase {
  //  source  str 数据来源
  //  type  str 标签类型
  //  sub_type  str 标签子类型
  //  label_lv1 str 标签一级分类
  //  label_lv2 str 标签二级分类
  //  label_lv3 str 标签三级分类
  //  score float 分数/置信度
  //  event_day str 时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)

    val YesterDayFormat = calcnDateFormat(YesterDay)
    println("yyyy-MM-dd日期格式：" + YesterDayFormat)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)

    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    val resDf = spark.sql(
        s"""
           |with bjh_tmp_data as (
           |SELECT
           |  nid,
           |  mis_url,
           |  rmb_self_build_url_http,
           |  m_content,
           |  url,
           |  author_main_url,
           |  author_user_id as uid,
           |  rmb_status,
           |  status,
           |  secure_not_pass_reason, --安全审核拒绝原因
           |  secure_status, --安全审核结果
           |  log_secure_not_pass_reason, --最新的一次拒绝原因,不区分人审机审
           |  secure_machine_status, --安全机审结果 pass通过, rejected拒绝, send_trial送人审
           |  secure_machine_reject_reason, --安全机审拒绝原因
           |  secure_trial_status, --安全人审结果 pass通过, rejected拒绝
           |  secure_trial_reject_reason, --安全人审拒绝原因
           |  secure_trial_time, --安全人审最近一次审核时间
           |  secure_trial_review_status, --安全人审复审结果(pass-通过,rejected-拒绝)
           |  secure_trial_review_reject_reason, --安全人审复审拒绝原因
           |  secure_trial_review_time, --安全人审复审最近一次审核时间
           |  quality_audit_status, --质量审核状态
           |  quality_last_check_time, --质量最新一次审核时间
           |  quality_status, --质量审核结果(20241211分区起该字段适用图短小动)#publish 或者rejected
           |  audit_m_quality_info, --审核-质量机审结果
           |  log_quality_not_pass_reason, --审核日志中的最新一次质量拒绝原因（不区分机审人审,动态是动态低质原因）
           |  quality_audit_type, --质量审核类型# qf：质量初审 qr：质量复审 qi：质量干预 qm：质量机审 s = secure q = quality f = first r = review i = intervation m = machine
           |  avi_quality_status, -- 视频质量人审状态
           |  quality_machine_status, --质量机审结果 pass通过, rejected拒绝, send_trial送人审
           |  quality_machine_reject_reason, --质量机审拒绝原因
           |  quality_machine_last_time, --质量机审最近一次审核时间
           |  quality_trial_status, --质量人审初审结果 pass通过, rejected拒绝 pass
           |  quality_trial_reject_reason, --质量人审初审拒绝原因
           |  quality_trial_time, --质量人审初审最近一次审核时间
           |  quality_trial_review_status, --质量人审复审结果 pass通过, rejected拒绝 pass
           |  quality_trial_review_reject_reason, --质量人审复审拒绝原因
           |  quality_trial_review_time, --质量人审复审最近一次审核时间
           |  click_publish_time
           |from
           |  bjh_data.bjh_feed_resource_rf
           |where
           |  event_day = '${YesterDay}'
           |  and click_publish_time between '${YesterDayFormat} 00:00:00' and '${YesterDayFormat} 23:59:59'
           |  and author_user_id in (
           |    '1227959945','641430995','6633345404','212918836'
           |  )
           |),
           |tmp_data as (
           |select
           | article_id as nid,
           | product,
           | title,
           | reject_reason,
           | status,
           | username,
           | event_day,
           | insert_time,
           | receive_time,
           | workflow_type,
           | type,
           | regexp_replace(ext_json_info, '\\\\s+', '')  as ext_json_info
           |from udw_ns.default.bjh_ods_audit_auditlog_esdump_di
           |where event_day = '${YesterDay}'
           |and article_id in (select distinct nid from bjh_tmp_data)
           |)
           |select
           |   t1.*,
           |   t2.product,
           |   t2.title,
           |   t2.reject_reason,
           |   t2.status as log_status,
           |   t2.username,
           |   t2.event_day,
           |   t2.insert_time,
           |   t2.receive_time,
           |   t2.workflow_type,
           |   t2.type,
           |   t2.ext_json_info
           |from bjh_tmp_data t1
           |left join tmp_data t2 on t1.nid = t2.nid
           |""".stripMargin)
      .cache()

    println("resDf:" + resDf.count())
    //resDf.show(10,false)
    resDf.repartition(1).write.mode("overwrite").parquet(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_uid.parquet")
    println("write parquet success!")
    writeExcel(resDf,s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/bjh_feed_result/${YesterDay}_uid.xlsx")

    spark.stop()
  }
}

