package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.customized.files801.Label801Bjh.{getTypeDict, splitEnZhUDF}
import com.baidu.sql.customized.files801.Label801ShouBai.{blockedWords, secureRejectReasonUDF}
import com.baidu.sql.customized.files801.Label801TieBa.{decodeObjParam3UDF, getFengjinData}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 *  @author: zhang<PERSON><PERSON>e 801风控用户标签表，违规处罚标签数据
 */
object Label801illegal {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""
  // 前日日期 yyyyMMdd
  var BeForeDay = ""
  //手百敏感词表字典
  val bdhdblockedWords = Seq(
    //词表关键词，label_lv2
    Row("政治敏感","涉政"),
    Row("广告作弊","黑产"),
    Row("色情低俗","色情"),
    Row("领导人","涉政"),
    Row("违法信息","违法违规"),
    Row("突发专项","涉政"),
    Row("涉恐涉暴","涉恐涉暴"),
    Row("涉一号首长","涉政"),
    Row("境外媒体","涉政"),
    Row("历史虚无","涉政"),
    Row("宗教民族","涉政"),
    Row("涉政","涉政"),
    Row("色情","色情")
  )

  // 百家号敏感词表
  val bjhblockedWords = Seq(
    //词表关键词，label_lv2
    Row("涉政","涉政"),
    Row("政治敏感","涉政"),
    Row("广告作弊","黑产"),
    Row("色情低俗","色情"),
    Row("领导人","涉政"),
    Row("违法信息","违法违规"),
    Row("突发专项","涉政"),
    Row("涉恐涉暴","涉恐涉暴"),
    Row("涉一号首长","涉政"),
    Row("境外媒体","涉政"),
    Row("历史虚无","涉政"),
    Row("宗教民族","涉政")
  )

  // 贴吧封禁词表
  val tiebablockedWords = Seq(
      //词表关键词，label_lv2
      Row("涉政","涉政"),
      Row("涉习","涉政"),
      Row("反动","涉政"),
      Row("政治","涉政"),
      Row("六四","涉政"),
      Row("台独","涉政"),
      Row("涉x","涉政"),
      Row("涉X","涉政"),
      Row("色情","色情"),
      Row("黄色","色情"),
      Row("涉黄","色情"),
      Row("漏点","色情"),
      Row("招嫖","色情"),
      Row("黑产","黑产"),
      Row("赌博","黑产"),
      Row("代孕","黑产"),
      Row("诈骗","黑产"),
      Row("涉毒","涉毒"),
      Row("未成年","未成年"),
      Row("违法","违反法规"),
      Row("违规","违反法规"),
      Row("机器发文","黑产"),
      Row("引流","黑产")
  )
  //封禁类型字典
  val tieba_block_type = Map(
    1 -> "单吧封禁",
    4 -> "全局封禁",
    16 -> "封禁屏蔽"
  )

  // 标签等级字典
  val labellevels = Seq(
    Row("黑","黑产","ODS日志脱端"),
    Row("黑","黑产","PC脱端实时"),
    Row("黑","黑产","apng"),
    Row("黑","黑产","cuid黑团伙"),
    Row("黑","黑产","cuid小黑团伙"),
    Row("黑","黑产","iphone异常"),
    Row("黑","黑产","ja3avg"),
    Row("黑","黑产","win8格式异常"),
    Row("黑","黑产","zhangqi_faketel"),
    Row("黑","黑产","黑cuid关联账户"),
    Row("黑","黑产","小米3g"),
    Row("黑","黑产","实时API脱端检测"),
    Row("黑","黑产","发帖机"),
    Row("黑","黑产","头像巡检"),
    Row("黑","黑产","批量注册"),
    Row("黑","黑产","机器刷帖"),
    Row("黑","色情","色情内容"),
    Row("黑","黑产","色情引流"),
    Row("黑","黑产","设备关联"),
    Row("黑","黑产","轨迹异常"),
    Row("黑","黑产","批量注册(UA异常)"),
    Row("黑","黑产","批量注册(云IP1)"),
    Row("黑","黑产","批量注册(云IP2)"),
    Row("黑","黑产","批量注册(用户名相似)"),
    Row("黑","黑产","发帖机引流"),
    Row("黑","黑产","地区吧作弊"),
    Row("黑","黑产","世界杯主题帖"),
    Row("黑","色情","色情相关图片"),
    Row("黑","色情","地区吧色情引流"),
    Row("黑","黑产","阳光发帖机拓展"),
    Row("黑","黑产","变体色情赌博引流"),
    Row("黑","黑产","实时流发帖机特征"),
    Row("黑","黑产","视频封面赌博引流"),
    Row("黑","黑产","解封账号重审封禁"),
    Row("黑","黑产","误解封账号再封禁"),
    Row("黑","黑产","楼中楼色情赌博引流"),
    Row("黑","黑产","贴吧视频封面赌博引流"),
    Row("黑","违反法规","违规用户短封"),
    Row("黑","其它lv1","quentin平台_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","mis_userpunish_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","pass_account_securit_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","mis_uegaudit_{封禁类型}"),
    Row("黑","黑产","mis_uegaudit_{封禁类型}"),
    Row("黑","通过依赖关键词表获取","吧务&pm_{封禁类型}")
  )

  //贴吧人审违规字典
  val tieba_human_punish = Seq(
    Row("涉政","涉政"),
    Row("政治","涉政"),
    Row("时政","涉政"),
    Row("涉习","涉政"),
    Row("涉历史","涉政"),
    Row("维尼熊","涉政"),
    Row("色情","色情"),
    Row("猥亵","色情"),
    Row("儿童色情","涉未成年色情"),
    Row("儿色","涉未成年色情"),
    Row("未成年人色情","涉未成年色情"),
    Row("儿童邪典","其它lv1"),
    Row("婴幼儿贩卖","其它lv1"),
    Row("lgbt","其它lv1"),
    Row("特殊性癖","其它lv1"),
    Row("自残自杀","其它lv1"),
    Row("药","其它lv1"),
    Row("涉嫌乱伦","其它lv1"),
    Row("作弊","黑产"),
    Row("黑产","黑产"),
    Row("变体","黑产"),
    Row("引流","黑产"),
    Row("发帖机","黑产"),
    Row("刷屏","黑产"),
    Row("赌博","黑产"),
    Row("水军","黑产"),
    Row("水帖","黑产"),
    Row("垃圾广告","黑产"),
    Row("未成年","涉未成年"),
    Row("违法","违反法规"),
    Row("其他有害","违反法规")
  )

  val blockschema = StructType(Array(
    StructField("keywords", StringType, true),
    StructField("label_lv2", StringType, true)
  ))

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)
    BeForeDay = calcnDate(YesterDay, -1)
    //是否删除已存在的违规数据，0不删除，1删除
    val isDrop = args(1)

    val BeForeDayTimeStamp = getTimeStampTake(BeForeDay,10)

    val YesterTimeStamp = getTimeStampTake(YesterDay,10)
    val ToDayTimeStamp = getTimeStampTake(ToDay,10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)
    println("T-1日期：" + YesterDay)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()
    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    //违规处罚标签,贴吧封禁============================================================
    //用户封禁操作日志明细数据
    val tiebaBlockDf = spark.sql(
        s"""
           |select
           |   uid,
           |   tieba_user_block_src,
           |   is_unblock,
           |   description,
           |   op_uname,
           |   tieba_block_type
           |from ubs_tieba.tieba_dwd_operate_block_user_di
           |where event_day = '${YesterDay}'
           |and uid > 0
           |and is_unblock = 0
           |and tieba_block_type in (1, 4, 16)
           |""".stripMargin)
      .repartition(5)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("用户封禁操作日志明细数据数据共：" + tiebaBlockDf.count())
    tiebaBlockDf.createOrReplaceTempView("tiebaBlockDf")

    // 定义DataFrame的模式
    val levelschema = StructType(Array(
      StructField("label_lv1", StringType, true),
      StructField("label_lv2", StringType, true),
      StructField("label_lv3", StringType, true)
    ))

    //定义广播
    val labelDf = spark.createDataFrame(spark.sparkContext.parallelize(labellevels), levelschema)
    val labellevelsBroad = spark.sparkContext.broadcast(labelDf)
    val tiebablockDf = spark.createDataFrame(spark.sparkContext.parallelize(tiebablockedWords),blockschema)
    val blockedWordsBroad = spark.sparkContext.broadcast(tiebablockDf)

    //贴吧封禁数据
    val tiebaBlockLabel = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "safe_mis" and col("op_uname") === "xteam").as("T")
      .join(labellevelsBroad.value.filter(!col("label_lv3").contains("{封禁类型}")).as("B"),
        col("T.description") contains col("B.label_lv3"))
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        $"label_lv1",
        $"label_lv2",
        $"label_lv3",
        lit("100") as "score"
      )
      .repartition(50)
    println("贴吧封禁数据：" + tiebaBlockLabel.count())
    //tiebaBlockLabel.show(5,false)

    //quentin平台
    val quentinDf = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "mis_quentin")
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        lit("黑") as "label_lv1",
        lit("其它lv1") as "label_lv2",
        lit("100") as "score"
      )
    var quentinResDf = spark.emptyDataFrame
    for (elem <- tieba_block_type.values) {
      if (quentinResDf.isEmpty){
        quentinResDf = quentinDf.withColumn("label_lv3",lit(s"quentin平台_${elem}"))
      }else{
        quentinResDf = quentinResDf.unionByName(quentinDf.withColumn("label_lv3",lit(s"quentin平台_${elem}")))
      }
    }
    println("quentin平台数据：" + quentinResDf.count())
    //quentinResDf.show(5,false)

    //黑产
    //mis_uegaudit_{封禁类型}
    val uegDf = tiebaBlockDf
      .filter(col("tieba_user_block_src") === "ueg_handlelogic")
      .select(
        $"uid",
        lit("帖吧") as "source",
        lit("违规处罚标签") as "type",
        lit("贴吧封禁") as "sub_type",
        lit("黑") as "label_lv1",
        lit("黑产") as "label_lv2",
        lit("100") as "score"
      )
    var uegResDf = spark.emptyDataFrame
    for (elem <- tieba_block_type.values) {
      if (uegResDf.isEmpty){
        uegResDf = uegDf.withColumn("label_lv3",lit(s"mis_uegaudit_${elem}"))
      }else{
        uegResDf = uegResDf.unionByName(uegDf.withColumn("label_lv3",lit(s"mis_uegaudit_${elem}")))
      }
    }
    println("黑产mis_uegaudit数据：" + uegResDf.count())
    //uegResDf.show(5,false)

    //mis_userpunish_{封禁类型}
    val misUserpunishResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"mis_userpunish")
    println("mis_userpunish_数据：" + misUserpunishResDf.count())
    //misUserpunishResDf.show(5,false)

    //pass_account_securit_{封禁类型}
    val passAccountResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"pass_account_securit")
    println("pass_account_securit数据：" + passAccountResDf.count())
    //passAccountResDf.show(5,false)

    //mis_uegaudit_{封禁类型}
    val misUegauditResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"mis_uegaudit","op_uname")
    println("mis_uegaudit数据：" + misUegauditResDf.count())
    //misUegauditResDf.show(5,false)

    //吧务&pm_{封禁类型}
    val pmResDf = getFengjinData(spark,tiebaBlockDf,blockedWordsBroad,"")
    println("吧务&pm数据：" + pmResDf.count())
    //pmResDf.show(5,false)

    //合并贴吧封禁数据
    val tiebaBlockResDf = tiebaBlockLabel
      .unionByName(quentinResDf)
      .unionByName(uegResDf)
      .unionByName(misUserpunishResDf)
      .unionByName(passAccountResDf)
      .unionByName(misUegauditResDf)
      .unionByName(pmResDf)
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧封禁数据：" + tiebaBlockResDf.count())
    //tiebaBlockResDf.show(5,false)

    tiebaBlockDf.unpersist()

    //贴吧运营域删贴维度表
    val tiebaDelpubDf = spark.sql(
        s"""
           |select
           | uid,
           | tid,
           | del_src,
           | ueg_strategy,
           | tieba_del_type,
           | ueg_strategy,
           | pid,
           | pub_type
           |from udw_ns.default.tieba_dim_operate_delpub_di
           |where event_day = '${YesterDay}'
           |and uid > 0
           |""".stripMargin)
      .repartition(10)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧运营域删贴维度表数据：" + tiebaDelpubDf.count())
    tiebaDelpubDf.createOrReplaceTempView("tiebaDelpubDf")

    //贴吧删帖违规规则
    val tiebaDelSrcDf = spark.sql(
      """
        |select
        | uid,
        | '黑' as label_lv1,
        | del_src,
        | ueg_strategy,
        | tieba_del_type
        |from tiebaDelpubDf
        |where del_src not in (100, 200)
        |""".stripMargin)
      .dropDuplicates()
      .withColumn("tieba_del_type",when(col("tieba_del_type") === "",lit("unknown")).otherwise(col("tieba_del_type")))
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧删帖数据：" + tiebaDelSrcDf.count())
    tiebaDelSrcDf.createOrReplaceTempView("tiebaDelSrcDf")

    val tiebaDelwords = readCsv(spark,"贴吧删帖策略字典")
      .withColumnRenamed("ueg_strategy","keywords")
    val tiebaDelBorad = spark.sparkContext.broadcast(tiebaDelwords)
    tiebaDelBorad.value.createOrReplaceTempView("tiebaDelwords")

    val tiebaDel500Df = tiebaDelSrcDf
      .filter(col("del_src") === "500")
      .withColumn("label_lv2",lit("其它lv1"))
      .withColumn("label_lv3",concat(lit(s"在线管理员删贴_"),col("tieba_del_type")))
      .dropDuplicates()
      .repartition(50)
    println("在线管理员删贴数据：" + tiebaDel500Df.count())
    //tiebaDel500Df.show(5,false)

    val tiebaDel400Df = tiebaDelSrcDf
      .filter(col("del_src") === "400" and col("ueg_strategy") === "0")
      .withColumn("label_lv2",lit("其它lv1"))
      .withColumn("label_lv3",concat(lit(s"机器删贴_"),col("tieba_del_type")))
      .dropDuplicates()
      .repartition(50)
    println("机器删贴400数据：" + tiebaDel400Df.count())
    //tiebaDel400Df.show(5,false)

    val tiebaUegDf = tiebaDelSrcDf
      .filter(col("del_src") === "400" and col("ueg_strategy") =!= "0" and col("tieba_del_type") === "ueg_handlelogic")
      .as("T")
      .join(tiebaDelBorad.value.as("B"),col("T.ueg_strategy").contains(col("B.keywords")))
      .select(
        col("uid").as("uid"),
        col("del_src").as("del_src"),
        col("ueg_strategy").as("ueg_strategy"),
        //col("keywords").as("keywords"),
        col("tieba_del_type").as("tieba_del_type"),
        col("label_lv1").as("label_lv1"),
        col("label_lv2").as("label_lv2")
      )
      .withColumn("label_lv3",concat(lit(s"机器删贴_"),col("ueg_strategy")))
      .dropDuplicates()
      .repartition(50)
    println("机器删贴ueg数据：" + tiebaUegDf.count())
    //tiebaUegDf.show(5,false)

    //合并贴吧删帖结果
    val tiebaDelResDf = tiebaDel500Df
      .unionByName(tiebaDel400Df)
      .unionByName(tiebaUegDf)
      .select("uid","label_lv1","label_lv2","label_lv3")
      .withColumn("source",lit("帖吧"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("贴吧删帖"))
      .withColumn("score",lit("100"))
      .dropDuplicates()
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧删帖最终结果：" + tiebaDelResDf.count())
    //tiebaDelResDf.show(5,false)

    tiebaDelSrcDf.unpersist()

    //贴吧人审违规数据=========================================================

    // 注册UDF
    spark.udf.register("decode_obj_param3", decodeObjParam3UDF)

    // 读取用户属性维数据
    val tiebaDwdSql =
      s"""
         |with tmp_data as (
         |SELECT
         |    uid,
         |    decode_obj_param3(obj_param3) AS obj_param3
         |FROM
         |    things_turing_ns.ubs_tieba.view_tieba_dwd_log_qi_crc_data
         |WHERE
         |    event_day = '${YesterDay}'
         |    AND event_action = 'tieba_server'
         |    AND event_client_type = 'pc_web'
         |    AND bhv_key IN ('uegnaudit-project-audited')
         |    AND uid > 0
         |)
         |SELECT
         |    uid,
         |    obj_param3
         |FROM
         |    tmp_data
         |WHERE  obj_param3 NOT LIKE '%正常%'
         |       AND obj_param3 NOT LIKE '%辱骂攻击%'
         |       AND obj_param3 NOT LIKE '%低价值%'
         |       AND obj_param3 NOT LIKE '%2749-不标注%'
         |       AND obj_param3 NOT LIKE '%一般违规%'
         |       AND obj_param3 NOT LIKE '%软色情%'
         |       AND obj_param3 NOT LIKE '%非涉政%'
         |       AND obj_param3 NOT LIKE '%通过%'
         |       AND obj_param3 NOT LIKE '%无害%'
         |       AND obj_param3 NOT LIKE '%低质%'
         |       AND obj_param3 NOT LIKE '%无涉政人物%'
         |       AND obj_param3 NOT LIKE '%有涉政人物但不违规%'
         |       AND obj_param3 NOT LIKE '%涉政不违规%'
         |       AND obj_param3 NOT LIKE '%拒绝%'
         |       AND obj_param3 NOT LIKE '%非正常%'
         |
         |""".stripMargin

    val tiebaDwdDf = spark.sql(tiebaDwdSql)
      .dropDuplicates()
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧人审数据：" + tiebaDwdDf.count())
    tiebaDwdDf.createOrReplaceTempView("tieba_dwd_view")

    //定义广播
    val punishDf = spark.createDataFrame(spark.sparkContext.parallelize(tieba_human_punish), blockschema)
    val punishBroad = spark.sparkContext.broadcast(punishDf)
    punishBroad.value.createOrReplaceTempView("tieba_human_punish")

    val punishResDf = spark.sql(
        """
          |with explode_punish as (
          |   select
          |     uid,
          |     explode(split(obj_param3, ',')) as keywords
          |   from tieba_dwd_view
          |),
          |keyword_view as (
          | select
          |   uid,
          |   keywords
          | from explode_punish
          | where keywords NOT RLIKE '不违规|不标注|辱骂|非未成年|软色情|非涉政|无涉政人物'
          |)
          |select
          | k.uid,
          | '帖吧' as source,
          | '违规处罚标签' as type,
          | '贴吧人审' as sub_type,
          | '黑' as label_lv1,
          | p.label_lv2,
          | concat('uegnaudit_', p.keywords) as label_lv3,
          | '100' as score
          |from keyword_view k
          |inner join tieba_human_punish p
          |on k.keywords LIKE CONCAT('%', p.keywords, '%')
          |""".stripMargin)
      .dropDuplicates()
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("贴吧人审最终结果：" + punishResDf.count())
    //punishResDf.show(5,false)

    //违规处罚标签,手百评论违规规则==========================
    var bjhEsdumpDf = spark.sql(
        s"""
           |with tmp_table as (
           |select
           |t.*
           |from (
           |select
           | ext_info,
           | publish_time,
           | CASE
           |    WHEN publish_time RLIKE '^\\\\d{4}-\\\\d{2}-\\\\d{2} \\\\d{2}:\\\\d{2}:\\\\d{2}\\$$'
           |    THEN unix_timestamp(publish_time, 'yyyy-MM-dd HH:mm:ss')
           |    ELSE NULL
           |  END AS parsed_timestamp
           |from udw_ns.default.bjh_ods_auditflow_esdump_di
           |where event_day = '${YesterDay}'
           |and status = '0'
           |and product = 'commentnew'
           |and get_json_object(ext_info,'$$.sf_subreason') != '通过'
           |and subproduct not in(
           |  'commentnew_40019130',
           |  'commentnew_20449098',
           |  'commentnew_116154826',
           |  'commentnew_115793742',
           |  'commentnew_115674670',
           |  'commentnew_87968004',
           |  'commentnew_115821912',
           |  'commentnew_105')
           |  ) t
           | where t.parsed_timestamp is not null
           |)
           |select
           |   ext_info
           |from tmp_table
           |-- where parsed_timestamp >= ${YesterTimeStamp}
           |-- and parsed_timestamp < ${ToDayTimeStamp}
           |""".stripMargin)
      //.filter(col("parsed_timestamp") >= YesterTimeStamp && col("parsed_timestamp") <= ToDayTimeStamp)
      .withColumn("uid", get_json_object(col("ext_info"), "$.user_id").cast("long"))
      .withColumn("sm_reason", get_json_object(col("ext_info"), "$.sm_reason").cast("string"))
      .withColumn("sm_subreason", get_json_object(col("ext_info"), "$.sm_subreason").cast("string"))
      .filter(col("uid") > 0 && (col("sm_reason") =!= "" || col("sm_subreason") =!= ""))
      .drop("ext_info")
      //.repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK)
    println("违规处罚标签数据量：" + bjhEsdumpDf.count())
    //bjhEsdumpDf.show(5,false)

    //敏感词数据集
    val bdhdblockDf = spark.createDataFrame(spark.sparkContext.parallelize(bdhdblockedWords),blockschema)
      .persist(StorageLevel.MEMORY_AND_DISK)
    //val blockBorad = spark.sparkContext.broadcast(bdhdblockDf)
    //blockBorad.value.createOrReplaceTempView("block_words")
    bdhdblockDf.createOrReplaceTempView("block_words")

    val bdhdillegalDf = readCsv(spark,"手百评论违规标签规则")
      .withColumn("index",monotonically_increasing_id())
    val bdhdillegalBorad = spark.sparkContext.broadcast(bdhdillegalDf)
    bdhdillegalBorad.value.createOrReplaceTempView("bdhd_illegal_words")

    val bjhSecureDF = bjhEsdumpDf.withColumn("secure_reject_reason", secureRejectReasonUDF(col("sm_reason"), col("sm_subreason")))
      .filter(col("secure_reject_reason") =!= "")
      .repartition(50)
      .persist(StorageLevel.MEMORY_AND_DISK)
    println("违规处罚标签数据量：" + bjhSecureDF.count())
    //bjhSecureDF.show(5,false)
    bjhSecureDF.createOrReplaceTempView("bjh_secure_data")

    val bdhdillegalWordsTable = spark.sql(
        """
          |-- 数据切分，后续敏感词表匹配
          |with explode_table as (
          | select
          |   uid,
          |   explode(split(secure_reject_reason, ' && ')) as reject_reason
          | from bjh_secure_data
          |),
          |-- 匹配label_lv3字段的规则
          |illegal_table_view as (
          |select
          | T.uid,
          | T.label_lv1,
          | T.label_lv2,
          | T.label_lv3,
          | T.score,
          | T.reject_reason
          |from (
          | select
          |   T.uid,
          |   E.label_lv1,
          |   E.label_lv2,
          |   FIRST(E.label_lv3) OVER (PARTITION BY T.uid ORDER BY E.index) as label_lv3,
          |   E.score,
          |   T.reject_reason,
          |   ROW_NUMBER() OVER (PARTITION BY T.uid ORDER BY 1) as rn
          | from explode_table T
          | inner join bdhd_illegal_words E
          | ON T.reject_reason LIKE CONCAT('%', E.label_lv3, '%')
          | ) T
          | where T.rn = 1
          |),
          |temp_table as (
          |select
          |   T.uid,
          |   T.label_lv1,
          |   B.label_lv2,
          |   T.label_lv3,
          |   T.score
          | from (select * from illegal_table_view where label_lv2 = '通过敏感词表字典获取') T
          | inner join block_words B
          | ON T.reject_reason LIKE CONCAT('%', B.keywords, '%')
          |)
          |select
          | *
          |from temp_table
          |union all
          |select
          | uid,
          | label_lv1,
          | label_lv2,
          | label_lv3,
          | score
          |from illegal_table_view
          |where label_lv2 != '通过敏感词表字典获取'
          |""".stripMargin)
      .repartition(20)
      .dropDuplicates()
      .withColumn("source",lit("手百评论"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("手百评论违规"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("手百illegalWordsTable数据量：" + bdhdillegalWordsTable.count())
    //bdhdillegalWordsTable.show(5,false)

    //百家号发文违规标签规则=======================================
    // 读取百家号发文数据
    val bjhSql =
      s"""
         |select
         |  cast(author_user_id as long) as uid,
         |  city,
         |  secure_not_pass_reason,
         |  nid,
         |  type3,
         |  status,
         |  author_status,
         |  unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') as secure_trial_timestamp,
         |  unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') as secure_machine_timestamp,
         |  unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') as timestamp
         |from
         |  bjh_data.bjh_feed_resource_rf
         |where
         |  event_day = '${YesterDay}'
         |  and cast(author_user_id as long) > 0
         |  and author_user_id != ''
         |  and
         |  (
         |    (unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') >= ${BeForeDayTimeStamp}
         |    and unix_timestamp(click_publish_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |    or (unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |    and unix_timestamp(secure_trial_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |    or (unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') >= ${YesterTimeStamp}
         |    and unix_timestamp(secure_machine_last_time, 'yyyy-MM-dd HH:mm:ss') < ${ToDayTimeStamp})
         |  )
         |""".stripMargin

    val bjhDf = spark.sql(bjhSql)
      .filter(col("timestamp") >= BeForeDayTimeStamp && col("timestamp") < ToDayTimeStamp)
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("百家号两天内数据共：" + bjhDf.count())
    bjhDf.dropDuplicates("uid","city").createOrReplaceTempView("bjh_data")

    //筛选一天内数据
    val bjhOneDf = bjhDf.filter(col("timestamp") >= YesterTimeStamp)
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内数据共：" + bjhOneDf.count())
    bjhOneDf.createOrReplaceTempView("bjh_one_data")

    //百家号敏感词读取
    val bjhblockDf = spark.createDataFrame(spark.sparkContext.parallelize(bjhblockedWords),blockschema)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    val bjhblockedWordsBroad = spark.sparkContext.broadcast(bjhblockDf)
    bjhblockedWordsBroad.value.createOrReplaceTempView("block_words")

    //读取违规标签规则文件,并拆分label_lv3字段为中文和英文部分
    val bjhillegalDf = readCsv(spark,"百家号发文违规标签规则")
      .withColumn("index",monotonically_increasing_id())
      .withColumn("label_part", splitEnZhUDF(col("label_lv3")))
    val bjhillegalBroad = spark.sparkContext.broadcast(bjhillegalDf)
    bjhillegalBroad.value.createOrReplaceTempView("bjh_illegal_words")

    //筛选一天内数据，包含secure_trial_time和secure_machine_last_time
    val bjhOneSecureDf = bjhDf
      .filter(col("timestamp") >= YesterTimeStamp || col("secure_trial_timestamp") >= YesterTimeStamp || col("secure_machine_timestamp") >= YesterTimeStamp)
      .filter(col("status").isin("deleted", "delete", "forbidden", "rejected", "withdraw", "freeze",  "frozen"))
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内数据包含secure数据共：" + bjhOneSecureDf.count())
    bjhOneSecureDf.createOrReplaceTempView("bjh_one_secure_data")

    val explodeDf = spark.sql(
      """
        |select
        | T.*
        |from
        |(select
        |   uid,
        |   nid,
        |   type3,
        |   explode(split(secure_not_pass_reason, '&&')) as reject_reason
        | from bjh_one_secure_data) T
        | where T.reject_reason != ''
        | """.stripMargin)
    explodeDf.createOrReplaceTempView("explode_bjh_secure_data")

    val illegalTable = spark.sql(
        """
          |with temp_table as (
          |select
          |   T.uid,
          |   E.label_lv1,
          |   E.label_lv2,
          |   FIRST(E.label_lv3) OVER (PARTITION BY T.uid ORDER BY E.index) as label_lv3,
          |   FIRST(E.label_part) OVER (PARTITION BY T.uid ORDER BY E.index) as label_part,
          |   E.score,
          |   T.reject_reason,
          |   nid,
          |   type3,
          |   ROW_NUMBER() OVER (PARTITION BY T.uid ORDER BY 1) as rn
          | from explode_bjh_secure_data T
          | inner join bjh_illegal_words E
          | ON forall(E.label_part, x -> instr(T.reject_reason, x) > 0)
          |)
          |select
          | uid,
          | label_lv1,
          | label_lv2,
          | label_lv3,
          | label_part,
          | score,
          | reject_reason,
          | nid,
          | type3
          |from temp_table
          |where rn = 1
          |""".stripMargin)
      .repartition(20)
      .dropDuplicates()
    //.persist(StorageLevel.MEMORY_AND_DISK_SER)
    //println("illegalTable数据量：" + illegalTable.count())
    //illegalTable.show(10,false)
    illegalTable.createOrReplaceTempView("illegal_table_view")

    val illegalWordsTable = spark.sql(
        """
          |with temp_table as (
          |select
          |   T.uid,
          |   T.label_lv1,
          |   B.label_lv2,
          |   T.label_lv3,
          |   label_part,
          |   T.score,
          |   B.keywords,
          |   T.reject_reason,
          |   T.nid,
          |   T.type3
          | from (select * from illegal_table_view where label_lv2 = '通过敏感词表字典获取') T
          | inner join block_words B
          | ON T.reject_reason LIKE CONCAT('%', B.keywords, '%')
          |)
          |select
          | *
          |from temp_table
          |union all
          |select
          | uid,
          | label_lv1,
          | label_lv2,
          | label_lv3,
          | label_part,
          | score,
          | '' as keywords,
          | reject_reason,
          | nid,
          | type3
          |from illegal_table_view
          |where label_lv2 != '通过敏感词表字典获取'
          |""".stripMargin)
      .repartition(20)
      .dropDuplicates()
      .drop("label_part","keywords","reject_reason")
      .withColumn("source",lit("百家号"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("百家号发文违规"))
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("illegalWordsTable数据量：" + illegalWordsTable.count())
    illegalWordsTable.createOrReplaceTempView("illegal_words_table")

    //筛选一天内数据，百家号封禁规则
    val bjhOneAuthorDf = bjhOneDf
      .filter(col("author_status").isin("freeze","frozen"))
      .select("uid")
      .withColumn("source",lit("百家号"))
      .withColumn("type",lit("违规处罚标签"))
      .withColumn("sub_type",lit("百家号封禁"))
      .withColumn("label_lv1",lit("黑"))
      .withColumn("label_lv2",lit("封禁"))
      .withColumn("label_lv3",lit("百家号作者封禁"))
      .withColumn("score",lit("100"))
      .repartition(20)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("一天内数据过滤author_status数据共：" + bjhOneAuthorDf.count())
    //bjhOneAuthorDf.show(10,false)
    bjhOneDf.unpersist()

    //合并结果
    var resDf = //儿童色情相关吧数据
        unionIfNotEmpty(tiebaBlockResDf,tiebaDelResDf)  // 贴吧封禁数据,贴吧删帖数据
        resDf = unionIfNotEmpty(resDf,bdhdillegalWordsTable) //手百评论违规
        resDf = unionIfNotEmpty(resDf,illegalWordsTable) //百家号发文违规
        resDf = unionIfNotEmpty(resDf,bjhOneAuthorDf) //百家号封禁规则
        resDf = unionIfNotEmpty(resDf,punishResDf) //贴吧人审数据
      .select("uid","source","type","sub_type","label_lv1","label_lv2","label_lv3","score")
        .withColumn("label_lv2",when(col("label_lv2").contains("其他"),regexp_replace(col("label_lv2"),"其他","其它")).otherwise(col("label_lv2")))
        .withColumn("score",col("score").cast("float"))
        .dropDuplicates()
        .coalesce(1)
        .persist(StorageLevel.MEMORY_AND_DISK_SER)

    println("最终结果：" + resDf.count())
    resDf.createOrReplaceTempView("res_label_data")

    //释放资源
    tiebaBlockResDf.unpersist()
    tiebaDelResDf.unpersist()
    bdhdillegalWordsTable.unpersist()
    illegalWordsTable.unpersist()
    bjhOneAuthorDf.unpersist()
    punishResDf.unpersist()

    //写入表help_ods_crcc_userlabel_data
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_crcc_userlabel_data partition(event_day = ${YesterDay})
         |select
         | uid,
         | source,
         | type,
         | sub_type,
         | label_lv1,
         | label_lv2,
         | label_lv3,
         | score
         |from res_label_data
         |""".stripMargin)

    resDf.unpersist()
    spark.stop()
  }

}

