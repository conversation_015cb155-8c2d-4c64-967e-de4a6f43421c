package com.baidu.sql.customized.usergroup

import com.baidu.sql.utils.CommonUtils
import com.baidu.sql.utils.TimeOperateUtil.calcnDate
import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, udf}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest.AliasActions
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.admin.indices.flush.{FlushRequest, FlushResponse}
import org.elasticsearch.client.indices.{CreateIndexRequest, GetIndexRequest}
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.xcontent.XContentType
import org.elasticsearch.spark.sql.EsSparkSQL

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, LocalDateTime, ZoneId}
import scala.collection.JavaConverters.mapAsScalaMapConverter

/**
 * <AUTHOR>  风铃的外溢监测数据表help_ods_fengling_sentiment 数据入库ES库
 * @date date 2024/9/6
 * @time 11:00
 * @package_name com.baidu.sql.customized
 */

object FengLingSentiment2Es {
  val client: RestHighLevelClient = createElasticsearchClient()

  //传入的日期
  var yesterday :LocalDate = null

  //索引别名：外溢表
  val feelingsIndexAlias = "public_fengling_sentiment_index"

  // 数据源表：风铃外溢表
  val feelingsTable = "udw_ns.default.help_ods_fengling_sentiment"

  def main(args: Array[String]): Unit = {
    /*
    * 本任务主要实现功能为：
    * 新建存储30天内数据的es索引：
    * 监管：  regulate_risk_label_index_2024_01_23 (日期使用运行任务日期)
    * 舆情：  public_opinion_risk_label_index_2024_01_23 (日期使用运行任务日期)
    * 每日例行从hive取数，写入es新建的索引中
    * 将新刷完数的索引映射值固定值的别名上  别名
    * 监管:regulate_risk_label_index
    * 舆情:public_opinion_risk_label_index
    * 删除30天之外刷数的索引
    * 操作es有三种方案，采用1写入 其他操作采用2 1.使用spark-es(之前经验是厂内调度不支持) 2.使用esclient 3.直接发送请求
    * */

    // 日期参数
    val yesterdayStr = args(0)
    //30天前日期
    val thirtydayStr = calcnDate(yesterdayStr,-29)
    //31天前日期,需要删除的索引日期
    val deletedayStr = calcnDate(yesterdayStr,-30)

    // sparkConf
    val sparkConf = new SparkConf().setAppName("FengLingHive2Es")
    sparkConf.set("es.nodes","************")
    sparkConf.set("es.port","8200")
    sparkConf.set("es.net.http.auth.user" ,"superuser")
    sparkConf.set("es.net.http.auth.pass","metis_MegSed")
    sparkConf.set("es.nodes.wan.only","true")
    sparkConf.set("es.nodes.discovery","false")
    sparkConf.set("es.index.auto.create","false")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("spark.task.maxFailures", "5")
      .config("spark.driver.allowMultipleContexts", true)
      //.master("local[*]")
      .getOrCreate()

    //读取hive的数据
    val feelingsDF = readHiveData(spark,thirtydayStr,yesterdayStr)

    val columns = feelingsDF.columns
    //循环获取列名和类型,形成ES的mapping
    val colMapping = columns.map(col => {
      if (col.equals("submit_time") || col.equals("case_id")) {
        (col -> "long")
      } else {
        (col -> "text")
      }
    }).toList

    //准备mapping
    val opinionMappingStr = generateMappingWithFields(colMapping)

    println("mapping:" + opinionMappingStr)

    //检索出全部旧的索引,并删除旧索引的别名映射和索引
    val superOldIndexList = indexIsExsitsAlias(feelingsIndexAlias)
    for (oldIndex <- superOldIndexList) {
      if(isExsitsIndex(oldIndex)){
        // 删除旧索引的别名映射
        indexRemoveAlias(oldIndex, feelingsIndexAlias)
        // 删除旧索引
        deleteIndex(oldIndex)
        println("index" + oldIndex + " is exsit,deleteIndex:" + oldIndex)
      }
    }

    try{
      // 循环打印从昨天开始的31天内的日期
      for (i <- 0 to 29) {
        // 通过minusDays递减天数,得到当天的日期新索引日期
        val currentdateStr = calcnDate(yesterdayStr,-i)
        println("新建索引日期：" + currentdateStr)

        //监管数据处理
        dealWithEs(sparkConf,currentdateStr, i, colMapping,feelingsDF, opinionMappingStr)
      }

      //删除30天之外的索引
      //deleteOldIndex(deletedayStr)

    }catch {
      case e: Exception => e.printStackTrace()
    }finally {
      try {
        // 关闭 Elasticsearch 客户端
        client.close()
      } catch {
        case e: Exception => e.printStackTrace()
      }
      spark.close()
    }

  }

  // 获取时间戳
  val getTimestamp = udf((dateTimeStr:String) => {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    // 解析日期时间字符串为LocalDateTime对象
    val localDateTime: LocalDateTime = LocalDateTime.parse(dateTimeStr, formatter)
    // 将LocalDateTime对象转换为ZonedDateTime对象，使用系统默认时区
    val zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
    // 将ZonedDateTime对象转换为时间戳（毫秒）
    zonedDateTime.toInstant.toEpochMilli
  })

  /**
   * 读取hive数据并返回
   * @param spark
   * @param thirtydayStr 30天前日期
   * @param yesterdayStr 昨天日期
   * @param dataType 舆情/监管
   * @return
   */
  private def readHiveData(spark:SparkSession,thirtydayStr:String,yesterdayStr:String): DataFrame = {

    //舆情数据读取
    val feelingsql =
      s"""
         |select
         |  *
         |from
         |$feelingsTable
         |where event_day <= '$yesterdayStr'
         |and event_day >= '$thirtydayStr'
         |""".stripMargin
    val resDF = spark.sql(feelingsql)
      .withColumn("case_id", col("case_id").cast("long"))
      .withColumn("submit_time", getTimestamp(col("submit_time")).cast("long"))
      .filter(col("channel_management").isin("舆情", "监管","品牌","消保"))
      .filter(col("business_attributes") === "保障")

    println(s"读取${thirtydayStr}-${yesterdayStr}日期${feelingsTable}的数据有：" + resDF.count())
    resDF.show(5)
    resDF
  }
  /**
   * 处理es数据
   * @param sparkConf
   * @param i        循环数
   * @param dataDf   读取的hive数据
   * @param dataType 舆情/监管
   */
  private def dealWithEs(sparkConf:SparkConf,currentdateStr:String, i:Int,fieldNames: List[(String, String)],dataDf:DataFrame,mapping:String): Unit = {
    // 索引名拼接
    val IndexName = feelingsIndexAlias + "_" + currentdateStr.substring(0, 4) +
      "_" + currentdateStr.substring(4, 6) +
      "_" + currentdateStr.substring(6, 8)

    sparkConf.set("es.resource", IndexName)

    // 如果该索引已经存在 则删除该索引
    if (isExsitsIndex(IndexName)) {
      deleteIndex(IndexName)
    }
    //创建索引
    createIndex(IndexName,fieldNames,mapping)

    // 数据写入
    val feelingTable = dataDf.filter(col("event_day") === currentdateStr).repartition(5)
    EsSparkSQL.saveToEs(feelingTable,IndexName,Map("es.mapping.id"->"case_id"))

    //刷新写入数据的索引
    flushIndex(IndexName)

    // 移除旧的别名映射 移除之前先判断旧的索引是否存在 以及别名是否为uid_user_tag1_index
    if (isExsitsIndex(IndexName)&&indexIsExsitsAlias(IndexName).contains(feelingsIndexAlias)){
      indexRemoveAlias(IndexName, feelingsIndexAlias)
    }

    // 建立新的别名映射
    indexUpdateAlias(IndexName, feelingsIndexAlias)
    println("建立别名映射" + feelingsIndexAlias + "成功")
  }

  /**
   * * 删除旧索引
   * @param deletedayStr 删除的索引日期
   * @param dataType 舆情/监管
   */
  private def deleteOldIndex(deletedayStr: String): Unit = {

    // 旧索引名拼接
    val oldfIndexName = feelingsIndexAlias + "_" + deletedayStr.substring(0, 4) + "_" + deletedayStr.substring(4, 6) +
      "_" + deletedayStr.substring(6, 8)

    // 旧索引删除 先判断旧索引是否存在
    if (isExsitsIndex(oldfIndexName)) {
      deleteIndex(oldfIndexName)
      println("index" + oldfIndexName + " is exsit,deleteIndex:" + oldfIndexName)
    }
  }

  /**
   * 创建es客户端
   * @return
   */
  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient = RestClient.builder(new HttpHost("************", 8200, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("superuser", "metis_MegSed"))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)
  }

  /**
   * Generate the mapping for the given fields.循环生成所有字段的索引
   * @param fieldNames 字段列表
   * @return
   */
  def generateMappingWithFields(fieldNames: List[(String, String)]): String = {
    val baseMapping =
      """
        |{
        |  "settings": {
        |    "number_of_shards": 5,
        |    "number_of_replicas": 1
        |  },
        |  "mappings": {
        |    "properties": {
        |    }
        |  }
        |}
    """.stripMargin

    val propertiesBuilder = new StringBuilder()
    fieldNames.foreach { fieldName =>
      //按照字段类型来匹配不同的mapping
      fieldName._2 match {
       case "text" =>
                      propertiesBuilder.append(s"""
                        |      "${fieldName._1}": {
                        |        "type": "text",
                        |        "fields": {
                        |          "keyword": {
                        |            "type": "keyword",
                        |            "ignore_above": 256
                        |          }
                        |        }
                        |      },
                    """.stripMargin)
       case "long" =>
                     propertiesBuilder.append(s"""
                         |      "${fieldName._1}": {
                         |        "type": "long"
                         |      },
                    """.stripMargin)
       case _ => ""
      }
    }

    // Remove the trailing comma移除尾随逗号
    while (propertiesBuilder.nonEmpty && propertiesBuilder.last != ','){
      propertiesBuilder.deleteCharAt(propertiesBuilder.length - 1)
    }
    propertiesBuilder.deleteCharAt(propertiesBuilder.length - 1)

    // 替换掉baseMapping中properties的空值
    val finalMapping = baseMapping.replace(
      """|    "properties": {
         |    }""".stripMargin,
      s"""|    "properties": {
          |${propertiesBuilder.toString()}
          |    }""".stripMargin
    )

    finalMapping
  }

  /**
   * 创建索引
   * @param indexName 索引名
   * @param fieldNames 字段列表
   * @return
   */
  private def createIndex(indexName:String,fieldNames: List[(String, String)],mappingStr:String): Boolean = {
    //判断索引是否存在
    val request = new CreateIndexRequest(indexName)
    //准备request对象
    request.source(mappingStr,XContentType.JSON)
    //通过client去操作
    val createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT)
    //检查响应结果
    createIndexResponse.isAcknowledged
  }


  /**
   * 判断索引是否存在
   * @param indexName 索引名
   * @return
   */
  private def isExsitsIndex(indexName: String): Boolean = {
    //准备request对象
    val myrequest:GetIndexRequest = new GetIndexRequest(indexName)
    //通过client去操作
    val  myresult :Boolean= client.indices().exists(myrequest, RequestOptions.DEFAULT)
    //结果
    myresult
  }


  /**
   * 刷新索引
   * @param indexName 索引名
   */
  private def flushIndex(indexName: String): Unit = {
    //准备request对象
    val myFlushRequest: FlushRequest = new FlushRequest(indexName)
    //通过client去操作
    val myFlushResponse: FlushResponse = client.indices().flush(myFlushRequest,RequestOptions.DEFAULT)
    val totalShards = myFlushResponse.getTotalShards
    println("index: "+ indexName +" has"+ totalShards +"flush over! ")
  }

  /**
   * 判断索引的别名是否符合预期
   * @param indexAlias
   * @return
   */
  /*private def indexIsExsitsAlias(indexAlias: String): String = {
    // 别名信息获取对象
    val getAliasesRequest = new GetAliasesRequest(indexAlias)
    // 获取别名信息
    val getAliasesResponse = client.indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT)
    getAliasesResponse.getAliases.toString
  }*/


  /**
   * 删除老的别名映射
   * @param indexName
   * @param indexAlias
   * @return
   */
  private def indexRemoveAlias(indexName:String,indexAlias:String): Boolean = {
    //删除老的index别名映射
    val request = new IndicesAliasesRequest()
    val aliasAction = new AliasActions(AliasActions.Type.REMOVE)
      .index(indexName)
      .alias(indexAlias)

    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)
    // 检查响应结果
    if (response.isAcknowledged) {
      println("旧索引的别名删除成功")
    } else {
      println("旧索引的别名删除失败")
    }
    response.isAcknowledged
  }


  /**
   * * 建立新的别名映射
   * @param indexName
   * @param indexAlias
   * @return
   */
  private def indexUpdateAlias(indexName: String,indexAlias:String): Boolean = {
    // 请求建立新的别名映射
    val request = new IndicesAliasesRequest()

    val aliasAction = new AliasActions(AliasActions.Type.ADD)
      .index(indexName)
      .alias(indexAlias)
    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)

    // 检查响应结果
    if (response.isAcknowledged) {
      println("新索引的别名设置成功")
    } else {
      println("新索引的别名设置失败")
    }
    response.isAcknowledged
  }

  /**
   * 删除索引
   * @param indexName
   * @return
   */
  private def deleteIndex(indexName: String): Boolean = {
    val myDeleteIndexRequest = new DeleteIndexRequest()
    myDeleteIndexRequest.indices(indexName)
    val acknowledgedResponse =
      client
      .indices()
      .delete(myDeleteIndexRequest, RequestOptions.DEFAULT)
    acknowledgedResponse.isAcknowledged
  }

  /**
   * 判断索引的别名是否符合预期
   * @param indexAlias
   * @return
   */
  private def indexIsExsitsAlias(indexAlias: String): List[String] = {
    // 别名信息获取对象
    val getAliasesRequest = new GetAliasesRequest(indexAlias)
    // 获取别名信息
    val getAliasesResponse = client.indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT)
    val Aliases = getAliasesResponse.getAliases.asScala
    val res = Aliases.map(x => x._1).toList.sorted
    res
  }

}
