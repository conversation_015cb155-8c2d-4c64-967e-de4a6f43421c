package com.baidu.sql.customized.usergroup

import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.log4j.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest.AliasActions
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.admin.indices.flush.{FlushRequest, FlushResponse}
import org.elasticsearch.action.bulk.BulkRequest
import org.elasticsearch.action.index.IndexRequest
import org.elasticsearch.client.indices.{CreateIndexRequest, GetIndexRequest}
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.xcontent.XContentType

import java.time.LocalDate
import scala.collection.JavaConverters.mapAsScalaMapConverter

/**
 * <AUTHOR>
 * @date date 2024/1/25
 * @time 15:23
 * @package_name com.baidu.sql.customized   help_ecommerce_ads_user_value_info_df tds任务名
 */

object UserGroupDiversionHive2EsClientWrite {
  val client: RestHighLevelClient = createElasticsearchClient()
  val logger: Logger = Logger.getLogger(getClass.getName)
  def main(args: Array[String]): Unit = {
    /*
    * 本任务主要实现功能为：
    * 新建存储当日数据的es索引  uid_user_tag1_index_2024_01_23 (日期使用运行任务日期)
    * 每日例行从hive取数，写入es新建的索引中
    * 将新刷完数的索引映射值固定值的别名上  别名uid_user_tag1_index
    * 删除昨日刷数的索引
    * 操作es有三种方案，采用2方案 1.使用spark-es(之前经验是厂内调度不支持) 2.使用esclient 3.直接发送请求
    * */
    // 日期参数
    val yesterdayStr = args(0)
    val yesterday = LocalDate.parse(yesterdayStr, java.time.format.DateTimeFormatter.BASIC_ISO_DATE)
    val nextDay = yesterday.plusDays(1)
    // 当前日期
    val eventDay = nextDay.format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE)

    println("昨日日期为：" + yesterdayStr + ",当前日期为：" + eventDay)
    // 数据源表
    val sourceTable = "yinhe.ecommerce_ads_user_value_info_df"
    // 老索引名拼接
    val oldIndexName = "uid_user_tag1_index_" + yesterdayStr.substring(0, 4) + "_" + yesterdayStr.substring(4, 6) +
      "_" + yesterdayStr.substring(6, 8)
    // 新索引名拼接
    val newIndexName = "uid_user_tag1_index_" + eventDay.substring(0, 4) + "_" + eventDay.substring(4, 6) +
      "_" + eventDay.substring(6, 8)
    // 索引别名
    val indexAlias = "uid_user_tag1_index"
    // sparkConf
    val sparkConf = new SparkConf().setAppName("Hive2Es")
    sparkConf.set("es.nodes","*************")
    sparkConf.set("es.port","8200")
    sparkConf.set("es.net.http.auth.user" ,"superuser")
    sparkConf.set("es.net.http.auth.pass","fl2fXUYyZYq8")
    sparkConf.set("es.nodes.wan.only","true")
    sparkConf.set("es.nodes.discovery","false")
    sparkConf.set("es.index.auto.create","false")
    sparkConf.set("es.resource",newIndexName)
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("spark.task.maxFailures", "5")
      .config("spark.driver.allowMultipleContexts", true)
      .getOrCreate()
    //数据读取
    val sql =
      s"""
         |select cast(uid as STRING) as uid,
         |user_value_tag1
         |from
         |$sourceTable
         |where event_day = '$yesterdayStr'
         |""".stripMargin
    val hiveDF = spark.sql(sql)
    println("查询sql为："+sql)
    println(s"查询表${sourceTable}数据量为:" + hiveDF.count())

    try{
      // 如果该索引已经存在 则删除该索引
      if (isExsitsIndex(newIndexName)) {
        deleteIndex(newIndexName)
        println("index" + newIndexName + " is exsit,deleteIndex:" + newIndexName)
      }
      //创建索引
      createIndex(newIndexName)
      // 数据写入
      val batchSize = 10000
      hiveDF.rdd.foreachPartition { partition =>
        val bulkRequest = new BulkRequest()
        var count = 0
        partition.foreach(row => {
          val uid = row.getAs[String]("uid")
          val user_value_tag1 = row.getAs[String]("user_value_tag1")
          val json = s"""{"uid": "$uid", "user_value_tag1": "$user_value_tag1"}"""
          val indexRequest = new IndexRequest(newIndexName).id(uid)
            .source(json, XContentType.JSON)
          bulkRequest.add(indexRequest)

          count += 1
          if (count % batchSize == 0) {
            val bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT)
            // 处理批量操作的结果
            if (bulkResponse.hasFailures) {
              // 检查是否有失败的操作
              bulkResponse.forEach { bulkItemResponse =>
                if (bulkItemResponse.isFailed) {
                  val failure = bulkItemResponse.getFailure
                  println(s"操作失败：${failure.getMessage}")
                }
              }
            } else {
              // 所有操作都成功
              println("所有操作都成功")
            }
            // 清空当前批次的数据
            bulkRequest.requests().clear()
            count = 0
          }
        })
        if (bulkRequest.numberOfActions() > 0) {
          val bulkResponseRest = client.bulk(bulkRequest, RequestOptions.DEFAULT)
          if (bulkResponseRest.hasFailures) {
            // 检查是否有失败的操作
            bulkResponseRest.forEach { bulkItemResponse =>
              if (bulkItemResponse.isFailed) {
                val failure = bulkItemResponse.getFailure
                println(s"操作失败：${failure.getMessage}")
              }
            }
          } else {
            // 所有操作都成功
            println("所有操作都成功")
          }
        }
      }

      //刷新写入数据的索引
      flushIndex(newIndexName)
      // 移除旧的别名映射 移除之前先判断旧的索引是否存在 以及别名是否为uid_user_tag1_index

      /*if (isExsitsIndex(oldIndexName)&&indexIsExsitsAlias(oldIndexName).contains(indexAlias)){
        indexRemoveAlias(oldIndexName, indexAlias)
      }*/
      //检索出全部旧的索引
      val oldIndexList = indexIsExsitsAlias(indexAlias)
      for (oldIndex <- oldIndexList) {
        if(isExsitsIndex(oldIndex)){
          // 删除旧索引的别名映射
          indexRemoveAlias(oldIndex, indexAlias)
          // 删除旧索引
          deleteIndex(oldIndex)
          println("index" + oldIndex + " is exsit,deleteIndex:" + oldIndex)
        }
      }

      // 建立新的别名映射
      indexUpdateAlias(newIndexName, indexAlias)
      // 旧索引删除 先判断旧索引是否存在
      /*if (isExsitsIndex(oldIndexName)) {
        deleteIndex(oldIndexName)
        println("index" + oldIndexName + " is exsit,deleteIndex:" + oldIndexName)
      }*/
    }catch {
      case e: Exception =>
        e.printStackTrace()
    }finally {
      try {
        // 关闭 Elasticsearch 客户端
        client.close()
      } catch {
        case e: Exception =>
          e.printStackTrace()
      }
      spark.close()
    }
  }

  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient: RestClientBuilder =
      RestClient.builder(new HttpHost("*************", 8200, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider
      .setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("superuser", "fl2fXUYyZYq8"))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)

  }

  //创建索引 传入日期 依据日期生成索引名 创建索引之前 先判断该索引是否存在 如果存在则中断本次导数任务 并提醒检查该索引数据情况
  private def createIndex(indexName:String): Boolean = {

    val request = new CreateIndexRequest(indexName)
    val mappingStr =
      """
        |{
        |  "settings": {
        |    "number_of_shards": 5,
        |    "number_of_replicas": 1
        |
        |  },
        |  "mappings": {
        |    "properties": {
        |      "uid": {
        |        "type": "text",
        |        "fields": {
        |          "keyword": {
        |            "type": "keyword"
        |          }
        |        }
        |      },
        |      "user_value_tag1": {
        |        "type": "text",
        |        "fields": {
        |          "keyword": {
        |            "type": "keyword"
        |          }
        |        }
        |      }
        |    }
        |  }
        |}
        |""".stripMargin
    request.source(mappingStr,XContentType.JSON)
    val createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT)
    createIndexResponse.isAcknowledged
  }
  //判断索引是否存在
  private def isExsitsIndex(indexName: String): Boolean = {
    //准备request对象
    val myrequest:GetIndexRequest = new GetIndexRequest(indexName)
    //通过client去操作
    val  myresult :Boolean= client.indices().exists(myrequest, RequestOptions.DEFAULT)
    //结果
    myresult
  }
  //刷新索引
  private def flushIndex(indexName: String): Unit = {
    //准备request对象
    val myFlushRequest: FlushRequest = new FlushRequest(indexName)
    //通过client去操作
    val myFlushResponse: FlushResponse = client.indices().flush(myFlushRequest,RequestOptions.DEFAULT)
    val totalShards = myFlushResponse.getTotalShards
    println("index: "+ indexName +" has"+ totalShards +"flush over! ")
  }

  // 判断索引的别名是否符合预期
  private def indexIsExsitsAlias(indexAlias: String): List[String] = {
    // 别名信息获取对象
    val getAliasesRequest = new GetAliasesRequest(indexAlias)
    // 获取别名信息
    val getAliasesResponse = client.indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT)
    val Aliases = getAliasesResponse.getAliases.asScala
    val res = Aliases.map(x => x._1).toList.sorted
    res
  }

  // 删除老的别名映射
  private def indexRemoveAlias(indexName:String,indexAlias:String): Boolean = {
    //删除老的index别名映射
    val request = new IndicesAliasesRequest()
    val aliasAction = new AliasActions(AliasActions.Type.REMOVE)
      .index(indexName)
      .alias(indexAlias)
    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)
    // 检查响应结果
    if (response.isAcknowledged) {
      println("旧索引的别名删除成功")
    } else {
      println("旧索引的别名删除失败")
    }
    response.isAcknowledged
  }
  //建立新的别名映射
  private def indexUpdateAlias(indexName:String,indexAlias:String): Boolean = {
    // 请求建立新的别名映射
    val request = new IndicesAliasesRequest()
    val aliasAction = new AliasActions(AliasActions.Type.ADD)
      .index(indexName)
      .alias(indexAlias)
    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)
    // 检查响应结果
    if (response.isAcknowledged) {
      println("新索引的别名设置成功")
    } else {
      println("新索引的别名设置失败")
    }
    response.isAcknowledged
  }
  //索引删除
  private def deleteIndex(indexName: String): Boolean = {
    val myDeleteIndexRequest = new DeleteIndexRequest()
    myDeleteIndexRequest.indices(indexName)
    val acknowledgedResponse = client.indices().delete(myDeleteIndexRequest, RequestOptions.DEFAULT)
    acknowledgedResponse.isAcknowledged
  }

}
