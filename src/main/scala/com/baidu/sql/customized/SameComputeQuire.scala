package com.baidu.sql.customized

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.text.SimpleDateFormat
import java.util.{Date, Properties}


/**
 * <AUTHOR>
 */
object SameComputeQuire {
  def main(args: Array[String]): Unit = {

    // sql文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores : String = args(2)
    // spark.task.maxFailures
    val maxFailures : String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)


    // 获取程序启动时间
    val startTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc","false")
      .set("spark.sql.hive.convertMetastoreParquet","false")
      .set("spark.cores.max",cores)
      .set("spark.task.maxFailures",maxFailures)
      .set("spark.jars","/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")
      .setAppName(sql_name)

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    //import spark.implicits._

    // 获取mysql的配置
    val url: String = JDBCUtils.url
    val user: String = JDBCUtils.user
    val password: String = JDBCUtils.password
    val driver: String = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver",driver)


    // 读取same_last1源表文件
    val sameResourcePath = getClass.getResourceAsStream("/sqlfiles/ll/other/dwb_same_cnt_last1.sql")
    var sameSql: String = scala.io.Source.fromInputStream(sameResourcePath).mkString
    //sameSql 查询
    val sameResult: DataFrame = spark.sql(sameSql)
    //same创建临时视图
    sameResult.createOrReplaceTempView("dwb_same_cnt_last1")


    // 读取resources下面的sql文件
    val resourcePath = getClass.getResourceAsStream("/sqlfiles/" + sql_file)
    var sql: String = scala.io.Source.fromInputStream(resourcePath).mkString
    // 检查文件内容是否包含 LIMIT 100 不包含就添加
    sql = if (!sql.toUpperCase.contains("LIMIT 100")) {
      s"$sql limit 100"
    } else {
      sql
    }

    val result: DataFrame = spark.sql(sql)

    // 将每条数据转换为 JSON
    val jsonDf: DataFrame = result.selectExpr("to_json(struct(*)) AS data")

    // 获取程序结束时间
    val endTime: Long = System.currentTimeMillis()

    // 将程序启动时间，结束时间，还有字段描述，sql文件名加入DataFrame
    val res: DataFrame = jsonDf.withColumn("start_time", lit(formatter.format(new Date(startTime))).cast("string"))
      .withColumn("end_time", lit(formatter.format(new Date(endTime))).cast("string"))
      .withColumn("sql_file", lit(sql_file).cast("string"))
      .withColumn("desc", lit(sql_desc).cast("string"))
    // 打印Schema
    res.printSchema()

    // 打印数据
//    res.show(20)

    // 将数据写入最终落地表
    res.repartition(50).write.option("truncate", value = false)
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    spark.close()
  }
}
