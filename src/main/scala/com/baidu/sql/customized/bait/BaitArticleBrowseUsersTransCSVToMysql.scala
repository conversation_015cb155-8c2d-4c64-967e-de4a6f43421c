package com.baidu.sql.customized.bait

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, lit}
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.sql.DriverManager
import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 */
object BaitArticleBrowseUsersTransCSVToMysql {
  // 目标表mysql的配置
  val url: String = JDBCUtils.strategyTestUrl
  val user: String = JDBCUtils.strategyTestUser
  val password: String = JDBCUtils.strategyTestPassword
  val driver: String = JDBCUtils.driver
  def main(args: Array[String]): Unit = {
    /*
    * 先从本地测试
    * 读取csv文件模拟图灵表数据源
    * */
    // 获取程序启动时间
    val creatTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // sparkConf
    val sparkConf = new SparkConf().setAppName("BaitArticleBrowseUsersTransToMysql")
      .setMaster("local[*]")
    // sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.driver.allowMultipleContexts", true)
      //      .enableHiveSupport()
      .getOrCreate()



    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    /*   // 读取源数据
       // 读取rid数据
       var ridDF: DataFrame = spark.read
         .jdbc(url, "engine_bait_article", properties)
         .selectExpr("rid")

       // 将 DataFrame 转换为包含 Row 对象的数组，并提取 field1 字段的值
       val ridArr = ridDF.collect().map(row => row.getString(0))

       // 使用 mkString 方法将字段值用逗号分隔成一个字符串
       val ridArrString = ridArr.mkString(",")
       println(ridArrString)

       val turingSql =
         s"""
            |select
            |  uid,
            |  cuid,
            |  rid,
            |  from_unixtime(ceil(opts/1000),'yyyy-MM-dd HH:mm:ss') as op_time,
            |  app_type,
            |  brand,
            |  device,
            |  net_provider_name,
            |  country,
            |  province,
            |  city,
            |  referrer,
            |  baiduid,
            |  appid,
            |  district,
            |  browser,
            |  rid_prefix,
            |  r_type,
            |  publish_time
            |from
            |  ubs_feed.feed_dwd_pub_log_hi
            |where
            |  event_day  =  "111111111"
            |and rid in (
            |$ridArrString
            |)""".stripMargin
       println(turingSql)*/

    // 读取csv获取模拟从图灵表取数
    val csvDF = spark.read
      .option("header", "true")
      .option("inferSchema", "true")
      .csv("src/main/scala/com/baidu/sql/datafile/esdata/testdata3.csv")
      .dropDuplicates("uid")

    // engine_profile_info 数据字段处理
    val infoSinkDF = csvDF.selectExpr("uid as pass_id")
      .withColumn("detail_info", lit("{\"classifications\":[{\"classificationName\":\"浏览诱饵内容\",\"classificationId\":64}]}"))
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))

    //创建临时视图
    infoSinkDF.createOrReplaceTempView("infoSinkDF")

    // engine_profile_info 数据写入
    infoSinkDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, "engine_profile_info_1", properties)

    // engine_profile_user_classification 存储数据字段处理
    val classificationSinkDF = csvDF
      .selectExpr("uid as pass_id")
      .withColumn("engine_profile_class_id", lit(64))
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))

    // engine_profile_user_classification 数据写入
    classificationSinkDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, "engine_profile_user_classification_1", properties)

    // recent_profile_info_id现有数据查询
    val infoSourceDF: DataFrame = spark.read
      .jdbc(url, "engine_profile_info_1", properties)
      .selectExpr("pass_id", "id")

    // 查找info表中pass_id的最大id
    infoSourceDF.createOrReplaceTempView("infoSourceDF")

    val infoSourceDFsql =
      """
        |select
        |pass_id as source_pass_id,
        |max(id) as recent_profile_info_id
        |from
        |infoSourceDF
        |group by pass_id""".stripMargin
    val infoSourceMaxIdDF = spark.sql(infoSourceDFsql)
    infoSourceMaxIdDF.createOrReplaceTempView("infoSourceMaxIdDF")
    // user表现有数据查询
    val userSourceDF: DataFrame = spark.read
      .jdbc(url, "engine_profile_user_1", properties)
      .selectExpr("pass_id")

//    userSourceDF.createOrReplaceTempView("userSourceDF")

    // engine_profile_user 写入数据字段处理
    var userDF = csvDF
      .selectExpr("uid as pass_id")
      .withColumn("portrait_status", lit(5))
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))

    // 去除userDF中已有pass_id的数据
    userDF = userDF.join(userSourceDF, userDF("pass_id") === userSourceDF("pass_id"), "left_anti")
      .drop("userSourceDF.pass_id")

    // 给去重后的userDF 拼接recent_profile_info_id字段
    val userSinkDF = userDF.join(infoSourceMaxIdDF, userDF("pass_id") === infoSourceMaxIdDF("source_pass_id"), "left_outer")
      .drop("source_pass_id")

    userSinkDF.createOrReplaceTempView("userSinkDF")

    // 将本次info新增，但user表原来已有pass_id的数据做info_id的更新
    val infoJionUserSql =
      """
        |select
        |t1.pass_id as pass_id,
        |t2.recent_profile_info_id as recent_profile_info_id
        |from
        |(
        |select pass_id
        |from
        |infoSinkDF
        |where
        |pass_id not in (
        |select pass_id
        |from userSinkDF
        |)
        |) t1
        |join
        |infoSourceMaxIdDF t2
        |on t1.pass_id = t2.source_pass_id
        |""".stripMargin
    val infoJionUserDF = spark.sql(infoJionUserSql)
      .select(col("pass_id").cast("string"), col("recent_profile_info_id").cast("string"))
    println("user表中更新条数为：" + infoJionUserDF.count())
    // 更新数据
    if (!infoJionUserDF.isEmpty) {
      val passIdMap = infoJionUserDF
        .rdd
        .map(row => (row.getString(0), row.getString(1)))
        .collect()
        .toMap

      var value = " "
      val keys = passIdMap.keys

      keys.foreach { key =>
        value = passIdMap(key)
        update2Mysql(key, value)
        println(s"key is $key , value is $value")
      }
    }

    // engine_profile_user 写入
    userSinkDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, "engine_profile_user_1", properties)

    spark.close()
  }

  def update2Mysql(key: String, value: String): Unit = {
    val connection = DriverManager.getConnection(url, user, password)
    val sql = s"UPDATE engine_profile_user_1 SET recent_profile_info_id = $value WHERE pass_id = $key"
    val statement = connection.prepareStatement(sql)
    statement.executeUpdate()
    statement.close()
    connection.close()
  }

}
