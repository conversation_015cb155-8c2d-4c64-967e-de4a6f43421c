package com.baidu.sql.customized.bait

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 */
object BaitArticleBrowseUsersTransTuringToMysql {

  def main(args: Array[String]): Unit = {
    // 日期
    val eventDay = args(0)
    // 环境 online/test
    val environment = args(1)
    // 最终落地表
    val writeBackTable: String = args(2)

    // 获取程序启动时间
    val creatTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // sparkConf
    val sparkConf = new SparkConf().setAppName("BaitArticleBrowseUsersTransToMysql")

    // sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    // 目标表mysql的配置
    val driver: String = JDBCUtils.driver
    val (url, user, password) = environment match {
      case "online" => (JDBCUtils.strategyUrl, JDBCUtils.strategyUser, JDBCUtils.strategyPassword)
      case "test" => (JDBCUtils.strategyTestUrl, JDBCUtils.strategyTestUser, JDBCUtils.strategyTestPassword)
      case _ => throw new IllegalArgumentException(s"Invalid configType: $environment")
    }

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    // 读取rid数据
    val ridJdbcDF: DataFrame = spark.read
      .jdbc(url, "engine_bait_article", properties)
      .selectExpr("rid")

    // 拼接rid in条件字符串
    var ridArr = "( " + ridJdbcDF.collect().mkString(",").replaceAll("\\[", "'").replaceAll("\\]", "'") + " )"

    if (ridArr.equals("(  )")) {
      ridArr = "('0')"
    }

    val turingSql =
      s"""
         |select
         |  uid as pass_id,
         |  cuid,
         |  rid,
         |  from_unixtime(ceil(opts/1000),'yyyy-MM-dd HH:mm:ss') as op_time,
         |  app_type,
         |  brand,
         |  device,
         |  net_provider_name,
         |  country,
         |  province,
         |  city,
         |  referrer,
         |  baiduid,
         |  appid,
         |  district,
         |  browser,
         |  rid_prefix,
         |  r_type,
         |  publish_time
         |from
         |  ubs_feed.feed_dwd_pub_log_hi
         |where
         |  event_day = '$eventDay'
         |and rid in ${ridArr}
         |""".stripMargin

    println(turingSql)

    val turingDF = spark.sql(turingSql)

    // mysql库规范字段添加
    val resultDF = turingDF
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))

    // mysql结果表数据写入
    resultDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    spark.close()
  }

}
