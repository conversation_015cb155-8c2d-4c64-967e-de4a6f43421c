package com.baidu.sql.customized.riskrecall

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.sql.functions.encode
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/4/29
 * @description: 风控举报数据同步到MySQL（范春雪提需）
 */
object ReCallJuBaoToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._
    
    // 读取Hive表
    val hiveSql =
      s"""
         |SELECT
         |  t1.event_day,
         |  t1.submit_time,
         |  t1.update_time,
         |  t1.id as case_id,
         |  t1.description,
         |  t1.username,
         |  t1.userid,
         |  t1.status AS ufo_status,
         |  t1.rid,
         |  t2.status AS status, -- 最后审核结果
         |  t2.reject_reason, -- 最后审核拒绝原因 标签类型后续在sugar上过滤
         |  t2.product AS product, -- 产品
         |  t2.check_type AS check_type, -- 审核类型
         |  t3.first_category -- 垂类
         |FROM
         |  (
         |    SELECT
         |      event_day,
         |      submit_time,
         |      update_time,
         |      id,
         |      description,
         |      username,
         |      userid,
         |      status,
         |      substring_index (resource_id, '_', -1) AS rid
         |    FROM
         |      udw_ns.default.help_ods_ufo_feed_report_di
         |    WHERE
         |      event_day = '${YesterDay}'
         |  ) AS t1
         |  LEFT JOIN (
         |    SELECT
         |      article_id,
         |      status,
         |      reject_reason,
         |      product,
         |      check_type
         |    FROM
         |      (
         |        SELECT
         |          article_id,
         |          row_number() OVER (
         |            PARTITION BY
         |              article_id
         |            ORDER BY
         |              insert_time DESC
         |          ) AS rm,
         |          product,
         |          status,
         |          reject_reason,
         |          check_type
         |        FROM
         |          crcc_data.crcc_bjh_ods_audit_auditlog_esdump_df
         |        WHERE
         |          event_day = '${YesterDay}'
         |          AND type IN (
         |            '1000002406',
         |            '1000001657',
         |            '1000001586',
         |            '1000001577',
         |            '1000000676',
         |            '1000002187',
         |            '1000002100',
         |            '1000002044',
         |            '1000001960',
         |            '1000001591',
         |            '1000001590',
         |            '1000000956',
         |            '1000000715',
         |            '1000000714',
         |            '1000000201',
         |            '1000000125',
         |            '1000000093',
         |            '202001',
         |            '201001',
         |            '108009',
         |            '107008',
         |            '1000001476',
         |            '1000001474',
         |            '1000000703',
         |            '1000000702',
         |            '1000000698',
         |            '1000000608',
         |            '1000000560',
         |            '1000000559',
         |            '1000000024',
         |            '110003',
         |            '1000001482',
         |            '1000001480',
         |            '1000002405',
         |            '1000001656',
         |            '1000001652',
         |            '109701',
         |            '109308',
         |            '1000002179',
         |            '1000002097',
         |            '1000002043',
         |            '1000001961',
         |            '1000001650',
         |            '1000001649',
         |            '1000001651',
         |            '1000000500',
         |            '109208',
         |            '1000002175',
         |            '1000002341',
         |            '1000002101',
         |            '1000001793',
         |            '1000001776',
         |            '1000002158',
         |            '1000001758',
         |            '1000001753',
         |            '1000001756',
         |            '1000001754',
         |            '1000001936',
         |            '1000001935',
         |            '1000001934',
         |            '1000001863',
         |            '1000001856',
         |            '1000001855',
         |            '1000001760',
         |            '1000001755',
         |            '1000001752',
         |            '1000001759',
         |            '1000001757'
         |          )
         |      )
         |    WHERE
         |      rm = 1
         |  ) AS t2 ON t1.rid = t2.article_id --  举报分区最后审核结果
         |  LEFT JOIN (
         |    SELECT
         |      nid,
         |      first_category
         |    FROM
         |      bjh_data.bjh_feed_resource_rf
         |    WHERE
         |      event_day = '${YesterDay}'
         |  ) AS t3 ON t1.rid = t3.nid -- 资源所属一级分类
         |""".stripMargin

    val hiveDf = spark.sql(hiveSql)
      .select(
        $"case_id".cast("string"),
        $"submit_time".cast("timestamp"),
        $"update_time".cast("timestamp"),
        $"description".cast("string"),
        $"username".cast("string"),
        $"userid".cast("string"),
        $"ufo_status".cast("string"),
        $"rid".cast("string"),
        $"status".cast("string"),
        $"reject_reason".cast("string"),
        $"product".cast("string"),
        $"check_type".cast("string"),
        $"first_category".cast("string"),
        $"event_day".cast("string")
      )
      .withColumn("description",encode($"description","utf-8"))
      .na.fill("", Seq("case_id","description","username","userid","ufo_status","rid","status","reject_reason",
        "product","check_type","first_category","event_day")) // 填充字符串列的空值
      .dropDuplicates()
      .cache()
    println(s"获取举报${YesterDay}数据共：${hiveDf.count()}条数据")

    val properties = PropertiesUtils.MysqlFengKongZhaoHuiProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    val deleteSql = s"delete from patrol_sugar_report where event_day = ${YesterDay} "
    //将当天的数据先删除再写入
    CommonUtils.deleteMysql(properties,deleteSql)
    println(s"删除历史${YesterDay}数据成功")

    // 写入MySQL数据库
    hiveDf
      .repartition(10)
      .write
      //.option("truncate", "true")
      .mode(SaveMode.Append)
      .jdbc(properties.getProperty("url"), "patrol_sugar_report", properties)
    spark.close()
  }
}
