package com.baidu.sql.customized.passport

import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.util.Properties

/**
 * <AUTHOR>
 */
object PassIdReadMysqlToFile {

    def main(args: Array[String]): Unit = {
      //从sms表中读取phone、sms_send_time两个字段，按照create_time分区写入hive中
      // 源表
      val sourceTable: String = "engine_profile_sms"
      val YESTERDAY: String = "20231106"
      //sparkConf
      val sparkConf = new SparkConf().setAppName("ReadEs")
        .setMaster("local[*]")
      //sparkSession配置
      val spark = SparkSession
        .builder()
        .config(sparkConf)
        .config("spark.task.maxFailures", "5")
        .config("spark.driver.allowMultipleContexts", true)
        .getOrCreate()
//************************************************************?" +
//        "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
//        "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
      // 数据源mysql的配置
      val url: String = "***********************************************"
      val user: String = "root"
      val password: String = "PlatTestDB@7!"
      val driver: String = "com.mysql.jdbc.Driver"

      val properties = new Properties()
      properties.setProperty("user", user)
      properties.setProperty("password", password)
      properties.setProperty("driver", driver)

      //读取源数据
      val sourceDF: DataFrame = spark.read
        .jdbc(url, sourceTable, properties)
        .where(s"""date_format(create_time,"yyyyMMdd") = $YESTERDAY""" )
        .selectExpr("phone", "date_format(sms_send_time,\"yyyy-MM-dd HH:mm:ss\") as sms_send_time", "date_format(create_time,\"yyyyMMdd\") as event_day")
      sourceDF.show()
      //取需要的字段
//      val resultDF = sourceDF.selectExpr("phone", "date_format(sms_send_time,\"yyyy-MM-dd HH:mm:ss\") as sms_send_time", "date_format(create_time,\"yyyyMMdd\") as event_day")
      sourceDF.printSchema()
      // 将数据写入最终落地表
      sourceDF.limit(20).write.mode(SaveMode.Overwrite).csv("src/main/scala/com/baidu/sql/datafile/esdata/mysqldata")
      spark.close()
    }

}
