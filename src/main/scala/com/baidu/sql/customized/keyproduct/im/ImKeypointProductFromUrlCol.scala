package com.baidu.sql.customized.keyproduct.im

import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import com.baidu.sql.customized.keyproduct.commons.{EsConf, MysqlConf}
import okhttp3._
import org.apache.commons.codec.digest.DigestUtils
import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.spark.SparkConf
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.functions.{col, from_json, get_json_object, lit, when}
import org.apache.spark.sql.types.{ArrayType, DataTypes, StringType, StructType}
import org.apache.spark.sql.{<PERSON><PERSON><PERSON><PERSON>, Row, SparkSession, functions}
import org.elasticsearch.action.search._
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.unit.TimeValue
import org.elasticsearch.index.query.{BoolQueryBuilder, QueryBuilders}
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.elasticsearch.search.{Scroll, SearchHit}

import java.io.IOException
import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDate, LocalDateTime, ZoneId}
import scala.collection.mutable
import scala.collection.mutable.{ArrayBuffer, ListBuffer}

/**
 * <AUTHOR>
 * @date date 2024/2/23
 * @time 15:24
 * @package_name com.baidu.sql.customized.keyproduct.im
 */
object ImKeypointProductFromUrlCol {
  /*新方案，从在线导数接口获取需要计算的字段
  需要传入im_id 和 产品线
  1、从es根据starttime获取每天新增及变化数据的im_id，并对im_id进行去重  和产品线，目前只查6 百家号 和 31 荣耀浏览器

  3、把获取到的id和产品线数据，每100个一批塞到数组内，请求接口获取数据
  4、把es查出来的数据，跟接口获取的数据，通过im_id进行join
  * */
  //
  val client: RestHighLevelClient = createElasticsearchClient()
  val indexName = "metis_access_rt_release"

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * */

//    // 开始时间 本地测试 2024-03-12
//    val startTimeStamp: Long = 1710172800000L
//    // 结束时间
//    val endTimeStamp: Long = 1710172800000L + 1000*60*60*12
//    // spark
//    val sparkConf = new SparkConf().setAppName("ReadEs").setMaster("local[*]")
//    // sparkSession配置
//    val spark = SparkSession
//      .builder()
//      .config(sparkConf)
//      .config("spark.task.maxFailures", "5")
//      .config("spark.driver.allowMultipleContexts", true)
//      .getOrCreate()

    // 分区时间
    val YESTERDAY: String = args(0)
    // 获取yesterday豪秒级时间戳
    val date = LocalDate.parse(YESTERDAY, java.time.format.DateTimeFormatter.BASIC_ISO_DATE)
    val yesterdayTimestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli
    val startTimeStamp = yesterdayTimestamp
    val endTimeStamp = yesterdayTimestamp + 1000 * 60 * 60 * 24
    //spark
    val sparkConf = new SparkConf().setAppName("ReadEs")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      //.master("local")
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled","true")
      .config("spark.sql.adaptive.coalescePartitions.enabled","true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    //获取“交互轮次”字段，round_num
    var commonSql =
      """
        |select session_id as mysql_session_id,round_num
        |from data_online_dialogue
        |where session_id is not null
        |""".stripMargin

    val roundNum = spark.read
      .format("jdbc")
      .option("url", "********************************************************************************************************************************" +
        "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true")
      .option("driver", MysqlConf.Driver)
      .option("user", "follow_on_read")
      .option("password", "123456")
      .option("query", commonSql)
      .load()
      .cache()

    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
    val productNameMap = Map(
      1L -> "百度APP",
      6L -> "百家号",
      3L -> "百度文库",
      5L -> "百度贴吧",
      8L -> "百度服务商城",
      11L -> "爱企查",
      12L -> "内容产品-综合",
      17L -> "用户增长",
      25L -> "问一问",
      31L -> "荣耀浏览器",
      35L -> "萝卜快跑",
      37L -> "文心一言",
      41L -> "百度教育"
    )


    for (product <- productNameMap) {
      val productId: Long = product._1
      val productName: String = product._2

      val rdd = spark.sparkContext.parallelize(getScrollData(startTimeStamp, endTimeStamp, productId))
      // 定义模式（即Schema），这里只有一个名为"data"的字符串字段
      val schema: StructType = new StructType()
        .add("data", DataTypes.StringType)
      // 使用SparkSession创建DataFrame
      val df: DataFrame = spark.createDataFrame(rdd.map(row => Row(row)), schema)
      // df.show(20, truncate = false)
      // 解析字段sql  这里把接口无法取出的字段做处理
      val tempDf = df
        .withColumn("product_id", getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.productId").cast("String")))
        .withColumn("app_id", getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.appId").cast("String")))
        //.withColumn("space_name", getProductName(col("product_id"),col("app_id")))
        .withColumn("product_line", lit(productName).cast("String"))
        .withColumn("im_session_id", getNullVal(get_json_object(col("data"), "$.imSessionId").cast("String")))
        .withColumn("os", getNullVal(get_json_object(col("data"), "$.startInfo.startContext.query.os").cast("String")))
        .withColumn("sdkvn", getNullVal(get_json_object(col("data"), "$.startInfo.startContext.query.sdkvn").cast("String")))
        .withColumn("ipRegion", getNullVal(get_json_object(col("data"), "$.obuzFollow.sysInfoMap.ipRegion").cast("String")))
        .withColumn("session_id", getNullVal(get_json_object(col("data"), "$.obuzFollow.id").cast("String")))
        .withColumn("im_session_status", getNullVal(get_json_object(col("data"), "$.imSessionStatus").cast("String")))
        // 一级产品问题类型
        .withColumn("product_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[0]"))
            .cast("String"))
        // 二级产品问题类型
        .withColumn("function_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[1]"))
            .cast("String"))
        // 三级产品问题类型
        .withColumn("function_detail_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[2]"))
            .cast("String"))
        // 四级产品问题类型
        .withColumn("customlv41",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[3]"))
            .cast("String"))
        // 五级产品问题类型
        .withColumn("customlv51",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[4]"))
            .cast("String"))
        // 产品问题分类
        // 一级产品问题类型
        .withColumn("product_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(0)))
        // 二级产品问题类型
        .withColumn("function_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(1)))
        // 三级产品问题类型
        .withColumn("function_detail_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(2)))
        // 四级产品问题类型
        .withColumn("customlv42",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(3)))
        // 五级产品问题类型
        .withColumn("customlv52",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(4)))
        // 智能客服内容id
        .withColumn("auto_content_id",
          getAutoContentId(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)))
            .cast("String"))
        // 智能客服CSI
        .withColumn("auto_content_csi",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("CSI"))
            .cast("String"))
        // 智能客服FCR
        .withColumn("auto_content_fcr",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("FCR"))
            .cast("String"))
        //icafe状态
        .withColumn("icafe_status",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.icafeStatus")).cast("String"))
        //是否转人工（包留言）
        .withColumn("is_transfer",
          when(getNullVal(get_json_object(col("data"), "$.routePoint.currentRouteInfoIndex")).cast("String") === "1", "是").otherwise("否"))
        //点赞数
        .withColumn("upvote_cnt",getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("普通"),lit("11")))
        //点踩数
        .withColumn("downvote_cnt",getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("普通"),lit("9")))
        //文心回答正确数
        .withColumn("wen_correct_cnt",getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心"),lit("点赞")))
        //文心回答错误数
        .withColumn("wen_error_cnt",getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心"),lit("点踩")))
        //文心未回答数
        .withColumn("wen_unanswered_cnt",getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心"),lit("未回答")))
        //文心回答错误消息明细
        .withColumn("wen_error_detail",getEvaluateDescDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("点踩")))
        //文心未回答消息明细
        .withColumn("wen_unanswered_detail",getEvaluateDescDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("未回答")))
        //是否触发会话监控
        .withColumn("is_session_monitor",
          when(getNullVal(get_json_object(col("data"), "$.sessionContext.monitorStrategyFollowKey").cast("String")) === "", "否").otherwise("是"))
        //会话监控人工接入情况
        .withColumn("sessionz_label_values",
          getSessionzLabelValues(from_json(get_json_object(col("data"), "$.obuzFollow.sessionzLabelValues"), ArrayType(StringType)))
            .cast("String"))
        //作弊策略
        .withColumn("hit_cheating_strategy",
          getNullVal(get_json_object(col("data"), "$.startInfo.startContext.hitCheatingStrategy").cast("String")))
        //UserAgent
        .withColumn("user_agent",
          getNullVal(get_json_object(col("data"), "$.sessionContext.userAgent").cast("String")))
        //cuid
        .withColumn("cuid_1",
          getNullVal(get_json_object(col("data"), "$.sessionContext.prod_cuid").cast("String")))
        .withColumn("cuid_2",
          getNullVal(get_json_object(col("data"), "$.startInfo.startContext.baiduCuid").cast("String")))
        .withColumn("cuid_3",
          getNullVal(get_json_object(col("data"), "$.startInfo.startUserInfo.baiduCuid").cast("String")))
        // 智能评价满意度标签
        .withColumn("auto_satisfaction_label",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("智能标签"))
            .cast("String"))
        // 人工评价满意度标签
        .withColumn("artificial__satisfaction_label",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("人工标签"))
            .cast("String"))
        //channelid
        .withColumn("channel_id",
          getNullVal(get_json_object(col("data"), "$.sessionContext.channelid").cast("String")))
        //活动来源
        .withColumn("channel_id_map",
          getNullVal(get_json_object(col("data"), "$.sessionContext.channelidMap").cast("String")))
        // 智能评价建议文本
        .withColumn("auto_advise_text",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("智能文本"))
            .cast("String"))
        // 人工评价建议文本
        .withColumn("artificial_advise_text",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluates"),lit("人工文本"))
            .cast("String"))
        //.filter(!(col("product_id") === "1" and !col("app_id").isin("2001087","2001088","2001126","2001092","2001093","2001094","2001095","2001122")))
        .drop("data")
        .dropDuplicates(Seq("im_session_id", "product_id"))
      resultDf.append(tempDf)
    }
    // 把各业务线的df Union到一起 这里的combinedDF是 筛除没有session_id的，并且根据product_id和im_session_id进行去重
    val esDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2)).dropDuplicates(Seq("im_session_id", "product_id")).repartition(2)
    esDF.show(5,false)
    println(s"esDF的数据量为${esDF.count()}条")
    // 拉取两个用于接口获取数据的字段
    val filterDF = esDF.select("im_session_id", "product_id")
    // 构造（product_id,im_session_id）元组
    val tupleRDD = filterDF.rdd.map(row => (row.getAs[String]("product_id"), row.getAs[String]("im_session_id")))
//    tupleRDD.foreach(println)
    // 按product_id 分组，把im_session_id形成一个array
    val groupedRDD = tupleRDD.groupByKey().mapValues(iter => iter.toArray)
//    groupedRDD.foreach(println)
    // 分批次请求
    val responseArray = groupedRDD.flatMap { case (productId, imSessionIdArray) =>
      val batchSize = 100
      val numBatches = Math.ceil(imSessionIdArray.length / batchSize.toDouble).toInt
      val requestBatches = imSessionIdArray.grouped(batchSize).toSeq

      var responses = new ArrayBuffer[(String, Seq[String])]()

      for (i <- 0 until numBatches) {
        val requestBatch = requestBatches(i)
        val response = postRequest(productId, requestBatch)
        //println("请求的结果是：" + response)
        responses :+= ((productId, Seq(response)))
        Thread.sleep(1000) // 控制请求 QPS
      }

      responses
    }
    // 接口返回结果处理
    val finalResponseArray: RDD[String] = responseArray.flatMap { case (_, response) =>
      response.flatMap { jsonStr =>
        var res = Seq.empty[String]
        val jsonObject = JSON.parseObject(jsonStr)
        if (!jsonObject.isEmpty){
          val dataArray: Option[JSONArray] = Option(jsonObject.getJSONArray("data"))
          res = dataArray match {
            case Some(array) => (0 until array.size()).map(i => array.getString(i))
            case None => Seq.empty[String]
          }
        }
        res
      }
    }
    import spark.implicits._
    val finalResponseDF = finalResponseArray.toDF("url_data").repartition(2)
//    finalResponseDF.show(20, false)
    // 接口获取的数据存储到内存中
//    finalResponseDF.cache()
    // 接口获取数据的结构化
    val urlDF = finalResponseDF
//      .withColumn("session_id", get_json_object(col("url_data"), "$.sessionzId").cast("String"))
      .withColumn("channel_platform_space", getNullVal(get_json_object(col("url_data"), "$.feedback_channel").cast("String")))
      .withColumn("userid", getNullVal(get_json_object(col("url_data"), "$.UID").cast("String")))
      .withColumn("user_name", getNullVal(get_json_object(col("url_data"), "$.cUName").cast("String")))
      .withColumn("baijiahao_user_id", getNullVal(get_json_object(col("url_data"), "$.baijiahao_user_id").cast("String")))
      .withColumn("baijiahao_name", getNullVal(get_json_object(col("url_data"), "$.baijiahao_name").cast("String")))
      .withColumn("baijiahao_type", getNullVal(get_json_object(col("url_data"), "$.baijiahao_type").cast("String")))
      .withColumn("session_content", getNullVal(get_json_object(col("url_data"), "$.sessionContent").cast("String")))
      .withColumn("submit_time", getNullVal(get_json_object(col("url_data"), "$.create_time").cast("String")))
      .withColumn("feedback_type", getNullVal(get_json_object(col("url_data"), "$.feedback_type").cast("String")))
      .withColumn("quesiton_desc", getNullVal(get_json_object(col("url_data"), "$.quesiton_desc").cast("String")))
      .withColumn("remark", getNullVal(get_json_object(col("url_data"), "$.remark").cast("String")))
      .withColumn("user_evaluate", getNullVal(get_json_object(col("url_data"), "$.userEvaluate").cast("String")))
      .withColumn("evaluate_csi", getNullVal(get_json_object(col("url_data"), "$.evaluateCsi").cast("String")))
      .withColumn("is_solve", getNullVal(get_json_object(col("url_data"), "$.isSolve").cast("String")))
      .withColumn("accept_group", getNullVal(get_json_object(col("url_data"), "$.acceptGroup").cast("String")))
      .withColumn("is_change", getNullVal(get_json_object(col("url_data"), "$.isChange").cast("String")))
      .withColumn("update_time", getNullVal(get_json_object(col("url_data"), "$.update_time").cast("String")))
      .withColumn("custom_label", getNullVal(get_json_object(col("url_data"), "$.custom_label").cast("String")))
      .withColumn("risk_type", getNullVal(get_json_object(col("url_data"), "$.risk_type").cast("String")))
      .withColumn("risk_level", getNullVal(get_json_object(col("url_data"), "$.risk_level").cast("String")))
      .withColumn("scene_type", getNullVal(get_json_object(col("url_data"), "$.scene_type").cast("String")))
      .withColumn("close_type", getNullVal(get_json_object(col("url_data"), "$.closeType").cast("String")))
      .withColumn("im_session_id_url", getNullVal(get_json_object(col("url_data"), "$.imSessionId").cast("String")))
      .withColumn("prod_type_list", getNullVal(get_json_object(col("url_data"), "$.prod_type_list").cast("String")))
      .withColumn("ernie_bot_flag", getNullVal(get_json_object(col("url_data"), "$.ernieBotFlag").cast("String")))
      .withColumn("sessionz_time", getNullVal(get_json_object(col("url_data"), "$.sessionz_time").cast("String")))
      .withColumn("first_handle_time", getNullVal(get_json_object(col("url_data"), "$.first_handle_time").cast("String")))
      .withColumn("avg_handle_time", getNullVal(get_json_object(col("url_data"), "$.avg_handle_time").cast("String")))
      .withColumn("accept_group", getNullVal(get_json_object(col("url_data"), "$.acceptGroup").cast("String")))
      .withColumn("not_accept", getNullVal(get_json_object(col("url_data"), "$.not_accept").cast("String")))
      .withColumn("update_user_1", getNullVal(get_json_object(col("url_data"), "$.updateUser").cast("String")))
      .withColumn("update_user_2", getNullVal(get_json_object(col("url_data"), "$.updateUser2").cast("String")))
      .withColumn("update_user_3", getNullVal(get_json_object(col("url_data"), "$.updateUser3").cast("String")))
      .withColumn("icafe_last_pull_time", when(get_json_object(col("url_data"), "$.icafeLastPullTime").cast("String") === "--","").otherwise(getNullVal(get_json_object(col("url_data"), "$.icafeLastPullTime").cast("String"))))
      .withColumn("icafe_flag", getNullVal(get_json_object(col("url_data"), "$.icafeFlag").cast("String")))
      .drop("url_data")
      .repartition(2)
      .cache()
    //urlDF.show(5, false)
    println(s"urlDF的数据量为${urlDF.count()}条")

    // es获取到的数据 join url获取到的数据
    val combinedDF = esDF.join(urlDF, esDF("im_session_id") === urlDF("im_session_id_url"), "inner")
      .join(roundNum,esDF("session_id") === roundNum("mysql_session_id"), "left")
      .drop("im_session_id_url","mysql_session_id")
      .filter(col("im_session_id").isNotNull and col("im_session_id") =!= "")
      .repartition(2)
    //combinedDF.show(5, false)
    combinedDF.createOrReplaceTempView("combined_data")
    // 对需要做拼接\中文映射的数据做处理并保存数据
    val sql =
      s"""
         |insert into table udw_ns.default.help_ods_ufo_key_point_productline_di partition (event_day = $YESTERDAY )
         |select
         |	product_line, --  重点业务
         |	'' as work_type, --  类型
         |	'' as channel_platform_space, --   迟滞渠道-平台-空间
         |	'' as space_name, --    产品线名称
         |	'' as space_id, --  产品线ID
         |	'' as space_id_old, --  原产品线ID
         |	'' as id, --  序号
         |	channel_platform_space as extend_feedback_channel, --	反馈来源
         |	'' as need_manual, --   转人工
         |	case when product_type1 = '' then product_type2 else product_type1 end as product_type, --  一级产品问题类型
         |	case when function_type1 = '' then function_type2 else function_type1 end as function_type, --    二级产品问题类型
         |	case when function_detail_type1 = '' then function_detail_type2 else function_detail_type1 end as function_detail_type, -- 三级产品问题类型
         |	case when customlv41 = '' then customlv42 else customlv41 end as customlv4, --   四级产品问题类型
         |	case when customlv51 = '' then customlv52 else customlv51 end as customlv5, --    五级产品问题类型
         |	'' as complaint_type, -- 投诉问题类型
         |	'' as complaint_deal_result, --  投诉处理结果
         |	'' as complaint_is_result, --  用户是否投诉
         |	'' as robot_eva_stat, --   智能客服CSI
         |	'' as robot_eva_sloved, --   智能客服FCR
         |	'' as robot_eva_advise, --   智能客服建议
         |	'' as robot_eva_stat_label, --   智能CSI标签
         |	'' as intp_digital_man, --   数字人
         |	'' as is_pipe, --    是否触发流水线
         |	'' as robot_eva_diss_content, --   用户不满意弹窗回答
         |	'' as mobile_app_name, --    App包含名
         |	'' as evaluation_count, --   用户评价
         |	'' as invite_evaluation_type, --     内容邀评类型
         |	'' as evaluation_filter, --    内容评价结果
         |	'' as invite_evaluation_filter, --   内容是否邀评
         |	'' as discontent_reason_filter, --   内容不满原因
         |	'' as discontent_labels_filter, --   内容不满标签
         |	'' as audit_pass, --   是否通过审核
         |	'' as audit_reason, --   未通过原因
         |	'' as violence_score, --   暴恐违禁分数
         |	'' as salacity_score, --   文本色情分数
         |	'' as politics_score, --   政治敏感分数
         |	'' as robot_reply, --    机器人回复
         |	'' as first_reply_length_second, --    首次回复时长
         |	'' as pipe_reply, --     流水线回复
         |	'' as manual_reply, --     人工回复
         |	userid,	--  用户id
         |	'' as ip, --   IP地域
         |	'' as ip_province, --   省份
         |	'' as mobile_model, --  手机型号
         |	'' as mobile_brand, --    手机品牌
         |	'' as update_user, --   更新人
         |	'' as status, --     反馈处理状态0 自动预处理1 人工待处理2 处理中9 已完成
         |	'' as valid, --    反馈处理状态1 有效2 无效3 已转出
         |	feedback_type, -- 	反馈分类
         |	submit_time, -- 	提交时间
         |	'' as icafe_filter, --  是否流转icafe 1 是 0 否
         |	'' as from_product_line, --   转入产品线
         |	'' as pre_product_info, --  流转来源
         |	risk_level, --  风险等级
         |  '' as to_product_line,  --   转出到
         |	"在线" as channel,  -- 渠道
         |	session_id, -- 会话id
         |	user_name,  --  用户名
         |	baijiahao_user_id, -- 百家号ID
         |	baijiahao_name, -- 百家号名称
         |	baijiahao_type, -- 百家号类型
         |	regexp_replace(regexp_replace(session_content, '\\n', ' '), '\\r', '') AS session_content, -- 反馈内容
         |	quesiton_desc, -- 问题描述
         |	remark, -- 备注
         |	user_evaluate, -- 用户评价
         |	evaluate_csi, -- 人工客服CSI
         |	is_solve, -- 解决状态
         |	accept_group, -- 客服组
         |	is_change, -- 是否转接
         |	update_time, -- 更新时间
         |	custom_label, -- 自定义标签
         |	scene_type, -- 场景类型
         |	close_type, -- 结束类型
         |	im_session_id, -- 会话标识
         |	prod_type_list, -- 问题类型
         |	ernie_bot_flag, -- 是否文心一言回复
         |	sessionz_time,  -- 会话时长（秒）
         |	first_handle_time, -- 首次响应时长（秒）
         |	avg_handle_time, -- 平均响应时长（秒）
         |	not_accept, -- 接入失败
         |  regexp_replace((case
         |    when update_user_3 != '' then concat(update_user_1, ',', update_user_2, ',', update_user_3)
         |    when update_user_2 != '' then concat(update_user_1, ',', update_user_2)
         |    when update_user_1 != '' then update_user_1
         |    else ''
         |  end), ',,' , '') as update_user_list, --  更新人
         |	case when im_session_status = "CLOSE"  then "关闭" else "开启" end  im_session_status, -- 是否关闭
         |	regexp_replace(concat(os,'-',sdkvn,'-',ipRegion), '--', '') as sys_info, -- 系统信息
         |  risk_type, -- 风险类型
         |  '' as good_evaluation, -- 好评次数
         |  '' as bad_evaluation, -- 差评次数
         |  '' as no_evaluation, -- 未评价次数
         |  '' as rx_process_instance_id, -- 热线编号
         |  '' as rx_relation_id, -- 关联id
         |  '' as rx_close_time, -- 关单时间
         |  '' as rx_subject, -- 主题
         |  '' as rx_process_definition_name, -- 流程名称
         |  '' as rx_temp_create_time, -- 联络历史创建时间
         |  '' as rx_temp_modified_time, -- 联络历史修改时间
         |  '' as rx_temp_create_user, -- 联络历史创建人
         |  '' as rx_temp_modified_user, -- 联络历史修改人
         |  '' as rx_temp_customer_id, -- 客户id
         |  '' as rx_temp_title, -- 标题
         |  '' as rx_temp_is_refund, -- 是否涉及退款
         |  '' as rx_temp_refund_order_id, -- 退款订单号
         |  '' as rx_temp_handle_process, -- 运营/PM/RD处理
         |  '' as rx_temp_sex, -- 性别
         |  '' as rx_temp_age, -- 年龄
         |  '' as rx_temp_education, -- 学历
         |  '' as rx_temp_rsjd, -- 人生阶段
         |  '' as rx_temp_is_self, -- 是否是机主本人
         |  '' as rx_temp_call_phone, -- 来电号码
         |  '' as rx_temp_call_cnt, -- 来电次数
         |  '' as rx_temp_one_level, -- 一级
         |  '' as rx_temp_two_level, -- 二级
         |  '' as rx_temp_three_level, -- 三级
         |  '' as rx_customer_username, -- 用户名
         |  '' as rx_customer_account_name, -- 账号名称
         |  '' as rx_customer_web_url, -- 网址
         |  '' as rx_customer_company_name, -- 公司名称
         |  '' as rx_customer_unit, -- 账户运营单位
         |  '' as rx_customer_first_people, -- 账户首要联系人
         |  '' as rx_customer_annotaions, -- 账户批注
         |  '' as rx_customer_account_status, -- 账户状态
         |  '' as rx_reply_record, -- 回复记录
         |  '' as rx_operations, -- 操作记录
         |  '' as rx_temp_candidate, -- 候选人
         |  '' as rx_temp_last_person, -- 最后回复人
         |  '' as rx_temp_question_type, -- 问题类型
         |  '' as rx_temp_cross_system, -- 跨系统工单
         |  '' as rx_temp_cross_system_status, -- 跨系统工单状态
         |  '' as rx_temp_operate_unit, -- 运营单位
         |  '' as rx_temp_promotion_username, -- 推广账户用户名/投诉方用户名
         |  '' as rx_temp_area, -- 所属地区
         |  '' as rx_temp_account_industry, -- 账户行业
         |  auto_content_id, -- 智能客服内容id
         |  auto_content_csi, -- 在线智能客服CSI
         |  auto_content_fcr, -- 在线智能客服FCR
         |  auto_satisfaction_label, -- 在线智能评价满意度标签
         |  auto_advise_text, -- 在线智能评价建议文本
         |  artificial__satisfaction_label, -- 在线人工评价满意度标签
         |  artificial_advise_text, -- 在线人工评价建议文本
         |  case when round_num is null then '' else round_num end as round_num, -- 交互轮次
         |  icafe_last_pull_time, -- icafe更新时间
         |  icafe_status, -- icafe状态
         |  icafe_flag, -- 是否创建icafe
         |  is_transfer, -- 是否转人工
         |  upvote_cnt, -- 点赞数
         |  downvote_cnt, -- 点踩数
         |  wen_correct_cnt, -- 文心回答正确数
         |  wen_error_cnt, -- 文心回答错误数
         |  wen_unanswered_cnt, -- 文心未回答数
         |  wen_error_detail, -- 文心回答错误消息明细
         |  wen_unanswered_detail, -- 文心未回答消息明细
         |  is_session_monitor, -- 是否触发会话监控
         |  sessionz_label_values, -- 会话监控人工接入情况
         |  hit_cheating_strategy, -- 作弊策略
         |  user_agent, -- UserAgent
         |  case
         |    when cuid_1 != '' then cuid_1
         |    else (case when cuid_2 != '' then cuid_2 else cuid_3 end)
         |  end as cuid, -- cuid
         |  channel_id, -- channel_id
         |  channel_id_map -- 活动来源
         |from combined_data
         |""".stripMargin

    spark.sql(sql)

//    // 本地测试
//    val frame = spark.sql(sql)
//    frame.show(10,false)
//    frame.repartition(1).write.option("header", "true").mode(SaveMode.Overwrite).csv("D:/tem_data/im")

    spark.close()
  }

  /**
   * 获取智能客服内容id
   */
  val getAutoContentId = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ListBuffer[String]()
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val extra = jsonObject.getString("extra")
        if (extra != null && extra != "") {
          val extraObject = JSON.parseObject(extra)
          val answerIdStr: String = extraObject.getString("answerId")
          if (answerIdStr != null && answerIdStr != "") {
            //            res = res + answerIdStr + ","
            res.+=(answerIdStr)
          }
        }
      })
      res.mkString(",")
    }
  })

  /**
   * 获取智能客服 CSI  FCR
   */
  val getAutoContentRobot = functions.udf((data: String,types:String) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      var res = ""
      val jsonObject = JSON.parseObject(data)
      for (key <- jsonObject.keySet().toArray()){
        val sessionjSON = jsonObject.getJSONObject(key.toString)
        val evaluateResult = sessionjSON.getString("evaluateResult")
        //判断level==SESSION_LEVEL，evaluated为true，evaluateResult不为空
        if (sessionjSON.getString("level") == "SESSION_LEVEL" && sessionjSON.getBoolean("evaluated") == true && evaluateResult != null && evaluateResult != ""){
          val resultJson = JSON.parseObject(evaluateResult)
          if(resultJson.getBoolean("robot") == true){
            if (types.equals("CSI")){
              res = resultJson.getString("robot_eva_stat") match {
                case "1" => "非常不满意"
                case "2" => "不满意"
                case "3" => "一般"
                case "4" => "满意"
                case "5" => "非常满意"
              }
            }else if (types.equals("FCR")){
              res = resultJson.getString("robot_eva_sloved") match {
                case "1" => "解决"
                case "2" => "未解决"
                case "3" => "持续关注"
              }
            }
            else if (types.equals("智能标签")){
              res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
            }
            else if (types.equals("智能文本")){
              res = resultJson.getString("robot_eva_advise")
            }
          }else if (resultJson.getBoolean("robot") == false){
            if (types.equals("人工标签")){
              res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
            }else if (types.equals("人工文本")){
              res = resultJson.getString("robot_eva_advise")
            }
          }
        }
      }
      res
    }
  })

  /**
   * 点赞数、点踩数，文心回答数量等
   * types:传入的类型，普通是获取点赞数和点踩数，文心是获取关于文心的点赞数等等
   * value：json对应的值，11是点赞数，9是点踩数
   */
  val getEvaluateDesc = functions.udf((data: mutable.WrappedArray[String],types:String,value:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      //var res = ListBuffer[String]()
      var res = 0
      if(types.equals("普通")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          val evaluateOn = jsonObject.getString("evaluateOn")
          val evaluateInfo = jsonObject.getString("evaluateInfo")
          if (evaluateOn == "true" && evaluateInfo != null && evaluateInfo != "") {
            val extraObject = JSON.parseObject(evaluateInfo).getString("evaluateResult")
            if(extraObject != null && extraObject != ""){
              val eval = JSON.parseObject(extraObject).getString("evaluation")
              //计算11的数量是点赞数，9的数量是点踩数
              if (eval == value){
                res += 1
              }
            }else{
              ""
            }
          }else{
            ""
          }
        })
      }else if (types.equals("文心")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          val ouserEvaluateInfo = jsonObject.getString("ouserEvaluateInfo")
          if (ouserEvaluateInfo != null && ouserEvaluateInfo != "") {
            val ouserEvaluateRet = JSON.parseObject(ouserEvaluateInfo).getString("ouserEvaluateRet")
            if(ouserEvaluateRet != null && ouserEvaluateRet != ""){
              val desc = JSON.parseObject(ouserEvaluateRet).getString("desc")
              //点赞：文心回答正确数量，点踩：文心回答错误数量，未回答：文心未回答数量，点踩明细：文心回答错误数量
              if (desc == value){
                res += 1
              }
            }else{
              ""
            }
          }else{
            ""
          }
        })
      }
      if (res.toString == "0") "" else res.toString
    }
  })

  /**
   * 文心消息回答明细
   */
  val getEvaluateDescDetail = functions.udf((data: mutable.WrappedArray[String],value:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      //var res = ListBuffer[String]()
      var res = ""
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val ouserEvaluateInfo = jsonObject.getString("ouserEvaluateInfo")
        if (ouserEvaluateInfo != null && ouserEvaluateInfo != "") {
          val ouserEvaluateRet = JSON.parseObject(ouserEvaluateInfo).getString("ouserEvaluateRet")
          if(ouserEvaluateRet != null && ouserEvaluateRet != ""){
            val desc = JSON.parseObject(ouserEvaluateRet).getString("desc")
            //点赞：文心回答正确数量，点踩：文心回答错误数量，未回答：文心未回答数量，点踩明细：文心回答错误数量
            if (desc == value){
              val bailing = jsonObject.getString("bailingRichCard")
              if(bailing == null || !JSON.parseObject(bailing).containsKey("content")) {
                res  = if (jsonObject.getString("text") == null || jsonObject.getString("text") == "") "" else jsonObject.getString("text")
              }else{
                val content = JSON.parseObject(bailing).getJSONObject("content")
                val typestr = content.getString("type")
                if("html".equals(typestr)){
                  val data = content.getString("data")
                  res = data.replaceAll("<.*?>","")
                }else if("card".equals(typestr)){
                  val dataJson = content.getJSONObject("data")
                  if (!dataJson.isEmpty){
                    val cardContents = dataJson.getJSONArray("content")
                    for (o <- 0 to cardContents.size() - 1){
                      val cardcont = cardContents.getJSONObject(o)
                      val cardType = cardcont.getString("type")
                      if (cardType.equals("html") || cardType.equals("text")){
                        val valuestr = cardcont.getString("value")
                        res =  if (valuestr == null || valuestr == "") "" else valuestr.replaceAll("<.*?>","")
                      }else{
                        res = cardcont.toString()
                      }
                    }
                  }
                }
              }
            }
          }else{
            res = ""
          }
        }else{
          res = ""
        }
      })
      res
    }
  })

  /**
   * 空值判断
   */
  val getNullVal = functions.udf((data: String) => {
    if (data == null || data.isEmpty || data == "，，" || data == "--") {
      ""
    } else {
      data
    }
  })

  /**
   * 获取产品问题类型
   */
  val getProductPro = functions.udf((data: String,index: Int) => {
    if (data == null || data.isEmpty || data == ",," || data == "--") {
      ""
    } else {
      val str = data.replaceAll("\\[","").replaceAll("]","").replaceAll("\"","").split(",").applyOrElse(index, (x: Int) => "")
      str
    }
  })

  /**
   * 会话监控人工接入情况
   */
  val getSessionzLabelValues = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      var res = ""
      data.foreach(row => {
        res = row match {
          case "SESSIONZ_MONITOR_START" => "客服未主动接管"
          case "SESSIONZ_MONITOR_ABANDON" => "放弃监控"
          case "SESSIONZ_MONITOR_OUSER_IN" => "客服主动接管"
          case _ => ""
        }
      })
      res
    }
  })

  /**
   * 获取产品名称
   */
  /*val getProductName = functions.udf((productId: String,appId: String) => {

    if (productId == null || productId.isEmpty || productId == "") {
      ""
    } else {
        productId match {
          case "1" => appId match {
            case "2001087" => "百度APP_Android"
            case "2001088" => "百度APP_IOS"
            case "2001126" => "百度APP_VIP"
            case "2001092" => "百度APP大字版_Android"
            case "2001093" => "百度APP大字版_IOS"
            case "2001094" => "百度APP极速版_Android"
            case "2001095" => "百度APP极速版_IOS"
            case "2001122" => "百度APP_度会员"
          }
          case "3" => appId match {
            case "2001125" =>	"百度阅读Android"
            case "2001127" =>	"百度阅读iOS"
            case "2001072" => "文库PC"
          }
          case "5" => appId match {
            case "2001131" => "贴吧pc端"
            case "2001099" => "贴吧用户反馈"
            case "2001098" => "贴吧用户反馈-IOS"
            case "2001097" => "贴吧用户反馈-安卓"
          }
          case "6" => appId match {
            case "2001030" => "PC线上正式"
            case "2001050" => "百家号-手百渠道"
          }
          case "8" => appId match {
            case "2001084" => "服务闭环商城"
            case "2001109" => "基木鱼开店-本地生活"
          }
          case "11" => appId match {
            case "2001076" => "爱企查-Android"
            case "2001077" => "爱企查-iOS"
            case "2001073" => "爱企查PC端"
          }
          case "12" => appId match {
            case "2001108" => "百度知道-PC"
            case "2001107" => "百度知道-移动端"
            case "2001085" => "好看视频-Android"
            case "2001086" => "好看视频-iOS"
          }
          case "17" => appId match {
            case "2001091" => "任务系统客诉H5"
          }
          case "25" => appId match {
            case "2001089" => "问一问"
            case "2001090" => "问一问PC端"
            case "2001047" => "问一问-手百矩阵"
          }
          case "31" => appId match {
            case "2001039" => "荣耀浏览器"
          }
          case "35" => appId match {
            case "2001071" => "萝卜快跑test"
          }
          case "37" => appId match {
            case "2001082" => "一言PC端"
            case "2001081" => "一言移动端"
          }
          case "41" => appId match {
            case "2001117" => "百度教育"
            case "2001128" => "百度教育Android"
            case "2001112" => "百度教育iOS"
            case "2001132" => "百度教育pc"
            case "2001123" => "百度教育PC端"
          }
        }
    }
  })*/

  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient: RestClientBuilder =
      RestClient.builder(new HttpHost(EsConf.ImHost, EsConf.ImPort, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider
      .setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(EsConf.ImUser, EsConf.ImPassword))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)
  }


  private def getScrollData(startTimeStamp: Long,
                            endTimeStamp: Long,
                            productId: Long): ListBuffer[String] = {
    val list: ListBuffer[String] = ListBuffer[String]()

    val searchRequest: SearchRequest = new SearchRequest(indexName)
    val searchSourceBuilder: SearchSourceBuilder = new SearchSourceBuilder
    val boolQueryBuilder =
        QueryBuilders
          .boolQuery
          .must(QueryBuilders.termQuery("startInfo.bailingInfoOnImSessionLevel.productId", productId))
          .must(QueryBuilders.boolQuery
            .should(QueryBuilders.rangeQuery("obuzFollow.updateTime").gte(startTimeStamp).lt(endTimeStamp))
            .should(QueryBuilders.rangeQuery("startInfo.startTime").gte(startTimeStamp).lt(endTimeStamp))
          )

    searchSourceBuilder.query(boolQueryBuilder)
    //      .fetchSource(Array("obuzFollow.formField_SELECT_baijiahao_type_百家号类型"),null)
    searchRequest.source(searchSourceBuilder)
    searchSourceBuilder.size(500)
    searchRequest.source(searchSourceBuilder)
    searchRequest.scroll(TimeValue.timeValueMinutes(5L))
    try {
      var searchResponse: SearchResponse = client.search(searchRequest, RequestOptions.DEFAULT)
      var scrollId: String = searchResponse.getScrollId
      var hits: Array[SearchHit] = searchResponse.getHits.getHits
      hits.foreach(searchHit => {
        list.append(searchHit.getSourceAsString)
      })
      val scroll: Scroll = new Scroll(TimeValue.timeValueMinutes(5L))
      while (hits.length > 0) {
        val scrollRequest: SearchScrollRequest = new SearchScrollRequest(scrollId)
        scrollRequest.scroll(scroll)
        searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT)
        scrollId = searchResponse.getScrollId
        hits = searchResponse.getHits.getHits
        hits.foreach(searchHit => {
          list.append(searchHit.getSourceAsString)
        })
      }
      val clearScrollRequest: ClearScrollRequest = new ClearScrollRequest
      clearScrollRequest.addScrollId(scrollId)
      val clearScrollResponse: ClearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT)
      val succeeded: Boolean = clearScrollResponse.isSucceeded
    } catch {
      case e: IOException =>

        // TODO Auto-generated catch block
        e.printStackTrace()
    }finally {

    }
    list
  }

  //从接口获取数据
  def postRequest(productId: String, imSessionIds: Array[String]): String = {
    val url = "http://follow.baidu-int.com/data/follow-common-api/querySessionzFiledList"
    //请求体拼接
    val body = new JSONObject
    body.put("productId", productId)
    body.put("imSessionIds", imSessionIds)
    //口令获取
    var response: Response = null
    val APP_KEY = "dashboard_udw_sync_data_interface"
    val APP_TOKEN = "d55cc05f-a8a0-4b8a-85f8-8c02b67ffaf8"
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val localDateTime = LocalDateTime
      .ofInstant(Instant.ofEpochMilli(System.currentTimeMillis), ZoneId.systemDefault)
    val createTime = localDateTime.format(formatter)
    val sign = DigestUtils.md5Hex(APP_KEY + APP_TOKEN + createTime)

    val requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON
      .toString), body.toString())
    val okHttpClient = new OkHttpClient
    val request = new Request.Builder().url(url).addHeader("appKey", APP_KEY).addHeader("createTime", s"""$createTime""").addHeader("sign", s"""$sign""").post(requestBody).build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

}
