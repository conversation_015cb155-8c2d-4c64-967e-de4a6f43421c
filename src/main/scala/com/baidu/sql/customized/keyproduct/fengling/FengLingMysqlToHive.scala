package com.baidu.sql.customized.keyproduct.fengling

import com.baidu.sql.utils.UdfUtils._
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}

import scala.collection.mutable.ListBuffer


/**
 * <AUTHOR>
 */
object FengLingMysqlToHive {

  var YesterDay: String = ""

  /*
  * 风铃mysql的监管外溢表数据，通过电话号码关联出userid后，同步至udw
  * 读取的Mysql数据库是客服新建的mysql，版本比较高，所以打包时需要修改mysql连接版本和配置
  * pom.xml中 : mysql-connector-java版本：8.0.31
  * mysql连接配置MysqlConf中：Driver = "com.mysql.cj.jdbc.Driver"
  * */
  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)

    val sparkConf = new SparkConf().setAppName("FengLingMysqlToHive")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", "true")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val mysqlDf = getMysqlData(spark,YesterDay)
    println("获取mysql的数据为："+mysqlDf.count())
    mysqlDf.show(5,false)
    //mysqlDf.createOrReplaceTempView("combined_data")

    val resDf = getHiveData(spark,mysqlDf)
    println("获取hive聚合后的数据为："+resDf.count())
    resDf.filter(col("ip") =!= "").show(5,false)
    resDf.createOrReplaceTempView("combined_data")

    spark.sql(s"""
                 |insert into table udw_ns.default.help_ods_fengling_sentiment PARTITION (event_day)
                 |select
                 |    auto_id,
                 |    create_time_data,
                 |    update_time_data,
                 |    uid,
                 |    contact_number,
                 |    submit_time,
                 |    product_line_name,
                 |    question_type,
                 |    case_id,
                 |    business_attributes,
                 |    channel_management,
                 |    complaint_platform,
                 |    resolved_or_not,
                 |    ip,
                 |    ip_province,
                 |    cuid,
                 |    mobile_model,
                 |    mobile_brand,
                 |    event_day
                 |from combined_data
        """.stripMargin)

    spark.close()
  }

  
  /**
   * 获取mysql数据
   * @param saprk
   * @param queryDate
   * @param querySql
   * @return
   */
  def getMysqlData(spark: SparkSession,queryDate:String): DataFrame = {
    import spark.implicits._

    val querySql =
      s"""
         |SELECT
         |	auto_id,
         |	create_time_data,
         |  update_time_data,
         |  contact_number,
         |  submit_time,
         |  product_line_name,
         |  question_type,
         |  case_id,
         |  business_attributes,
         |  channel_management,
         |  complaint_platform,
         |  resolved_or_not
         |from sentiment
         |where DATE_FORMAT(create_time_data,'%Y%m%d') = '${queryDate}'
         |""".stripMargin

    val caseTransDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlKeFuProperties,querySql)
      .withColumn("event_day",date_format(col("submit_time"),"yyyyMMdd").cast("string"))
      .withColumn("uid", getUserId(col("contact_number")))

    //将userid进行关联
    val resDf = caseTransDf
      .select(
        $"auto_id",
        $"create_time_data",
        $"update_time_data",
        $"uid",
        $"contact_number",
        $"submit_time",
        $"product_line_name",
        $"question_type",
        $"case_id",
        $"business_attributes",
        $"channel_management",
        $"complaint_platform",
        $"resolved_or_not",
        $"event_day"
      )
      .na.fill("")
      .repartition(20)
      .cache()
    resDf.count()
    resDf
  }

  /**
   * 获取hive数据
   * @param saprk
   * @param queryDate
   * @param querySql
   * @return
   */
  def getHiveData(spark: SparkSession,mysqlDf:DataFrame): DataFrame = {
    import spark.implicits._

    val dateList = mysqlDf.select(col("event_day"))
      .distinct()
      .rdd
      .map(row => row.getAs[String](0))
      .collect()

    val zaixianResDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
    val chizhiResDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    for (date <- dateList) {
      println("date:"+date)
      val zaixianSql =
        s"""
           |SELECT
           |	ip,
           |  ip_province,
           |  cuid,
           |  userid,
           |  event_day
           |from udw_ns.default.help_ods_ufo_key_point_productline_di
           |where event_day = '${date}'
           |and userid != ''
           |and channel = '在线'
           |""".stripMargin

      //获取迟滞数据
      val chizhiSql =
        s"""
           |SELECT
           |	mobile_model,
           |  mobile_brand,
           |  userid,
           |  event_day
           |from udw_ns.default.help_ods_ufo_key_point_productline_di
           |where event_day >= '${date}'
           |and userid != ''
           |and channel = '迟滞'
           |""".stripMargin

      //在线数据
      val zxDf = spark.sql(zaixianSql)
        .repartition(20)
      zaixianResDf.append(zxDf)

      //迟滞数据
      val czDf = spark.sql(chizhiSql)
        .repartition(20)
      chizhiResDf.append(czDf)
    }

    //在线数据聚合
    val zaixainDf: DataFrame = zaixianResDf
      .reduce((df1, df2) => df1.union(df2))
      .repartition(20)
      .dropDuplicates("userid","event_day")

    //迟滞数据聚合
    val chizhiDf: DataFrame = chizhiResDf
      .reduce((df1, df2) => df1.union(df2))
      .repartition(20)
      .dropDuplicates("userid","event_day")

    zaixainDf.count()
    chizhiDf.count()

    //将userid进行关联
    val resDf = mysqlDf.as("C")
      .join(zaixainDf.as("Z"), $"C.uid" === $"Z.userid" and $"C.event_day" === $"Z.event_day", "left_outer")
      .join(chizhiDf.as("D"), $"C.uid" === $"D.userid" and $"C.event_day" === $"D.event_day", "left_outer")
      .select(
        $"C.auto_id",
        $"C.create_time_data",
        $"C.update_time_data",
        $"C.uid",
        $"C.contact_number",
        $"C.submit_time",
        $"C.product_line_name",
        $"C.question_type",
        $"C.case_id",
        $"C.business_attributes",
        $"C.channel_management",
        $"C.complaint_platform",
        $"C.resolved_or_not",
        $"Z.ip",
        $"Z.ip_province",
        $"Z.cuid",
        $"D.mobile_model",
        $"D.mobile_brand",
        $"C.event_day"
      )
      .na.fill("")
      .repartition(20)
      .cache()

    resDf
  }
 }
