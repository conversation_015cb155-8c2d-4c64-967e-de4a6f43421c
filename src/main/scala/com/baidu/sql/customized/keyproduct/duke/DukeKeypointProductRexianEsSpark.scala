package com.baidu.sql.customized.keyproduct.duke

import com.alibaba.fastjson.JSON
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import org.apache.spark.SparkConf
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}
import java.util.Properties
import scala.collection.mutable
import scala.collection.mutable.ListBuffer


object DukeKeypointProductRexianEsSpark {
  val indexName = "work_order_info"
  // 需要查询的产品线,robot_id代表在IVR总明细中有的业务线数据，key有值的话代表在ES工单页面中有此业务线的数据
  val productMap = Map(
    "7" -> Map("title" -> "商业广告热线", "robot_name" -> "大搜商业推广,转商业咨询,转商业投诉,信誉认证,推广政策","robot_id" -> "6403572500,0179434049,2079030237,2613806794,3473219828"),
    "21" -> Map("title" -> "百度热线","robot_name" -> "百度热线","robot_id" -> "0460155362,8466104144"),
    "29" -> Map("title" -> "百度保障","robot_name" -> "百度保障","robot_id" -> ""),
    "" -> Map("title" -> "百度游戏,好看视频,举报热线","robot_name" -> "百度游戏,好看视频,举报热线","robot_id" -> "1612133276,0769909154,4807542845"),
    "5" -> Map("title" -> "百度文库", "robot_name" -> "百度文库","robot_id" -> "1801319027"),
    "25" -> Map("title" -> "收银台", "robot_name" -> "糯米客服","robot_id" -> "8275551621"),
    "58" -> Map("title" -> "萝卜快跑","robot_name" -> "萝卜快跑","robot_id" -> "6171275680"),
    "40" -> Map("title" -> "百度DU会员","robot_name" -> "百度DU会员","robot_id" -> "3995123514"),
    "28" -> Map("title" -> "网信办","robot_name" -> "网信办","robot_id" -> "7358766871"),
    "34" -> Map("title" -> "暖阳热线","robot_name" -> "暖阳热线","robot_id" -> "4164946039"),
    "37" -> Map("title" -> "青少年热线","robot_name" -> "青少年热线","robot_id" -> "3327558151"),
    "66" -> Map("title" -> "百度教育","robot_name" -> "百度教育","robot_id" -> "4172082553"),
    "62" -> Map("title" -> "文心一言","robot_name" -> "文心一言","robot_id" -> "9815695832"),
    "65" -> Map("title" -> "百度账号"),
    "67" -> Map("title" -> "爱企查","robot_name" -> "爱企查","robot_id" -> "4258142818"),
    "69" -> Map("title" -> "上海12345绿通","robot_name" -> "糯米8218","robot_id" -> "2982365421")
  )

  //匹配Es工单页面产品线与IVR页面产品线
  val productsubMap = Map(
    "大搜商业推广" -> "商业广告热线",
    "转商业咨询" -> "商业广告热线",
    "转商业投诉" -> "商业广告热线",
    "信誉认证" -> "商业广告热线",
    "推广政策" -> "商业广告热线",
    "95099" -> "百度热线",
    "糯米客服" -> "收银台",
    "糯米8218" -> "上海12345绿通"
  )

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)

    //sparkSession配置
    val sparkConf = new SparkConf().setAppName("ReadEs")
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields","1000")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val sc = spark.sparkContext
    sc.setLogLevel("WARN")

    import spark.implicits._

    // 获取YesterDay豪秒级时间戳
    val startTimeStamp = getTimeStamp(YesterDay)
    val endTimeStamp = getTimeStamp(calcnDate(YesterDay,1))

    //将所有product_id获取出来
    val productIdStr: String = productMap.keys.filter(x => x != "").mkString(",")
    // 提取"robot_id"的值到列表
    val robotId = productMap.values.flatMap { subMap =>
      val robot_id = subMap.getOrElse("robot_id","")
      if (robot_id.contains(",")) robot_id.split(",") else List(robot_id)
    }.toArray.filter(x => x != "")

    println("robotId:" + robotId.mkString(","))

    // 读取数据库问题分类数据
    /*val commonSql =
      """
        |select
        |	id,
        |	parent_id,
        |	`name`
        |from common_select_data
        |""".stripMargin

    val commonDF = getClassifyData(spark,PropertiesUtils.MysqlHyccaCustomerProperties,commonSql)*/

    //获取爱企查和百度教育的问题分类目录
    //val aqcClassifyDf = getClassifyData(spark)
    //420 百度教育  421 百度账号。424 爱企查问题分类
    val otherClassifySql =
      """
        |select
        | id,
        |	parent_id,
        |	`name`,
        | archive_id
        |from pub_archive_item
        |where archive_id in (
        |select id from pub_archive where name like '%问题分类%'
        |)
        |""".stripMargin

    /*CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,commonSql)
      .createOrReplaceTempView("temp_common")*/

    //百度教育，百度账号，爱企查问题分类
    val totalDf = getClassifyData(spark,PropertiesUtils.MysqlXinHuiShengProperties,otherClassifySql)
    //将两部分问题分类数据整合起来
    //val totalDf = commonDF.unionByName(otherDF)

    // 获取所有的字段映射,value_id是三级类别的编号,value_desc是中文描述
    val levelSql =
      """
        |select
        |	value_id,
        |	value_desc
        |from baidu_emun_value
        |union all
        |select
        |	concat('lb_', value_id) as value_id,
        |	value_desc
        |from bdidg_emun_value
        |""".stripMargin

    // 三级等级信息
    val levelDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,levelSql)
    levelDf.createOrReplaceTempView("temp_enum")

    // 获取所有的字段映射,lable是中文字段名，name是es数据库里的字段名
    val fieldSql =
      s"""
         |SELECT
         | label,
         | `name`,
         | option_value,
         | template_id
         |FROM customer_field
         |""".stripMargin

    val fieldDF = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,fieldSql)

    //baidu登陆的IVR数据
    val ivrBaiduDf = getIvrDetail(spark,"baidu",YesterDay)
    println(s"读取ivrBaiduIVR数据为:${ivrBaiduDf.count()}条")

    //bdidg登陆的IVR数据
    val ivrBdidgDf = getIvrDetail(spark,"bdidg",YesterDay)
    println(s"读取ivrBdidgIVR数据为:${ivrBdidgDf.count()}条")

    //合并ivr明细数据
    val ivrDf = ivrBaiduDf.unionByName(ivrBdidgDf)

    //读取新版历史呼入明细页面字段数据:baidu
    val processBaiduDf = getCallInDetail(spark,"baidu",YesterDay)
    println(s"processBaiduDf数据量为${processBaiduDf.count()}条")

    //读取新版历史呼入明细页面字段数据:bdidg
    val processBdidgDf = getCallInDetail(spark,"bdidg",YesterDay)
    println(s"processBdidgDf数据量为${processBdidgDf.count()}条")

    //合并新版历史呼入明细数据
    val processDf = processBaiduDf.unionByName(processBdidgDf)

    //开始关联IVR数据
    val ivrTotalDf = ivrDf.filter($"robot_id".isin(robotId:_*)).as("I")
      .join(processDf.as("C"),$"I.session_id" === $"C.session_id","left_outer")
      .select(
        $"I.session_id",
        $"I.robot_name" as "rx_robot_name",
        $"I.remote_url" as "rx_robot_number",
        $"I.phone_num" as "rx_phone_num",
        when($"C.process_instance_id".isNull,"").otherwise($"C.process_instance_id") as "process_instance_id",
        when($"C.session_id_hex".isNull,"").otherwise($"C.session_id_hex") as "session_id_hex",
        when($"I.turn_user_flag".isNull,"").otherwise($"I.turn_user_flag") as "rx_turn_user_flag",
        when($"I.hangup_type".isNull,"").otherwise($"I.hangup_type") as "rx_hangup_type",
        when($"I.meet_num".isNull,"").otherwise($"I.meet_num") as "rx_meet_num",
        when($"I.start_time".isNull,"").otherwise($"I.start_time") as "rx_start_time",
        when($"I.end_time".isNull,"").otherwise($"I.end_time") as "rx_end_time",
        when($"I.duration".isNull,"").otherwise($"I.duration") as "rx_duration",
        when($"I.process_tag".isNull,"").otherwise($"I.process_tag") as "rx_process_tag",
        when($"C.skill_group_name".isNull,"").otherwise($"C.skill_group_name") as "rx_skill_group_name",
        when($"C.queue_duration".isNull,"").otherwise($"C.queue_duration") as "rx_queue_duration",
        when($"C.ring_duration".isNull,"").otherwise($"C.ring_duration") as "rx_ring_duration",
        when($"C.talk_duration".isNull,"").otherwise($"C.talk_duration") as "rx_talk_duration",
        when($"C.end_type_name".isNull,"").otherwise($"C.end_type_name") as "rx_end_type_name",
        when($"C.keypress_name".isNull,"").otherwise($"C.keypress_name") as "rx_keypress_name",
        when($"C.is_repeat".isNull,"").otherwise($"C.is_repeat") as "rx_is_repeat",
        when($"C.resource_id".isNull,"").otherwise($"C.resource_id") as "rx_resource_id",
        when($"C.resource_name".isNull,"").otherwise($"C.resource_name") as "rx_resource_name",
        //when($"C.is_upgrade".isNull,"").otherwise($"C.is_upgrade") as "rx_is_upgrade",
        when($"C.start_time".isNull,"").otherwise($"C.start_time") as "rx_call_in_start_time",
        when($"C.end_time".isNull,"").otherwise($"C.end_time") as "rx_call_in_end_time"
    )
      .withColumn("product_line",productTrans($"rx_robot_name"))
      .repartition(2)

    ivrTotalDf.createOrReplaceTempView("ivr_detail_data")
    println(s"ivrTotalDf总数量为:${ivrTotalDf.count()}条")

    // 查询template表，获取group_id 产品线, id-模板id
    val templateSql =
      s"""
         |SELECT
         |  ct.id as id,
         |  ct.group_id as group_id
         |FROM hycca_customer.customer_template ct
         |where ct.group_id in ($productIdStr)
         |""".stripMargin

    val templateDF= CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,templateSql)
    val templateList = templateDF.collect()
    val resultDfList: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    // 遍历集合
    for (row <- templateList) {
      val id = row.getAs[Long]("id")
      val groupId = row.getAs[Long]("group_id")
      val product: Map[String, String] = productMap.getOrElse(groupId.toString, Map())
      val title: String = product.getOrElse("title", "")

      // map收集label和name，后续在es中查询对应数据
      val map = mutable.Map[String, String]()
      fieldDF.where(s"template_id = ${id}").collect().foreach(field => {
        val label: String = field.getAs("label")
        val name: String = field.getAs("name")
        val option_value: String = field.getAs("option_value")
        // 特殊处理，收银台的一级二级三级取收银台问题分类的映射
        if (title.equals("收银台") && (label.equals("一级") || label.equals("二级") || label.equals("三级"))) {
          if (option_value.contains("收银台问题分类")) {
            map.put(label, name)
          }
        } else {
          map.put(label, name)
        }
      })
      //来电号码的key枚举,每个产品线的电话号码字段名称不同，做特殊处理
      //val templatePhone = if (map.contains("来电号码")) "来电号码" else "联系电话"
      val templatePhone = if (map.contains("来电号码")){
        "来电号码"
      }else if (map.contains("联系电话")){
        "联系电话"
      }else{
        "联系方式"
      }

      // 根据group_id, id 查询时间范围的es数据
      val rdd = getEsData(spark, startTimeStamp, endTimeStamp, id.toString, groupId.toString)
      // 定义模式（即Schema），这里只有一个名为"data"的字符串字段
      val schema: StructType = new StructType().add("data", DataTypes.StringType)
      // 使用SparkSession创建DataFrame
      val df: DataFrame = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)

      // 通过字段 map 获取 es 数据,并解析字段
      var esFieldsDF = df
        // 编号
        .withColumn("process_instance_id", getNullVal(get_json_object(col("data"), "$.processInstanceId").cast("String")))
        // 关联id
        .withColumn("relation_id", getNullVal(get_json_object(col("data"), "$.relationId").cast("String")))
        // 创建时间
        .withColumn("create_time", getNullVal(get_json_object(col("data"), "$.createTime").cast(LongType)./(1000).cast(TimestampType)))
        // 修改时间
        .withColumn("update_time", getNullVal(get_json_object(col("data"), "$.modifiedTime").cast(LongType)./(1000).cast(TimestampType)))
        // 关单时间
        .withColumn("close_time", getNullVal(get_json_object(col("data"), "$.closeTime").cast(LongType)./(1000).cast(TimestampType)))
        // 主题
        .withColumn("subject", getNullVal(get_json_object(col("data"), "$.subject").cast("String")))
        //问题分类
        .withColumn("rx_temp_question_type", getNullVal(get_json_object(col("data"), "$.processDefinitionName").cast("String")))
        // 流程名称
        .withColumn("process_definition_name", getNullVal(get_json_object(col("data"), "$.processDefinitionName").cast("String")))
        // 工单状态
        .withColumn("global_state", getNullVal(get_json_object(col("data"), "$.globalState").cast("String")))
        // 联络历史创建时间
        .withColumn("temp_create_time", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("创建时间", "")).cast("String")))
        // 联络历史修改时间
        .withColumn("temp_modified_time", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("修改时间", "")).cast("String")))
        // 联络历史创建人
        .withColumn("temp_create_user", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("创建人", "")).cast("String")))
        // 修改人
        .withColumn("temp_modified_user", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("修改人", "")).cast("String")))
        // 会话id
        .withColumn("session_id", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("会话id", "")).cast("String")))
        // 客户id
        .withColumn("temp_customer_id", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("客户id", "")).cast("String")))
        // 问题描述
        .withColumn("temp_question_description", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("问题描述", "")).cast("String")))
        // 标题
        .withColumn("temp_title", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("标题", "")).cast("String")))
        // 数据来源 json解析
        .withColumn("temp_data_source", getNullVal(get_json_object(get_json_object(col("data"), "$.templateData." + map.getOrElse("数据来源", "")), "$.name").cast("String")))
        // 百度pass账号uid
        .withColumn("temp_pass_uid", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("百度pass账号uid", "")).cast("String")))
        // 百度账号
        .withColumn("temp_account", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("百度账号", "")).cast("String")))
        // 百度pass账号
        .withColumn("temp_pass_account", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("百度pass账号", "")).cast("String")))
        // 是否涉及退款
        .withColumn("temp_is_refund", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("是否涉及退款", "")).cast("String")))
        // 退款订单号
        .withColumn("temp_refund_order_id", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("退款订单号", "")).cast("String")))
        // 风险类型
        .withColumn("temp_risk_type", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("风险类型", "")).cast("String")))
        // 问题分类
        // 一级产品问题类型
        .withColumn("product_type_id", getNullVal(get_json_object(get_json_object(col("data"), "$.templateData." + map.getOrElse("问题分类", "")), "$.id")))
        //三级产品问题类型名称
        .withColumn("product_type_name", getNullVal(get_json_object(get_json_object(col("data"), "$.templateData." + map.getOrElse("问题分类", "")), "$.name")))
        // 运营/PM/RD处理
        .withColumn("temp_handle_process", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("运营/PM/RD处理", "")).cast("String")))
        // 事件类型
        .withColumn("temp_event_class", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("事件类型", "")).cast("String")))
        // 性别
        .withColumn("temp_sex", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("性别", "")).cast("String")))
        // 年龄
        .withColumn("temp_age", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("年龄", "")).cast("String")))
        // 学历
        .withColumn("temp_education", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("学历", "")).cast("String")))
        // 人生阶段
        .withColumn("temp_rsjd", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("人生阶段", "")).cast("String")))
        // 是否是机主本人
        .withColumn("temp_is_self", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("是否是机主本人", "")).cast("String")))
        // 来电号码
        .withColumn("temp_call_phone", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse(templatePhone, "")).cast("String")))
        // 来电次数
        .withColumn("temp_call_cnt", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("来电次数", "")).cast("String")))
        // 用户名
        .withColumn("rx_customer_username",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("用户名")))
        // 账号名称
        .withColumn("rx_customer_account_name",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账号名称")))
        // 网址
        .withColumn("rx_customer_web_url",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("网址")))
        // 公司名称
        .withColumn("rx_customer_company_name",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("公司名称")))
        // 账户运营单位
        .withColumn("rx_customer_unit",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户运营单位")))
        // 账户首要联系人
        .withColumn("rx_customer_first_people",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户首要联系人")))
        // 账户批注
        .withColumn("rx_customer_annotaions",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户批注")))
        // 账户状态
        .withColumn("rx_customer_account_status",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户状态")))
        // --记录
        // 回复记录 json解析拼接
        .withColumn("reply_record", formatReply(from_json(get_json_object(col("data"), "$.commentList"), ArrayType(StringType))))
        // 操作记录  json数组解析拼接
        .withColumn("operations", formatOperation(from_json(get_json_object(col("data"), "$.operations"), ArrayType(StringType))))
        // 筛选项
        // 候选人
        .withColumn("temp_candidate", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("候选人", "")).cast("String")))
        // 最后回复人
        .withColumn("temp_last_person", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("最后回复人", "")).cast("String")))
        // 运营单位
        .withColumn("temp_operate_unit", getNullVal(get_json_object(get_json_object(col("data"), "$.templateData." + map.getOrElse("运营单位", "")), "$.name").cast("String")))
        // 推广账户用户名/投诉方用户名
        .withColumn("temp_promotion_username", getNullVal(get_json_object(get_json_object(col("data"), "$.templateData." + map.getOrElse("推广账户用户名/投诉方用户名", "")), "$.name").cast("String")))
        // 所属地区
        .withColumn("temp_area", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("所属地区", "")).cast("String")))
        // 账户行业
        .withColumn("temp_account_industry", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("账户行业", "")).cast("String")))
        // 产品线
        .withColumn("product_line", lit(title).cast("String"))
        //option_value
        .withColumn("template_id", lit(id).cast("String"))
        //用户认可结果
        .withColumn("rx_user_approval_result", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("用户认可结果", "")).cast("String")))
        //风险等级
        .withColumn("rx_risk_level", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("风险等级", "")).cast("String")))
        //人群类型
        .withColumn("rx_population_type", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("人群类型", "")).cast("String")))
        //重复性诉求
        .withColumn("rx_repetitive_appeal", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("重复性诉求", "")).cast("String")))
        //及时性
        .withColumn("rx_timeliness", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("及时性", "")).cast("String")))
        //身份属性
        .withColumn("rx_identity_attribute", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("身份属性", "")).cast("String")))
        //诉求属性
        .withColumn("rx_appeal_attribute", getNullVal(get_json_object(col("data"), "$.templateData." + map.getOrElse("诉求属性", "")).cast("String")))
        //一级
        .withColumn("temp_one_level",
          get_json_object(col("data"), "$.templateData." + map.getOrElse("一级", "")).cast("String"))
        // 二级
        .withColumn("temp_two_level",
          get_json_object(col("data"), "$.templateData." + map.getOrElse("二级", "")).cast("String"))
        // 三级
        .withColumn("temp_three_level",
          get_json_object(col("data"), "$.templateData." + map.getOrElse("三级", "")).cast("String"))

      // 针对58萝卜快跑产品线，将一级、二级、三级分类字段拼接前缀lb_，获取的bdidg_emun_value里面的问题分类数据，不是baidu_emun_value
      if (groupId.toString.equals("58")) {
        esFieldsDF = esFieldsDF
          .withColumn("temp_one_level", when(col("temp_one_level").isNotNull, concat(lit("lb_"), col("temp_one_level"))).otherwise(""))
          .withColumn("temp_two_level", when(col("temp_two_level").isNotNull, concat(lit("lb_"), col("temp_two_level"))).otherwise(""))
          .withColumn("temp_three_level", when(col("temp_three_level").isNotNull, concat(lit("lb_"), col("temp_three_level"))).otherwise(""))
      }
      resultDfList.append(esFieldsDF)
    }

    //将所有产品线的数据进行合并
    val combinedDF: DataFrame = resultDfList.reduce((df1, df2) => df1.union(df2))
      //电话号码获取用户id
      .withColumn("userid", getUserId(col("temp_call_phone")))
      .repartition(2)
    println("combinedDF数据量：" + combinedDF.count())

    // 关联合并数据，userid和五级分类数据
    val resDf = combinedDF
      // 拉取用于接口获取五级分类数据的字段
      .join(totalDf,
          (combinedDF("product_type_id") === totalDf("a_id")) ||
          (combinedDF("product_type_id") === totalDf("b_id")) ||
          (combinedDF("product_type_id") === totalDf("c_id")) ||
          (combinedDF("product_type_id") === totalDf("d_id")) ||
          (combinedDF("product_type_id") === totalDf("e_id")) , "left")
      .na.fill("")
      .repartition(10)

    println("resDf数据量：" + resDf.count())
    resDf.createOrReplaceTempView("temp_sink_data")

    val sinkDf = spark.sql(
      s"""
         |select
         |  i.product_line, --重点业务
         |  '' as work_type, --类型
         |  '' as channel_platform_space, --迟滞渠道-平台-空间
         |  '' as space_name, --产品线名称
         |  '' as space_id, --产品线ID
         |  '' as old_spce_id, --原产品线ID
         |  '' as id, --序号
         |  case when temp_data_source is null then '' else temp_data_source end as extend_feedback_channel, --反馈来源
         |  '' as need_manual, --转人工
         |  case when product_type is null
         |    then ''
         |    else product_type
         |  end as product_type, --一级产品问题类型
         |  case when function_type is null
         |    then ''
         |    else function_type
         |  end as function_type, --二级产品问题类型
         |  case when function_detail_type is null
         |    then ''
         |    else function_detail_type
         |  end as function_detail_type, --三级产品问题类型
         |  case when customlv4 is null
         |    then ''
         |    else customlv4
         |  end as customlv4, --四级产品问题类型
         |  case when customlv5 is null
         |    then ''
         |    else customlv5
         |  end as customlv5, --五级产品问题类型
         |  '' as complaint_type, --投诉问题类型
         |  '' as complaint_deal_result, --投诉处理结果
         |  '' as complaint_is_result, --用户是否投诉
         |  '' as robot_eva_stat, --智能客服CSI
         |  '' as robot_eva_sloved, --智能客服FCR
         |  '' as robot_eva_advise, --智能客服建议
         |  '' as robot_eva_stat_label, --智能CSI标签
         |  '' as intp_digital_man, --数字人
         |  '' as is_pipe, --是否触发流水线
         |  '' as robot_eva_diss_content, --用户不满意弹窗回答
         |  '' as mobile_app_name, --App包含名
         |  '' as evaluation_count, --用户评价
         |  '' as invite_evaluation_type, --内容邀评类型
         |  '' as evaluation_filter, --内容评价结果
         |  '' as invite_evaluation_filter, --内容是否邀评
         |  '' as discontent_reason_filter, --内容不满原因
         |  '' as discontent_labels_filter, --内容不满标签
         |  '' as audit_pass, --是否通过审核
         |  '' as audit_reason, --未通过原因
         |  '' as violence_score, --暴恐违禁分数
         |  '' as salacity_score, --文本色情分数
         |  '' as politics_score, --政治敏感分数
         |  '' as robot_reply, --机器人回复
         |  '' as first_reply_length_second, --首次回复时长
         |  '' as pipe_reply, --流水线回复
         |  '' as manual_reply, --人工回复
         |  case when userid = '0' or userid is null then '' else userid end as userid, --用户id
         |  '' as ip, --IP地域
         |  '' as ip_province, --省份
         |  '' as mobile_model, --手机型号
         |  '' as mobile_brand, --手机品牌
         |  '' as update_user, --更新人
         |  case when global_state is null then '' else global_state end as status, --反馈处理状态 --工单状态
         |  '' as valid, --反馈处理状态1 有效2 无效3 已转出
         |  case when temp_event_class is null then '' else temp_event_class end as feedback_type, --反馈类型,事件类型
         |  case when create_time is null then '' else create_time end as submit_time, --提交时间
         |  '' as icafe_filter, --是否流转icafe 1 是 0 否
         |  '' as from_product_line, --转入产品线
         |  '' as pre_product_info, --流转来源
         |  '' as risk_level, --风险等级
         |  '' as to_product_line, --转出到
         |  "热线" as channel, --渠道
         |  i.session_id as session_id, --会话id
         |  '' as user_name, --   用户名
         |  '' as baijiahao_user_id, --   百家号ID
         |  '' as baijiahao_name, --    百家号名称
         |  '' as baijiahao_type, --    百家号类型
         |  case when temp_question_description is null then ''
         |  else
         |  regexp_replace(
         |    regexp_replace(temp_question_description, '\\n', ' '),
         |     '\\r', ''
         |  ) end as session_content, --反馈内容、 热线-问题描述
         |  '' as quesiton_desc, --问题描述
         |  '' as remark, --备注
         |  '' as user_evaluate, --用户评价
         |  '' as evaluate_csi, --人工客服CSI
         |  '' as is_solve, --解决状态
         |  '' as accept_group, --客服组
         |  '' as is_change, --是否转接
         |  case when update_time is null then '' else update_time end as update_time, --    更新时间
         |  '' as custom_label, --自定义标签
         |  '' as scene_type, --场景类型
         |  '' as close_type, --结束类型
         |  '' as im_session_id, --会话标识
         |  '' as prod_type_list, --问题类型
         |  '' as ernie_bot_flag, --是否文心一言回复
         |  '' as sessionz_time, --会话时长（秒）
         |  '' as first_handle_time, --首次响应时长（秒）
         |  '' as avg_handle_time, --平均响应时长（秒）
         |  '' as not_accept, --接入失败
         |  '' as update_user_list, --更新人
         |  '' as im_session_status, --是否关闭
         |  '' as sys_info, --系统信息
         |  case when temp_risk_type is null then '' else temp_risk_type end as risk_type, --风险类型
         |  '' as good_evaluation, --好评次数
         |  '' as bad_evaluation, --差评次数
         |  '' as no_evaluation, --未评价次数
         |  case when i.process_instance_id is null then '' else i.process_instance_id end as rx_process_instance_id, --热线编号
         |  case when relation_id is null then '' else relation_id end as rx_relation_id, --关联id
         |  case when close_time is null then '' else close_time end as rx_close_time, --关单时间
         |  case when subject is null then '' else subject end as rx_subject, --主题
         |  case when process_definition_name is null then '' else process_definition_name end as rx_process_definition_name, --流程名称
         |  case when temp_create_time is null then '' else temp_create_time end as rx_temp_create_time, --联络历史创建时间
         |  case when temp_modified_time is null then '' else temp_modified_time end as rx_temp_modified_time, --联络历史修改时间
         |  case when temp_create_user is null then '' else temp_create_user end as rx_temp_create_user, --联络历史创建人
         |  case when temp_modified_user is null then '' else temp_modified_user end as rx_temp_modified_user, --联络历史修改人
         |  case when temp_customer_id is null then '' else temp_customer_id end as rx_temp_customer_id, --客户id
         |  case when temp_title is null then '' else temp_title end as rx_temp_title, --标题
         |  case when temp_is_refund is null then '' else temp_is_refund end as rx_temp_is_refund, --是否涉及退款
         |  case when temp_refund_order_id is null then ''
         |  else
         |  regexp_replace(
         |    regexp_replace(temp_refund_order_id, '\\n', ' '),
         |     '\\r', ''
         |  ) end as rx_temp_refund_order_id, --退款订单号
         |  case when temp_handle_process is null then '' else temp_handle_process end as rx_temp_handle_process, --运营/PM/RD处理
         |  case when temp_sex is null then '' else temp_sex end as rx_temp_sex, --性别
         |  case when temp_age is null then '' else temp_age end as rx_temp_age, --年龄
         |  case when temp_education is null then '' else temp_education end as rx_temp_education, --学历
         |  case when temp_rsjd is null then '' else temp_rsjd end as rx_temp_rsjd, --人生阶段
         |  case when temp_is_self is null then '' else temp_is_self end as rx_temp_is_self, --是否是机主本人
         |  case when temp_call_phone is null then '' else temp_call_phone end as rx_temp_call_phone, --来电号码
         |  case when temp_call_cnt is null then '' else temp_call_cnt end as rx_temp_call_cnt, --来电次数
         |  case when b.value_desc is null then '' else b.value_desc end as rx_temp_one_level, --一级
         |  case when c.value_desc is null then '' else c.value_desc end as rx_temp_two_level, --二级
         |  case when d.value_desc is null then '' else d.value_desc end as rx_temp_three_level, --三级
         |  case when rx_customer_username is null then '' else rx_customer_username end as rx_customer_username, --用户名
         |  case when rx_customer_account_name is null then '' else rx_customer_account_name end as rx_customer_account_name, --账号名称
         |  case when rx_customer_web_url is null then '' else rx_customer_web_url end as rx_customer_web_url, --网址
         |  case when rx_customer_company_name is null then '' else rx_customer_company_name end as rx_customer_company_name, --公司名称
         |  case when rx_customer_unit is null then '' else rx_customer_unit end as rx_customer_unit, --账户运营单位
         |  case when rx_customer_first_people is null then '' else rx_customer_first_people end as rx_customer_first_people, --账户首要联系人
         |  case when rx_customer_annotaions is null then '' else rx_customer_annotaions end as rx_customer_annotaions, --账户批注
         |  case when rx_customer_account_status is null then '' else rx_customer_account_status end as rx_customer_account_status, --账户状态
         |  case when reply_record is null then ''
         |  else
         |  regexp_replace(
         |    regexp_replace(reply_record, '\\n', ' '),
         |    '\\r', ''
         |  ) end as rx_reply_record, --回复记录
         |  case when operations is null then ''
         |  else
         |  regexp_replace(
         |    regexp_replace(operations, '\\n', ' '),
         |    '\\r', ''
         |  ) end as rx_operations,   --操作记录
         |  case when temp_candidate is null then '' else temp_candidate end as rx_temp_candidate, --候选人
         |  case when temp_last_person is null then '' else temp_last_person end as rx_temp_last_person, --最后回复人
         |  rx_temp_question_type, --问题类型
         |  '' as rx_temp_cross_system, --跨系统工单
         |  '' as rx_temp_cross_system_status, --跨系统工单状态
         |  case when temp_operate_unit is null then '' else temp_operate_unit end as rx_temp_operate_unit, --运营单位
         |  case when temp_promotion_username is null then '' else temp_promotion_username end as rx_temp_promotion_username, --推广账户用户名/投诉方用户名
         |  case when temp_area is null then '' else temp_area end as rx_temp_area, --所属地区
         |  case when temp_account_industry is null then '' else temp_account_industry end as rx_temp_account_industry, --账户行业
         |  '' as auto_content_id, --智能客服内容id
         |  '' as auto_content_csi, --在线智能客服CSI
         |  '' as auto_content_fcr, --在线智能客服FCR
         |  '' as auto_satisfaction_label, --在线智能评价满意度标签
         |  '' as auto_advise_text, --在线智能评价建议文本
         |  '' as artificial__satisfaction_label, --在线人工评价满意度标签
         |  '' as artificial_advise_text, --在线人工评价建议文本
         |  '' as round_num, --交互轮次
         |  '' as icafe_last_pull_time, --icafe更新时间
         |  '' as icafe_status, --icafe状态
         |  '' as icafe_flag, --是否创建icafe
         |  '' as is_transfer, --是否转人工
         |  '' as upvote_cnt, --点赞数
         |  '' as downvote_cnt, --点踩数
         |  '' as wen_correct_cnt, --文心回答正确数
         |  '' as wen_error_cnt, --文心回答错误数
         |  '' as wen_unanswered_cnt, --文心未回答数
         |  '' as wen_error_detail, --文心回答错误消息明细
         |  '' as wen_unanswered_detail, --文心未回答消息明细
         |  '' as is_session_monitor, --是否触发会话监控
         |  '' as sessionz_label_values, --会话监控人工接入情况
         |  '' as hit_cheating_strategy, --作弊策略
         |  '' as user_agent, --UserAgent
         |  '' as cuid, --cuid
         |  '' as channel_id, --channel_id
         |  '' as channel_id_map, --活动来源
         |  '' as icafe_current_status, --icafe写入表时状态
         |  case when rx_user_approval_result is null then '' else rx_user_approval_result end as rx_user_approval_result, --用户认可结果
         |  case when rx_risk_level is null then '' else rx_risk_level end as rx_risk_level, --风险等级
         |  case when a.template_id in ('118','119') then
         |    concat(
         |       coalesce(b.value_desc,''),
         |       case when c.value_desc is null then '' else concat('/',c.value_desc) end
         |    )
         |  else '' end as rx_risk_type_classification, --风险类型-含二级分类
         |  case when a.template_id in ('130','131','203','204','385') then
         |    concat(
         |       coalesce(b.value_desc,''),
         |       case when c.value_desc is null then '' else concat('/',c.value_desc) end
         |    )
         |  else '' end as rx_risk_type_classification_new, --风险类型分类（新）
         |  case when rx_population_type is null then '' else rx_population_type end as rx_population_type, --人群类型
         |  case when rx_repetitive_appeal is null then '' else rx_repetitive_appeal end as rx_repetitive_appeal, --重复性诉求
         |  case when rx_timeliness is null then '' else rx_timeliness end as rx_timeliness, --及时性
         |  case when rx_identity_attribute is null then '' else rx_identity_attribute end as rx_identity_attribute, --身份属性
         |  case when rx_appeal_attribute is null then '' else rx_appeal_attribute end as rx_appeal_attribute, --诉求属性
         |  case when rx_turn_user_flag is null then '' else rx_turn_user_flag end as rx_turn_user_flag, --	是否转人工
         |  case when rx_hangup_type is null then '' else rx_hangup_type end as rx_hangup_type, --	挂断类型
         |  case when rx_meet_num is null then '' else rx_meet_num end as rx_meet_num, --	交互次数
         |  case when rx_start_time is null then '' else rx_start_time end as rx_start_time, --	发起时间
         |  case when rx_end_time is null then '' else rx_end_time end as rx_end_time, --	结束时间
         |  case when rx_duration is null then '' else rx_duration end as rx_duration, --	总通话时长
         |  case when rx_process_tag is null then '' else rx_process_tag end as rx_process_tag, --	ivr流程业务标签,
         |  case when rx_skill_group_name is null then '' else rx_skill_group_name end as rx_skill_group_name, --	技能组
         |  case when rx_queue_duration is null then '' else rx_queue_duration end as rx_queue_duration, --	排队时长
         |  case when rx_ring_duration is null then '' else rx_ring_duration end as rx_ring_duration, --	振铃时长
         |  case when rx_talk_duration is null then '' else rx_talk_duration end as rx_talk_duration, --	人工通话时长
         |  case when rx_end_type_name is null then '' else rx_end_type_name end as rx_end_type_name, --	结束类型
         |  case when rx_keypress_name is null then '' else rx_keypress_name end as rx_keypress_name, --	满意度评价
         |  case when rx_is_repeat is null then '' else rx_is_repeat end as rx_is_repeat, -- 前48小时是否来电
         |  '' as cz_fb_url, --	反馈现场
         |  '' as cz_extend_query, --	检索词
         |  '' as cz_relation_words, --	关联词
         |  '' as cz_adjoint_word_supple, --	关联词补充
         |  '' as cz_user_urls, --	用户URL补充
         |  '' as zx_queue_total_time, --	排队时长
         |  '' as zx_sessionz_total_time, -- 人工会话时长
         |  '' as zx_is_roll_out, -- 是否转出
         |  '' as zx_roll_out_product_line, -- 转出产品线
         |  '' as cz_extend_url,  -- URL
         |  rx_robot_name, -- 机器人名称
         |  rx_robot_number, -- 机器人号码
         |  rx_resource_id,-- 坐席工号
         |  rx_resource_name, -- 坐席姓名
         |  '' as rx_is_upgrade, -- 是否升级
         |  rx_call_in_start_time, -- 新版历史呼入明细开始时间
         |  rx_call_in_end_time,-- 新版历史呼入明细结束时间
         |  '' as zx_is_now_prod_sessionz, -- 是否属于本产品线
         |  rx_phone_num, -- IVR明细电话
         |  '' as product_before_line, -- 在线处理前产品线
         |  '' as cz_extend_keyword, -- 关键词
         |  '' as app_vn, -- app版本
         |  '' as fl_product_line_type, -- 产品线分类
         |  '' as fl_product_line, -- 产品线
         |  '' as question_type_first, -- 一级问题类型
         |  '' as case_id, -- case编号
         |  '' as opt_conclusion, -- 处理结论
         |  '' as check_label, -- 排查标签
         |  '' as assign_processor_history, -- 历史分配处理人
         |  '' as actual_processor_history, -- 历史实际处理人
         |  '' as newest_question_time, -- 最新追问时间
         |  '' as first_check_finish_time, -- case首次结束排查时间
         |  '' as check_finish_processor, -- 结束排查操作人
         |  '' as is_reask, -- 是否追问
         |  '' as zx_is_fill_form, -- 是否填写表单
         |  '' as zx_form_name, -- 表单名称
         |  '' as zx_form_content, -- 表单内容
         |  '' as zx_user_num, -- 人工会话轮次
         |  '' as check_finish_processor_all, -- 结束排查人（全部）
         |  '' as check_finish_time_all, -- 结束排查时间（全部）
         |  '' as cz_is_question_valid, -- 问题是否有效(涉诈)
         |  '' as cz_in_product_info, -- 转入产品线（涉诈）
         |  '' as cz_manual_remark, -- 人工备注（涉诈）
         |  '' as cz_feedback_position, -- 反馈位置
         |  '' as zx_risk_label, -- 危险用户标记
         |  '' as zx_user_risk_label, -- 用户风险标记
         |  '' as zx_session_details, -- 在线用户多轮对话内容明细
         |  '' as zx_bjh_is_encouragement, -- 百家号是否鼓励层用户
         |  '' as zx_bjh_equity_level -- 百家号权益等级
         |from
         |  ivr_detail_data i left join temp_sink_data a on i.process_instance_id = a.process_instance_id
         |  left join temp_enum b on a.temp_one_level = b.value_id
         |  left join temp_enum c on a.temp_two_level = c.value_id
         |  left join temp_enum d on a.temp_three_level = d.value_id
         |""".stripMargin)
      .na.fill("")

    //对没有匹配到的userid，用IVR明细电话再次匹配
    val resultDf = sinkDf
      .na.fill("")
      .withColumn("userid",when(col("userid") === "", getUserId(col("rx_phone_num"))).otherwise(col("userid")))

    resultDf.createOrReplaceTempView("result_data")

    spark.sql(
      s"""
        |insert into table udw_ns.default.help_ods_ufo_key_point_productline_di partition (event_day = $YesterDay)
        |select * from result_data
        |""".stripMargin)

    spark.close()
  }

  /**
   * 匹配Es工单页面产品线与IVR页面产品线
   */
  val productTrans = udf((productLine: String) => {
    productsubMap.getOrElse(productLine, productLine)
  })


  /**
   * 获取客户数据
   */
  val getContaceValue = functions.udf((data: mutable.WrappedArray[String], key: String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var str = ""
      for (elem <- data) {
        val jsonObject = JSON.parseObject(elem)
        val label = jsonObject.getString("label")
        if (label == key) {
          str = jsonObject.getString("value")
            .replaceAll("[\\t|\\r]", "")
            .replaceAll("\\n", " ")
        }
      }
      str
    }
  })

  /**
   * 拼接回复记录
   */
  val formatReply = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val content = jsonObject.getString("content")
        val userName = jsonObject.getString("userName")
        val time = jsonObject.getString("time")

        res = res + "回复内容:" + content + " 回复人:" + userName + " 回复时间:" + time
      })
      res
    }
  })

  /**
   * 拼接操作记录
   */
  val formatOperation = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val assignee = jsonObject.getString("assignee")
        val time = jsonObject.getString("time")
        val taskName = jsonObject.getString("taskName")

        res = (res + "操作人: " + assignee + " 操作时间: " + time + " 记录: " + taskName + "|")
          .replaceAll("[\\t|\\r]", "")
          .replaceAll("\\n", " ")
      })
      res
    }
  })


  /**
   * 读取ES数据
   * @param spark
   * @param startTimeStamp 开始时间
   * @param endTimeStamp 结束时间
   * @param templateId  子产品线id
   * @param templateGroupId 产品线id
   * @return
   */
  private def getEsData(spark: SparkSession,
                        startTimeStamp: Long,
                        endTimeStamp: Long,
                        templateId: String,
                        templateGroupId: String): RDD[(String, String)] = {

    val query =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "filter": [
         |        { "term": { "templateId": "$templateId" }},
         |        { "term": { "templateGroupId": "$templateGroupId" }
         |        },
         |        {
         |          "bool": {
         |            "should": [
         |              {
         |                "range": {
         |                  "createTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              },
         |              {
         |                "range": {
         |                  "modifiedTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    CommonUtils.ElasticSearchOperate(spark,indexName,PropertiesUtils.ESDukeProperties,query)
  }

  /**
   * 获取热线IVR明细数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getIvrDetail(spark: SparkSession, loginUser: String, dataDate: String): DataFrame = {
    //先判断当天日期是否是当月最后一天，是的话取出日期的月份来:202407，否则用yyyy
    val dataMonth = dataDate.substring(0, 6)

    //分区表不存在的话取总表
    val tableSession =
      if (CommonUtils.checkTableExists(PropertiesUtils.MysqlRobotNewProperties,s"${loginUser}_rep_user_session_${dataMonth}")){
        s"${loginUser}_rep_user_session_${dataMonth}"
      }else{
        s"${loginUser}_rep_user_session_yyyy"
      }

    val tableLevel = {
      if (CommonUtils.checkTableExists(PropertiesUtils.MysqlRobotNewProperties,s"${loginUser}_rep_user_session_level_${dataMonth}")){
        s"${loginUser}_rep_user_session_level_${dataMonth}"
      }else{
        s"${loginUser}_rep_user_session_level_yyyy"
      }
    }

    val ivrDataInfo =
      s"""
         |select se.session_id, -- 会话ID
         |       r.robot_id, -- 产品线ID
         |       r.robot_name, -- 机器人名称
         |       remote_url,-- 机器人号码
         |       phone_num,-- IVR电话号码
         |       if(se.turn_user_flag = '1', '转人工', '未转人工') as turn_user_flag,-- 是否转人工
         |       concat_ws(',',group_concat(a.name),group_concat(b.name)) as process_tag, -- ivr流程业务标签
         |       start_time, -- 发起时间
         |       end_time, -- 结束时间
         |       duration, -- 总通话时长(s)
         |       meet_num, -- 交互次数
         |       if(hook_flag = '0', '机器人挂断', (if(hook_flag = '2', '用户挂断', ''))) as hangup_type -- 挂断类型
         |from ${tableSession} se
         |         join (select robot_id,name as robot_name from rbc_robot) r on se.robot_id = r.robot_id
         |         left join (select session_id, l.name
         |                    from ${tableLevel} sl,
         |                         rbc_scene_level l
         |                    where sl.level_id = l.level_id) a on se.session_id = a.session_id
         |         left join (select session_id, t.name
         |                    from ${loginUser}_rep_tag_sid_rel_${dataDate.substring(0, 6)} sr,
         |                         rbc_tag t
         |                    where sr.tag_id = t.tag_id) b on se.session_id = b.session_id
         |where DATE_FORMAT(se.start_time,'%Y%m%d') = '${dataDate}'
         |group by se.session_id
         |""".stripMargin

    val ivrDF: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlRobotNewProperties,ivrDataInfo)
      .withColumn("process_tag",getNullVal(col("process_tag")))

    ivrDF
  }


  /**
   * 获取热线新版呼入明细数据
   *
   * @param spark     SparkSession
   * @param loginUser 平台登陆用户名:baidu,bdidg
   * @param datadate  查询日期
   */
  private def getCallInDetail(spark: SparkSession, loginUser: String, dataDate: String):DataFrame = {
    val dataMonth = dataDate.substring(0, 6)

    //登陆用户不通，读取的表名不同
    val tableName = loginUser match {
      case "baidu" =>
        //先判断表是否存在
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"baidu_voice_call_result_data_${dataMonth}")) {
          s"baidu_call_in_result_data_${dataMonth}"
        } else {
          "baidu_call_in_result_data"
        }*/
        "baidu_call_in_result_data"
      case "bdidg" =>
        /*if(CommonUtils.checkTableExists(PropertiesUtils.MysqlHyccaProperties,s"bdidg_call_in_result_data_${dataMonth}")) {
          s"bdidg_call_in_result_data_${dataMonth}"
        } else {
          "bdidg_call_in_result_data"
        }*/
        "bdidg_call_in_result_data"
    }

    val tableCol = loginUser match {
      case "bdidg" => ("accept_skill_name","alert_duration","handle_duration","end_type","repeat_call")
      //case "baidu" => ("skill_group_name","ring_duration","talk_duration","end_type_name","is_repeat")
      case "baidu" => ("accept_skill_name","alert_duration","handle_duration","end_type","repeat_call")
    }

    //读取新版历史呼入明细页面字段数据
    val callDeail =
      s"""SELECT
         |	s.*,
         |	p.process_instance_id
         |FROM
         |	   (SELECT
         |	    	session_id,-- 会话标识
         |	    	CONCAT( HEX( session_id ), ':', '${loginUser.toUpperCase()}' ) AS session_id_hex,-- 16进制会话标识编号
         |	    	${tableCol._1} as skill_group_name,-- 技能组
         |	    	queue_duration,-- 排队时长
         |	    	${tableCol._2} as ring_duration,-- 振铃时长
         |	    	${tableCol._3} as talk_duration,-- 人工通话时长
         |	    	${tableCol._4} as end_type_name,-- 结束类型
         |	    	keypress_name,-- 满意度评价
         |	      IF ( ${tableCol._5} = '0', '否', '是' ) AS is_repeat, -- 前48小时是否来电
         |        resource_id, -- 坐席工号
         |		    resource_name, -- 坐席姓名
         |		    -- ${if (loginUser.equals("bdidg"))  "'' as" else "IF (is_upgrade = '0','否', '是') as"} is_upgrade, -- 是否升级
         |		    start_time, -- 开始时间
         |		    end_time -- 结束时间
         |	    FROM hycca.${tableName}
         |	    WHERE DATE_FORMAT( start_time, '%Y%m%d' ) = '${dataDate}') s
         |	LEFT JOIN
         |    (SELECT
         |       session_id,
         |       process_instance_id
         |     FROM workflow.${loginUser}_workflow_process_instance
         |     WHERE DATE_FORMAT( create_time, '%Y%m%d' ) = '${dataDate}') p
         | ON s.session_id_hex = p.session_id
         |""".stripMargin

    val processDf: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaProperties,callDeail)

    processDf
  }


  /**
   * 获取问题分类的5级分类数据
   * @param spark
   * @param properties 数据库连接信息
   * @param sql 查询语句
   * @return
   */
  def getClassifyData(spark: SparkSession, properties: Properties, sql: String): DataFrame = {
    val commonDf = CommonUtils.mysqlOperate(spark, properties, sql)
    // 过滤出非爱企查的含有五级目录分类数据
    val tempDf = commonDf.filter(col("archive_id") =!= "424")
      .repartition(10)
    tempDf.createOrReplaceTempView("temp_common")

    // 过滤出爱企查的含有六级目录分类数据
    val aqclassifyDF = commonDf.filter(col("archive_id") === "424")
      .repartition(10)
    aqclassifyDF.createOrReplaceTempView("temp_aqc")

    // 五级分类
    val commonDF = spark.sql(
      """
        |select
        |  t.*
        |from
        |  (
        |    SELECT
        |      a.id as a_id,
        |      a.`name` as product_type,
        |      b.`id` as b_id,
        |      b.`name` as function_type,
        |      c.`id` as c_id,
        |      c.`name` as function_detail_type,
        |      d.`id` as d_id,
        |      d.`name` as customlv4,
        |      e.`id` as e_id,
        |      e.`name` as customlv5
        |    FROM
        |      temp_common a
        |      left join temp_common b on a.id = b.parent_id
        |      left join temp_common c on b.id = c.parent_id
        |      left join temp_common d on c.id = d.parent_id
        |      left join temp_common e on d.id = e.parent_id
        |    where a.parent_id = -1 or a.parent_id = 0
        |  ) t
        |""".stripMargin)

    // 六级分类,只取后五级数据
    val commonDF2 = spark.sql(
      """
        |select
        |  t.*
        |from
        |  (
        |    SELECT
        |      b.`id` as a_id,
        |      b.`name` as product_type,
        |      c.`id` as b_id,
        |      c.`name` as function_type,
        |      d.`id` as c_id,
        |      d.`name` as function_detail_type,
        |      e.`id` as d_id,
        |      e.`name` as customlv4,
        |      f.`id` as e_id,
        |      f.`name` as customlv5
        |    FROM
        |      temp_aqc a
        |      left join temp_aqc b on a.id = b.parent_id
        |      left join temp_aqc c on b.id = c.parent_id
        |      left join temp_aqc d on c.id = d.parent_id
        |      left join temp_aqc e on d.id = e.parent_id
        |      left join temp_aqc f on e.id = f.parent_id
        |    where a.parent_id = -1 or a.parent_id = 0
        |  ) t
        |""".stripMargin)

    commonDF.union(commonDF2)
  }
}
