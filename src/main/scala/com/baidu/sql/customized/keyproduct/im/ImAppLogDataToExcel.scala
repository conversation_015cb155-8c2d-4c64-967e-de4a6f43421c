package com.baidu.sql.customized.keyproduct.im

import com.baidu.sql.utils.SparkUtils.{getHttpurl, readExcel, writeExcel}
import com.baidu.sql.utils.TimeOperateUtil.{calcnDate, calcnDateFormat, currentTimestampMillis}
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.functions._
import io.circe.parser.parse
import io.circe.{<PERSON>son, JsonObject}
import org.apache.spark.sql.types.{ArrayType, StringType, StructField, StructType}

import java.util.concurrent.{Callable, ExecutorCompletionService, Executors, TimeUnit, Future => JFuture}
import scala.collection.mutable
import scala.util.{Failure, Success, Try}
import scala.collection.mutable.ListBuffer

/**
 * <AUTHOR> 从app埋点日志中导出数据到excel
 */
object ImAppLogDataToExcel {
  var YesterDay: String = ""
  var BeForeDay: String = ""
  var YesterDayFormat: String = ""
  // 请求地址bns地址
  val bnsUrl = "bns://group.opera-yq02-bigpipeGW-all-yqdisk2.Bigpipe.all:http"
  val eventTypeMap = Map(
    "handleFocus" -> "搜索框",
    "linkImagesBannerClick" -> "图片链接组件",
    "getToService" -> "自助服务按钮",
    "bannerClick" -> "轮播图",
    "questionTabClick" -> "常见问题tab分类",
    "goQuestionDetail" -> "跳转常见问题",
    "fotterBtn" -> "底部入口",
  )

  val serviceNameList = Seq(
    "清理缓存",
    "网络诊断",
    "账号管理",
    "会员客服",
    "内测入口",
    "账号申诉",
    "创作者反馈",
  )

  // 1 保障维权 2 广告评价/反馈
  val indexMap = Map(
    "1" -> "保障维权",
    "2" -> "广告评价/反馈",
  )

  // 所有埋点的名称
  val pointNameSeq = Seq(
    ("搜索框","埋点1"),
    ("图片链接组件-保障维权","埋点2"),
    ("图片链接组件-广告评价/反馈","埋点3"),
    ("自助服务按钮-清理缓存","埋点4"),
    ("自助服务按钮-网络诊断","埋点5"),
    ("自助服务按钮-账号管理","埋点6"),
    ("自助服务按钮-会员客服","埋点7"),
    ("自助服务按钮-内测入口","埋点8"),
    ("自助服务按钮-账号申诉","埋点9"),
    ("自助服务按钮-创作者反馈","埋点10"),
    ("自助服务按钮-意见反馈",""),
    ("轮播图","埋点11"),
    ("常见问题tab分类","埋点12"),
    ("跳转常见问题","埋点13"),
    ("底部入口","埋点14")
  )
  //id:238226806 2025-07-01 00:00:03.697

  //判断是否出循环，在请求url时，如果id超过了一天的日期，那么就跳出循环
  var isBreak = true

  val key = "0123456789abcdef" // 16字节密钥（必须是16/24/32字节）

  //获取最后一个id进行保存，下次再取数据时，id为最后一个id
  var GlobalID = ""

  //解析bns地址
  val httpUrl = getHttpurl(bnsUrl,"/rest/pipe/metis-query")
  //val httpUrl = "http://*************:8080/rest/pipe/metis-query"
  val APP_TOKEN = "$1$rsbqaJC0$/u6qijK7d56bGz8UoDI661"

  // 定义过期时间，单位为毫秒
  var expires:Long = 0L
  var sign:String = ""
  //公共路径
  //val basePath = "/Users/<USER>/Desktop/zrj_files/801/导数据文件"
  val basePath = "afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test"

  def main(args: Array[String]): Unit = {

    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    BeForeDay = calcnDate(YesterDay, -1)
    // yyyy-MM-dd日期格式
    YesterDayFormat = calcnDateFormat(YesterDay)
    println("yyyy-MM-dd日期格式：" + YesterDayFormat)

    // 定义额外循环次数
    val whileCnt = args(1).toInt
    //定义全局id
    //GlobalID = args(2)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.sql.debug.maxToStringFields", "1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local[*]")  // 使用所有可用核心
      .getOrCreate()

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)
    import spark.implicits._

    // 将pointNameSeq转换为DataFrame
    val pointDf = pointNameSeq.toDF("pointName","location")
    pointDf.createOrReplaceTempView("pointName")

    // 定义JSON结构
    val valueSchema = new ArrayType(
      StructType(Seq(
        StructField("uid", StringType),
        StructField("index", StringType),
        StructField("serviceName", StringType),
        //StructField("path", StringType),
        StructField("pageRouter", StringType),
        StructField("time", StringType),
        StructField("bailingBaseInfo", StringType),
        StructField("eventType", StringType)
      )),
      true
    )

    val tmpIdDf = readExcel(spark,s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/pointLogData/appTmpId.xlsx")
      .filter(col("event_day") === BeForeDay)
      .select(col("id").cast(StringType))

    //获取上一次id
    //GlobalID = "240660000"
    //GlobalID = "240743952" -- 20250711
    //GlobalID = "240745752"  -- 20250712
    //GlobalID = "241150252"  // 20250713
    //GlobalID = "241168752"  // 20250714
    GlobalID = tmpIdDf.head().get(0).toString
    println("GlobalID:" + GlobalID)

    //定义批量大小
    val batchSize = 100

    // 在变量声明区域添加
    var extraLoopTriggered = false  // 标记是否触发了额外循环

    // 创建线程池 (控制并发度)
    val threadPool = Executors.newFixedThreadPool(32)
    val completionService = new ExecutorCompletionService[(String, Int)](threadPool)

    // 计算预估批次范围
    val startId = GlobalID.toInt - (batchSize * whileCnt * 3)
    val endId = GlobalID.toInt + 350000
    val totalBatches = (endId - startId) / batchSize

    // 创建结果队列
    val resultQueue = mutable.Queue[DataFrame]()
    var processedCount = 0
    var submittedCount = 0
    // 新增：批次计数器
    var batchCounter = 0

    // 定义分批写入Excel的变量
    //val tempOutputPath = s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/pointLogData/tmp_${YesterDay}"
    val tempOutputPath = s"${basePath}/tmp_${YesterDay}"
    val hadoopPath = new org.apache.hadoop.fs.Path(tempOutputPath)
    val fs = hadoopPath.getFileSystem(spark.sparkContext.hadoopConfiguration)
    if (fs.exists(hadoopPath)) fs.delete(hadoopPath, true)
    var writeBatchCounter = 0

    // 恢复原始标记变量（关键修复）
    var needExtraLoop = false
    var extraLoopCount = 0
    var continueProcessing = true

    // 新增：停止提交标志
    var stopSubmitting = false

    // 定义异步任务
    class BatchTask(batchId: Int, batchSize: Int) extends Callable[(String, Int)] {
      override def call(): (String, Int) = {
        (getUrlData(batchId.toString, batchSize.toString), batchId)
      }
    }

    // 初始提交任务
    val initialTasks = math.min(32, totalBatches)
    (0 until initialTasks).foreach { i =>
      completionService.submit(new BatchTask(startId + i * batchSize, batchSize))
      submittedCount += 1
    }

    // 异步处理循环
    var currentId = startId + initialTasks * batchSize
    var hasMoreTasks = true

    while (processedCount < submittedCount && continueProcessing) {
      // 获取已完成任务
      val completedFuture = Try(completionService.poll(30, TimeUnit.SECONDS)) match {
        case Success(future) if future != null => future
        case _ =>
          println("等待任务完成超时，检查网络或接口状态")
          null
      }

      if (completedFuture != null) {
        Try(completedFuture.get()) match {
          case Success((messageJson, batchId)) =>
            // 处理结果
            if (messageJson.equals("Empty response")) {
              println(s"批次 $batchId 返回空响应")
            } else {
              val rawDF = parseMessageJsonWithStartTime(messageJson)(spark)

              // 完整保留原始跨天检测逻辑（关键修复）
              // ============= 原有逻辑开始 =============
              val maxDateOpt = rawDF.select(max(col("startTime"))).head().getAs[String](0)
              if (maxDateOpt != null && maxDateOpt > YesterDayFormat) {
                if (!needExtraLoop && extraLoopCount == 0) {
                  needExtraLoop = true
                  println(s"检测到跨天数据($maxDateOpt)，将再执行${whileCnt}次循环（每次100批次）后停止")
                }
              }
              // ============= 原有逻辑结束 =============

              val resultDF = processFilteredData(rawDF, valueSchema, eventTypeMap, serviceNameList, indexMap)
              if (resultDF.count() > 0) {
                resultQueue.enqueue(resultDF)
              }
            }

            processedCount += 1
            batchCounter += 1  // 增加批次计数器

            // 修改：每100批次打印一次信息
            if (batchCounter % 100 == 0 || batchCounter == 1) {
              if (resultQueue.size >= 1) {
                val batchDF = resultQueue.reduce(_ union _)
                // 分批写入临时存储
                val batchPath = s"$tempOutputPath/batch_$writeBatchCounter"
                batchDF.coalesce(1).write.mode("overwrite").parquet(batchPath)
                writeBatchCounter += 1
                resultQueue.clear()
                println(s"已将批次数据写入临时存储: $batchPath")
              }
              println(s"已完成批次: $batchId ($processedCount/$submittedCount) - 累计完成 $batchCounter 批次")
            }

            // 提交新任务 - 关键修复：添加停止提交条件
            if (currentId <= endId && hasMoreTasks && !stopSubmitting) {
              // 提交正常批次
              completionService.submit(new BatchTask(currentId, batchSize))
              submittedCount += 1
              currentId += batchSize

              // 完整保留原始额外循环逻辑（关键修复）
              // ============= 原有逻辑开始 =============
              if (needExtraLoop) {
                if (extraLoopCount < whileCnt) {
                  // 提交额外批次
                  completionService.submit(new BatchTask(currentId, batchSize))
                  submittedCount += 1
                  currentId += batchSize
                  extraLoopCount += 1

                  if (extraLoopCount == whileCnt) {
                    println(s"已完成${whileCnt}次额外循环")
                    // 恢复原始退出标记
                    isBreak = false
                    // 关键修复：设置停止提交标志
                    stopSubmitting = true
                    // 关键修复：设置退出循环标志
                    continueProcessing = false
                    extraLoopTriggered = true
                    println("最后的id为:" + currentId)
                    println("expires:" + expires)
                    println("sign:" + sign)
                  }
                }
              }
              // ============= 原有逻辑结束 =============
            } else {
              hasMoreTasks = false
            }

          case Failure(e) =>
            println(s"批次处理失败: ${e.getMessage}")
        }
      } else if ((!hasMoreTasks || stopSubmitting) && processedCount >= submittedCount) {
        // 关键修复：添加停止提交条件
        continueProcessing = false
      }

      // 安全退出检查（保留原始逻辑）
      if (currentId > endId) {
        println("安全机制：已处理超过30万个ID，强制退出循环")
        hasMoreTasks = false
        if (processedCount >= submittedCount) continueProcessing = false
      }
    }

    // 确保所有剩余任务完成
    while (processedCount < submittedCount) {
      val completedFuture = completionService.take()
      Try(completedFuture.get()) match {
        case Success((messageJson, batchId)) =>
          // 处理结果（包含跨天检测）
          if (messageJson != null && !messageJson.equals("Empty response")) {
            val rawDF = parseMessageJsonWithStartTime(messageJson)(spark)

            // 保留跨天检测逻辑
            val maxDateOpt = rawDF.select(max(col("startTime"))).head().getAs[String](0)
            if (maxDateOpt != null && maxDateOpt > YesterDayFormat && !needExtraLoop) {
              needExtraLoop = true
              println(s"[剩余任务]检测到跨天数据($maxDateOpt)")
            }

            val resultDF = processFilteredData(rawDF, valueSchema, eventTypeMap, serviceNameList, indexMap)
            if (resultDF.count() > 0) {
              resultQueue.enqueue(resultDF)
            }
          }

          processedCount += 1
          batchCounter += 1  // 增加批次计数器

          // 修改：每100批次打印一次信息
          if (batchCounter % 100 == 0 || batchCounter == 1) {
            // 合并队列中的所有DataFrame
            if (resultQueue.size >= 1) {
              val batchDF = resultQueue.reduce(_ union _)
              // 分批写入临时存储
              val batchPath = s"$tempOutputPath/batch_$writeBatchCounter"
              batchDF.coalesce(1).write.mode("overwrite").parquet(batchPath)
              writeBatchCounter += 1
              resultQueue.clear()
              println(s"已将批次数据写入临时存储: $batchPath")
            }
            println(s"完成剩余批次: $batchId ($processedCount/$submittedCount) - 累计完成 $batchCounter 批次")
          }

        case Failure(e) =>
          println(s"剩余批次处理失败: ${e.getMessage}")
      }
    }

    // 循环结束后添加
    if (resultQueue.nonEmpty) {
      val batchDF = resultQueue.reduce(_ union _)
      val batchPath = s"$tempOutputPath/batch_$writeBatchCounter"
      batchDF.coalesce(1).write.mode("overwrite").parquet(batchPath)
      writeBatchCounter += 1
      resultQueue.clear()
      println(s"强制写入剩余批次数据: ${batchDF.count()}行 -> $batchPath")
    }

    // 定义最终DataFrame的schema
    val finalSchema = StructType(Seq(
      StructField("id", StringType),
      StructField("uid", StringType),
      StructField("index", StringType),
      StructField("serviceName", StringType),
      StructField("pageRouter", StringType),
      StructField("time", StringType),
      StructField("eventType", StringType),
      StructField("productId", StringType),
      StructField("pointName", StringType)
    ))

    // 最终合并写入Excel
    // 1. 获取所有批次目录
    val batchDirs = fs.listStatus(hadoopPath)
      .filter(_.isDirectory)
      .filter(_.getPath.getName.startsWith("batch_"))
      .map(_.getPath.toString)

    // 2. 读取并合并所有批次数据
    val finalDF = if (batchDirs.nonEmpty) {
      // 读取所有批次目录下的parquet文件
      val batchDFs = batchDirs.map(dir => spark.read.schema(finalSchema).parquet(dir))

      // 合并所有DataFrame
      batchDFs.reduce(_ union _)
    } else {
      // 如果没有批次数据，创建空DataFrame
      spark.createDataFrame(spark.sparkContext.emptyRDD[Row], finalSchema)
    }
    println("最终合并数据如下：" + finalDF.count())
    finalDF.show(false)
    finalDF.createOrReplaceTempView("res_data")
    writeExcel(finalDF, s"${basePath}/pointLogData/appLogData${YesterDay}.xlsx")
    println(s"最终合并数据写入Excel: ${basePath}/pointLogData/appLogData${YesterDay}.xlsx")

    // 关闭线程池
    threadPool.shutdownNow()

    //获取pvuv数据
    val pvDf = spark.sql(
        """
          |with tmp_data as (
          |select
          |   pointName,
          |   count(*) as pv,
          |   count(distinct uid) as uv
          |from res_data
          |group by pointName
          |)
          |select
          |   a.pointName,
          |   a.location,
          |   case when b.pv is null then 0 else b.pv end as pv,
          |   case when b.uv is null then 0 else b.uv end as uv
          |from pointName a
          |left join tmp_data b
          |on a.pointName = b.pointName
          |""".stripMargin)
      .na.fill("0")
      .coalesce(1)
    println("pvuv数据如下：")
    pvDf.show(false)

    // 写入数据到新sheet页（追加模式）
    writeExcel(
      pvDf,
      s"${basePath}/pointLogData/pvuvData${YesterDay}.xlsx",
      mode = "append",  // 使用追加模式
      sheetName = Some(YesterDayFormat),  // 指定sheet名称
      overwriteSheet = true      // 覆盖同名sheet页
    )
    println("pvuv数据已写入pvuvData的Excel文件的新sheet页")

    // 确定要记录的ID
    val recordId = finalDF.agg(max("id")).first().getString(0)

    val DayIdDf = spark.createDataFrame(Seq((recordId, YesterDay))).toDF("id", "event_day")
    //writeExcel(DayIdDf,s"/Users/<USER>/Desktop/zrj_files/801/导数据文件/appTmpId${YesterDay}.xlsx")
    writeExcel(
      DayIdDf,
      s"${basePath}/pointLogData/appTmpId.xlsx",
      mode = "append"  // 使用追加模式
    )
    println("id已写入excel页")

    if (fs.exists(hadoopPath)) fs.delete(hadoopPath, true)
    // 添加最终汇总打印
    println(s"所有批次处理完成，累计完成 $batchCounter 批次")

    // 收集结果（保持原有逻辑）
    /*val ResDf = resultQueue.reduce((df1, df2) => df1.unionByName(df2))
      .dropDuplicates()
    println("结果数据共" + ResDf.count())
    ResDf.show(10,false)
    ResDf.createOrReplaceTempView("res_data")*/

    //建立一个bufferList，用于存储数据
    /*val bufferList = ListBuffer[DataFrame]()

    // 修改状态标记：使用计数器记录额外循环次数
    var needExtraLoop = false
    var extraLoopCount = 0  // 记录已经执行的额外循环次数
    var continueProcessing = true  // 增加处理控制标志
    //开始循环获取数据
    while (isBreak && continueProcessing) {
      val messageJson = getUrlData(tmp_id.toString, batchSize.toString)
      //println("tmp_id:" + tmp_id.toString)

      if (messageJson.equals("Empty response")) {
        // 无法获取数据，标记退出循环
        isBreak = false
      }

      // 解析原始数据并保留startTime字段
      val rawDF = parseMessageJsonWithStartTime(messageJson)(spark)

      // 检测是否有跨天数据
      val maxDateOpt = rawDF.select(max(col("startTime"))).head().getAs[String](0)
      if (maxDateOpt != null && maxDateOpt > YesterDayFormat) {
        if (!needExtraLoop && extraLoopCount == 0) {  // 当还没有开始额外循环时
          needExtraLoop = true  // 标记需要额外循环
          println(s"检测到跨天数据($maxDateOpt)，将再执行${whileCnt}次循环（每次100批次）后停止")
        }
      }

      // 过滤昨日数据并处理
      val resultDF = processFilteredData(rawDF, valueSchema, eventTypeMap, serviceNameList, indexMap)
      if (resultDF.count() > 0) {
        bufferList += resultDF  // 尾部追加结果
      }
      // id加batchSize批次大小
      tmp_id += batchSize

      // 额外循环处理逻辑
      if (needExtraLoop) {
        if (extraLoopCount < whileCnt) {  // 执行额外循环
          //println(s"执行额外循环(${extraLoopCount+1}/10)，tmp_id增加100批次")
          tmp_id += batchSize  // 额外增加100批次
          extraLoopCount += 1

          // 如果是第二次额外循环，设置结束标志
          if (extraLoopCount == whileCnt) {
            isBreak = false
            println(s"已完成${whileCnt}次额外循环，结束任务")
            println("expires:" + expires)
            println("sign:" + sign)
          }
        }
      }

      // 添加安全退出机制（防止死循环）
      if (tmp_id > GlobalID.toInt + 300000) {
        println("安全机制：已处理超过30万个ID，强制退出循环")
        continueProcessing = false
      }
    }*/

    /*val ResDf = bufferList.reduce((df1, df2) => df1.unionByName(df2))
      .dropDuplicates()
    println("结果数据共" + ResDf.count())
    ResDf.show(10,false)
    ResDf.createOrReplaceTempView("res_data")*/

    // 写入数据到新sheet页（追加模式）
    /*writeExcel(
      ResDf,
      s"afs://pegasus.afs.baidu.com:9902/user/baisheng/data-Operation/biaoqian_test/appLogData.xlsx",
      mode = "append",  // 使用追加模式
      sheetName = Some(YesterDayFormat),  // 指定sheet名称
      overwriteSheet = true      // 覆盖同名sheet页
    )
    println("明细数据已写入Excel文件的新sheet页")*/



    //解析message中的json数据
    //var dataArray = Seq.empty[(String, String)]

    /*if (messageJson != null && JSONUtil.isJson(messageJson)) {
      val jsonObject = JSON.parseObject(messageJson)
      if (!jsonObject.isEmpty) {
        //获取数据data
        val messageArray: Option[JSONArray] = Option(jsonObject.getJSONArray("messages"))
        dataArray = messageArray match {
          case Some(array) =>
            //array的循环次数就是batch批次的数量
            array.toArray.map(x => {
              var id = ""
              var value = ""
              if (x != null && JSONUtil.isJson(x.toString)) {
                val message = JSON.parseObject(x.toString)
                //获取id
                id = message.getString("id")
                //获取埋点数据data
                val data = message.getString("data")
                if (JSONUtil.isJson(data)) {
                  val dataJson = JSON.parseObject(data)
                  //获取startTime,并0到10位的字符串，即日期格式 yyyy-MM-dd
                  val startTime = dataJson.getString("startTime").substring(0, 10)
                  //判断日期是否为昨日日期,只取昨日日期的数据
                  if (startTime == YesterDayFormat) {
                    if (dataJson.containsKey("paramterInfos")) {
                      val parameterInfos = dataJson.getJSONArray("paramterInfos").get(0).toString
                      val parameterJson = JSON.parseObject(parameterInfos)
                      if (parameterJson.containsKey("value")) {
                        value = parameterJson.getString("value")
                      }
                    }
                  }
                }
              }
              (id, value)
            })
          case None => Seq.empty[(String, String)]
        }
      }
    }else {
      println("messageJson解析失败")
    }*/

    spark.stop()
    println("SparkSession已正确关闭")
  }


  /**
   * 事件类型映射
   */
  val eventTypeUdf = udf((value:String) => {
    if (value != null && value.nonEmpty) {
      eventTypeMap.getOrElse(value, "")
    }else{
      ""
    }
  })

  /**
   * index映射
   */
  val indexUdf = udf((value:String) => {
    if (value != null && value.nonEmpty) {
      indexMap.getOrElse(value, "")
    }else{
      ""
    }
  })



  /**
   * 获取接口数据
   *
   * @param id    id必须是唯一id，并且关联到对应日期
   * @param batch 批次，一次调用接口获取多少数据量，不超过100
   * @return
   */
  def getUrlData(id: String, batch: String = "100"): String = {
    //口令获取
    var response: Response = null

    //当前时间戳
    expires = currentTimestampMillis(10) + 600
    sign = DigestUtils.md5Hex(expires + APP_TOKEN)

    val okHttpClient = new OkHttpClient
    val request = new Request.Builder()
      .url(httpUrl + "?sign=" + sign + "&method=" + "fetch" + "&pipelet=" + "10" + "&id=" + id + "&batch=" + batch + "&expires=" + expires + "&username=" + "metis")
      .addHeader("Content-Type", "application/json")
      .get()
      .build

    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        //println("response:" + unescapeJson(response.body.string).toString)
        unescapeJson(response.body.string).toString
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

  /**
   * 解析反斜杠转义的JSON字符串
   * @param input
   * @return
   */
  def unescapeJson(input: String): Json = {
    parse(input) match {
      case Right(json) => recursiveUnescape(json)
      case Left(error) => throw new RuntimeException(s"JSON解析失败: $error")
    }
  }

  /**
   * 递归解转义
   * @param json
   * @return
   */
  def recursiveUnescape(json: Json): Json = {
    json.fold(
      jsonNull = Json.Null,
      jsonBoolean = Json.fromBoolean,
      jsonNumber = Json.fromJsonNumber,
      jsonString = { str =>
        parse(str).getOrElse(Json.fromString(str)) // 尝试解析字符串中的JSON
      },
      jsonArray = { arr =>
        Json.fromValues(arr.map(recursiveUnescape))
      },
      jsonObject = { obj =>
        Json.fromJsonObject(JsonObject.fromIterable(
          obj.toIterable.map { case (k, v) =>
            (k, recursiveUnescape(v))
          }
        ))
      }
    )
  }

  def parseMessageJsonWithStartTime(messageJson: String)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    // 1. 定义批量解析的Schema
    val messageSchema = new StructType()
      .add("messages", ArrayType(new StructType()
        .add("id", StringType)
        .add("data", new StructType()
          .add("startTime", StringType)
          .add("paramterInfos", ArrayType(new StructType()
            .add("value", StringType)
          ))
        )
      ), true)

    // 2. 创建包含原始JSON的DataFrame
    val jsonDF = spark.createDataFrame(Seq((messageJson, 1))).toDF("raw_json", "dummy")

    // 3. 批量解析JSON
    val parsedDF = jsonDF
      .withColumn("parsed", from_json($"raw_json", messageSchema))
      .withColumn("message", explode($"parsed.messages"))
      .select(
        $"message.id".as("id"),
        $"message.data.startTime".as("startTime"),
        $"message.data.paramterInfos".as("paramterInfos")
      )
      // 过滤无效数据
      .filter(col("id").isNotNull && col("startTime").isNotNull)

    // 4. 处理paramterInfos数组（保持原有逻辑）
    val resultDF = parsedDF
      .withColumn("value",
        when(size($"paramterInfos") > 0,
          element_at($"paramterInfos", 1).getField("value")
        ).otherwise(lit(""))
      )
      .drop("paramterInfos")
      .withColumn("startTime",
        when(length($"startTime") >= 10, substring($"startTime", 1, 10))
          .otherwise(lit(null).cast(StringType))
      )

    // 5. 添加空值检查（保持原有错误处理）
    if (resultDF.isEmpty) {
      println("dataList为空")
      resultDF
    } else {
      resultDF
    }
  }

  /**
   * 增强版解析函数（保留startTime字段）
   */
  /*def parseMessageJsonWithStartTime(messageJson: String)(implicit spark: SparkSession): DataFrame = {
    if (spark == null || spark.sparkContext.isStopped) {
      throw new IllegalStateException("SparkSession is not initialized or already stopped")
    }
    import spark.implicits._

    val dataList = ListBuffer[(String, String, String)]() // (id, value, startTime)

    try {
      if (messageJson != null && JSONUtil.isJson(messageJson)) {
        val jsonObject = JSON.parseObject(messageJson)
        if (!jsonObject.isEmpty) {
          Option(jsonObject.getJSONArray("messages")).foreach { array =>
            array.toArray.foreach { x =>
              if (x != null && JSONUtil.isJson(x.toString)) {
                val message = JSON.parseObject(x.toString)
                val id = message.getString("id")
                val data = message.getString("data")

                if (JSONUtil.isJson(data)) {
                  val dataJson = JSON.parseObject(data)
                  val startTime = Option(dataJson.getString("startTime"))
                    .filter(_.length >= 10)
                    .map(_.substring(0, 10))
                    .orNull

                  var value = ""
                  // 不管日期是否跨天都解析value（便于后续检测）
                  if (dataJson.containsKey("paramterInfos")) {
                    val paramArray = dataJson.getJSONArray("paramterInfos")
                    if (paramArray != null && !paramArray.isEmpty) {
                      val paramInfo = paramArray.getJSONObject(0)
                      value = Option(paramInfo.getString("value")).getOrElse("")
                    }
                  }
                  dataList += ((id, value, startTime))
                }
              }
            }
          }
        }
      }
    } catch {
      case e: Exception =>
        println(s"JSON解析异常: ${e.getMessage}")
    }

    if (dataList.isEmpty) {
      println("dataList为空")
      spark.emptyDataFrame
    }else{
      spark.createDataFrame(dataList.toSeq).toDF("id", "value", "startTime")
    }
  }*/

  /**
   * 处理过滤后的数据（原resultDF构建逻辑）
   */
  def processFilteredData(
                           filteredDF: DataFrame,
                           valueSchema: ArrayType,
                           eventTypeMap: Map[String, String],
                           serviceNameList: Seq[String],
                           indexMap: Map[String, String]
                         ): DataFrame = {
    // 原resultDF构建逻辑（从withColumn开始到dropDuplicates）
    filteredDF
      .withColumn("value_array", from_json(col("value"), valueSchema))
      .select(col("id"), explode(col("value_array")).as("value_obj"))
      .select(
        col("id"),
        col("value_obj.uid").as("uid"),
        col("value_obj.index").as("index"),
        col("value_obj.serviceName").as("serviceName"),
        col("value_obj.pageRouter").as("pageRouter"),
        col("value_obj.time").as("time"),
        col("value_obj.bailingBaseInfo").as("bailingBaseInfo"),
        col("value_obj.eventType").as("eventType")
      )
      .withColumn("productId", get_json_object(col("bailingBaseInfo"), "$.productId"))
      .drop("bailingBaseInfo")
      .filter(col("pageRouter") === "/bailing/helpCenter")
      .filter(col("productId") === "1")
      .filter(col("time").startsWith(YesterDayFormat))
      .filter(
        col("eventType").isin(eventTypeMap.keys.toArray: _*) ||
          (col("eventType") === "getToService" && col("serviceName").isin(serviceNameList: _*)) ||
          (col("eventType") === "linkImagesBannerClick" && col("index").isin(indexMap.keys.toArray: _*))
      )
      .na.fill("")
      .withColumn("pointName",
        when(col("eventType") === "getToService", concat_ws("-", eventTypeUdf(col("eventType")), col("serviceName")))
          .when(col("eventType") === "linkImagesBannerClick", concat_ws("-", eventTypeUdf(col("eventType")), indexUdf(col("index"))))
          .otherwise(eventTypeUdf(col("eventType"))))
      .dropDuplicates()
      .repartition(100)
  }

}
