package com.baidu.sql.customized.keyproduct.duke

import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import org.apache.spark.SparkConf
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.elasticsearch.index.query.{BoolQueryBuilder, QueryBuilders}
import scala.collection.mutable.ListBuffer


object DukeBanQuanToHive {
  //ES索引名称(总)
  val indexName = "copyright_parent"

  //业务审核区 + 产品线id
  val businessIndexName = "copyright_business_"

  //业务审核区操作记录 + 产品线id
  val businessLogIndexName = "log_copyright_business_"

  //投诉url
  val urlIndexName = "copyright_url"

  //实名审核区
  val realNameIndexName = "copyright_realname"

  //实名操作记录
  val realNameLogIndexName = "log_copyright_realname"

  //法务审核区 + 产品线id
  val legalIndexName = "copyright_legal_"

  //法务操作记录 + 产品线id
  val legalLogIndexName = "log_copyright_legal_"

  // 需要查询的产品线
  val productMap = Map(
    "实名审核区" -> (realNameIndexName,realNameLogIndexName),
    "业务审核区-百度搜索" -> (businessIndexName,businessLogIndexName),
    "业务审核区-百度网盘"-> (businessIndexName,businessLogIndexName),
    "业务审核区-百度贴吧"-> (businessIndexName,businessLogIndexName),
    "业务审核区-百度知道"-> (businessIndexName,businessLogIndexName),
    "业务审核区-百度APP"-> (businessIndexName,businessLogIndexName),
    "业务审核区-百度图片"-> (businessIndexName,businessLogIndexName),
    "业务审核区-百度手机浏览器"-> (businessIndexName,businessLogIndexName)
  )

  //创建ES客户端
  val client = CommonUtils.createElasticsearchClient(PropertiesUtils.ESDukeBanQuanProperties)

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)

    //sparkSession配置
    val sparkConf = new SparkConf().setAppName("ReadEs")
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val sc = spark.sparkContext
    sc.setLogLevel("WARN")

    // 获取YesterDay豪秒级时间戳,取前10位时间戳
    val startTimeStamp = getTimeStampTake(YesterDay,10)
    val endTimeStamp = getTimeStampTake(calcnDate(YesterDay,1),10)

    println(s"startTimeStamp: $startTimeStamp, endTimeStamp: $endTimeStamp")

    //获取产品线名称
    val productNameStr = productMap.keys.mkString("','")

    // 查询表，获取group_id 产品线
    val productSql =
      s"""
         |SELECT
         |  product_line,
         |  name
         |FROM tbl_copyright_product_line
         |where name in ('$productNameStr')
         |""".stripMargin

    val productDF= CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlDukeProperties,productSql)

    //所有产品线id -> 产品线名称映射
    val array = productDF.collect().map(x => x.get(0).toString -> x.get(1).toString)

    // 查询枚举表，
    val mapSql =
      s"""
         |SELECT
         |  code,
         |  name,
         |  value
         |FROM tbl_dictionary_children
         |where code in ('owner_type','work_type','priority','qualidication_status','copyright_user_type','to_status')
         |""".stripMargin

    val mapDF= CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlDukeProperties,mapSql)

    //获取枚举表映射
    val valueMap = mapDF
      .rdd
      .map(
        x => {
        x.get(0).toString -> (x.get(2).toString -> x.get(1).toString)
      })
      .groupByKey()
      .mapValues(x => x.toMap)
      .collectAsMap()

    /**
     * 匹配映射值
     */
    val getMapValue = udf((data: String,colName:String) => {
      if (data == null || data.isEmpty) {
        ""
      }else{
        val value = valueMap.get(colName).get
        value.get(data).get
      }
    })

    //定义读取Es后的数据schema
    val schema = new StructType().add("data", StringType)
    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
    //定义ES的查询query
    var boolQueryBuilder: BoolQueryBuilder = null
    if (indexName.contains("log")){
      boolQueryBuilder =
        QueryBuilders
          .boolQuery
          .must(QueryBuilders.boolQuery
            .should(QueryBuilders.rangeQuery("operateTime").gte(startTimeStamp).lt(endTimeStamp))
          )
    }else{
      boolQueryBuilder =
        QueryBuilders
          .boolQuery
          .must(QueryBuilders.boolQuery
            //入审时间
            .should(QueryBuilders.rangeQuery("add_time").gte(startTimeStamp).lt(endTimeStamp))
            //审核时间
            .should(QueryBuilders.rangeQuery("audit_time").gte(startTimeStamp).lt(endTimeStamp))
            //处理时间
            .should(QueryBuilders.rangeQuery("duke_optime").gte(startTimeStamp).lt(endTimeStamp))
            //投诉时间
            .should(QueryBuilders.rangeQuery("complaint_date").gte(startTimeStamp).lt(endTimeStamp))
          )
    }

    for (product <- array) {
      //获取产品线id
      val productLineId = product._1
      //获取产品线名称
      val productName = product._2
      //获取索引名称
      val indexName = productMap.get(productName).get._1
      //获取操作记录索引名称
      val indexLogName = productMap.get(productName).get._2
      //获取索引拼接后名称
      val indexNames = if (indexName.endsWith("_")) indexName + productLineId else indexName
      val indexLogNames = if (indexLogName.endsWith("_")) indexLogName + productLineId else indexName
      //获取ES数据
      val listBuffer = CommonUtils.getScrollData(startTimeStamp, endTimeStamp, indexNames, client, boolQueryBuilder)
      val listLogBuffer = CommonUtils.getScrollData(startTimeStamp, endTimeStamp, indexLogNames, client, boolQueryBuilder)
      // 创建RDD
      val rdd = spark.sparkContext.parallelize(listBuffer)
      val rdd2 = spark.sparkContext.parallelize(listLogBuffer)

      // 使用SparkSession创建DataFrame
      val dataDf: DataFrame = spark.createDataFrame(rdd.map(row => Row(row)), schema)
      // 使用SparkSession创建DataFrame
      val dataLogDf: DataFrame = spark.createDataFrame(rdd2.map(row => Row(row)), schema)

      // 解析字段sql  这里把接口无法取出的字段做处理
      dataDf
        .withColumn("space_name",lit(productName))
        .withColumn("status",getMapValue(get_json_object(col("data"),"$.status").cast("string"),lit("to_status")))
        .withColumn("id",getNullVal(get_json_object(col("data"),"$.id").cast("string")))
        .withColumn("feedback_number",getNullVal(get_json_object(col("data"),"$.feedback_number").cast("string")))
        .withColumn("cp_id",getNullVal(get_json_object(col("data"),"$.cp_id").cast("string")))
        .withColumn("owner_type",getMapValue(get_json_object(col("data"),"$.owner_type").cast("string"),lit("owner_type")))
        .withColumn("work_name",getNullVal(get_json_object(col("data"),"$.work_name").cast("string")))
        .withColumn("work_type",getMapValue(get_json_object(col("data"),"$.work_type").cast("string"),lit("work_type")))
        .withColumn("client_name",getNullVal(get_json_object(col("data"),"$.client_name").cast("string")))
        .withColumn("audit_time",getNullVal(getDateTimeVal((get_json_object(col("data"),"$.audit_time").cast("long")*1000).cast("string"))))
        .withColumn("complaint_description",getNullVal(get_json_object(col("data"),"$.complaint_description").cast("string")))
        .withColumn("duke_operator",getNullVal(get_json_object(col("data"),"$.duke_operator").cast("string")))
        .withColumn("duke_optime",getNullVal(getDateTimeVal((get_json_object(col("data"),"$.duke_optime").cast("long")*1000).cast("string"))))
        .withColumn("result",getNullVal(get_json_object(col("data"),"$.result").cast("string")))
        .withColumn("remark",getNullVal(get_json_object(col("data"),"$.remark").cast("string")))
        .withColumn("priority",getMapValue(get_json_object(col("data"),"$.priority").cast("string"),lit("priority")))
        .withColumn("qualidication_status",getMapValue(get_json_object(col("data"),"$.qualidication_status").cast("string"),lit("qualidication_status")))
        .withColumn("complaint_account",getNullVal(get_json_object(col("data"),"$.complaint_account").cast("string")))
        .withColumn("uid",getNullVal(get_json_object(col("data"),"$.uid").cast("string")))
        .withColumn("complaint_date",getNullVal(getDateTimeVal((get_json_object(col("data"),"$.complaint_date").cast("long")*1000).cast("string"))))
        .withColumn("complaint_number",getNullVal(get_json_object(col("data"),"$.complaint_number").cast("string")))
        .withColumn("add_time",getNullVal(getDateTimeVal((get_json_object(col("data"),"$.add_time").cast("long")*1000).cast("string"))))
        .withColumn("copyright_user_type",getMapValue(get_json_object(col("data"),"$.copyright_user_type").cast("string"),lit("copyright_user_type")))
        .withColumn("worker",getNullVal(get_json_object(col("data"),"$.worker").cast("string")))
        .withColumn("pseudonym",getNullVal(get_json_object(col("data"),"$.pseudonym").cast("string")))
        .withColumn("actual_url",getNullVal(get_json_object(col("data"),"$.actual_url").cast("string")))
        .withColumn("actual_name",getNullVal(get_json_object(col("data"),"$.actual_name").cast("string")))
        .withColumn("copyright_user_type",when(col("copyright_user_type") === "企业","组织").otherwise("个人"))
        .drop("data")
        .createOrReplaceTempView("tempDf")

      // 解析Log字段sql  这里把接口无法取出的字段做处理
      dataLogDf
        .withColumn("status",getMapValue(get_json_object(col("data"),"$.status").cast("string"),lit("to_status")))
        .withColumn("feedback",getNullVal(get_json_object(col("data"),"$.feedback").cast("string")))
        .withColumn("processing_result",getNullVal(get_json_object(col("data"),"$.processing_result").cast("string")))
        .drop("data")
        .createOrReplaceTempView("logDf")

      val resDf = spark.sql(
        """
          |select
          |t.*,
          |l.processing_result
          |from tempDf t
          |left join logDf l
          |on t.id = l.feedback and t.status = l.status
          |-- where l.status in ('待人工处理','处理中','已完成')
          |""".stripMargin)
        .dropDuplicates()
        .repartition(10)
        .cache()

      println(s"${productName}的数据量为" + resDf.count())
      resultDf.append(resDf)
    }
    //合并数据
    val combinDf = resultDf
      .reduce((x, y) => x.union(y))
      .dropDuplicates("id", "status","space_name","audit_time","duke_optime","complaint_date")
      .na.fill("")
      .repartition(10)
      .cache()

    println("combinDf数据量：" + combinDf.count())
    //combinDf.show(5,false)
    combinDf.createOrReplaceTempView("res_data")

    spark.sql(
      s"""
         |insert overwrite table udw_ns.default.help_ods_duke_copyright_system partition (event_day = $YesterDay )
         |select
         |  space_name, --投诉产品
         |  status, --状态
         |  id, --序号
         |  feedback_number, --反馈单号
         |  cp_id, --CPID
         |  owner_type, --权利属性
         |  work_name, --作品名称
         |  work_type, --作品类型
         |  client_name, --权利人名称
         |  audit_time, --审核时间
         |  complaint_description, --问题描述
         |  duke_operator, --处理人
         |  duke_optime, --处理时间
         |  processing_result, --处理结果
         |  result, --处理意见
         |  remark, --备注
         |  priority, --是否高优
         |  qualidication_status, --审核状态
         |  complaint_account, --名称
         |  uid, --UID
         |  complaint_date, --投诉时间
         |  complaint_number, --投诉链接数量
         |  add_time, --入审时间
         |  copyright_user_type, --用户类型
         |  worker, --被分配人
         |  pseudonym, --作者名称
         |  actual_url, --原版链接
         |  actual_name --原版标题
         |from
         |  res_data
         |""".stripMargin)

    spark.close()
  }

  /**
   * 获取Es数据
   * @param spark
   * @param startTimeStamp 日期开始时间时间戳
   * @param endTimeStamp 日期结束时间时间戳
   * @param productId 产品线id
   * @return
   */
  private def getEsData(spark: SparkSession,
                        startTimeStamp: String,
                        endTimeStamp: String,
                        productId: String,
                        indexName:String): RDD[(String,String)] = {
    //如果结尾是_则加上产品线id
    val indexNames = if (indexName.endsWith("_")) indexName + productId else indexName
    var query = ""
    if (indexName.contains("log")){
      query =
        s"""
           |{
           |  "query": {
           |    "bool": {
           |      "filter": [
           |        {
           |          "bool": {
           |            "must": [
           |              {
           |                "range": {
           |                  "operateTime": {
           |                    "gte": "${startTimeStamp}",
           |                    "lt": "${endTimeStamp}"
           |                  }
           |                }
           |              }
           |            ]
           |          }
           |        }
           |      ]
           |    }
           |  }
           |}
           |""".stripMargin
    }else{
      query =
        s"""
           |{
           |  "query": {
           |    "bool": {
           |      "filter": [
           |        {
           |          "bool": {
           |            "should": [
           |              {
           |                "range": {
           |                  "add_time": {
           |                    "gte": "${startTimeStamp}",
           |                    "lt": "${endTimeStamp}"
           |                  }
           |                }
           |              },
           |              {
           |                "range": {
           |                  "duke_optime": {
           |                    "gte": "${startTimeStamp}",
           |                    "lt": "${endTimeStamp}"
           |                  }
           |                }
           |              },
           |              {
           |                "range": {
           |                  "audit_time": {
           |                    "gte": "${startTimeStamp}",
           |                    "lt": "${endTimeStamp}"
           |                  }
           |                }
           |              }
           |            ]
           |          }
           |        }
           |      ]
           |    }
           |  }
           |}
           |""".stripMargin
    }
    println(s"查询的索引是：$indexNames")
    println(s"查询的es条件是：$query")
    CommonUtils.ElasticSearchOperate(spark,indexNames,PropertiesUtils.ESDukeBanQuanProperties,query)
  }
}
