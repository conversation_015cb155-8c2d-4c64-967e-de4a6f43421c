package com.baidu.sql.customized.keyproduct.im

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import com.baidu.sql.customized.keyproduct.im.ImKeypointFeedback.{robot_eva_sloved, robot_eva_stat}
import com.baidu.sql.utils.SparkUtils.unionIfNotEmpty
import com.baidu.sql.utils.TimeFormat.SEC_FORMAT_MYSQL
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils, TimeOperateUtil}
import okhttp3._
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.SparkConf
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}

import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDateTime, ZoneId}
import java.util.Base64
import scala.collection.mutable
import scala.collection.mutable.{ArrayBuffer, ListBuffer}
import scala.util.control.Breaks.{break, breakable}

/**
 * <AUTHOR>
 * @date date 2025/8/11
 * @time 11:00
 * @package_name com.baidu.sql.customized
 * @description 描述： 百度地图业务线，获取用户对话级别的标准问和答案数据
 */
object ImFollowMapStandardEs extends Serializable {

  val indexName = "metis_access_rt_release"

  //产品线名称，处理前，处理后名称。
  var productNameMap: Map[Long,String] = Map(
    4L -> "百度地图"
  )

  var contentMap: collection.Map[Long, String] = null

  //开始时间 20241228
  var startTimeStamp:Long = 0L
  //结束时间 20241229
  var endTimeStamp:Long = 0L
  //结束时间天数+1 20241230
  var endTomorrowTimeStamp :Long = 0L

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * */

    // 分区时间
    val YesterDay: String = args(0)
    // 获取YesterDay豪秒级时间戳
    startTimeStamp = TimeOperateUtil.getTimeStamp(YesterDay)
    endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))
    endTomorrowTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,2))

    println(s"开始时间戳为：$startTimeStamp")
    println(s"结束时间戳为：$endTimeStamp")

    val sparkConf = new SparkConf().setAppName("IMReadEs")
    //sparkSession配置
    val spark = new SparkSession
      .Builder()
      //.master("local")
      //.config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.debug.maxToStringFields", "200")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    spark.sparkContext.setLogLevel("WARN")

    //转成id -> name的映射
    val robotMapDf = getAutoContentIdMap(spark)
      .coalesce(1)
      .cache()
    println("获取机器人内容映射表为：" + robotMapDf.count())
    robotMapDf.createOrReplaceTempView("robot_map_data")

    //获取新建数据
    val createDf = dealWithEsData(spark,"新增")
      .withColumn("icafe_current_status", lit("新增"))
      .select("im_session_id","zx_session_details")
      //.filter(col("im_session_id") === "__METIS_IM__-580594268_4-4_2001042_3000496_4000791_5001067-1754646429249YmAhwp")
    println("获取新建的数据为：" + createDf.count())
    //createDf.show(false)

    //获取修改数据
    val updateDf = dealWithEsData(spark,"修改")
      .withColumn("icafe_current_status", lit("修改"))
    println("获取修改的数据为：" + updateDf.count())

    val combinedDF = unionIfNotEmpty(createDf,updateDf)
    println(s"合并后的数据量为${combinedDF.count()}条")
    combinedDF.createOrReplaceTempView("combined_data")

    combinedDF.createOrReplaceTempView("res_com_data")

    val sessionDetailsDf = spark.sql(
      """
        |select
        | im_session_id,
        | explode(split(zx_session_details,'<=>')) as session_details
        |from res_com_data
        |""".stripMargin)

    //users,standardId,standardContent,answer,answer_time,feedbackType,productType,downvoteResult,downvoteTime,content
    val operSplitDf = sessionDetailsDf
      .withColumn("session_list",split(col("session_details"),"=>"))
      //操作人
      .withColumn("users",getNullVal(col("session_list").getItem(0).cast("string")))
      //标准问id
      .withColumn("standard_id",getNullVal(col("session_list").getItem(1).cast("string")))
      //标准问内容
      .withColumn("standard_name",getNullVal(col("session_list").getItem(2).cast("string")))
      //答案时间
      .withColumn("answer_time",getNullVal(col("session_list").getItem(3).cast("string")))
      //反馈类型
      .withColumn("feedback_type",getNullVal(col("session_list").getItem(4).cast("string")))
      //产品类型
      .withColumn("access_type",getNullVal(col("session_list").getItem(5).cast("string")))
      //点踩结果
      .withColumn("updown_result",getNullVal(col("session_list").getItem(6).cast("string")))
      //点踩时间
      .withColumn("updown_time",getNullVal(col("session_list").getItem(7).cast("string")))
      //回复内容,答案
      .withColumn("answer_content",getNullVal(col("session_list").getItem(8).cast("string")))
      .filter(col("answer_content") =!= "关联问数据")
      .drop("session_details","session_list")
      .dropDuplicates()
      .coalesce(1)

    println("用户对话拆分后的数据条数：" + operSplitDf.count())
    //operSplitDf.show(5,false)
    operSplitDf.createOrReplaceTempView("res_data")

    val sql =
      s"""
         |insert overwrite table udw_ns.default.help_ods_follow_map_standard_data partition (event_day = $YesterDay )
         |select
         |  r.im_session_id,
         |  m.robot_id,
         |  m.robot_name,
         |  r.standard_id,
         |  r.standard_name,
         |  r.answer_content,
         |  r.answer_time,
         |  r.updown_result,
         |  r.updown_time,
         |  r.access_type,
         |  r.feedback_type
         |from res_data r
         |inner join robot_map_data m on r.standard_id = m.standard_id
         |""".stripMargin

    spark.sql(sql)
    println("数据写入完成")

    spark.close()
  }

  /**
   * 获取会话内容
   */
  val getSessionDetail = udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      var productLine = ""
      data.foreach(elem => {
        val normalTalk = JSON.parseObject(elem)
        try {
          val talkType = normalTalk.getString("talkType")
          //记录类型
          var users = ""
          //记录时间/评价时间,格式：yyyy-MM-dd HH:mm:ss
          var answer_time = getDateTime(normalTalk.getString("time"),SEC_FORMAT_MYSQL)
          //客服账号/满意度
          var userName = ""
          //是否解决
          var status = ""
          //机器人ID
          var robotId = ""
          //机器人名称
          var robotName = ""
          //标准问ID
          var standardId = ""
          //标准问内容
          var standardContent = ""
          //点踩结果
          var downvoteResult = ""
          //点踩时间
          var downvoteTime = ""
          //产品问题分类
          var productType = ""
          //反馈类型
          var feedbackType = ""
          //回复内容,答案
          var content = ""
          //获取用户对话记录数据NORMAL_TALK类型
          if (talkType != null && talkType != "" && ("NORMAL_TALK".equals(talkType) || "EVENT_TALK".equals(talkType))) {
            val userInfo = normalTalk.getJSONObject("userInfo")
            if (userInfo != null && !userInfo.isEmpty) {
              if ("__METIS_IM__".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") != true) {
                users = "【用户回复】"
              } else if ("metis-bot".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") == true) {
                users = "【metis-bot】"
              } else if ("系统客服".equals(userInfo.getString("showName")) && userInfo.getBoolean("isBot") == true) {
                users = "【系统客服】"
              } else if ("follow".equals(userInfo.getString("sysName"))) {
                users = "【人工客服】"
                userName = userInfo.getString("userName")
              }
            }
            //获取标准问数据
            val talkExtra = normalTalk.getString("extra")
            if (talkExtra != null && JSONUtil.isJson(talkExtra.replace("\\\"", "\""))) {
              val standardInfo = JSON.parseObject(talkExtra)
              if (standardInfo != null && !standardInfo.isEmpty) {
                if (standardInfo.containsKey("standardId")) {
                  standardId = standardInfo.getString("standardId")
                }
                if (standardInfo.containsKey("standardContent")) {
                  standardContent = standardInfo.getString("standardContent")
                }
              }
            }
            //对话级别：talks.talkContext.feedbackTypeLabel、questionCategory
            if (normalTalk.containsKey("talkContext")){
              val talkContext = normalTalk.getJSONObject("talkContext")
              if (talkContext != null && !talkContext.isEmpty) {
                if (talkContext.containsKey("feedbackTypeLabel")) {
                  feedbackType = talkContext.getString("feedbackTypeLabel").replace("\\\"", "\"")
                }
                if (talkContext.containsKey("questionCategory")) {
                  productType = talkContext.getString("questionCategory").replace("\\\"", "\"")
                }
              }
            }
            //点赞和点踩结果获取
            val evaluateOn = normalTalk.getString("evaluateOn")
            val evaluateInfo = normalTalk.getJSONObject("evaluateInfo")
            if (evaluateOn == "true" && evaluateInfo != null && !evaluateInfo.isEmpty) {
              val extraObject = evaluateInfo.getString("evaluateResult")
              if(extraObject != null && extraObject != ""){
                val eval = JSON.parseObject(extraObject).getString("evaluation")
                //计算11的数量是点赞数，9的数量是点踩数
                //println(s"evaluation的值是：${eval}")
                downvoteResult = eval match {
                  case "11" => "点赞"
                  case "9" => "点踩"
                  case _ => ""
                }
                //获取点踩时间
                downvoteTime = getDateTime(evaluateInfo.getString("evaluateTime"),SEC_FORMAT_MYSQL)
              }
            }

            //具体的对话内容直接硬解富卡，有的text是空,有的bailingRichCard是空，是个坑
            val bailingRichCard = normalTalk.getString("bailingRichCard")
            breakable {
              if (bailingRichCard == null || !JSON.parseObject(bailingRichCard).containsKey("content")) {
                content = if (normalTalk.getString("text") == null) "" else normalTalk.getString("text")
                break
              }else if (normalTalk.containsKey("text") && normalTalk.getString("text") != null && !bailingRichCard.contains("clarifyList")){
                content = normalTalk.getString("text")
                break
              }else {
                val tent = JSON.parseObject(bailingRichCard).getString("content")
                if (tent != null || !(tent.isEmpty)) {
                  val contentJson = JSON.parseObject(tent)
                  val types = contentJson.getString("type")
                  if ("html".equals(types)) {
                    val data = contentJson.getString("data")
                    content = if (data == null) "" else data.replaceAll("<.*?>", "")
                  } else if ("card".equals(types)) {
                    val dataJson = contentJson.getJSONObject("data")
                    if (dataJson == null || dataJson.isEmpty) {
                      content = ""
                      break
                    }
                    val array = dataJson.getJSONArray("content")
                    if (array == null || array.isEmpty) {
                      content = ""
                      break
                    } else {
                      for (item <- 0 to array.size() - 1) {
                        val cardContent = array.getJSONObject(item)
                        val cardType = cardContent.getString("type")
                        if ("html".equals(cardType) || "text".equals(cardType)) {
                          val valuestr = cardContent.getString("value")
                          content = if (valuestr == null) "" else valuestr.replaceAll("<.*?>", "")
                        }else if ("clarifyList".equals(cardType)){
                          content = "关联问数据"
                        }else {
                          //其他类型富卡直接转json
                          content = cardContent.toString()
                        }
                      }
                    }
                  }
                }
              }
            }
          }else if (talkType != null && talkType != "" && "SESSION_EVALUATE".equals(talkType)){
            val evaluateInfo = normalTalk.getJSONObject("evaluateInfo")
            if (evaluateInfo != null && !evaluateInfo.isEmpty) {
              if (evaluateInfo.containsKey("evaluated")) {
                //是否被评价
                val evaluated = evaluateInfo.getString("evaluated")
                if ("true".equals(evaluated)) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    //评价结果
                    val evaluateResult = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResult != null && !evaluateResult.isEmpty) {
                      if (evaluateResult.containsKey("robot_eva_sloved")) {
                        //是否解决
                        val score = evaluateResult.getString("robot_eva_sloved")
                        status = robot_eva_sloved.getOrElse(score, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_stat")) {
                        //评价得分
                        val stat = evaluateResult.getString("robot_eva_stat")
                        userName = stat + robot_eva_stat.getOrElse(stat, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_diss_content")) {
                        //服务评价
                        content = evaluateResult.getString("robot_eva_diss_content")
                      }
                      if (evaluateResult.containsKey("robot")) {
                        //机器人评价
                        if (evaluateResult.getBoolean("robot") == true) users = "【机器人评价】" else users = "【人工评价】"
                      }
                    }
                  }
                }
              }
            }
          }
          //满足条件的数据才接入
          if (users != ""){
            if (users == "【机器人评价】" || users == "【metis-bot】"){
              res.append(Seq(users,standardId,standardContent,answer_time,feedbackType,productType,downvoteResult,downvoteTime,content).mkString("=>"))
            }else{
              ""
            }
          }
        } catch {
          //case e:Exception => println(s"buildExportSessionTalkContent fail imSessionId:" + normalTalk.getString("imSessionId") + "talkSeqId:"  + normalTalk.getString("seqId"))
          case e:Exception => println(s"session_id:" + normalTalk.getString("imSessionId") + ",error:"  + e.getMessage)
        }
      })
      res.mkString("<=>")
    }
  })

  /**
   * 获取智能客服内容id
   */
  val getAutoContentId = functions.udf((data: mutable.WrappedArray[String],dataType:String) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      val res = new ListBuffer[String]()
      data.foreach(elem => {
        try {
          val jsonObject = JSON.parseObject(elem)
          if (jsonObject != null && jsonObject.containsKey("extra")) {
            val extra = jsonObject.getJSONObject("extra")
            if (extra != null && !extra.isEmpty) {
              if (dataType.equals("answerId")){
                if (extra.containsKey("answerId")) {
                  val answerIdStr: Long = extra.getLong("answerId")
                  if (answerIdStr != null && answerIdStr >= 0) {
                    val content = contentMap.getOrElse(answerIdStr,answerIdStr).toString
                    if (!res.contains(content) && content != "") {
                      res.append(content)
                    }
                  }
                }
              }else if (dataType.equals("standardId")) {
                if (extra.containsKey("standardId")) {
                  val answerIdStr = extra.getString("standardId")
                  if (answerIdStr != null && answerIdStr >= "0") {
                    res.append(answerIdStr)
                  }
                }
              }
            }
          }
        } catch {
          case e:Exception => println(" getAutoContentId error:" + e.getMessage())
        }
      })
      res.toSet.mkString(",")
    }
  })


  /**
   * 获取智能客服评价等内容
   */
  val getEvaluateData = functions.udf((data: mutable.WrappedArray[String],dataType:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        if (JSONUtil.isJson(elem)) {
          val jsonObject = JSON.parseObject(elem)
          if (jsonObject != null &&
            jsonObject.containsKey("talkType") &&
            jsonObject.getString("talkType").equals("SESSION_EVALUATE") &&
            jsonObject.containsKey("evaluateInfo") &&
            !jsonObject.getJSONObject("evaluateInfo").isEmpty) {
              val evaluateInfo = jsonObject.getJSONObject("evaluateInfo")
              try {
                if (dataType.equals("is_solve")) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    val evaluateResultObj = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResultObj != null && !evaluateResultObj.isEmpty && evaluateResultObj.containsKey("robot")) {
                      val robot = evaluateResultObj.getBoolean("robot")
                      if (!robot) {
                        val robot_eva_sloved = evaluateResultObj.getString("robot_eva_sloved")
                        res = robot_eva_sloved match { // 使用Java 12+的switch表达式
                          case "1" => "已解决"
                          case "2" => "未解决"
                          case "3" => "持续关注"
                          case _ => ""
                        }
                      }
                    }
                  }
                }else if(dataType.equals("evaluate_csi")) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    val evaluateResultObj = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResultObj != null && !evaluateResultObj.isEmpty && evaluateResultObj.containsKey("robot")) {
                      val robot = evaluateResultObj.getBoolean("robot")
                      if (!robot) {
                        //用户评价
                        res = evaluateResultObj.getString("robot_eva_stat")
                      }
                    }
                  }
                }else if(dataType.equals("user_evaluate")) {
                  if ((jsonObject.containsKey("from") &&
                    !jsonObject.getJSONObject("from").isEmpty &&
                    jsonObject.getJSONObject("from").getString("sysName").equals("follow"))
                    ||
                    (evaluateInfo.containsKey("evaluateResult") &&
                      !evaluateInfo.getJSONObject("evaluateResult").isEmpty &&
                      evaluateInfo.getJSONObject("evaluateResult") != null &&
                      evaluateInfo.getJSONObject("evaluateResult").containsKey("robot") &&
                      evaluateInfo.getJSONObject("evaluateResult").getBoolean("robot") == false)) {
                        val evaluated = evaluateInfo.getString("evaluated")
                        res = evaluated match {
                          case "true" => "已评价"
                          case "false" => "未评价"
                          case _ => ""
                        }
                      }
                } else if(dataType.equals("robot_evaluate")) {
                  if ((jsonObject.containsKey("from") &&
                    !jsonObject.getJSONObject("from").isEmpty &&
                    jsonObject.getJSONObject("from").getString("sysName").equals("__METIS_IM__"))
                    ||
                    (evaluateInfo.containsKey("evaluateResult") &&
                      !evaluateInfo.getJSONObject("evaluateResult").isEmpty &&
                      evaluateInfo.getJSONObject("evaluateResult") != null &&
                      evaluateInfo.getJSONObject("evaluateResult").containsKey("robot") &&
                      evaluateInfo.getJSONObject("evaluateResult").getBoolean("robot") == true)) {
                    val evaluated = evaluateInfo.getString("evaluated")
                    res = evaluated match {
                      case "true" => "已评价"
                      case "false" => "未评价"
                      case _ => ""
                    }
                  }
                }else {
                  res = ""
                }
              } catch {
                //case e:Exception => println("evaluateInfo:" + evaluateInfo.toString() + "getEvaluateData error:" + e.getMessage())
                case e:Exception => null
              }
           }
        }
      })
      res
    }
  })

  /**
   * 获取智能客服 CSI  FCR
   */
  val getAutoContentRobot = functions.udf((data: String,types:String) => {
    if (data == null || data.isEmpty || data == "" || Option(data).getOrElse("") == "") {
      ""
    } else {
      var res = ""
      val jsonObject = JSON.parseObject(data)
      val array = jsonObject.keySet().toArray.sortBy(x => x.toString.toInt)
      for (key <- array){
        val sessionjSON = jsonObject.getJSONObject(key.toString)
        val evaluateResult = sessionjSON.getString("evaluateResult")
        //判断level==SESSION_LEVEL，evaluated为true，evaluateResult不为空
        if (sessionjSON.getString("level") == "SESSION_LEVEL" && sessionjSON.getBoolean("evaluated") == true && evaluateResult != null && evaluateResult != ""){
          val resultJson = JSON.parseObject(evaluateResult)
          if(resultJson.getBoolean("robot") == true){
            if (types.equals("CSI")){
              res = resultJson.getString("robot_eva_stat") match {
                case "1" => "非常不满意"
                case "2" => "不满意"
                case "3" => "一般"
                case "4" => "满意"
                case "5" => "非常满意"
                case _ => "其他"
              }
            }else if (types.equals("FCR")){
              res = resultJson.getString("robot_eva_sloved") match {
                case "1" => "解决"
                case "2" => "未解决"
                case "3" => "持续关注"
                case _ => "其他"
              }
            }
            else if (types.equals("智能标签")){
              if(resultJson.containsKey("robot_eva_stat_label") && resultJson.getJSONArray("robot_eva_stat_label").size() > 0){
                res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
              }
            }
            else if (types.equals("智能文本")){
              res = resultJson.getOrDefault("robot_eva_advise","").toString
            }
          }else if (resultJson.getBoolean("robot") == false){
            if (types.equals("人工标签")){
              if(resultJson.containsKey("robot_eva_stat_label") && resultJson.getJSONArray("robot_eva_stat_label").size() > 0) {
                res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
              }
            }else if (types.equals("人工文本")){
              res = resultJson.getOrDefault("robot_eva_advise","").toString
            }
          }
        }
      }
      res
    }
  })

  /**
   * 点赞数、点踩数，文心回答数量等
   * types:传入的类型，普通是获取点赞数和点踩数，文心是获取关于文心的点赞数等等
   * value：json对应的值，11是点赞数，9是点踩数
   */
  val getEvaluateDesc = functions.udf((data: mutable.WrappedArray[String],types:String,value:String) => {
    //    println(data)
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = 0
      if(types.equals("普通")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          val evaluateOn = jsonObject.getString("evaluateOn")
          val evaluateInfo = jsonObject.getString("evaluateInfo")
          if (evaluateOn == "true" && evaluateInfo != null && evaluateInfo != "") {
            val extraObject = JSON.parseObject(evaluateInfo).getString("evaluateResult")
            if(extraObject != null && extraObject != ""){
              val eval = JSON.parseObject(extraObject).getString("evaluation")
              //计算11的数量是点赞数，9的数量是点踩数
              //println(s"evaluation的值是：${eval}")
              if (eval == value){
                res += 1
              }
            }else{
              ""
            }
          }else{
            ""
          }
        })
      }else if (types.equals("文心")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          if(jsonObject.containsKey("ouserEvaluateInfo")){
            val ouserEvaluateInfo = jsonObject.getString("ouserEvaluateInfo")
            if (ouserEvaluateInfo != null && ouserEvaluateInfo != "") {
              val ouserEvaluateInfoJson = JSON.parseObject(ouserEvaluateInfo)
              if(ouserEvaluateInfoJson.containsKey("ouserEvaluateRet")){
                val ouserEvaluateRet = ouserEvaluateInfoJson.getString("ouserEvaluateRet")
                //GOOD：文心回答正确数量，BAD：文心回答错误数量，未回答：文心未回答数量，点踩明细：文心回答错误数量
                if (ouserEvaluateRet.equals(value)){
                  res += 1
                }
              }
            }
          }
        })
      }
      if (res.toString == "0") "" else res.toString
    }
  })

  /**
   * 列表数据处理
   */
  val getListVal = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      data.foreach(row => {
        if(row.startsWith("[") && row.endsWith("]")){
          res.append(row.replaceAll("\"","").replaceAll("\\[","").replaceAll("]","").split(",").mkString("/"))
        }else{
          res.append(row.replaceAll("[\\t|\\r]","").replaceAll("\\n"," ").replaceAll("\"",""))
        }
      })
      res.mkString(",")
    }
  })


  /**
   * 获取产品问题类型
   */
  val getProductPro = functions.udf((data: String,index: Int) => {
    if (data == null || data.isEmpty || data == ",," || data == "--") {
      ""
    } else {
      val str = data.replaceAll("\\[","").replaceAll("]","")
        .replaceAll(" ","")
        .replaceAll("\"","").replaceAll("[\\t|\\r]","")
        .replaceAll("\\n"," ").split(",").applyOrElse(index, (x: Int) => "")
      str
    }
  })

  /**
   * 处理ES字段逻辑
   * @param spark
   * @param resDfList extend_feedback_channel 数据
   * @param sessionDf 计算会话数据
   * @param isDefault 新增/修改
   * @return
   */
  def dealWithEsData(spark: SparkSession,isDefault:String): DataFrame = {
    import spark.implicits._
    val queryCol = isDefault match {
      case "新增" => ("createTime","create_time")
      case "修改" => ("obuzFollow.icafeLastPullTime","update_time")
    }

    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    //循环所有产品线
    for (product <- productNameMap) {
      val productId = product._1.toString
      //处理后的产品线名称
      val productName = product._2
      //通过EsSpark读取Es数据
      val rdd = getEsData(spark, productId, queryCol._1)
      //定义读取Es后的数据schema
      val schema = new StructType()
        .add("data", StringType)

      // 使用SparkSession创建DataFrame，row._2是所有Json字符串的数据内容
      val df = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)

      // 解析字段sql  这里把接口无法取出的字段做处理
      val tempDf = df
        //产品线id
        .withColumn("product_id",lit(productId))
        //在线的session_id
        .withColumn("im_session_id",
          getNullVal(get_json_object(col("data"), "$.imSessionId")
            .cast("String")))
        //创建时间
        .withColumn("answer_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.createTime")
            .cast("String"))))
        //更新时间
        .withColumn("update_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.obuzFollow.updateTime")
            .cast("String"))))
         // 智能客服内容id
        //会话标记，true为没用，null或者false为有用
        .withColumn("session_flag",
          getNullVal(get_json_object(col("data"), "$.startInfo.toServiceTalkSessionFlag"))
            .cast("String"))
        .withColumn("icafe_last_pull_time",
          getDateTimeVal(get_json_object(col("data"), "$.obuzFollow.icafeLastPullTime"))
            .cast("String"))
        //创建时间
        .withColumn("submit_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.createTime")
            .cast("String"))))
        //会话内容
        .withColumn("zx_session_details",getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType))))
        //会话内容(带文心分类标签)
        //.withColumn("session_details_label",getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心分类标签")))
        .drop("data")
        .dropDuplicates(Seq("im_session_id", "product_id"))

      resultDf.append(tempDf)
    }

    //将用户增长的手百主入口（任务中台）过滤掉
    val esDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2))
      //过滤掉会话标记为true的会话，即没用会话
      .filter($"session_flag" =!= "true")
      .dropDuplicates(Seq("im_session_id", "product_id"))
      .repartition(20)

    val esCnt = esDF.count()
    println("esDF的数量为：" + esCnt)

    if (esCnt > 0){
      // es获取到的数据 join url获取到的数据
      var combinedDF = esDF

      if (isDefault == "修改") {
        //如果是“修改”的数据，取修改时间和创建时间不为当天的
        combinedDF = combinedDF.filter(substring($"icafe_last_pull_time",1,11) =!= substring($"submit_time",1,11) )
      }
      combinedDF
    }else{
      spark.emptyDataFrame
    }
  }

  /**
   *  读取ES数据库数据
   * @param spark
   * @param productId 产品线ID
   * @param queryCol 查询过滤字段
   * @return
   */
  def getEsData(spark: SparkSession,
                        productId: String,
                        queryCol:String): RDD[(String, String)] = {

    val query =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "must": [
         |        {"bool":{
         |              "should": [
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
         |                    {"term": {"obuzFollow.productId": "${productId}"}}
         |              ]
         |            }
         |         },
         |        {
         |          "bool": {
         |            "must": [
         |              {
         |                "range": {
         |                  "${queryCol}": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    CommonUtils.ElasticSearchOperate(spark,indexName,PropertiesUtils.ESImProperties,query)
  }

  /**
   * 获取自动回复内容
   *
   * @return
   */
  def getAutoContentIdMap(spark: SparkSession): DataFrame = {
    val querySql =
      s"""
         |select
         |  r.id as robot_id,
         |  r.robot_name,
         |  s.id as standard_id
         |from
         |(select id,robot_name from robot where id in ('200','51','221','260','261','262','293','295')) r
         |inner join (select id,robot_id from standard where robot_id in ('200','51','221','260','261','262','293','295')) s
         |on r.id = s.robot_id
         |""".stripMargin

    val contentDf = CommonUtils.mysqlOperate(spark, PropertiesUtils.MysqlFollowIdProperties, querySql)
    contentDf
  }
}

