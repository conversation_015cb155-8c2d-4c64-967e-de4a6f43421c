package com.baidu.sql.customized.keyproduct.im

import com.baidu.sql.customized.usergroup.FengLingHive2EsSparkWrite.{deleteIndex, indexIsExsitsAlias, indexRemoveAlias, isExsitsIndex, supervisionIndexAlias}
import com.baidu.sql.utils.TimeOperateUtil.calcnDate
import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, udf}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest.AliasActions
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.admin.indices.flush.{FlushRequest, FlushResponse}
import org.elasticsearch.client.indices.{CreateIndexRequest, GetIndexRequest}
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.xcontent.XContentType
import org.elasticsearch.spark.sql.EsSparkSQL

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, LocalDateTime, ZoneId}
import scala.collection.JavaConverters.mapAsScalaMapConverter

/**
 * <AUTHOR> follow在线爱企查产品线写入ES库，代理型举报人数据
 * @date date 2025/2/17
 * @time 11:00
 * @package_name com.baidu.sql.customized
 */

object ImFollow2EsSparkWrite {
  val client: RestHighLevelClient = createElasticsearchClient()

  //传入的日期
  var yesterday :LocalDate = null

  //索引别名：public_follow_informer_index
  val informerIndexAlias = "public_follow_informer_index"

  // 数据源表：follow大宽表
  val followTable = "udw_ns.default.help_ods_ufo_key_point_productline_di"

  def main(args: Array[String]): Unit = {
    /*
    * 本任务主要实现功能为：
    * 新建存储20天内数据的es索引：
    * 举报人：public_follow_informer_index_2025_02_01 (日期使用运行任务日期)
    * 每日例行从hive取数，写入es新建的索引中
    * 将新刷完数的索引映射值固定值的别名上  别名
    * 举报人：public_follow_informer_index
    * 删除20天之外刷数的索引
    * 操作es有三种方案，采用1写入 其他操作采用2 1.使用spark-es(之前经验是厂内调度不支持) 2.使用esclient 3.直接发送请求
    * */

    // 日期参数
    val yesterdayStr = args(0)
    //30天前日期
    val thirtydayStr = calcnDate(yesterdayStr,-19)
    //31天前日期,需要删除的索引日期
    val deletedayStr = calcnDate(yesterdayStr,-20)

    // sparkConf
    val sparkConf = new SparkConf().setAppName("FengLingHive2Es")
    sparkConf.set("es.nodes","************")
    sparkConf.set("es.port","8200")
    sparkConf.set("es.net.http.auth.user" ,"superuser")
    sparkConf.set("es.net.http.auth.pass","metis_MegSed")
    sparkConf.set("es.nodes.wan.only","true")
    sparkConf.set("es.nodes.discovery","false")
    sparkConf.set("es.index.auto.create","false")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("spark.task.maxFailures", "5")
      .config("spark.driver.allowMultipleContexts", true)
      //.master("local[*]")
      .getOrCreate()

    val superOldIndexList = indexIsExsitsAlias(informerIndexAlias)
    for (oldIndex <- superOldIndexList) {
      if(isExsitsIndex(oldIndex)){
        // 删除旧索引的别名映射
        indexRemoveAlias(oldIndex, informerIndexAlias)
        // 删除旧索引
        deleteIndex(oldIndex)
        println("index" + oldIndex + " is exsit,deleteIndex:" + oldIndex)
      }
    }

    //读取hive的数据
    val dataDf = readHiveData(spark,thirtydayStr,yesterdayStr)

    //准备mapping
    val regulateMappingStr = generateMappingWithFields(dataDf)

    try{
      // 循环打印从昨天开始的21天内的日期
      for (i <- 0 to 19) {
        // 通过minusDays递减天数,得到当天的日期新索引日期
        val currentdateStr = calcnDate(yesterdayStr,-i)
        println("新建索引日期：" + currentdateStr)

        //数据处理
        dealWithEs(sparkConf,currentdateStr, i, dataDf,regulateMappingStr)
      }

    }catch {
      case e: Exception => e.printStackTrace()
    }finally {
      try {
        // 关闭 Elasticsearch 客户端
        client.close()
      } catch {
        case e: Exception => e.printStackTrace()
      }
      spark.close()
    }

  }

  // 获取时间戳
  val getTimestamp = udf((dateTimeStr:String) => {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    // 解析日期时间字符串为LocalDateTime对象
    val localDateTime: LocalDateTime = LocalDateTime.parse(dateTimeStr, formatter)
    // 将LocalDateTime对象转换为ZonedDateTime对象，使用系统默认时区
    val zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
    // 将ZonedDateTime对象转换为时间戳（毫秒）
    zonedDateTime.toInstant.toEpochMilli
  })

  /**
   * 读取hive数据并返回
   * @param spark
   * @param thirtydayStr 20天前日期
   * @param yesterdayStr 昨天日期
   * @param dataType 舆情/监管
   * @return
   */
  private def readHiveData(spark:SparkSession,thirtydayStr:String,yesterdayStr:String): DataFrame = {

    //hive数据读取
    val feelingsql =
      s"""
         |select
         |  product_line,
         |  userid,
         |  im_session_id,
         |  session_id,
         |  submit_time,
         |  zx_user_risk_label,
         |  event_day
         |from
         | $followTable
         |where event_day <= '$yesterdayStr'
         |and event_day >= '$thirtydayStr'
         |and channel = '在线'
         |and product_line = '爱企查'
         |and zx_user_risk_label = '代理型举报人'
         |""".stripMargin
    val resDF = spark.sql(feelingsql)
      .withColumn("userid", col("userid").cast("long"))
      .withColumn("submit_time", getTimestamp(col("submit_time")).cast("long"))

    println(s"读取${thirtydayStr}-${yesterdayStr}日期${followTable}的数据有：" + resDF.count())
    resDF.show(5)
    resDF
  }
  /**
   * 处理es数据
   * @param sparkConf
   * @param i        循环数
   * @param dataDf   读取的hive数据
   * @param dataType 舆情/监管
   */
  private def dealWithEs(sparkConf:SparkConf,currentdateStr:String, i:Int,dataDf:DataFrame,mapping:String): Unit = {
    // 索引名
    val indexName = informerIndexAlias + "_"
    // 索引别名 informerIndexAlias

    // 索引名拼接
    val IndexName = indexName + currentdateStr.substring(0, 4) +
      "_" + currentdateStr.substring(4, 6) +
      "_" + currentdateStr.substring(6, 8)

    sparkConf.set("es.resource", IndexName)

    // 如果该索引已经存在 则删除该索引
    if (isExsitsIndex(IndexName)) {
      deleteIndex(IndexName)
    }
    //创建索引
    createIndex(IndexName,mapping)

    // 数据写入
    val feelingTable = dataDf.filter(col("event_day") === currentdateStr).repartition(5)
    EsSparkSQL.saveToEs(feelingTable,IndexName,Map("es.mapping.id"->"userid"))

    //刷新写入数据的索引
    flushIndex(IndexName)

    // 移除旧的别名映射 移除之前先判断旧的索引是否存在 以及别名是否为uid_user_tag1_index
    if (isExsitsIndex(IndexName)&&indexIsExsitsAlias(IndexName).contains(informerIndexAlias)){
      indexRemoveAlias(IndexName, informerIndexAlias)
    }

    // 建立新的别名映射
    indexUpdateAlias(IndexName, informerIndexAlias)
    println("建立别名映射" + informerIndexAlias + "成功")
  }

  /**
   * * 删除旧索引
   * @param deletedayStr 删除的索引日期
   * @param dataType 舆情/监管
   */
  private def deleteOldIndex(deletedayStr: String): Unit = {
    // 索引名
    val indexName = informerIndexAlias + "_"

    // 旧索引名拼接
    val oldfIndexName = indexName + deletedayStr.substring(0, 4) + "_" + deletedayStr.substring(4, 6) +
      "_" + deletedayStr.substring(6, 8)

    // 旧索引删除 先判断旧索引是否存在
    if (isExsitsIndex(oldfIndexName)) {
      deleteIndex(oldfIndexName)
      println("index" + oldfIndexName + " is exsit,deleteIndex:" + oldfIndexName)
    }
  }

  /**
   * 创建es客户端
   * @return
   */
  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient = RestClient.builder(new HttpHost("************", 8200, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("superuser", "metis_MegSed"))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)
  }

  /**
   * Generate the mapping for the given fields.循环生成所有字段的索引
   * @param dataFrame 数据集
   * @return
   */
  def generateMappingWithFields(dataFrame: DataFrame): String = {
    //准备mapping
    val dataColumns = dataFrame.columns
    //循环获取列名和类型,形成ES的mapping
    val fieldNames = dataColumns.map(col => {
      if (col.equals("submit_time") || col.equals("userid")) {
        (col -> "long")
      } else {
        (col -> "text")
      }
    }).toList

    val baseMapping =
      """
        |{
        |  "settings": {
        |    "number_of_shards": 5,
        |    "number_of_replicas": 1
        |  },
        |  "mappings": {
        |    "properties": {
        |    }
        |  }
        |}
    """.stripMargin

    val propertiesBuilder = new StringBuilder()
    fieldNames.foreach { fieldName =>
      //按照字段类型来匹配不同的mapping
      fieldName._2 match {
       case "text" =>
                      propertiesBuilder.append(s"""
                        |      "${fieldName._1}": {
                        |        "type": "text",
                        |        "fields": {
                        |          "keyword": {
                        |            "type": "keyword",
                        |            "ignore_above": 256
                        |          }
                        |        }
                        |      },
                    """.stripMargin)
       case "long" =>
                     propertiesBuilder.append(s"""
                         |      "${fieldName._1}": {
                         |        "type": "long"
                         |      },
                    """.stripMargin)
       case _ => ""
      }
    }

    // Remove the trailing comma移除尾随逗号
    while (propertiesBuilder.nonEmpty && propertiesBuilder.last != ','){
      propertiesBuilder.deleteCharAt(propertiesBuilder.length - 1)
    }
    propertiesBuilder.deleteCharAt(propertiesBuilder.length - 1)

    // 替换掉baseMapping中properties的空值
    val finalMapping = baseMapping.replace(
      """|    "properties": {
         |    }""".stripMargin,
      s"""|    "properties": {
          |${propertiesBuilder.toString()}
          |    }""".stripMargin
    )

    finalMapping
  }

  /**
   * 创建索引
   * @param indexName 索引名
   * @param fieldNames 字段列表
   * @return
   */
  private def createIndex(indexName:String,mappingStr:String): Boolean = {
    //判断索引是否存在
    val request = new CreateIndexRequest(indexName)
    //准备request对象
    request.source(mappingStr,XContentType.JSON)
    //通过client去操作
    val createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT)
    //检查响应结果
    createIndexResponse.isAcknowledged
  }


  /**
   * 判断索引是否存在
   * @param indexName 索引名
   * @return
   */
  private def isExsitsIndex(indexName: String): Boolean = {
    //准备request对象
    val myrequest:GetIndexRequest = new GetIndexRequest(indexName)
    //通过client去操作
    val  myresult :Boolean= client.indices().exists(myrequest, RequestOptions.DEFAULT)
    //结果
    myresult
  }


  /**
   * 刷新索引
   * @param indexName 索引名
   */
  private def flushIndex(indexName: String): Unit = {
    //准备request对象
    val myFlushRequest: FlushRequest = new FlushRequest(indexName)
    //通过client去操作
    val myFlushResponse: FlushResponse = client.indices().flush(myFlushRequest,RequestOptions.DEFAULT)
    val totalShards = myFlushResponse.getTotalShards
    println("index: "+ indexName +" has"+ totalShards +"flush over! ")
  }

  /**
   * 判断索引的别名是否符合预期
   * @param indexAlias
   * @return
   */
  private def indexIsExsitsAlias(indexAlias: String): List[String] = {
    // 别名信息获取对象
    val getAliasesRequest = new GetAliasesRequest(indexAlias)
    // 获取别名信息
    val getAliasesResponse = client.indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT)
    val Aliases = getAliasesResponse.getAliases.asScala
    val res = Aliases.map(x => x._1).toList.sorted
    res
  }


  /**
   * 删除老的别名映射
   * @param indexName
   * @param indexAlias
   * @return
   */
  private def indexRemoveAlias(indexName:String,indexAlias:String): Boolean = {
    //删除老的index别名映射
    val request = new IndicesAliasesRequest()
    val aliasAction = new AliasActions(AliasActions.Type.REMOVE)
      .index(indexName)
      .alias(indexAlias)

    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)
    // 检查响应结果
    if (response.isAcknowledged) {
      println("旧索引的别名删除成功")
    } else {
      println("旧索引的别名删除失败")
    }
    response.isAcknowledged
  }


  /**
   * * 建立新的别名映射
   * @param indexName
   * @param indexAlias
   * @return
   */
  private def indexUpdateAlias(indexName: String,indexAlias:String): Boolean = {
    // 请求建立新的别名映射
    val request = new IndicesAliasesRequest()

    val aliasAction = new AliasActions(AliasActions.Type.ADD)
      .index(indexName)
      .alias(indexAlias)
    request.addAliasAction(aliasAction)
    val response = client.indices().updateAliases(request, RequestOptions.DEFAULT)

    // 检查响应结果
    if (response.isAcknowledged) {
      println("新索引的别名设置成功")
    } else {
      println("新索引的别名设置失败")
    }
    response.isAcknowledged
  }

  /**
   * 删除索引
   * @param indexName
   * @return
   */
  private def deleteIndex(indexName: String): Boolean = {
    val myDeleteIndexRequest = new DeleteIndexRequest()
    myDeleteIndexRequest.indices(indexName)
    val acknowledgedResponse =
      client
      .indices()
      .delete(myDeleteIndexRequest, RequestOptions.DEFAULT)
    acknowledgedResponse.isAcknowledged
  }

}
