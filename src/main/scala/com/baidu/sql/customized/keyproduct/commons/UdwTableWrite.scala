package com.baidu.sql.customized.keyproduct.commons

import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

object UdwTableWrite {


  /**
   * 直接获取udw表现有的数据，将需要的部分留下覆盖进原分区，不用重新跑程序消耗资源
   */
  def main(args: Array[String]): Unit = {
    // 分区时间
    val yesterDay: String = args(0)
    //第二个参数，将除了此渠道的数据，数仓数据读取需要的数据覆盖原分区
    val channel = args(1) match {
      case "zaixian" => "在线"
      case "rexian" => "热线"
      case "chizhi" => "迟滞"
      case "fengling" => "风铃"
      case "waiyi" => "外溢"
      case "relation" => "用户关联"
      case "userlabel" => "用户标签"
      //删除分区
      case "drop" => "dropPartition"
      case _ => args(1)
    }
    println(s"开始读取${channel}渠道以外的数据，覆盖原分区")

    //第三个参数，表名
    val tableName = args(2) match {
      case "help_ods_ufo_key_point_productline_di" => "udw_ns.default.help_ods_ufo_key_point_productline_di"
      case "help_ods_fengling_sentiment" => "udw_ns.default.help_ods_fengling_sentiment"
      case "help_ods_ufo_service_details" => "udw_ns.default.help_ods_ufo_service_details"
      case "help_ods_crcc_user_relation" => "udw_ns.default.help_ods_crcc_user_relation"
      case "help_ods_crcc_userlabel_data" => "udw_ns.default.help_ods_crcc_userlabel_data"
      case _ => "null"
    }
    println(s"表名：${tableName}")

    //sparkSession配置
    val sparkConf = new SparkConf().setAppName("udw_overwrite")
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val sc = spark.sparkContext
    sc.setLogLevel("WARN")

    if (tableName.equals("udw_ns.default.help_ods_ufo_key_point_productline_di")) {
      val result = spark.sql(
        s"""select
           |  *
           |from
           |  ${tableName}
           |where
           |  channel != '${channel}'
           |  and event_day = ${yesterDay}""".stripMargin)

      println(s"读取help_ods_ufo_key_point_productline_di表${yesterDay}日期，除${channel}渠道的数据一共${result.count()}条数据")
      result.drop("event_day").createOrReplaceTempView("tmp_udw_data")

      spark.sql(
        s"""
           |insert overwrite table udw_ns.default.help_ods_ufo_key_point_productline_di partition (event_day = ${yesterDay} )
           |select * from tmp_udw_data """.stripMargin)

    } else if(tableName.equals("udw_ns.default.help_ods_fengling_sentiment")) {

      println(s"外溢渠道，开始删除${yesterDay}分区")
      spark.sql("ALTER TABLE udw_ns.default.help_ods_fengling_sentiment DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")

    }else if(tableName.equals("udw_ns.default.help_ods_ufo_service_details")){
      val spaceId = channel.replace(",","','")

      val result = spark.sql(
        s"""select
           |  *
           |from
           |  ${tableName}
           |where
           |  space_id not in ('${spaceId}')
           |  and event_day = ${yesterDay}""".stripMargin)

      println(s"读取help_ods_ufo_service_details表${yesterDay}日期，除${channel}渠道的数据一共${result.count()}条数据")
      result.drop("event_day").createOrReplaceTempView("tmp_udw_data")

      spark.sql(
        s"""
           |insert overwrite table ${tableName} partition (event_day = ${yesterDay} )
           |select * from tmp_udw_data """.stripMargin)
    }else if(tableName.equals("udw_ns.default.help_ods_crcc_user_relation")){
      if(channel.equals("dropPartition")){
        println(s"开始删除help_ods_crcc_user_relation 表的${yesterDay}分区")
        spark.sql("ALTER TABLE udw_ns.default.help_ods_crcc_user_relation DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")
      }else{
        //数据源表
        val source = channel.replace(",","','")

        val result = spark.sql(
          s"""select
             |  *
             |from
             |  ${tableName}
             |where
             |  source not in ('${source}')
             |  and event_day = ${yesterDay}""".stripMargin)

        println(s"读取help_ods_crcc_user_relation表${yesterDay}日期，除${channel}渠道的数据一共${result.count()}条数据")
        result.drop("event_day").createOrReplaceTempView("tmp_udw_data")

        spark.sql(
          s"""
             |insert overwrite table ${tableName} partition (event_day = ${yesterDay} )
             |select * from tmp_udw_data """.stripMargin)
      }
    } else if(tableName.equals("udw_ns.default.help_ods_crcc_userlabel_data")) {
      if(channel.equals("dropPartition")){
        println(s"开始删除help_ods_crcc_userlabel_data 表的${yesterDay}分区")
        spark.sql("ALTER TABLE udw_ns.default.help_ods_crcc_userlabel_data DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")
      }else{
        val sourceMap = Map(
          "tieba" -> "贴吧",
          "shoubai" -> "手百",
          "bjh" -> "百家号",
          "search" -> "搜索",
          "relation" -> "用户关联表",
          "bjh_appid" -> "百家号_竞品情报"
        )

        //数据源表
        val source =
          if (channel.contains(",")){
            channel.split(",").map(x => sourceMap(x)).mkString("','")
          }else{
            sourceMap(channel)
          }

        val result = spark.sql(
          s"""select
             |  *
             |from
             |  ${tableName}
             |where
             |  source not in ('${source}')
             |  and event_day = ${yesterDay}""".stripMargin)

        println(s"读取help_ods_crcc_userlabel_data表${yesterDay}日期，除${channel}渠道的数据一共${result.count()}条数据")
        result.drop("event_day").createOrReplaceTempView("tmp_udw_data")

        spark.sql(
          s"""
             |insert overwrite table ${tableName} partition (event_day = ${yesterDay} )
             |select * from tmp_udw_data """.stripMargin)
      }
    }else{
      println("表名不匹配，请检查")
    }
  }
}
