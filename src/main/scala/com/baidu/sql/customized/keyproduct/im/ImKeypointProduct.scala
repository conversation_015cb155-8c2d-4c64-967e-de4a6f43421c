package com.baidu.sql.customized.keyproduct.im

import com.baidu.sql.customized.keyproduct.commons.EsConf
import org.apache.http.{HttpHost, HttpStatus}
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.spark.SparkConf
import org.apache.spark.sql.catalyst.dsl.expressions.StringToAttributeConversionHelper
import org.apache.spark.sql.functions.{col, expr, get_json_object, lit, when}
import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}
import org.apache.spark.sql.types.{DataTypes, StructType}
import org.elasticsearch.action.search.{ClearScrollRequest, ClearScrollResponse, SearchRequest, SearchResponse, SearchScrollRequest}
import org.elasticsearch.client.{Request, RequestOptions, ResponseException, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.unit.TimeValue
import org.elasticsearch.index.query.{BoolQueryBuilder, QueryBuilders}
import org.elasticsearch.search.{Scroll, SearchHit}
import org.elasticsearch.search.builder.SearchSourceBuilder

import java.io.IOException
import scala.collection.mutable.ListBuffer

/**
 * <AUTHOR>
 * @date date 2024/2/5
 * @time 14:12
 * @package_name com.baidu.sql.customized.im
 */
object ImKeypointProduct {
  val client: RestHighLevelClient = createElasticsearchClient()
  val indexName = "metis_access_rt_release"

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * */
    //开始时间
    val startTimeStamp: Long = 1708617600000l
    //结束时间
    val endTimeStamp: Long = 1708671600000l

    //spark
    val sparkConf = new SparkConf().setAppName("ReadEs")
      .setMaster("local[*]")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("spark.task.maxFailures", "5")
      .config("spark.driver.allowMultipleContexts", true)
      //.enableHiveSupport()
      .getOrCreate()
    val rdd = spark.sparkContext.parallelize(getScrollData(startTimeStamp, endTimeStamp))
    // 定义模式（即Schema），这里只有一个名为"data"的字符串字段
    val schema: StructType = new StructType()
      .add("data", DataTypes.StringType)
    // 使用SparkSession创建DataFrame
    val df: DataFrame = spark.createDataFrame(rdd.map(row => Row(row)), schema)
    // 解析字段sql
    val esFieldsDF = df
      .withColumn("session_id", get_json_object(col("data"), "$.obuzFollow.id").cast("String"))
      .withColumn("feedback_channels", get_json_object(col("data"), "$.obuzFollow.formField_TEXT_feedback_channel_反馈渠道").cast("String"))
      .withColumn("uid", get_json_object(col("data"), "$.startInfo.startUserInfo.userId").cast("String"))
      .withColumn("user_name", get_json_object(col("data"), "$.startInfo.startUserInfo.userName").cast("String"))
      .withColumn("bjh_uid", get_json_object(col("data"), "$.obuzFollow.formField_TEXT_baijiahao_user_id_百家号ID").cast("String"))
      .withColumn("bjh_name", get_json_object(col("data"), "$.obuzFollow.formField_TEXT_baijiahao_name_百家号名称").cast("String"))
      .withColumn("bjh_type", get_json_object(col("data"), "$.obuzFollow.formField_SELECT_baijiahao_type_百家号类型").cast("String"))
      .withColumn("start_time", get_json_object(col("data"), "$.startInfo.startTime").cast("String"))
      .withColumn("feedback_type", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_feedback_type_反馈分类").cast("String"))
      .withColumn("prod_type", get_json_object(col("data"), "$.obuzFollow.formFieldVal.formField_CASCADER_prod_type_产品问题分类").cast("String"))
      .withColumn("quesiton_desc", get_json_object(col("data"), "$.obuzFollow.formField_TEXT_quesiton_desc_问题描述").cast("String"))
      .withColumn("remark", get_json_object(col("data"), "$.obuzFollow.formField_TEXT_remark_备注").cast("String"))
      .withColumn("update_user_list", get_json_object(col("data"), "$.obuzFollow.updateUserNameList").cast("String"))
      .withColumn("is_change", get_json_object(col("data"), "$.obuzFollow.isChange").cast("String"))
      .withColumn("update_time", get_json_object(col("data"), "$.obuzFollow.updateTime").cast("String"))
      .withColumn("custom_label", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_custom_label_自定义标签").cast("String"))
      .withColumn("im_session_status", get_json_object(col("data"), "$.imSessionStatus").cast("String"))
      .withColumn("risk_type", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_risk_type_风险类型").cast("String"))
      .withColumn("risk_level", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_risk_level_风险等级").cast("String"))
      .withColumn("scene_type", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_scene_type_场景类型").cast("String"))
      .withColumn("close_type", get_json_object(col("data"), "$.obuzFollow.closeType").cast("String"))
      .withColumn("im_session_id", get_json_object(col("data"), "$.imSessionId").cast("String"))
      .withColumn("prod_type_list", get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_CHECKBOX_SEARCH_prod_type_list_问题类型（多选）").cast("String"))
      .withColumn("is_ernie_bot", get_json_object(col("data"), "$.sessionContext.isErnieBot").cast("String"))
      .withColumn("product_id", get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.productId").cast("String"))
      .drop("data")
    esFieldsDF.show(100,false)
//    esFieldsDF.repartition(1).write.option("header", "true").mode(SaveMode.Append).csv("/Users/<USER>/Desktop/tem_data")

    client.close()
    spark.close()
  }

  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient: RestClientBuilder =
      RestClient.builder(new HttpHost(EsConf.ImHost, EsConf.ImPort, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider
      .setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(EsConf.ImUser, EsConf.ImPassword))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)
  }


  private def getScrollData(startTimeStamp: Long,
                            endTimeStamp: Long): ListBuffer[String] = {
    val list: ListBuffer[String] = ListBuffer[String]()

    val searchRequest: SearchRequest = new SearchRequest(indexName)
    val searchSourceBuilder: SearchSourceBuilder = new SearchSourceBuilder
    val boolQueryBuilder: BoolQueryBuilder =
      QueryBuilders
        .boolQuery
        .must(QueryBuilders.termQuery("startInfo.bailingInfoOnImSessionLevel.productId", 6L))
        .filter(QueryBuilders.rangeQuery("createTime").gte(startTimeStamp).lte(endTimeStamp))
//        .must(QueryBuilders.existsQuery("obuzFollow.formField_TEXT_feedback_channel_反馈渠道"))

    searchSourceBuilder.query(boolQueryBuilder)
//      .fetchSource(Array("obuzFollow.formField_SELECT_baijiahao_type_百家号类型"),null)
    searchRequest.source(searchSourceBuilder)
    searchSourceBuilder.size(500)
    searchRequest.source(searchSourceBuilder)
    searchRequest.scroll(TimeValue.timeValueMinutes(5L))
    try {
      var searchResponse: SearchResponse = client.search(searchRequest, RequestOptions.DEFAULT)
      var scrollId: String = searchResponse.getScrollId
      var hits: Array[SearchHit] = searchResponse.getHits.getHits
      hits.foreach(searchHit => {
        list.append(searchHit.getSourceAsString)
      })
      val scroll: Scroll = new Scroll(TimeValue.timeValueMinutes(5L))
      while (hits.length > 0) {
        val scrollRequest: SearchScrollRequest = new SearchScrollRequest(scrollId)
        scrollRequest.scroll(scroll)
        searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT)
        scrollId = searchResponse.getScrollId
        hits = searchResponse.getHits.getHits
        hits.foreach(searchHit => {
          list.append(searchHit.getSourceAsString)
        })
      }
      val clearScrollRequest: ClearScrollRequest = new ClearScrollRequest
      clearScrollRequest.addScrollId(scrollId)
      val clearScrollResponse: ClearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT)
      val succeeded: Boolean = clearScrollResponse.isSucceeded
    } catch {
      case e: IOException =>

        // TODO Auto-generated catch block
        e.printStackTrace()
    }
    list
  }
}
