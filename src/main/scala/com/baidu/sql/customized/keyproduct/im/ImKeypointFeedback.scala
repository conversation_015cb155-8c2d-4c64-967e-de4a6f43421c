package com.baidu.sql.customized.keyproduct.im

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils, TimeOperateUtil}
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.types._
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import com.baidu.sql.customized.keyproduct.im.ImKeypointProductFromUrlEsSpark.{productMap, productNameMap, readFollowConf}
import com.baidu.sql.utils.SparkUtils.getHttpurl
import com.baidu.sql.utils.TimeFormat.SEC_FORMAT_MYSQL
import com.baidu.sql.utils.UdfUtils._
import okhttp3.{OkHttpClient, Request, Response}
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.broadcast.Broadcast
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.storage.StorageLevel

import java.text.SimpleDateFormat
import java.time.{Instant, LocalDateTime, ZoneId}
import java.time.format.DateTimeFormatter
import java.util.Calendar
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.util.control.Breaks.{break, breakable}


object ImKeypointFeedback extends Serializable {

  val indexName = "metis_access_rt_release"

  // 机器人评价满意度
  val robot_eva_stat = Map(
    "1" -> "(极不满意)",
    "2" -> "(不满意)",
    "3" -> "(一般)",
    "4" -> "(满意)",
    "5" -> "(非常满意)"
  )

  // 机器人解决状态
  val robot_eva_sloved = Map(
    "1" -> "已解决",
    "2" -> "未解决",
    "3" -> "持续关注",
  )

  //用户id和用户名对应关系
  var id_uname: collection.Map[String, String] = collection.mutable.Map.empty[String, String]

  //bns转换后的http地址
  var url = ""

  var YesterDay :String = ""
  var yesterDay :String = ""
  var startTimeStamp:Long = 0L
  var endTimeStamp:Long = 0L

  //区域回溯数据的起始时间 yyyyMMdd
  var PartitionDay:String = ""
  //区域回溯数据的起始时间 yyyy-MM-dd
  var partitionDay:String = ""

  val productNameMapList : Map[Long, (String, String)]= productNameMap

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * 将会话内容写入help_ods_ufo_key_point_feedback数仓表中
    * */

    // 分区时间 yyyyMMdd
    YesterDay = args(0)
    // 昨天日期 yyyy-MM-dd
    yesterDay = calcnDateFormat(YesterDay)

    println("yesterDay:" + YesterDay)

    // 操作类型,delete,partition_delete
    val is_delete = args(1)
    // 区域回溯数据的起始时间 yyyyMMdd
    PartitionDay = args(2)
    // 区域回溯数据的起始时间 yyyy-MM-dd
    partitionDay = calcnDateFormat(PartitionDay)

    if (is_delete.equals("delete")){
      // 获取yesterday豪秒级时间戳
      startTimeStamp = TimeOperateUtil.getTimeStamp(YesterDay)
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,2))
    }else{
      // 获取partitionDay区域豪秒级时间戳
      startTimeStamp = TimeOperateUtil.getTimeStamp(PartitionDay)
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,2))
    }

    println("startTime:" + startTimeStamp + " endTime:" + endTimeStamp)

    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    //读取客服组信息
    val commonSql =
      """
        |select
        |   id,  -- 客服id
        |   baidu_uname  -- 客服账号
        |from rbac_user
        |where baidu_uname is not null
        |""".stripMargin

    val idUnameDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,commonSql)
    println("客服组信息读取" + idUnameDf.count())
    val id_unameMap = idUnameDf.rdd.map(row => {
      val id = row.getAs[Long]("id").toString.trim
      val baidu_uname = row.getAs[String]("baidu_uname").trim
      (id,baidu_uname)
    }).collectAsMap()
    id_unameMap.foreach{case (id,uname) =>{
      id_uname += (id -> uname)
    }}
    println("id_unam的长度为：" + id_uname.size)

    //对id和name映射关系进行广播
    val broadcastValue = spark.sparkContext.broadcast(id_uname)

    //定义udf函数，获取操作日志数据
    val getOperatorData = getOperatorDataUDF(broadcastValue)

    //定义udf函数，获取技能组名称
    val getSkillGroupName = getSkillGroupNameUDF(broadcastValue)

    //获取新增数据
    val createDF = dealData(spark,"insert",getSkillGroupName)
    //获取修改数据
    val updateDF = dealData(spark,"update",getSkillGroupName)

    val combinedDF = createDF.unionByName(updateDF)
      .dropDuplicates("im_session_id","session_id","product_line","product_before_line")

    combinedDF.createOrReplaceTempView("temp_data")

    //操作日志数据获取
    val sessionArray = combinedDF
      .filter($"session_id" =!= "")
      .rdd
      .map(row => {
        val session_id = row.getAs[String]("session_id")
        (session_id,postRequestTest(session_id))
      })

    //操作日志数据处理
    val dataDf = sessionArray.map{case (session_id,responseStr) => {
      var resArray = Seq.empty[String]
      if (responseStr != null && responseStr.nonEmpty && JSONUtil.isJson(responseStr)){
        val jsonObject = JSON.parseObject(responseStr)
        if (!jsonObject.isEmpty){
          //获取操作日志数据data
          val dataArray: Option[JSONArray] = Option(jsonObject.getJSONArray("data"))
          resArray = dataArray match {
            case Some(array) =>
              //按seqNum升序排序，然后转成字符串
              array.toArray.sortBy(x => JSON.parseObject(x.toString).getInteger("seqNum")).map(x => x.toString)
            case None => Seq.empty[String]
          }
        }
      }
      //返回一个session_id对应一个Array
      (session_id,resArray)
    }}

    val operatorDF = dataDf.toDF("session_id","url_data")
      //先去重，转产品线的话数据一样会重复，避免下面的join产生笛卡尔积
      .filter(size($"url_data") =!= 0)
      .dropDuplicates()

    operatorDF.show(5,false)

    val esUrlDf = combinedDF.join(operatorDF,Seq("session_id"))
      //再次去重，防止笛卡尔积操作的数据重复
      .dropDuplicates()
      .withColumn("operatorData", getOperatorData(col("url_data"),col("accept_group")))
      .drop("url_data")
      .repartition(100)
      .persist(StorageLevel.MEMORY_AND_DISK)

    esUrlDf.show(5,false)
    println("操作日志拆分前的数据条数：" + esUrlDf.count())
    esUrlDf.createOrReplaceTempView("temp_urldata")

    //操作日志数据拆分
    val operDeailDf = spark.sql(
      """
        |select
        |   product_id,
        |   product_before_line,
        |   product_line,
        |   userid,
        |   session_id,
        |   im_session_id,
        |   submit_time,
        |   extend_feedback_channel,
        |   accept_group,
        |   zx_is_now_prod_sessionz,
        |   explode(split(operatorData,'<=>')) as operator_details
        |from temp_urldata
        |where operatorData != ''
        |""".stripMargin)

    val operSplitDf = operDeailDf
      //记录类型，为空
      .withColumn("msg_type",lit(""))
      //操作人id（后面删除）
      //.withColumn("operator_id",getNullVal(split(col("operator_details"),"=>").getItem(0).cast("string")))
      //操作人
      .withColumn("service_name_csi",getNullVal(split(col("operator_details"),"=>").getItem(0).cast("string")))
      //操作内容
      .withColumn("content",getNullVal(split(col("operator_details"),"=>").getItem(2).cast("string")))
      //操作时间
      .withColumn("record_time",getNullVal(split(col("operator_details"),"=>").getItem(3).cast("string")))
      //转接客服组id（后面删除）
      .withColumn("skill_productId",getNullVal(split(col("operator_details"),"=>").getItem(4).cast("string")))
      //客服组/解决状态
      .withColumn("service_group_status",getGroupStatus($"service_name_csi",$"accept_group"))
      //表单类型
      .withColumn("form_type",lit("操作日志"))
      .filter($"content" =!= "")
      .filter($"product_id" === $"skill_productId")
      .drop("operator_details","accept_group","product_id","skill_productId")
      .dropDuplicates()

    println("操作日志拆分后的数据条数：" + operSplitDf.count())
    operSplitDf.show(5,false)

    //多轮对话数据拆分
    val explodeDf = spark.sql(
      """
        |select
        |   product_id,
        |   product_before_line,
        |   product_line,
        |   userid,
        |   session_id,
        |   im_session_id,
        |   submit_time,
        |   extend_feedback_channel,
        |   accept_group,
        |   zx_is_now_prod_sessionz,
        |   explode(split(session_details,'<=>')) as session_details
        |from temp_data
        |where session_details != ''
        |""".stripMargin)

    var splitDf = explodeDf
      //记录类型
      .withColumn("msg_type",getNullVal(split(col("session_details"),"=>").getItem(0).cast("string")))
      //记录时间
      .withColumn("record_time",getNullVal(split(col("session_details"),"=>").getItem(1).cast("string")))
      //客服账号/操作人/满意度CSI
      .withColumn("service_name_csi",getNullVal(split(col("session_details"),"=>").getItem(2).cast("string")))
      //客服组/解决状态（后面删除）
      .withColumn("group_status",getNullVal(split(col("session_details"),"=>").getItem(3)))
      //如果group_status为空，则取客服组（总的客服组/解决状态）
      .withColumn("service_group_status",when($"group_status" === "",getGroupStatus($"service_name_csi",$"accept_group")).otherwise($"group_status"))
      //对话内容/评价内容
      .withColumn("content",getNullVal(split(col("session_details"),"=>").getItem(4).cast("string")))
      //转接产品线id（后面删除）
      .withColumn("skill_productId",getNullVal(split(col("session_details"),"=>").getItem(5).cast("string")))
      //表单类型
      .withColumn("form_type",when(col("msg_type").contains("评价"),lit("评价信息")).otherwise(lit("对话记录")))
      .filter($"msg_type" =!= "")
      .filter($"skill_productId" === "" || $"product_id" === $"skill_productId")
      .drop("session_details","group_status","accept_group","product_id","skill_productId")
      .dropDuplicates()

    println("对话记录拆分后的数据条数：" + splitDf.count())
    splitDf.show(5,false)
    esUrlDf.unpersist()

    if (is_delete.equals("delete")){
      //合并对话记录和操作日志数据
      val writeDf = operSplitDf.unionByName(splitDf)
        .withColumn("session_details",col("content"))
        .filter(substring(col("record_time"),1,10) === yesterDay)
        .na.fill("")
        .dropDuplicates()
        .repartition(100)

      println("最终合并的数量为：" + writeDf.count())
      writeDf.createOrReplaceTempView("combined_data")

      val sql =
        s"""
           |insert overwrite table udw_ns.default.help_ods_ufo_key_point_feedback partition (event_day = $YesterDay )
           |select
           |  product_before_line, -- 在线处理前产品线
           |	product_line, --  重点业务
           |	userid,	--  用户id
           |	session_id, -- 会话id
           |	im_session_id, -- 会话标识
           |  zx_is_now_prod_sessionz, -- 是否属于本产品线
           |  session_details, -- 对话内容/评价内容
           |  form_type, -- 表单类型
           |  extend_feedback_channel, -- 反馈渠道
           |  submit_time, -- 提交时间
           |  msg_type, -- 记录类型
           |  record_time, -- 记录时间
           |  service_name_csi, -- 客服账号/操作人/满意度CSI
           |  service_group_status -- 客服组/解决状态
           |from combined_data
           |""".stripMargin

      spark.sql(sql)
    }else{
      //partition_delete
      //合并对话记录和操作日志数据
      val writeDf = operSplitDf.unionByName(splitDf)
        .withColumn("session_details",col("content"))
        .filter(substring(col("record_time"), 1, 10) >= partitionDay && substring(col("record_time"), 1, 10) <= yesterDay)
        .withColumn("event_day",regexp_replace(substring(col("record_time"), 1, 10),"-",""))
        .na.fill("")
        .dropDuplicates()
        .repartition(100)

      println("最终合并的数量为：" + writeDf.count())
      writeDf.createOrReplaceTempView("combined_data")

      spark.sql(
          """
            |select
            |   event_day
            |from combined_data
            |group by event_day
            |order by event_day
            |""".stripMargin)
        .show(false)

      val seqEvent_day = generateDropPartitionStatements(PartitionDay, YesterDay)
      println(s"将要删除的分区为：$seqEvent_day")

      val sql =
        s"""
           |insert overwrite table udw_ns.default.help_ods_ufo_key_point_feedback partition (event_day)
           |select
           |  product_before_line, -- 在线处理前产品线
           |	product_line, --  重点业务
           |	userid,	--  用户id
           |	session_id, -- 会话id
           |	im_session_id, -- 会话标识
           |  zx_is_now_prod_sessionz, -- 是否属于本产品线
           |  session_details, -- 对话内容/评价内容
           |  form_type, -- 表单类型
           |  extend_feedback_channel, -- 反馈渠道
           |  submit_time, -- 提交时间
           |  msg_type, -- 记录类型
           |  record_time, -- 记录时间
           |  service_name_csi, -- 客服账号/操作人/满意度CSI
           |  service_group_status, -- 客服组/解决状态
           |  event_day -- 分区字段
           |from combined_data
           |""".stripMargin

      spark.sql(sql)
    }


    spark.close()
  }

  /**
   * 通过传入的json串获取产品线名称和技能组名称
   */
  val getGroupStatus = udf((userName: String,jsonData:String) => {
    if (userName == null || userName == "" || jsonData == null || jsonData == "") {
      ""
    } else {
      val json = JSON.parseObject(jsonData)
      //用户名
      var res = ""
      // 使用keySet获取所有的键，并遍历它们
      for (d <- 0 until json.size()) {
        val jSONObject = json.getJSONObject(d.toString)
        val ouserName = jSONObject.getString("ouserName")
        if (ouserName.equals(userName)) {
          res = jSONObject.getString("skillGroup")
        }
      }
      res
    }
  })

  /**
   * 获取会话内容
   */
  val getSessionDetail = udf((data: mutable.WrappedArray[String],jsonData:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      var json :JSONObject = null
      if(jsonData != null && !jsonData.isEmpty && !jsonData.equals("")){
        json = JSON.parseObject(jsonData)
      }
      var index = 0
      var productLine = ""
      data.foreach(elem => {
        val normalTalk = JSON.parseObject(elem)
        try {
          val talkType = normalTalk.getString("talkType")
          //初始产品线id，转接产品线后id会改变
          if (json != null){
            productLine = json.getJSONObject(index.toString).getOrDefault("productId","").toString
          }
          //记录类型
          var users = ""
          //记录时间/评价时间,格式：yyyy-MM-dd HH:mm:ss
          val time = getDateTime(normalTalk.getString("time"),SEC_FORMAT_MYSQL)
          //客服账号/满意度
          var userName = ""
          //客服组，解决状态
          var status = ""
          //回复内容
          var content = ""
          //获取用户对话记录数据NORMAL_TALK类型
          if (talkType != null && talkType != "" && ("NORMAL_TALK".equals(talkType) || "EVENT_TALK".equals(talkType))) {
            val userInfo = normalTalk.getJSONObject("userInfo")
            if (userInfo != null && !userInfo.isEmpty) {
              if ("__METIS_IM__".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") != true) {
              //if ("__METIS_IM__".equals(userInfo.getString("sysName")) && (userInfo.getBoolean("isBot") == false || userInfo.getString("isBot") == null)) {
                users = "用户回复"
              } else if ("metis-bot".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") == true) {
                users = "metis-bot"
              } else if ("系统客服".equals(userInfo.getString("showName")) && userInfo.getBoolean("isBot") == true) {
                users = "系统客服"
              } else if ("follow".equals(userInfo.getString("sysName"))) {
                users = "人工客服"
                userName = userInfo.getString("userName")
              }
            }
            
            /*var extra = normalTalk.getJSONObject("extra")
            if (extra == null || extra.isEmpty) {
              extra = new JSONObject()
            }
            //是否文心回复
            if (extra.containsKey("ernieBot") && extra.getBoolean("ernieBot") == true) {
              users = "文心回复"
            }
            //文心前前向拦截
            if (extra.containsKey("inputTalkKeywordsIntercept")) {
              val inputTalkKeywordsIntercept = extra.getString("inputTalkKeywordsIntercept")
              content = "前向拦截:" + (if (inputTalkKeywordsIntercept == null) "" else inputTalkKeywordsIntercept)
            }
            //文心后向拦截
            if (extra.containsKey("outputTalkKeywordsIntercept")) {
              val outputTalkKeywordsIntercept = extra.getString("outputTalkKeywordsIntercept")
              content ="后向拦截:" + (if (outputTalkKeywordsIntercept == null) "" else outputTalkKeywordsIntercept)
            }*/
            //**** 带文心分类标签 ****
            /*if ("文心分类标签".equals(value)) {
              //文心命中的分类标签
              if (extra.containsKey("ernieClassifyHit")) {
                val ernieClassifyHit = extra.getString("ernieClassifyHit")
                res.append("命中的分类标签:")
                res.append(if (ernieClassifyHit == null) "" else ernieClassifyHit)
                res.append("")
              }
              //文心命中的分类结果
              if (extra.containsKey("ernieClassifyRet")) {
                val ernieClassifyRet = extra.getString("ernieClassifyRet")
                res.append("命中的分类结果:")
                res.append(if (ernieClassifyRet == null) "" else ernieClassifyRet)
                res.append("")
              }
            }*/

            //具体的对话内容直接硬解富卡，有的text是空,有的bailingRichCard是空，是个坑
            val bailingRichCard = normalTalk.getString("bailingRichCard")
            breakable {
              if (bailingRichCard == null || !JSON.parseObject(bailingRichCard).containsKey("content")) {
                content = if (normalTalk.getString("text") == null) "" else normalTalk.getString("text")
                break
              } else {
                val tent = JSON.parseObject(bailingRichCard).getString("content")
                if (tent != null || !(tent.isEmpty)) {
                  val contentJson = JSON.parseObject(tent)
                  val types = contentJson.getString("type")
                  if ("html".equals(types)) {
                    val data = contentJson.getString("data")
                    content = if (data == null) "" else data.replaceAll("<.*?>", "")
                  } else if ("card".equals(types)) {
                    val dataJson = contentJson.getJSONObject("data")
                    if (dataJson == null || dataJson.isEmpty) {
                      content = ""
                      break
                    }
                    val array = dataJson.getJSONArray("content")
                    if (array == null || array.isEmpty) {
                      content = ""
                      break
                    } else {
                      for (item <- 0 to array.size() - 1) {
                        val cardContent = array.getJSONObject(item)
                        val cardType = cardContent.getString("type")
                        if ("html".equals(cardType) || "text".equals(cardType)) {
                          val valuestr = cardContent.getString("value")
                          content = if (valuestr == null) "" else valuestr.replaceAll("<.*?>", "")
                        } else {
                          //其他类型富卡直接转json
                          content = cardContent.toString()
                        }
                      }
                    }
                  }
                }
              }
            }
          }else if (talkType != null && talkType != "" && "SESSION_EVALUATE".equals(talkType)){
            val evaluateInfo = normalTalk.getJSONObject("evaluateInfo")
            if (evaluateInfo != null && !evaluateInfo.isEmpty) {
              if (evaluateInfo.containsKey("evaluated")) {
                //是否被评价
                val evaluated = evaluateInfo.getString("evaluated")
                if ("true".equals(evaluated)) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    //评价结果
                    val evaluateResult = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResult != null && !evaluateResult.isEmpty) {
                      if (evaluateResult.containsKey("robot_eva_sloved")) {
                        //是否解决
                        val score = evaluateResult.getString("robot_eva_sloved")
                        status = robot_eva_sloved.getOrElse(score, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_stat")) {
                        //评价得分
                        val stat = evaluateResult.getString("robot_eva_stat")
                        userName = stat + robot_eva_stat.getOrElse(stat, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_diss_content")) {
                        //服务评价
                        content = evaluateResult.getString("robot_eva_diss_content")
                      }
                      if (evaluateResult.containsKey("robot")) {
                        //机器人评价
                        if (evaluateResult.getBoolean("robot") == true) users = "机器人评价" else users = "人工评价"
                      }
                    }
                  }
                }
              }
            }
          }
          //满足条件的数据才接入
          if (users != ""){
            //转接产品线
            if (content.trim.equals("百度客服为您服务")){
              index += 1
              if (json != null){
                productLine = json.getJSONObject(index.toString).getOrDefault("productId","").toString
              }
            }
            if (users == "机器人评价" || users == "人工评价"){
              res.append(Seq(users,time,userName,status,content,productLine).mkString("=>"))
            } else if(users != "机器人评价" && content != ""){
              res.append(Seq(users,time,userName,status,content,productLine).mkString("=>"))
            }else{
              res
            }
          }
        } catch {
          //case e:Exception => println(s"buildExportSessionTalkContent fail imSessionId:" + normalTalk.getString("imSessionId") + "talkSeqId:"  + normalTalk.getString("seqId"))
          case e:Exception => println(s"session_id:" + normalTalk.getString("imSessionId") + ",error:"  + e.getMessage)
        }
      })
      res.mkString("<=>")
    }
  })

  /**
   * 获取业务线
   */
  val getProductLine = udf((data: String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      productMap.getOrElse(data, "")
    }
  })

  /**
   * 是否属于当前产品线
   */
  val getIsNowProd = udf((data:String,obuzFollow:String,productId:String) => {
    var res = "是"
    val bailingInfoOnImSessionLevel = JSON.parseObject(data)
    //判断obuzFollow里面的productId和总的productId是否一样
    if(obuzFollow != null && obuzFollow != ""){
      val obuzFollowJson = JSON.parseObject(obuzFollow)
      if (obuzFollowJson != null && obuzFollowJson.containsKey("productId")){
        val obuzFollowProductId = obuzFollowJson.getString("productId")
        res = if(obuzFollowProductId.equals(productId)) "是" else "否"
      }
    }else if(bailingInfoOnImSessionLevel != null && bailingInfoOnImSessionLevel.containsKey("productId")){
      //判断bailingInfoOnImSessionLevel中的productId，与level下的containerProduct的productId对比
      //如果productId一致是属于当前产品线，反之不属于
      val levelProductId = bailingInfoOnImSessionLevel.getString("productId")
      res = if(levelProductId.equals(productId)) "是" else "否"
    }else{
      res
    }
    res
  })

  // 定义 UDF（在 main 之外），通过广播变量访问值
  def getOperatorDataUDF(broadcastVar: Broadcast[scala.collection.Map[String,String]]): UserDefinedFunction = {
    udf((data: mutable.WrappedArray[String],jsonData:String) => {
      if(data == null || data.isEmpty) {
        ""
      }else{
        val res = ListBuffer[String]()
        var productLine = ""
        var index = 0
        // 安全解析JSON，防止空指针
        val JsonData = try {
          if (jsonData != null && jsonData.nonEmpty) JSON.parseObject(jsonData) else null
        } catch {
          case _: Exception => null
        }

        data.foreach(row => {
          // 安全获取操作日志json数据
          val json = try {
            if (row != null && row.nonEmpty) JSON.parseObject(row) else new JSONObject()
          } catch {
            case _: Exception => new JSONObject()
          }

          //操作人
          val creator = broadcastVar.value.getOrElse(json.getOrDefault("creator","").toString,"")
          //操作人
          val creatorName = json.getOrDefault("creatorName","").toString
          //操作描述
          val opDesc = json.getOrDefault("opDesc","").toString
          //操作时间
          val opTime = getDateTime(json.getOrDefault("opTime","").toString, SEC_FORMAT_MYSQL)
          //初始产品线id，转接产品线后id会改变
          if (opDesc.equals("客服确认转入会话")) {
            index += 1
          }

          // 安全获取技能组数据
          if (JsonData != null && !JsonData.isEmpty) {
            val skillData = JsonData.getJSONObject(index.toString)
            if (skillData != null) {
              productLine = skillData.getOrDefault("productId","").toString
            }
          }

          res += Seq(creator, creatorName, opDesc, opTime, productLine).mkString("=>")
        })
        res.mkString("<=>")
      }
    })
  }

  def getSkillGroupNameUDF(broadcastVar: Broadcast[scala.collection.Map[String,String]]): UserDefinedFunction = {
    udf((data: mutable.WrappedArray[String]) => {
      if (data == null || data.isEmpty || data == "") {
        ""
      } else {
        val resMap = new JSONObject()
        //给转接产品线的加个顺序索引
        var index = 0
        data.foreach(row => {
          var skillGroupName = ""
          var ouserName = ""
          var productId = ""
          val json = JSON.parseObject(row)
          if(json.containsKey("skillGroupName")) {
            //客服组
            skillGroupName = json.getOrDefault("skillGroupName", "").toString
          }
          if(json.containsKey("ouserId")){
            val ouserId = json.getString("ouserId")

            //用户id获取用户名
            ouserName = broadcastVar.value.getOrElse(ouserId, "")
          }

          if(json.containsKey("productId")){
            productId = json.getOrDefault("productId","").toString
          }
          //构建json串
          resMap.put(index.toString,JSON.parseObject(s"{'skillGroup':'${skillGroupName}','productId':'${productId}','ouserName':'${ouserName}'}"))
          index += 1
        })
        resMap.toJSONString
      }
    })
  }


  /**
   *  读取ES数据库数据
   * @param spark
   * @param startTimeStamp 开始时间戳
   * @param endTimeStamp 结束时间戳
   * @param productId 产品线ID
   * @param isDefault 是新增数据还是修改数据
   * @return
   */
  private def getEsData(spark: SparkSession,
                        startTimeStamp: Long,
                        endTimeStamp: Long,
                        productId: String,
                        isDefault:String): RDD[(String, String)] = {

    //新建数据查询语句
    val createQuery =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "must": [
         |        {"bool":{
         |              "should": [
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
         |                    {"term": {"obuzFollow.productId": "${productId}"}}
         |              ]
         |            }
         |         },
         |        {
         |          "bool": {
         |            "must": [
         |              {
         |                "range": {
         |                  "startInfo.startTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    //修改数据查询语句
    val updateQuery =
      s"""
        |{
        |  "query": {
        |    "bool": {
        |      "must": [
        |        {
        |          "bool": {
        |            "should": [
        |              {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
        |              {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
        |              {"term": {"obuzFollow.productId": "${productId}"}}
        |            ]
        |          }
        |        },
        |        {
        |          "nested": {
        |            "path": "talks",
        |            "query": {
        |              "range": {
        |                "talks.time": {
        |                  "gte": "${startTimeStamp}",
        |                  "lte": "${endTimeStamp}"
        |                }
        |              }
        |            }
        |          }
        |        }
        |      ]
        |    }
        |  }
        |}
        |""".stripMargin

    val query = isDefault match {
      case "insert" => createQuery
      case "update" => updateQuery
    }

    CommonUtils.ElasticSearchOperate(spark,indexName,PropertiesUtils.ESImProperties,query)
  }

  /**
   * 通过接口获取follow的操作日志数据（临时接口）
   * @param imSessionIds
   * @return
   */
  def postRequest(sessionzId: String): String = {
    val url = s"https://duke.baidu-int.com/follow/data/sessionz-buz/findSessionzBuzOpLog?sessionzId=${sessionzId}"
    //请求体拼接
    /*val body = new JSONObject
    body.put("sessionzId", sessionzId)*/
    //口令获取
    var response: Response = null

    /*val requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON
      .toString), body.toString())*/
    val okHttpClient = new OkHttpClient
    val request = new Request.Builder()
      .url(url)
      .addHeader("Cookie", "SECURE_UUAP_P_TOKEN=PT-1076465159345553409-TPlnxz0w7jWcuFCx6FFU-uuap; UUAP_P_TOKEN=PT-1076465159345553409-TPlnxz0w7jWcuFCx6FFU-uuap; UUAP_TRACE_TOKEN=0f0f2e0352ab529e8fcdd1e7131888d7; SECURE_ZT_EXTRA_INFO=K47sG0oVR74HZG0oBfXCu67yghpdpa4TW9kaEVgFZU3aP3YUjZA7Ny1CnYLu7qJh9_W7umAbSpVRCJXF_2DM3gWhldzQRsqMBMxI9dW-Vms; ZT_EXTRA_INFO=K47sG0oVR74HZG0oBfXCu67yghpdpa4TW9kaEVgFZU3aP3YUjZA7Ny1CnYLu7qJh9_W7umAbSpVRCJXF_2DM3gWhldzQRsqMBMxI9dW-Vms; FOLLOW_ONLINE=NjFlOTI5MzItMGU2YS00NjA2LTgwMDItYmU5YzY5YzQ1OTgy; SECURE_ZT_GW_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOiI2OTMyRDY5My1GMTczLTU1OUQtQjExNi02Qjg5MTNFNzg4NDgiLCJ0eXBlIjoidXVhcCIsInVzZXJuYW1lIjoidl96aGFuZ3J1bmppZSIsInNhYyI6ImM6YmFpZHUiLCJkb21haW4iOiJkdWtlLmJhaWR1LWludC5jb20iLCJpYXQiOjE3MzQ1Nzg4OTZ9._VPYCdlYqAlqizsQkn57yaP1w23Z_bsGg6BQBN_xzh8; ZT_GW_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOiI2OTMyRDY5My1GMTczLTU1OUQtQjExNi02Qjg5MTNFNzg4NDgiLCJ0eXBlIjoidXVhcCIsInVzZXJuYW1lIjoidl96aGFuZ3J1bmppZSIsInNhYyI6ImM6YmFpZHUiLCJkb21haW4iOiJkdWtlLmJhaWR1LWludC5jb20iLCJpYXQiOjE3MzQ1Nzg4OTZ9._VPYCdlYqAlqizsQkn57yaP1w23Z_bsGg6BQBN_xzh8; SECURE_ZT_APP_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InZfemhhbmdydW5qaWUiLCJpYXQiOjE3MzQ1Nzg4OTUsImV4cCI6MTczNDgzODA5NX0.binF8ehGVn2LqDE7Ff6nKg2CkvqGjHAMRxeF-seWzXU; ZT_APP_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InZfemhhbmdydW5qaWUiLCJpYXQiOjE3MzQ1Nzg4OTUsImV4cCI6MTczNDgzODA5NX0.binF8ehGVn2LqDE7Ff6nKg2CkvqGjHAMRxeF-seWzXU; BAILING_ONLINE_UUPSSS=ST-1076465189586440194-mpEpOp4922MDYbU1ewdp-uuap; RT=\"z=1&dm=baidu-int.com&si=3f80e0a5-ebdd-42cb-abd6-1d37979bfd84&ss=m4urhlge&sl=1&tt=212&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=754&ul=832&hd=8cs\"; BEC=7590b1ea1e932868633140726d0f2070")
      .get()
      .build

    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

  //从接口获取数据
  def postRequestTest(SessionId: String): String = {
    var httpurl = getHttpurl()
    httpurl = httpurl + s"/data/follow-common-api/findSessionzBuzOpLog?sessionzId=${SessionId}"
    //println(httpurl)

    //口令获取
    var response: Response = null
    val APP_KEY = "dashboard_udw_sync_data_interface"
    val APP_TOKEN = "d55cc05f-a8a0-4b8a-85f8-8c02b67ffaf8"
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val localDateTime = LocalDateTime
      .ofInstant(Instant.ofEpochMilli(System.currentTimeMillis), ZoneId.systemDefault)
    val createTime = localDateTime.format(formatter)
    val sign = DigestUtils.md5Hex(APP_KEY + APP_TOKEN + createTime)

    val okHttpClient = new OkHttpClient
    val request = new Request.Builder()
      .url(httpurl)
      .addHeader("appKey", APP_KEY)
      .addHeader("createTime", s"""$createTime""")
      .addHeader("sign", s"""$sign""")
      //.post(requestBody)
      .get()
      .build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

  /**
   *
   * @param spark
   * @return
   */
  def dealData(spark:SparkSession,isDefault:String,getSkillGroupName:UserDefinedFunction): DataFrame = {
    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    if (isDefault == "insert") {
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))
    }
    println("startTime:" + startTimeStamp + ",endTimeStamp:" + endTimeStamp)

    for (product <- productNameMapList) {
      val productId = product._1.toString
      //处理前的产品线名称
      val productBeforeName = product._2._1
      //处理后的产品线名称
      val productName = product._2._2
      //通过EsSpark读取Es数据
      val rdd = getEsData(spark, startTimeStamp, endTimeStamp, productId,isDefault)
      //定义读取Es后的数据schema
      val schema = new StructType()
        .add("data", StringType)

      // 使用SparkSession创建DataFrame，row._2是所有Json字符串的数据内容
      val df = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)

      // 解析字段sql  这里把接口无法取出的字段做处理
      val tempDf = df
        //产品线id
        .withColumn("product_id",lit(productId))
        //处理前产品线名称
        .withColumn("product_before_line",lit(productBeforeName))
        //product_id第一次入的产品线
        .withColumn("product_first_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.productId")
            .cast("String")))
        //app_id子产品线
        .withColumn("app_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.appId")
            .cast("String")))
        //app_id子产品线
        .withColumn("path_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.entryPathId")
            .cast("String")))
        //app_id子产品线
        .withColumn("plan_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.planId")
            .cast("String")))
        //app_id子产品线
        .withColumn("page_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.clientPageId")
            .cast("String")))
        .withColumn("product_id_total",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.productId")
            .cast("String")))
        //产品线名称
        .withColumn("product_line",
          when(getProductLine(col("app_id")) === "", lit(productName)).otherwise(getProductLine(col("app_id")))
            .cast("String"))
        //在线的session_id
        .withColumn("im_session_id",
          getNullVal(get_json_object(col("data"), "$.imSessionId")
            .cast("String")))
        //会话标识
        .withColumn("session_id",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.id")
            .cast("String")))
        //创建时间
        .withColumn("submit_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.startInfo.startTime")
            .cast("String"))))
        //是否属于当前产品线
        .withColumn("zx_is_now_prod_sessionz",
          getNullVal(getIsNowProd(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel"),get_json_object(col("data"), "$.obuzFollow"),lit(productId))
            .cast("String")))
        .withColumn("userid", getNullVal(get_json_object(col("data"), "$.startInfo.startUserInfo.userId").cast("String")))
        //客服组
        .withColumn("accept_group",
          getSkillGroupName(from_json(get_json_object(col("data"), "$.obuzFollow.sessionzAllocationESDTOList"),ArrayType(StringType)))
            .cast("string"))
        //会话内容
        .withColumn("session_details",getNullVal(getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),col("accept_group"))))
        //会话内容(带文心分类标签)
        //.withColumn("session_details_label",getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心分类标签")))
        .drop("data")
        .dropDuplicates(Seq("im_session_id", "product_id","product_before_line"))

      resultDf.append(tempDf)
    }

    //将用户增长的手百主入口（任务中台）过滤掉
    val esDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2))
      //.filter(col("app_id") =!= "2001060")
      .dropDuplicates(Seq("im_session_id", "product_id","product_before_line"))
      .toDF()
      .repartition(100)

    println("esDF数据量：" + esDF.count())

    //解决extend_feedback_channel反馈渠道字段
    val resDfList = readFollowConf(spark)
    val productChannel = resDfList._1
    val appChannel = resDfList._2
    val planChannel = resDfList._3
    val pageChannel = resDfList._4
    val pathChannel = resDfList._5

    val combinedDF = esDF.as("E")
      .join(productChannel.as("P"),col("E.product_first_id") === col("P.id"),"left_outer")
      .join(appChannel.as("A"),col("E.app_id") === col("A.id"),"left_outer")
      .join(planChannel.as("C"),col("E.plan_id") === col("C.id"),"left_outer")
      .join(pageChannel.as("G"),col("E.page_id") === col("G.id"),"left_outer")
      .join(pathChannel.as("T"),col("E.path_id") === col("T.id"),"left_outer")
      .withColumn("extend_feedback_channel", concat_ws("-",col("product_name"),col("app_name"),col("plan_name"),col("page_name"),col("breadcrumb_name")).cast("String"))
      .drop("id")
      .dropDuplicates("im_session_id","session_id","product_line","product_before_line")
      .repartition(100)
      .cache()

    combinedDF
  }

  /**
   * 生成删除分区语句
   * @param startDateStr
   * @param endDateStr
   * @return
   */
  def generateDropPartitionStatements(startDateStr: String, endDateStr: String): Seq[String] = {
    val dateFormat = new SimpleDateFormat("yyyyMMdd")
    val cal = Calendar.getInstance()
    val startDate = dateFormat.parse(startDateStr)
    val endDate = dateFormat.parse(endDateStr)

    val statements = scala.collection.mutable.ListBuffer.empty[String]
    var currentDate = startDate

    while (currentDate.before(endDate) || currentDate.equals(endDate)) {
      val formattedDate = dateFormat.format(currentDate)
      //statements += s"ALTER TABLE udw_ns.default.help_ods_ufo_key_point_feedback DROP IF EXISTS PARTITION (event_day='$formattedDate')"
      statements += formattedDate
      cal.setTime(currentDate)
      cal.add(Calendar.DAY_OF_MONTH, 1)
      currentDate = cal.getTime()
    }
    statements.toSeq
  }

}
