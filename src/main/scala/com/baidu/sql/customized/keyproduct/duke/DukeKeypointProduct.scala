package com.baidu.sql.customized.keyproduct.duke

import com.alibaba.fastjson.JSON
import com.baidu.sql.customized.keyproduct.commons.{EsConf, MysqlConf}
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.http.HttpHost
import org.apache.http.auth.{AuthScope, UsernamePasswordCredentials}
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.log4j.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, from_json, get_json_object, lit, udf}
import org.apache.spark.sql.{Column, DataFrame, Row, SaveMode, SparkSession, functions}
import org.apache.spark.sql.types.{ArrayType, DataTypes, LongType, StringType, StructType, TimestampType}
import org.elasticsearch.action.search.{ClearScrollRequest, ClearScrollResponse, SearchRequest, SearchResponse, SearchScrollRequest}
import org.elasticsearch.client.{RequestOptions, RestClient, RestClientBuilder, RestHighLevelClient}
import org.elasticsearch.common.unit.TimeValue
import org.elasticsearch.index.query.QueryBuilders
import org.elasticsearch.search.{Scroll, SearchHit}
import org.elasticsearch.search.builder.SearchSourceBuilder

import java.io.IOException
import java.time.{LocalDate, ZoneId}
import scala.collection.mutable
import scala.collection.mutable.ListBuffer

/**
 * <AUTHOR>
 * @date date 2024/2/5
 * @time 14:11
 * @package_name com.baidu.sql.customized.duke
 */
object DukeKeypointProduct {
  val client: RestHighLevelClient = createElasticsearchClient()
  val indexName = "work_order_info"
  val logger: Logger = Logger.getLogger(getClass.getName)

  var enumMap = new mutable.HashMap[String, mutable.HashMap[String, String]]()
  val nameMap: mutable.HashMap[Long, ListBuffer[String]] = new mutable.HashMap()

  /*
     * hycca_customer.customer_template_group 业务线中文映射
     * workflow.baidu_workflow_process_instance 热线常规字段存储
     *
     * */
  def main(args: Array[String]): Unit = {


//    //开始时间  本地测试 2024-03-12
//    val startTimeStamp = 1710172800000L
//    //结束时间
//    val endTimeStamp = 1710172800000L + 1000*60*60*24
//    //spark
//    val sparkConf = new SparkConf().setAppName("ReadEs")
//      .setMaster("local[*]")
//    //sparkSession配置
//    val spark = SparkSession
//      .builder()
//      .config(sparkConf)
//      .config("spark.task.maxFailures", "5")
//      .config("spark.driver.allowMultipleContexts", true)
//      .getOrCreate()

    // 分区时间
    val YESTERDAY: String = args(0)
    // 获取yesterday豪秒级时间戳
    val date = LocalDate.parse(YESTERDAY, java.time.format.DateTimeFormatter.BASIC_ISO_DATE)
    val yesterdayTimestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli
    val startTimeStamp = yesterdayTimestamp
    val endTimeStamp = yesterdayTimestamp + 1000 * 60 * 60 * 24
    //spark
    val sparkConf = new SparkConf().setAppName("ReadEs")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled","true")
      .config("spark.sql.adaptive.coalescePartitions.enabled","true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    // 查询产品问题分类字典值
    getCommonDataDesc(spark)
    // 需要查询的产品线
    val productMap = Map(
      "7" -> Map( "title" -> "百度推广",  "enumValueTable" -> "baidu_emun_value"),
      "21" -> Map( "title" -> "百度热线",  "enumValueTable" -> ""),
      "5" -> Map( "title" -> "百度文库",  "enumValueTable" -> "baidu_emun_value"),
      "25" -> Map( "title" -> "收银台",  "enumValueTable" -> "baidu_emun_value"),
      "58" -> Map( "title" -> "萝卜快跑",  "enumValueTable" -> "bdidg_emun_value"),
      "40" -> Map( "title" -> "百度DU会员",  "enumValueTable" -> "bdidg_emun_value"), // 1
      "28" -> Map( "title" -> "网信办",  "enumValueTable" -> ""),
      "34" -> Map( "title" -> "暖阳热线",  "enumValueTable" -> ""),
      "37" -> Map( "title" -> "青少年热线",  "enumValueTable" -> "baidu_emun_value"), // 1
      "66" -> Map( "title" -> "百度教育",  "enumValueTable" -> ""),
      "62" -> Map( "title" -> "文心一言",  "enumValueTable" -> "baidu_emun_value")
    )
    val productIdStr: String = productMap.keys.mkString(",")
    // 查询template表，获取group_id, id-模板id
    // 读取customer_template_group
    val templateSql = s"""
                        |SELECT ct.id as id, ct.group_id as group_id
                        |FROM hycca_customer.customer_template ct
                        |where ct.group_id in ($productIdStr)
                        |""".stripMargin

    val templateDF: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,templateSql)

    val templateList = templateDF.collect()
    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
    // 遍历集合
    for (template <- templateList) {
//      println(template.toString())
      val id:Long = template.getAs("id")
      val groupId:Long = template.getAs("group_id")
      val product: Map[String, String] = productMap.getOrElse(groupId.toString, Map())
      val title: String = product.getOrElse("title", "")
      val tableName: String = product.getOrElse("enumValueTable", "")
      if (tableName != "") {
          getEnumMapByTable(spark, tableName, groupId)
      }
      // 根据id查询 customer_field 映射字段， label-中文字段 name-es字段名
      val fieldSql = s"""
                        |SELECT label, name, option_value
                        |FROM hycca_customer.customer_field cf
                        |where cf.template_id = $id
                        |""".stripMargin

      val fieldDF: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,fieldSql)

      val fieldList = fieldDF.select("label", "name", "option_value").collect()
      // 遍历集合, 转为map
      val map = mutable.Map[String, String]()
      for (field <- fieldList) {
        val label:String = field.getAs("label")
        val name:String = field.getAs("name")
        val option_value:String = field.getAs("option_value")
        // 特殊处理，收银台的一级二级三级取收银台问题分类的映射
        if(title.equals("收银台") && (label.equals("一级") || label.equals("二级") || label.equals("三级"))) {
          if(option_value.contains("收银台问题分类")) {
            map.put(label, name)
          }
        }else {
          map.put(label, name)
        }
      }
      // 根据group_id, id 查询时间范围的es数据
      val rdd = spark.sparkContext.parallelize(getScrollData(startTimeStamp, endTimeStamp, id, groupId))
      // 定义模式（即Schema），这里只有一个名为"data"的字符串字段
      val schema: StructType = new StructType().add("data", DataTypes.StringType)

      // 使用SparkSession创建DataFrame
      val df: DataFrame = spark.createDataFrame(rdd.map(row => Row(row)), schema)

      // 通过字段 map 获取 es 数据
      // 解析字段
      val esFieldsDF = df
        // 编号
        .withColumn("process_instance_id", get_json_object(col("data"), "$.processInstanceId").cast("String"))
        // 关联id
        .withColumn("relation_id", get_json_object(col("data"), "$.relationId").cast("String"))
        // 创建时间
        .withColumn("create_time", get_json_object(col("data"), "$.createTime").cast(LongType)./(1000).cast(TimestampType))
        // 修改时间
        .withColumn("update_time", get_json_object(col("data"), "$.modifiedTime").cast(LongType)./(1000).cast(TimestampType))
        // 关单时间
        .withColumn("close_time", get_json_object(col("data"), "$.closeTime").cast(LongType)./(1000).cast(TimestampType))
        // 主题
        .withColumn("subject", get_json_object(col("data"), "$.subject").cast("String"))
        // 流程名称
        .withColumn("process_definition_name", get_json_object(col("data"), "$.processDefinitionName").cast("String"))
        // 工单状态
        .withColumn("global_state", get_json_object(col("data"), "$.globalState").cast("String"))
        // 联络历史
        // 创建时间
        .withColumn("temp_create_time", get_json_object(col("data"), "$.templateData." + map.getOrElse("创建时间", "")).cast("String"))
        // 联络历史修改时间
        .withColumn("temp_modified_time", get_json_object(col("data"), "$.templateData." + map.getOrElse("修改时间", "")).cast("String"))
        // 联络历史创建人
        .withColumn("temp_create_user", get_json_object(col("data"), "$.templateData." + map.getOrElse("创建人", "")).cast("String"))
        // 修改人
        .withColumn("temp_modified_user", get_json_object(col("data"), "$.templateData." + map.getOrElse("修改人", "")).cast("String"))
        // 会话id
        .withColumn("session_id", get_json_object(col("data"), "$.templateData." + map.getOrElse("会话id", "")).cast("String"))
        // 客户id
        .withColumn("temp_customer_id", get_json_object(col("data"), "$.templateData." + map.getOrElse("客户id", "")).cast("String"))
        // 问题描述
        .withColumn("temp_question_description", get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题描述", "")).cast("String"))
        // 标题
        .withColumn("temp_title", get_json_object(col("data"), "$.templateData."+ map.getOrElse("标题", "")).cast("String"))
        // 数据来源 json解析
        .withColumn("temp_data_source", get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("数据来源", "")), "$.name").cast("String"))
        // 百度pass账号uid
        .withColumn("temp_pass_uid", get_json_object(col("data"), "$.templateData."+ map.getOrElse("百度pass账号uid", "")).cast("String"))
        // 百度账号
        .withColumn("temp_account", get_json_object(col("data"), "$.templateData." + map.getOrElse("百度账号", "")).cast("String"))
        // 百度pass账号
        .withColumn("temp_pass_account", get_json_object(col("data"), "$.templateData."+ map.getOrElse("百度pass账号", "")).cast("String"))
        // 是否涉及退款
        .withColumn("temp_is_refund", get_json_object(col("data"), "$.templateData."+ map.getOrElse("是否涉及退款", "")).cast("String"))
        // 退款订单号
        .withColumn("temp_refund_order_id", get_json_object(col("data"), "$.templateData."+ map.getOrElse("退款订单号", "")).cast("String"))
        // 风险类型
        .withColumn("temp_risk_type", get_json_object(col("data"), "$.templateData."+ map.getOrElse("风险类型", "")).cast("String"))
        // 问题分类
        // 一级产品问题类型
        .withColumn("product_type",
          getRxProductVal(get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题分类", "")), "$.id"), lit(0))
            .cast("String"))
        // 二级产品问题类型
        .withColumn("function_type",
          getRxProductVal(get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题分类", "")), "$.id"), lit(1))
            .cast("String"))
        // 三级产品问题类型
        .withColumn("function_detail_type",
          getRxProductVal(get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题分类", "")), "$.id"), lit(2))
            .cast("String"))
        // 四级产品问题类型
        .withColumn("customlv4",
          getRxProductVal(get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题分类", "")), "$.id"), lit(3))
            .cast("String"))
        // 五级产品问题类型
        .withColumn("customlv5",
          getRxProductVal(get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题分类", "")), "$.id"), lit(4))
            .cast("String"))


        // 运营/PM/RD处理
        .withColumn("temp_handle_process", get_json_object(col("data"), "$.templateData."+ map.getOrElse("运营/PM/RD处理", "")).cast("String"))
        // 事件类型
        .withColumn("temp_event_class", get_json_object(col("data"), "$.templateData."+ map.getOrElse("事件类型", "")).cast("String"))
        // 性别
        .withColumn("temp_sex", get_json_object(col("data"), "$.templateData."+ map.getOrElse("性别", "")).cast("String"))
        // 年龄
        .withColumn("temp_age", get_json_object(col("data"), "$.templateData."+ map.getOrElse("年龄", "")).cast("String"))
        // 学历
        .withColumn("temp_education", get_json_object(col("data"), "$.templateData."+ map.getOrElse("学历", "")).cast("String"))
        // 人生阶段
        .withColumn("temp_rsjd", get_json_object(col("data"), "$.templateData."+ map.getOrElse("人生阶段", "")).cast("String"))
        // 是否是机主本人
        .withColumn("temp_is_self", get_json_object(col("data"), "$.templateData."+ map.getOrElse("是否是机主本人", "")).cast("String"))
        // 来电号码
        .withColumn("temp_call_phone", get_json_object(col("data"), "$.templateData."+ map.getOrElse("来电号码", "")).cast("String"))
        // 来电次数
        .withColumn("temp_call_cnt", get_json_object(col("data"), "$.templateData."+ map.getOrElse("来电次数", "")).cast("String"))
        // 一级
        .withColumn("temp_one_level",
          getLevelDesc(get_json_object(col("data"), "$.templateData."+ map.getOrElse("一级", "")), get_json_object(col("data"), "$.templateGroupId"))
            .cast("String"))
        // 二级
        .withColumn("temp_two_level",
            getLevelDesc(get_json_object(col("data"), "$.templateData."+ map.getOrElse("二级", "")), get_json_object(col("data"), "$.templateGroupId"))
            .cast("String"))
        // 三级
        .withColumn("temp_three_level",
          getLevelDesc(get_json_object(col("data"), "$.templateData."+ map.getOrElse("三级", "")), get_json_object(col("data"), "$.templateGroupId"))
            .cast("String"))
        // -- 客户资源 contactData  json数组解析
        // 用户名
        .withColumn("customer_username",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("用户名")).cast("String"))
        // 账号名称
        .withColumn("customer_account_name",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账号名称")).cast("String"))
        // 网址
        .withColumn("customer_web_url",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("网址")).cast("String"))
        // 公司名称
        .withColumn("customer_company_name",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("公司名称")).cast("String"))
        // 账户运营单位
        .withColumn("customer_unit",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户运营单位")).cast("String"))
        // 账户首要联系人
        .withColumn("customer_first_people",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户首要联系人")).cast("String"))
        // 账户批注
        .withColumn("customer_annotaions",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户批注")).cast("String"))
        // 账户状态
        .withColumn("customer_account_status",
          getContaceValue(from_json(get_json_object(col("data"), "$.contactData"), ArrayType(StringType)), lit("账户状态")).cast("String"))
        // -- 记录
        // 回复记录 json解析拼接
        .withColumn("reply_record", formatReply(from_json(get_json_object(col("data"), "$.commentList"), ArrayType(StringType))).cast("String"))
        // 操作记录  json数组解析拼接
        .withColumn("operations", formatOperation(from_json(get_json_object(col("data"), "$.operations"), ArrayType(StringType))).cast("String"))
        // 筛选项
        // 候选人
        .withColumn("temp_candidate", get_json_object(col("data"), "$.templateData."+ map.getOrElse("候选人", "")).cast("String"))
        // 最后回复人
        .withColumn("temp_last_person", get_json_object(col("data"), "$.templateData."+ map.getOrElse("最后回复人", "")).cast("String"))
        // 问题类型
//        .withColumn("temp_question_type", get_json_object(col("data"), "$.templateData."+ map.getOrElse("问题类型", "")).cast("String"))
        // 跨系统工单
//        .withColumn("temp_cross_system", get_json_object(col("data"), "$.templateData."+ map.getOrElse("跨系统工单", "")).cast("String"))
        // 跨系统工单状态
//        .withColumn("temp_cross_system_status", get_json_object(col("data"), "$.templateData."+ map.getOrElse("跨系统工单状态", "")).cast("String"))
        // 运营单位
        .withColumn("temp_operate_unit", get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("运营单位", "")), "$.name").cast("String"))
        // 推广账户用户名/投诉方用户名
        .withColumn("temp_promotion_username", get_json_object(get_json_object(col("data"), "$.templateData."+ map.getOrElse("推广账户用户名/投诉方用户名", "")), "$.name").cast("String"))
        // 所属地区
        .withColumn("temp_area", get_json_object(col("data"), "$.templateData."+ map.getOrElse("所属地区", "")).cast("String"))
        // 账户行业
        .withColumn("temp_account_industry", get_json_object(col("data"), "$.templateData."+ map.getOrElse("账户行业", "")).cast("String"))
        // 产品线
        .withColumn("product_line", lit(title).cast("String"))
        .drop("data")

      resultDf.append(esFieldsDF)
    }
    val combinedDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2))
    combinedDF.show(20, false)

    //将构建的新df生成临时表
    combinedDF.createOrReplaceTempView("combined_data")

    // 把数字值中文映射上
    val sql =
      s"""
         |insert into table udw_ns.default.help_ods_ufo_key_point_productline_di partition (event_day = $YESTERDAY )
         |select  product_line, -- 重点业务
         |        '' as work_type, --  类型
         |        '' as channel_platform_space,  --  迟滞渠道-平台-空间
         |        '' as space_name,  --  产品线名称
         |        '' as space_id,  --  产品线ID
         |        '' as old_spce_id,  --  原产品线ID
         |        '' as id,  --  序号
         |        temp_data_source as extend_feedback_channel, --  反馈来源 热线-数据来源
         |        '' as need_manual,  --  转人工
         |        product_type, --  一级产品问题类型
         |        function_type,  -- 二级产品问题类型
         |        function_detail_type,   --  三级产品问题类型
         |        customlv4,  --  四级产品问题类型
         |        customlv5,  --  五级产品问题类型
         |        '' as complaint_type,        --  投诉问题类型
         |        '' as complaint_deal_result,     --  投诉处理结果
         |        '' as complaint_is_result,     --  用户是否投诉
         |        '' as robot_eva_stat,    --  智能客服CSI
         |        '' as robot_eva_sloved,  --  智能客服FCR
         |        '' as robot_eva_advise,  --  智能客服建议
         |        '' as robot_eva_stat_label,  --  智能CSI标签
         |        '' as intp_digital_man,  --  数字人
         |        '' as is_pipe,  --  是否触发流水线
         |        '' as robot_eva_diss_content,  --  用户不满意弹窗回答
         |        '' as mobile_app_name, --  App包含名
         |        '' as evaluation_count,  -- 用户评价
         |        '' as invite_evaluation_type,  --  内容邀评类型
         |        '' as evaluation_filter, -- 内容评价结果
         |        '' as invite_evaluation_filter,  --  内容是否邀评
         |        '' as discontent_reason_filter,  --  内容不满原因
         |        '' as discontent_labels_filter,  --  内容不满标签
         |        '' as audit_pass,  --  是否通过审核
         |        '' as audit_reason,  --  未通过原因
         |        '' as violence_score,  --  暴恐违禁分数
         |        '' as salacity_score,  --  文本色情分数
         |        '' as politics_score,  --  政治敏感分数
         |        '' as robot_reply,  --  机器人回复
         |        '' as first_reply_length_second,  --  首次回复时长
         |        '' as pipe_reply,    --  流水线回复
         |        '' as manual_reply,    --  人工回复
         |        case
         |         when temp_pass_account is not null then temp_pass_account
         |         when temp_account is not null then temp_account
         |         when temp_pass_uid is not null then temp_pass_uid
         |        else '' end as userid,  --  用户id   热线-（百度pass账号uid、百度账号、百度pass账号）
         |        '' as ip,  --  IP地域
         |        '' as ip_province, --  省份
         |        '' as mobile_model,  --  手机型号
         |        '' as mobile_brand,  --  手机品牌
         |        '' as update_user,   --  更新人
         |        global_state as status,    --  反馈处理状态 --工单状态
         |        '' as valid,    --  反馈处理状态1 有效2 无效3 已转出
         |        temp_event_class as feedback_type,   --  反馈类型  事件类型
         |        create_time as submit_time,   --  提交时间
         |        '' as icafe_filter,    --  是否流转icafe 1 是 0 否
         |        '' as from_product_line, --  转入产品线
         |        '' as pre_product_info,    --  流转来源
         |        '' as risk_level,   -- 风险等级
         |        '' as to_product_line,   --  转出到
         |		    "热线" as channel,  --  渠道
         |		    session_id as session_id,   --  会话id
         |		    '' as user_name,   --   用户名
         |		    '' as baijiahao_user_id,   --   百家号ID
         |		    '' as baijiahao_name,   --    百家号名称
         |		    '' as baijiahao_type,   --    百家号类型
         |		    regexp_replace(regexp_replace(temp_question_description, '\\n', ' '), '\\r', '') as session_content,   --  反馈内容、 热线-问题描述
         |		    '' as quesiton_desc,   --  问题描述
         |		    '' as remark,   --  备注
         |		    '' as user_evaluate,   --  用户评价
         |		    '' as evaluate_csi,   --  人工客服CSI
         |		    '' as is_solve,   --  解决状态
         |		    '' as accept_group,   --  客服组
         |		    '' as is_change,   --  是否转接
         |		    update_time,   --    更新时间
         |		    '' as custom_label,   --  自定义标签
         |		    '' as scene_type,   --  场景类型
         |		    '' as close_type,   --  结束类型
         |		    '' as im_session_id,   --  会话标识
         |		    '' as prod_type_list,   --  问题类型
         |		    '' as ernie_bot_flag,   --  是否文心一言回复
         |		    '' as sessionz_time,   --  会话时长（秒）
         |		    '' as first_handle_time,   --  首次响应时长（秒）
         |		    '' as avg_handle_time,   --  平均响应时长（秒）
         |		    '' as not_accept,   --  接入失败
         |		    '' as update_user_list,   --  更新人
         |		    '' as im_session_status,   --  是否关闭
         |		    '' as sys_info,   --  系统信息
         |		    temp_risk_type as risk_type,   --  风险类型
         |        '' as good_evaluation, -- 好评次数
         |        '' as bad_evaluation, -- 差评次数
         |        '' as no_evaluation, -- 未评价次数
         |
         |        process_instance_id as rx_process_instance_id, -- 热线编号
         |        relation_id as rx_relation_id, -- 关联id
         |        close_time as rx_close_time, -- 关单时间
         |        subject as rx_subject, -- 主题
         |        process_definition_name as rx_process_definition_name, -- 流程名称
         |        temp_create_time as rx_temp_create_time, -- 联络历史创建时间
         |        temp_modified_time as rx_temp_modified_time, -- 联络历史修改时间
         |        temp_create_user as rx_temp_create_user, -- 联络历史创建人
         |        temp_modified_user as rx_temp_modified_user, -- 联络历史修改人
         |        temp_customer_id as rx_temp_customer_id, -- 客户id
         |        temp_title as rx_temp_title, -- 标题
         |        temp_is_refund as rx_temp_is_refund, -- 是否涉及退款
         |        regexp_replace(regexp_replace(temp_refund_order_id, '\\n', ' '), '\\r', '') as rx_temp_refund_order_id, -- 退款订单号
         |        temp_handle_process as rx_temp_handle_process, -- 运营/PM/RD处理
         |        temp_sex as rx_temp_sex, -- 性别
         |        temp_age as rx_temp_age, -- 年龄
         |        temp_education as rx_temp_education, -- 学历
         |        temp_rsjd as rx_temp_rsjd, -- 人生阶段
         |        temp_is_self as rx_temp_is_self, -- 是否是机主本人
         |        temp_call_phone as rx_temp_call_phone, -- 来电号码
         |        temp_call_cnt as rx_temp_call_cnt, -- 来电次数
         |        temp_one_level as rx_temp_one_level, -- 一级
         |        temp_two_level as rx_temp_two_level, -- 二级
         |        temp_three_level as rx_temp_three_level, -- 三级
         |        customer_username as rx_customer_username, -- 用户名
         |        customer_account_name as rx_customer_account_name, -- 账号名称
         |        customer_web_url as rx_customer_web_url, -- 网址
         |        customer_company_name as rx_customer_company_name, -- 公司名称
         |        customer_unit as rx_customer_unit, -- 账户运营单位
         |        customer_first_people as rx_customer_first_people, -- 账户首要联系人
         |        customer_annotaions as rx_customer_annotaions, -- 账户批注
         |        customer_account_status as rx_customer_account_status, -- 账户状态
         |        regexp_replace(regexp_replace(reply_record, '\\n', ' '), '\\r', '') as rx_reply_record, -- 回复记录
         |        regexp_replace(regexp_replace(operations, '\\n', ' '), '\\r', '') as rx_operations, -- 操作记录
         |        temp_candidate as rx_temp_candidate, -- 候选人
         |        temp_last_person as rx_temp_last_person, -- 最后回复人
         |        '' as rx_temp_question_type, -- 问题类型
         |        '' as rx_temp_cross_system, -- 跨系统工单
         |        '' as rx_temp_cross_system_status, -- 跨系统工单状态
         |        temp_operate_unit as rx_temp_operate_unit, -- 运营单位
         |        temp_promotion_username as rx_temp_promotion_username, -- 推广账户用户名/投诉方用户名
         |        temp_area as rx_temp_area, -- 所属地区
         |        temp_account_industry as rx_temp_account_industry, -- 账户行业
         |        '' as auto_content_id -- 智能客服内容id
         | from combined_data
         |""".stripMargin

    spark.sql(sql)

//    val mappingDF = spark.sql(sql)
//    mappingDF.show(20, false)
//    // 输出到文件中查看完整数据样例
//    mappingDF.repartition(1).write.option("header", "true").mode(SaveMode.Overwrite).csv("D:/tem_data/rexian")

    spark.close()
  }


  def getEnumMapByTable(spark: SparkSession, tableName: String, groupId: Long) = {
    // 根据id查询 customer_field 映射字段， label-中文字段 name-es字段名
    val enumValueSql =
      s"""
         |SELECT value_id, value_desc
         |FROM hycca_customer.$tableName
         |""".stripMargin

    val valueDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,enumValueSql)

    val enumValueList = valueDf.collect()
    val enumValueMap = new mutable.HashMap[String, String]()
    for (enumValue <- enumValueList) {
      val value_id: String = enumValue.getAs(0).toString
      val value_desc: String = enumValue.getAs(1).toString
      enumValueMap.put(value_id, value_desc)
    }
    enumMap.put(groupId.toString, enumValueMap)
  }

  def getCommonDataDesc(spark: SparkSession): Unit = {
    // 读取 common_select_data
    val commonDataSql =
      s"""
         |SELECT id, parent_id, name
         |FROM hycca_customer.common_select_data
         |""".stripMargin

    val commonDataDF: DataFrame = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlHyccaCustomerProperties,commonDataSql)
    val commonDataMap: Map[Long, Row] = commonDataDF.collect().map((row => row.getAs[Long](0) -> row)).toMap

    for (dataId <- commonDataMap.keys) {
      val elem = commonDataMap.getOrElse(dataId, null)
      if (elem != null) {
        val id = elem.getAs[Long](0)
        var parentId = elem.getAs[Long](1)
        val name = elem.getAs[String](2)
        var nameList = new ListBuffer[String]()
        nameList.+=(name)
        while (parentId != 0) {
          val row = commonDataMap.getOrElse(parentId, null)
          if (row != null) {
            nameList.+=(row.getAs[String](2))
            parentId = row.getAs[Long](1)
          }
        }
        nameList = nameList.reverse
        nameMap.put(id, nameList)
      }
    }
  }
  /**
   * 获取产品问题类型
   */
  val getRxProductVal = functions.udf((data: String, index: Int) => {
    //    println(data)
    if (data == null || data == "") {
      ""
    } else {
      val longData = data.toLong
      val maybeStrings: ListBuffer[String] = nameMap.getOrElse(longData, new ListBuffer[String])
      if(maybeStrings.isEmpty || maybeStrings.size <= index) {
        ""
      }else {
        maybeStrings(index)
      }
    }
  })

  /**
   * 获取等级信息
   */
  val getLevelDesc = functions.udf((data: String, groupId:String) => {
//    println(enumMap)
    val descMap: Option[mutable.HashMap[String, String]] = enumMap.get(groupId)
    if (descMap.isEmpty) {
      data
    }else {
      val desc = descMap.get.getOrElse(data, data)
      desc
    }
  })

  /**
   * 获取客户数据
   */
  val getContaceValue = functions.udf((data: mutable.WrappedArray[String], key:String) => {
    //    println(data)
    if (data == null || data.isEmpty) {
      ""
    } else {
      var str = ""
      for (elem <- data) {
        val jsonObject = JSON.parseObject(elem)
        val label = jsonObject.getString("label")
        if(label == key) {
          str = jsonObject.getString("value")
        }
      }
      //    println(str)
      str
    }
  })

  /**
   * 拼接回复记录
   */
  val formatReply = functions.udf((data: mutable.WrappedArray[String]) => {
    //    println(data)
    if (data == null || data.isEmpty) {
      //      println("data为空")
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val content = jsonObject.getString("content")
        val userName = jsonObject.getString("userName")
        val time = jsonObject.getString("time")

        res = res + "回复内容:" + content + " 回复人:" + userName + " 回复时间:" + time
      })
      //    println(res)
      res
    }
  })

  /**
   * 拼接操作记录
   */
  val formatOperation = functions.udf((data: mutable.WrappedArray[String]) => {
    //    println(data)
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        val assignee = jsonObject.getString("assignee")
        val time = jsonObject.getString("time")
        val taskName = jsonObject.getString("taskName")

        res = res + "操作人: " + assignee + " 操作时间: " + time + " 记录: " + taskName + "|"
      })
      //    println(res)
      res
    }
  })

  private def createElasticsearchClient(): RestHighLevelClient = {
    val lowLevelRestClient: RestClientBuilder =
      RestClient.builder(new HttpHost(EsConf.DukeHost, EsConf.DukePort, "http"))
    val credentialsProvider = new BasicCredentialsProvider
    credentialsProvider
      .setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(EsConf.DukeUser, EsConf.DukePassword))

    lowLevelRestClient.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
      def customizeHttpClient(httpClientBuilder: HttpAsyncClientBuilder): HttpAsyncClientBuilder = {
        httpClientBuilder.disableAuthCaching
        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
      }
    })
    new RestHighLevelClient(lowLevelRestClient)
  }


  private def getScrollData( startTimeStamp: Long,
                             endTimeStamp: Long,
                             templateId: Long,
                             templateGroupId: Long
                           ): ListBuffer[String] = {
    val list: ListBuffer[String] = ListBuffer[String]()


    val searchSourceBuilder: SearchSourceBuilder = new SearchSourceBuilder()
      .query(QueryBuilders.boolQuery()
        .must(QueryBuilders.termQuery("templateId", templateId))
        .must(QueryBuilders.termQuery("templateGroupId", templateGroupId))
        .must(
          QueryBuilders.boolQuery()
            .should(QueryBuilders.rangeQuery("createTime").gte(startTimeStamp).lt(endTimeStamp))
            .should( QueryBuilders.rangeQuery("modifiedTime").gte(startTimeStamp).lt(endTimeStamp))
        )
//        .must(QueryBuilders.termsQuery("processInstanceId", "20231222121710170321863042244972", "20240317182522171067112233185685", "20240317181650171067061021487937"))
      )
      .size(500)
    val searchRequest: SearchRequest = new SearchRequest(indexName)
      .source(searchSourceBuilder)
      .scroll(TimeValue.timeValueMinutes(5L))

    try {
      var searchResponse: SearchResponse = client.search(searchRequest, RequestOptions.DEFAULT)
      var scrollId: String = searchResponse.getScrollId
      var hits: Array[SearchHit] = searchResponse.getHits.getHits
      hits.foreach(searchHit => {
        list.append(searchHit.getSourceAsString)
      })
      val scroll: Scroll = new Scroll(TimeValue.timeValueMinutes(5L))
      while (hits.length > 0) {
        val scrollRequest: SearchScrollRequest = new SearchScrollRequest(scrollId)
        scrollRequest.scroll(scroll)
        searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT)
        scrollId = searchResponse.getScrollId
        hits = searchResponse.getHits.getHits
        hits.foreach(searchHit => {
          list.append(searchHit.getSourceAsString)
        })
      }
      val clearScrollRequest: ClearScrollRequest = new ClearScrollRequest
      clearScrollRequest.addScrollId(scrollId)
      val clearScrollResponse: ClearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT)
      val succeeded: Boolean = clearScrollResponse.isSucceeded
//      logger.info("游标清除" + succeeded)
    } catch {
      case e: IOException =>
        e.printStackTrace()
    }
    list
  }

}
