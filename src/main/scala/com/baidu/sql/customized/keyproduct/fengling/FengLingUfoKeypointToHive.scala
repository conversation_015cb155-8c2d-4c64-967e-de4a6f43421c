package com.baidu.sql.customized.keyproduct.fengling

import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import org.apache.spark.SparkConf
import org.apache.spark.sql.expressions.{UserDefinedFunction, Window}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import scala.collection.mutable
import scala.collection.mutable.{ArrayBuffer, ListBuffer}
import scala.util.matching.Regex


/**
 * <AUTHOR>
 */
object FengLingUfoKeypointToHive {

  var YesterDay: String = ""

  // 所有风铃ufo业务线
  val productMapList = List(
    Map("space_id" -> "334","service_line" -> "百度APP","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "内容付费"),
    Map("space_id" -> "334","service_line" -> "百度APP","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "小说"),
    Map("space_id" -> "334","service_line" -> "百度APP","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "du会员"),
    Map("space_id" -> "335","service_line" -> "pass账号","product_line" -> "pass账号","question_type_first" -> "空间名称","space_name" -> "PASS帐号管理页意见反馈-IOS"),
    Map("space_id" -> "335","service_line" -> "pass账号","product_line" -> "pass账号","question_type_first" -> "空间名称","space_name" -> "PASS帐号管理页意见反馈-Android"),
    Map("space_id" -> "335","service_line" -> "pass账号","product_line" -> "pass账号","question_type_first" -> "空间名称","space_name" -> "帐号问题-其他产品线流转"),
    Map("space_id" -> "335","service_line" -> "pass账号","product_line" -> "pass账号","question_type_first" -> "空间名称","space_name" -> "PASS-PC端帮助中心"),
    Map("space_id" -> "334","service_line" -> "百度文库","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "百度文库"),
    Map("space_id" -> "44","service_line" -> "爱企查","product_line" -> "企业信用","question_type_first" -> "企业问题","space_name" -> ""),
    Map("space_id" -> "44","service_line" -> "爱企查","product_line" -> "企业信用","question_type_first" -> "迁移空间","space_name" -> ""),
    Map("space_id" -> "334","service_line" -> "爱企查","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "爱企查"),
    Map("space_id" -> "334","service_line" -> "百度游戏","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "小游戏"),
    Map("space_id" -> "334","service_line" -> "小程序","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "小程序"),
    Map("space_id" -> "334","service_line" -> "古物潮玩","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "古物潮玩"),
    Map("space_id" -> "334","service_line" -> "消费协调-本地生活","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "基木鱼"),
    Map("space_id" -> "334","service_line" -> "消费协调-小程序","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "小程序"),
    Map("space_id" -> "334","service_line" -> "问一问","product_line" -> "交易支付平台","question_type_first" -> "产品问题类型","space_name" -> "问一问")
    )

  //处理人对应的角色名称和编号
  val userRoleMap = Map(
    "1" -> "超级管理员",
    "2" -> "产品线管理员",
    "3" -> "销售管理员",
    "4" -> "代理商管理员",
    "5" -> "代理商客服经理",
    "6" -> "代理商客服",
    "7" -> "分公司管理员",
    "8" -> "分公司客服经理",
    "9" -> "分公司客服",
    "10" -> "KA管理员",
    "11" -> "KA客服经理",
    "12" -> "KA客服",
    "13" -> "IMO管理员",
    "14" -> "IMO经理",
    "15" -> "IMO",
    "16" -> "区域管理员",
    "17" -> "区域经理",
    "18" -> "区域",
    "30" -> "地区支持",
    "31" -> "地区接口",
    "60" -> "地区接口",
    "19" -> "其他客服（其他渠道或入口账号）",
    "20" -> "产品线机器人",
    "21" -> "PM",
    "22" -> "RD",
    "24" -> "风控",
    "27" -> "运营",
    "26" -> "QA",
    "40" -> "质检",
    "41" -> "总部客服",
    "52" -> "IMS",
    "53" -> "IMS_CRM",
    "100" -> "政令处置组成员",
    "101" -> "政令处置组组长",
    "102" -> "政令MEG产品组成员",
    "103" -> "政令GR",
    "104" -> "政令非MEG产品组成员",
    "106" -> "政令客诉团队成员",
    "107" -> "政令产品组成员",
    "105L" -> "政令下发员",
    "120L" -> "电商专属总接口人",
    "121L" -> "电商专属销售单位接口人（CRM）",
    "122L" -> "电商专属销售单位接口人（UUAP）"
  )

  //case状态Map
  val statusMap = Map(
    "0" -> "状态异常",
    "1" -> "未提交",
    "2" -> "待处理",
    "3" -> "处理中",
    "4" -> "待关闭",
    "5" -> "已转出",
    "6" -> "已关闭"
  )

  //处理回复人数据
  val pTagRegex: Regex = "<p>(.*?)</p>".r

  /*
  * 风铃服务表单同步至udw
  * */
  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)

    val sparkConf = new SparkConf().setAppName("FengLingUfoKeypointToHive")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", "true")
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._

    val schema: StructType = StructType(Seq(
      StructField("case_id", LongType, false),
      StructField("user_name", StringType, false),
      StructField("product_type", StringType, false),
      StructField("function_type", StringType, false),
      StructField("function_detail_type", StringType, false),
      StructField("customlv4", StringType, false),
      StructField("customlv5", StringType, false),
      StructField("feedback_type", StringType, false),
      StructField("remark", StringType, false),
      StructField("risk_level", StringType, false),
      StructField("session_content", StringType, false),
      StructField("userid", StringType, false)
    ))

    //查询新建数据
    val createDf = transDataFrame(spark, schema,"新建")
      .withColumn("icafe_current_status",lit("新增"))
    println("查询新建数据一共:" + createDf.count())

    //查询修改数据
    val updateResDf = transDataFrame(spark, schema,"修改")
      .withColumn("icafe_current_status",lit("修改"))
    println("查询修改数据一共:" + updateResDf.count())

    val combinedDF = createDf.unionByName(updateResDf)
    println(s"合并后的数据量为${combinedDF.count()}条")
    combinedDF.show(5,false)
    combinedDF.createOrReplaceTempView("combined_data")

    //数据写入到hive中
    spark.sql(
      s"""
         |insert into table udw_ns.default.help_ods_ufo_key_point_productline_di partition (event_day = ${ YesterDay} )
         |select
         |   product_line, --产品线
         |   '' as work_type,
         |   '' as channel_platform_space,
         |   space_name,
         |   '' as space_id,
         |   '' as space_id_old,
         |   id,
         |   '' as extend_feedback_channel,
         |   '' as need_manual,
         |   product_type,
         |   function_type,
         |   function_detail_type,
         |   customlv4,
         |   customlv5,
         |   '' as complaint_type,
         |   '' as complaint_deal_result,
         |   '' as complaint_is_result,
         |   '' as robot_eva_stat,
         |   '' as robot_eva_sloved,
         |   '' as robot_eva_advise,
         |   '' as robot_eva_stat_label,
         |   '' as intp_digital_man,
         |   '' as is_pipe,
         |   '' as robot_eva_diss_content,
         |   '' as mobile_app_name,
         |   '' as evaluation_count,
         |   '' as invite_evaluation_type,
         |   '' as evaluation_filter,
         |   '' as invite_evaluation_filter,
         |   '' as discontent_reason_filter,
         |   '' as discontent_labels_filter,
         |   '' as audit_pass,
         |   '' as audit_reason,
         |   '' as violence_score,
         |   '' as salacity_score,
         |   '' as politics_score,
         |   '' as robot_reply,
         |   '' as first_reply_length_second,
         |   '' as pipe_reply,
         |   '' as manual_reply,
         |   userid,
         |   '' as ip,
         |   '' as ip_province,
         |   '' as mobile_model,
         |   '' as mobile_brand,
         |   update_user,
         |   status,
         |   '' as valid,
         |   feedback_type,
         |   submit_time,
         |   '' as icafe_filter,
         |   '' as from_product_line,
         |   '' as pre_product_info,
         |   risk_level,
         |   '' as to_product_line,
         |   '风铃' as channel,
         |   '' as session_id,
         |   user_name,
         |   '' as baijiahao_user_id,
         |   '' as baijiahao_name,
         |   '' as baijiahao_type,
         |   session_content,
         |   '' as quesiton_desc,
         |   remark,
         |   '' as user_evaluate,
         |   '' as evaluate_csi,
         |   '' as is_solve,
         |   '' as accept_group,
         |   '' as is_change,
         |   update_time,
         |   custom_label,
         |   '' as scene_type,
         |   '' as close_type,
         |   '' as im_session_id,
         |   '' as prod_type_list,
         |   '' as ernie_bot_flag,
         |   '' as sessionz_time,
         |   '' as first_handle_time,
         |   '' as avg_handle_time,
         |   '' as not_accept,
         |   '' as update_user_list,
         |   '' as im_session_status,
         |   '' as sys_info,
         |   '' as risk_type,
         |   '' as good_evaluation,
         |   '' as bad_evaluation,
         |   '' as no_evaluation,
         |   '' as rx_process_instance_id,
         |   '' as rx_relation_id,
         |   '' as rx_close_time,
         |   '' as rx_subject,
         |   '' as rx_process_definition_name,
         |   '' as rx_temp_create_time,
         |   '' as rx_temp_modified_time,
         |   '' as rx_temp_create_user,
         |   '' as rx_temp_modified_user,
         |   '' as rx_temp_customer_id,
         |   '' as rx_temp_title,
         |   '' as rx_temp_is_refund,
         |   '' as rx_temp_refund_order_id,
         |   '' as rx_temp_handle_process,
         |   '' as rx_temp_sex,
         |   '' as rx_temp_age,
         |   '' as rx_temp_education,
         |   '' as rx_temp_rsjd,
         |   '' as rx_temp_is_self,
         |   '' as rx_temp_call_phone,
         |   '' as rx_temp_call_cnt,
         |   '' as rx_temp_one_level,
         |   '' as rx_temp_two_level,
         |   '' as rx_temp_three_level,
         |   '' as rx_customer_username,
         |   '' as rx_customer_account_name,
         |   '' as rx_customer_web_url,
         |   '' as rx_customer_company_name,
         |   '' as rx_customer_unit,
         |   '' as rx_customer_first_people,
         |   '' as rx_customer_annotaions,
         |   '' as rx_customer_account_status,
         |   '' as rx_reply_record,
         |   '' as rx_operations,
         |   '' as rx_temp_candidate,
         |   '' as rx_temp_last_person,
         |   '' as rx_temp_question_type,
         |   '' as rx_temp_cross_system,
         |   '' as rx_temp_cross_system_status,
         |   '' as rx_temp_operate_unit,
         |   '' as rx_temp_promotion_username,
         |   '' as rx_temp_area,
         |   '' as rx_temp_account_industry,
         |   '' as auto_content_id,
         |   '' as auto_content_csi,
         |   '' as auto_content_fcr,
         |   '' as auto_satisfaction_label,
         |   '' as auto_advise_text,
         |   '' as artificial__satisfaction_label,
         |   '' as artificial_advise_text,
         |   '' as round_num,
         |   '' as icafe_last_pull_time,
         |   '' as icafe_status,
         |   '' as icafe_flag,
         |   '' as is_transfer,
         |   '' as upvote_cnt,
         |   '' as downvote_cnt,
         |   '' as wen_correct_cnt,
         |   '' as wen_error_cnt,
         |   '' as wen_unanswered_cnt,
         |   '' as wen_error_detail,
         |   '' as wen_unanswered_detail,
         |   '' as is_session_monitor,
         |   '' as sessionz_label_values,
         |   '' as hit_cheating_strategy,
         |   '' as user_agent,
         |   '' as cuid,
         |   '' as channel_id,
         |   '' as channel_id_map,
         |   icafe_current_status,
         |   '' as rx_user_approval_result,
         |   '' as rx_risk_level,
         |   '' as rx_risk_type_classification,
         |   '' as rx_risk_type_classification_new,
         |   '' as rx_population_type,
         |   '' as rx_repetitive_appeal,
         |   '' as rx_timeliness,
         |   '' as rx_identity_attribute,
         |   '' as rx_appeal_attribute,
         |   '' as rx_turn_user_flag,
         |   '' as rx_hangup_type,
         |   '' as rx_meet_num,
         |   '' as rx_start_time,
         |   '' as rx_end_time,
         |   '' as rx_duration,
         |   '' as rx_process_tag,
         |   '' as rx_skill_group_name,
         |   '' as rx_queue_duration,
         |   '' as rx_ring_duration,
         |   '' as rx_talk_duration,
         |   '' as rx_end_type_name,
         |   '' as rx_keypress_name,
         |   '' as rx_is_repeat,
         |   '' as cz_fb_url,
         |   '' as cz_extend_query,
         |   '' as cz_relation_words,
         |   '' as cz_adjoint_word_supple,
         |   '' as cz_user_urls,
         |   '' as zx_queue_total_time,
         |   '' as zx_sessionz_total_time,
         |   '' as zx_is_roll_out,
         |   '' as zx_roll_out_product_line,
         |   cz_extend_url,
         |   '' as rx_robot_name,
         |   '' as rx_robot_number,
         |   '' as rx_resource_id,
         |   '' as rx_resource_name,
         |   '' as rx_is_upgrade,
         |   '' as rx_call_in_start_time,
         |   '' as rx_call_in_end_time,
         |   '' as zx_is_now_prod_sessionz,
         |   '' as rx_phone_num,
         |   '' as product_before_line,
         |   '' as cz_extend_keyword,
         |   '' as app_vn,
         |   fl_product_line_type,
         |   fl_product_line,
         |   question_type_first,
         |   case_id,
         |   opt_conclusion,
         |   check_label,
         |   assign_processor_history,
         |   actual_processor_history,
         |   newest_question_time,
         |   first_check_finish_time,
         |   check_finish_processor,
         |   is_reask,
         |   '' as zx_is_fill_form, -- 是否填写表单
         |   '' as zx_form_name, -- 表单名称
         |   '' as zx_form_content, -- 表单内容
         |   '' as zx_user_num, -- 人工会话轮次
         |   check_finish_processor_all, -- 结束排查人（全部）
         |   check_finish_time_all, -- 结束排查时间（全部）
         |   '' as cz_is_question_valid, -- 问题是否有效(涉诈)
         |   '' as cz_in_product_info, -- 转入产品线（涉诈）
         |   '' as cz_manual_remark, -- 人工备注（涉诈）
         |   '' as cz_feedback_position, -- 反馈位置
         |   '' as zx_risk_label, -- 危险用户标记
         |   '' as zx_user_risk_label, -- 用户风险标记
         |   '' as zx_session_details, -- 在线用户多轮对话内容明细
         |   '' as zx_bjh_is_encouragement, -- 百家号是否鼓励层用户
         |   '' as zx_bjh_equity_level -- 百家号权益等级
         |from combined_data""".stripMargin)

    spark.close()
  }

  /**
   * 去重不是数字的字符串
   */
  val dealNumData = udf((data:String) => {
    if (data == null || data.isEmpty) {
      ""
    }else {
      data.replaceAll("[^0-9]","")
    }
  })

  /**
   * 或缺用户角色
   */
  val dealUserRole = udf((data: String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      userRoleMap.getOrElse(data, "")
    }
  })

  /**
   * 匹配case状态名称
   */
  val dealCaseStatus = udf((data:String) => {
    if (data == null || data.isEmpty) {
      ""
    }else{
      statusMap.getOrElse(data,"")
    }
  })

  //获取处理人回复
  val dealOpt: UserDefinedFunction = udf((data: String) => {
    var pdata = ""
    if (data == null || data == "") {
      ""
    } else {
      pdata = pTagRegex.findAllMatchIn(data)
        .map(x => x.group(1)).toArray.mkString(" ")
        .replaceAll("<span.*?>", "")
        .replaceAll("</span>", "")
        .replaceAll("<br>", "")
        .replaceAll("&nbsp;", "")
        .replaceAll("\" rel=\".*?\">", " ")
        .replaceAll("<.*?=\"", " ")
        .replaceAll("</.*?>", " ")
        .replaceAll("\">", " ")
        .replaceAll("[\\t|\\r]", "")
        .replaceAll("\\n", " ")
    }
    pdata
  })

  /**
   * 处理历史实际处理人，+当前处理人
   */
  val dealActiveProcess = udf((submitUser:String,data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      val array = mutable.WrappedArray.empty[String]
      array :+ submitUser
    }else{
      val uniqueSet = (data.toSet + submitUser) // 将data转换为Set，添加submitUser，自动去重
      uniqueSet.toArray[String] // 将Set转换回Array，如果需要mutable.WrappedArray，则在下一步进行转换
        .to[mutable.WrappedArray] // 将Array转换为mutable.WrappedArray
    }
  })

  /**
   * 特殊数值空值判断
   */
  val getOtrherNullVal = udf((data: String) => {
    if (data == null || data.isEmpty || data == ",," || data == "--") {
      ""
    } else {
      data.replaceAll("[\\t|\\r]","").replaceAll("\\n"," ")
    }
  })

  /**
   * 读取一天的case信息
   * @param spark
   * @param  YesterDay 运行日期
   * @param imgFunc 自定义的udf
   * @param schema 数据模型
   * @param dataType 查询数据类型：新建、已关闭
   * @return
   */
  def transDataFrame(spark:SparkSession, schema: StructType,dataType:String): DataFrame = {

    val queryDate = dataType match {
      case "新建" => "create_time"
      case "修改" => "update_time"
    }

    val querySql = dataType match {
      case "新建" => ""
      case "修改" => "and DATE_FORMAT(update_time,'%Y%m%d') != DATE_FORMAT(create_time,'%Y%m%d')"
    }

    val resultDfList: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    //遍历所有的业务线
    for (productMap <- productMapList){
      //业务线id
      val space_id = productMap.getOrElse("space_id","")
      //业务线名称
      val service_line = productMap.getOrElse("service_line","")
      //产品线名称
      val product_line = productMap.getOrElse("product_line","")
      //一级问题类型
      val question_type_first = productMap.getOrElse("question_type_first","")
      //二级问题类型
      val space_name = productMap.getOrElse("space_name","")

      //查询case的基础信息数据
      val caseInfoDf = getMysqlData(spark,queryDate,querySql,space_id,question_type_first,space_name)

      //获取case_id列表
      val idBaseList = caseInfoDf.select("case_id").distinct().rdd.map(row => row.getLong(0)).collect()
      // 创建广播变量并保留引用
      val broadcastVar = spark.sparkContext.broadcast(idBaseList)
      // 使用广播变量
      val caseidString = broadcastVar.value.mkString("'", "','", "'")

      //历史分配处理人，结束排查操作人
      val caseDf = getCaseData(spark,caseidString)

      //将case基础信息与历史分配处理人进行合并
      val caseResDf = caseInfoDf.join(caseDf,Seq("case_id"),"left_outer")
        .withColumn("product_line",lit(service_line))
        .na.fill("")
        .dropDuplicates()
        .repartition(10)
        .cache()
      
      resultDfList.append(caseResDf)
      broadcastVar.destroy() //释放广播变量
    }
    //将所有产品线的数据进行合并
    val combinedDF: DataFrame = resultDfList.reduce((df1, df2) => df1.union(df2))
      .repartition(10)
      .cache()
    println("combinedDF数据量：" + combinedDF.count())

    val uniqueCaseDf = combinedDF.dropDuplicates("case_id")
      .select("case_id")

    // 2. 转换为广播变量
    val broadcastCaseIds = spark.sparkContext.broadcast(
      uniqueCaseDf
        .rdd
        .map(row => row.getAs[Long]("case_id"))
        .collect()
    )

    // 3. 在JDBC读取时过滤（使用下推查询）
    val inClause = broadcastCaseIds.value.mkString("'", "','", "'")

    //账户名称，产品问题类型 （5个字段），UFO反馈类型，备注，风险等级
    val getLogFiledDataDf = getLogFiledData(spark,inClause,combinedDF,schema)
      .repartition(10)
      .cache()
    println(s"${dataType} 的getLogFiledDataDf数据量：" + getLogFiledDataDf.count())
    //getLogFiledDataDf.show(10,false)

    //自定义标签, 排查标签, 是否追问
    val getCaseLableDataDf = getCaseLableData(spark,inClause,combinedDF)
      .repartition(10)
      .cache()
    println(s"${dataType} 的getCaseLableDataDf数据量：" + getCaseLableDataDf.count())
    //getCaseLableDataDf.show(5,false)

    //所有字段全部合并
    val resultDf = combinedDF.as("C")
      .join(getLogFiledDataDf.as("L"),col("C.case_id") === col("L.case_id"), "left_outer")
      .join(getCaseLableDataDf.as("G"),col("C.case_id") === col("G.case_id"), "left_outer")
      .select(
        col("C.product_line").cast("string") as "product_line",
        col("C.status").cast("string") as "status",
        col("C.fl_product_line_type").cast("string") as "fl_product_line_type",
        col("C.fl_product_line").cast("string") as "fl_product_line",
        col("C.question_type_first").cast("string") as "question_type_first",
        col("C.space_name").cast("string") as "space_name",
        col("L.feedback_type").cast("string") as "feedback_type",
        col("L.product_type").cast("string") as "product_type",
        col("L.function_type").cast("string") as "function_type",
        col("L.function_detail_type").cast("string") as "function_detail_type",
        col("L.customlv4").cast("string") as "customlv4",
        col("L.customlv5").cast("string") as "customlv5",
        col("C.case_id").cast("string") as "case_id",
        col("C.id").cast("string") as "id",
        col("C.submit_time").cast("string") as "submit_time",
        col("C.update_time").cast("string") as "update_time",
        col("C.opt_conclusion").cast("string") as "opt_conclusion",
        col("G.custom_label").cast("string") as "custom_label",
        col("G.check_label").cast("string") as "check_label",
        col("C.update_user").cast("string") as "update_user",
        col("C.assign_processor_history").cast("string") as "assign_processor_history",
        col("C.actual_processor_history").cast("string") as "actual_processor_history",
        col("C.newest_question_time").cast("string") as "newest_question_time",
        col("L.userid").cast("string") as "userid",
        col("L.user_name").cast("string") as "user_name",
        col("L.session_content").cast("string") as "session_content",
        col("C.first_check_finish_time").cast("string") as "first_check_finish_time",
        col("C.check_finish_processor").cast("string") as "check_finish_processor",
        col("C.check_finish_processor_all").cast("string") as "check_finish_processor_all",
        col("C.check_finish_time_all").cast("string") as "check_finish_time_all",
        col("G.is_reask").cast("string") as "is_reask",
        col("L.remark").cast("string") as "remark",
        col("L.risk_level").cast("string") as "risk_level",
        col("C.cz_extend_url").cast("string") as "cz_extend_url"
      )
      .na.fill("")
      .withColumn("is_reask",when(col("is_reask").equalTo(""),"否").otherwise(col("is_reask")))
      .dropDuplicates()
      .repartition(10)
      .cache()

    broadcastCaseIds.destroy() //释放广播变量
    resultDf
  }

  /**
   * 获取mysql数据
   * @param saprk
   * @param queryDate
   * @param querySql
   * @return
   */
  def getMysqlData(spark: SparkSession,queryDate:String,querySql:String,product_id:String,question_type_first:String,space_name:String): DataFrame = {
    //如果space_name为空，则不进行过滤
    var queryFilter = ""
    if(space_name.equals("")){
      queryFilter = ""
    }else{
      queryFilter = s"where name = '${space_name}'"
    }

    val caseInfo =
      s"""
         |SELECT distinct
         |	  n.name as node_name,
         |    p.`status`,  -- 状态
         |    '其他' as fl_product_line_type, -- 产品线分类
         |		p.product_line_id, -- 产品线ID
         |		r.fl_product_line, -- 产品线
         |		t.question_type_first,		-- 一级问题分类
         |		q.space_name,		-- 二级问题分类
         |		p.case_id,-- case编号
         |		p.title as id, -- case标题
         |		p.submit_time, -- 提交时间
         |		p.update_time, -- 更新时间
         |		u.user_name,
         |    p.role_id,		-- 当前处理人
         |		l.case_deal_reslult as opt_conclusion,	-- 处理结论
		     |    l.create_time as deal_create_time, -- 处理创建时间
         |		p.STATUS AS case_status,	-- case状态
         |  	CONCAT('https://ifengling.baidu.com/#/process/detail?caseId=',p.case_id) as cz_extend_url -- case链接
         |FROM
         |	( SELECT product_line_id, question_id AS question_id2, id AS case_id,
         |           submit_user_id,submit_time,update_time,status,title,role_id,
         |           process_template_id
         |    FROM case_info
         |    WHERE DATE_FORMAT(${queryDate},'%Y%m%d') = '$YesterDay' and product_line_id = '${product_id}' ${querySql}) p
         |	JOIN (select id,name as space_name,parent_id from question ${queryFilter}) q ON p.question_id2 = q.id
         |	JOIN (select id,name as question_type_first from question where name = '${question_type_first}') t on q.parent_id = t.id
         |	JOIN (select id,user_name from userz) u on p.submit_user_id = u.id
         | 	JOIN (select process_template_id,name from process_node where name not in ('START','END'))n on p.process_template_id = n.process_template_id
         | 	JOIN (select id,name as fl_product_line from product_line)r on p.product_line_id = r.id
         |	LEFT JOIN (SELECT case_id, case_deal_reslult,create_time FROM case_deal_result) l on p.case_id = l.case_id
         |""".stripMargin

    //获取一天的case信息
    val caseInfoDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseInfo)
    val baseDf = caseInfoDf
      .na.fill("")
      .withColumn("update_user",concat_ws("-",col("user_name"),dealUserRole(col("role_id").cast("string"))))
      .withColumn("status",concat_ws("-",col("node_name"),dealCaseStatus(col("status"))))
      //对id字段进行数字处理
      .withColumn("id",dealNumData(col("id")))
      .selectExpr(
        "status",
        "fl_product_line_type",
        "fl_product_line",
        "question_type_first",
        "space_name",
        "id",
        "submit_time",
        "update_time",
        "update_user",
        "cz_extend_url",
        "case_id")
      .dropDuplicates()
      .repartition(10)

    //对处理结论进行处理
    val optDf = caseInfoDf
      .withColumn("rn",row_number().over(Window.partitionBy("case_id").orderBy(col("deal_create_time").desc)))
      .filter(col("rn") === 1)
      .selectExpr("case_id","opt_conclusion","deal_create_time")
      .withColumn("opt_conclusion",dealOpt(col("opt_conclusion")))
      .dropDuplicates()
      .repartition(10)

    val resCaseDf = baseDf.join(optDf,Seq("case_id"),"left_outer")
      .selectExpr(
        "status",
        "fl_product_line_type",
        "fl_product_line",
        "question_type_first",
        "space_name",
        "id",
        "submit_time",
        "update_time",
        "update_user",
        "cz_extend_url",
        "case_id",
        "opt_conclusion")
      .dropDuplicates()
      .repartition(10)
      .cache()
    resCaseDf.count()

    //计算 最新追问时间newest_question_time，case首次结束排查时间first_check_finish_time，历史实际处理人actual_processor_history
    val caseTrans =
      s"""
         |SELECT distinct
         |	f.case_id, -- case编号
         |  u.user_name,
         |  u.role_id,
         |  t.deal_start_time as process_start_time, -- 处理开始时间
         |  t.deal_end_time as process_end_time, -- 处理结束时间
         |  t.bpm_activity_id -- 判断生效时间用的
         |from
         |(select id as case_id,question_id AS question_id2 from case_info
         | where DATE_FORMAT(${queryDate},'%Y%m%d') = '${YesterDay}'
         | and product_line_id = '${product_id}' ${querySql}) f
         |join (select id,name as space_name,parent_id from question ${queryFilter}) q ON f.question_id2 = q.id
         |join (select id, name as question_type_first from question where name = '${question_type_first}') t on q.parent_id = t.id
         |left join deal_log t on f.case_id = t.case_id
         |left join (select id,user_name,role_id from userz) u on t.deal_user_id = u.id
         |""".stripMargin

    //first_check_finish_time需要正序第一的process_end_time，newest_question_time需要倒序第一的process_start_time
    val caseTransDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseTrans)
      .na.fill("")
      .withColumn("process_history",concat_ws("-",col("user_name"),dealUserRole(col("role_id").cast("string"))))
      .cache()

    //取first_check_finish_time case首次结束排查时间
    val firstCheckDf = caseTransDf
      .filter(col("bpm_activity_id") =!= "")
      .withColumn("rn_asc",row_number().over(Window.partitionBy("case_id").orderBy(col("process_end_time"))))
      .filter(col("rn_asc") === 1)
      .select(col("case_id"),col("process_end_time") as "first_check_finish_time")
      .cache()

    //取newest_question_time 最新追问时间
    val newestQuestionDf = caseTransDf
      .filter(col("bpm_activity_id") =!= "")
      .withColumn("rn_desc",row_number().over(Window.partitionBy("case_id").orderBy(col("process_start_time").desc_nulls_last)))
      .filter(col("rn_desc") === 1)
      .select(col("case_id"),col("process_start_time") as "newest_question_time")
      .cache()

    //取actual_processor_history 历史实际处理人
    val actualProcessorDf = caseTransDf
      .groupBy("case_id")
      .agg(collect_set("process_history").alias("actual_processor_history"))
      .select(col("case_id"), col("actual_processor_history") ) // 选择需要的列
      .cache()

    //根据case_id关联，合并所有字段
    val caseTransJoinDf = firstCheckDf
      .join(actualProcessorDf,Seq("case_id"))
      .join(newestQuestionDf,Seq("case_id"))
      .select(
        "case_id",
        "newest_question_time",
        "first_check_finish_time",
        "actual_processor_history"
      )
      .dropDuplicates()
      .repartition(2)
    caseTransJoinDf.count()
    //println(s"读取${space_name} caseTransDf的数量为" + caseTransJoinDf.count())

    val resDf = resCaseDf.join(caseTransJoinDf,Seq("case_id"),"left_outer")
      .select(
        "status",
        "fl_product_line_type",
        "fl_product_line",
        "question_type_first",
        "space_name",
        "case_id",
        "id",
        "submit_time",
        "update_time",
        "opt_conclusion",
        "update_user",
        "cz_extend_url",
        "newest_question_time",
        "first_check_finish_time",
        "actual_processor_history"
      )
      .na.fill("")
    resDf.count()
    println(s"读取${space_name} resDf的数量为" + resDf.count())
    //resDf.show(5,false)
    resDf
  }

  /**
   * 获取case数据
   * @param spark
   * @param caseInfo
   * @return
   */
  def getCaseData(spark: SparkSession,caseidString:String) = {

    //历史分配处理人
    val caseProcessor =
      s"""
         |SELECT distinct
         | c.case_id,
         | z.user_name,
         | z.role_id
         |from (select case_id,user_id from case_next_user where case_id in (${caseidString})) c
         |left join userz z on c.user_id = z.id
         |""".stripMargin

    val caseProcessorDf = 
      CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseProcessor)
        .na.fill("")
        //将账号名和角色拼接
        .withColumn("process_user_role",concat_ws("-",col("user_name"),dealUserRole(col("role_id").cast("string"))))
        .groupBy("case_id")
        .agg(collect_set("process_user_role").alias("assign_processor_history"))

    caseProcessorDf.count()
    //println("读取caseProcessorDf的数量为" + caseDf.count())

    //结束排查操作人
    val checkProcessor =
      s"""
         |SELECT distinct
         | c.create_time,
         | c.case_id,
         | z.user_name as check_finish_processor
         |from
         |(select case_id,create_time,userid from case_deal_result where case_id in (${caseidString})) c
         |left join userz z on c.userid = z.id
         |order by c.create_time
         |""".stripMargin

    val checkProcessorDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,checkProcessor)
    //结束排查操作人取最近一次
    val checkDf = checkProcessorDf
      .na.fill("")
      .withColumn("RN",row_number().over(Window.partitionBy("case_id").orderBy(desc("create_time"))))
      .filter(col("RN") === 1)
      .drop("RN")
      .dropDuplicates()
    checkDf.count()

    //结束排查操作人取全部数据
    val checkListDf = checkProcessorDf
      .sort(col("create_time").asc_nulls_last)
      .groupBy("case_id")
      .agg(
        concat_ws(",",collect_list("check_finish_processor")).alias("check_finish_processor_all"),
        concat_ws(",",collect_list("create_time")).alias("check_finish_time_all")
      )
    checkListDf.count()

    //合并两个字段
    val resDf = caseProcessorDf
      .join(checkDf,Seq("case_id"))
      .join(checkListDf,Seq("case_id"))
      .select(
        "case_id",
        "assign_processor_history",
        "check_finish_processor",
        "check_finish_processor_all",
        "check_finish_time_all"
      )
      .dropDuplicates()
      .repartition(2)

    resDf
  }


  /**
   * 获取deal_log_filed字段数据
   * username: 账户名称 product_classification: 产品问题类型 （5个字段）
   * feedback_type: UFO反馈类型 remark: 备注 risklevel: 风险等级
   * @param spark
   * @param caseInfo
   * @return
   */
  def getLogFiledData(spark: SparkSession,inClause:String,caseInfo:DataFrame,schema: StructType) = {

    val dealLog =
      s"""
         |SELECT DISTINCT
         |    d.create_time,
         |    d.case_id,
         |    d.form_eng_name,
         |    d.value
         |FROM deal_log_filed d
         |INNER JOIN (
         |    SELECT
         |        case_id,
         |        form_eng_name,
         |        MAX(update_time) AS max_update_time
         |    FROM deal_log_filed
         |    WHERE case_id IN (${inClause})  -- 隐含DISTINCT
         |    GROUP BY case_id, form_eng_name
         |) m ON d.case_id = m.case_id
         |    AND d.form_eng_name = m.form_eng_name
         |    AND d.update_time = m.max_update_time
         |""".stripMargin

    val dealLogDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,dealLog)

    val caseDf = caseInfo.dropDuplicates("case_id").as("C")
      .join(dealLogDf.as("P"),col("C.case_id") === col("P.case_id"))
      .select(
        col("C.case_id").cast("long"),
        col("P.form_eng_name"),
        col("P.value")
      )
      .dropDuplicates()
      .repartition(2)

    caseDf.count()
    println("读取caseProcessorDf的数量为" + caseDf.count())

    val filterDf = caseDf.rdd.map(row => (row.getAs[Long]("case_id"), row.getAs[String]("form_eng_name") -> row.getAs[String]("value")))
      .groupByKey()
      .mapValues(iter => iter.toMap)
      .map({ case (caseId, engNameArray) =>
        val buffer = ArrayBuffer.empty[Any]
        //账户名称
        val username = engNameArray.getOrElse("username", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        //产品问题类型，爱企查空间取数逻辑稍有不同
        val product_classification = engNameArray.getOrElse("product_classification","").replaceAll("\\[\"", "").replaceAll("\"]", "").split("\",\"")
        val product_type = if (product_classification.length > 0) product_classification(0) else ""
        val function_type = if (product_classification.length > 1) product_classification(1) else engNameArray.getOrElse("ywfl","").replaceAll("\\[\"", "").replaceAll("\"]", "").trim
        val function_detail_type = if (product_classification.length > 2) product_classification(2) else engNameArray.getOrElse("ejfl","").replaceAll("\\[\"", "").replaceAll("\"]", "").trim
        val customlv4 = if (product_classification.length > 3) product_classification(3) else ""
        val customlv5 = if (product_classification.length > 4) product_classification(4) else ""
        //UFO反馈类型
        val feedback_type = engNameArray.getOrElse("feedback_type", "").replaceAll("\\[\"", "").replaceAll("\"]", "")
        //备注
        val remark = engNameArray.get("remark").orElse(engNameArray.get("tssq")).getOrElse("")
        val risklevel = engNameArray.get("risklevel").orElse(engNameArray.get("sfsjts")).getOrElse("").replaceAll("\\[\"", "").replaceAll("\"]", "")
        //问题描述
        val session_content = engNameArray.getOrElse("describe", "").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")
        val userid = engNameArray.get("userid").orElse(engNameArray.get("passportid")).getOrElse("").replaceAll("[\\t|\\r]", "").replaceAll("\\n", " ")

        buffer.append(caseId)
        buffer.append(username)
        buffer.append(product_type)
        buffer.append(function_type)
        buffer.append(function_detail_type)
        buffer.append(customlv4)
        buffer.append(customlv5)
        buffer.append(feedback_type)
        buffer.append(remark)
        buffer.append(risklevel)
        buffer.append(session_content)
        buffer.append(userid)
        Row.fromSeq(buffer)
      })

    val filterRes = spark.createDataFrame(filterDf, schema)
      .na.fill("")
      .dropDuplicates("case_id")
      .repartition(2)

    filterRes.count()
    //println("读取filterRes的数量为" + filterRes.count())
    filterRes
  }

  /**
   * 获取case_label字段数据 custom_label自定义标签, check_label排查标签, is_reask是否追问
   * @param spark
   * @param caseInfo
   * @return
   */
  def getCaseLableData(spark: SparkSession,inClause:String,caseInfo:DataFrame) = {

    import spark.implicits._

    val caseLabel =
      s"""
         |select
         |  case_id,
         |  label,
         |  value
         |FROM case_label
         |where case_id in (${inClause})
         |""".stripMargin
    val caseLabelDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFengLingProperties,caseLabel)

    //获取标签和对应的值
    val caseDf = caseInfo.dropDuplicates("case_id").as("C")
      .join(caseLabelDf.as("P"),col("C.case_id") === col("P.case_id"))
      .select(
        col("C.case_id").cast("long"),
        col("P.label"),
        col("P.value")
      )
      .na.fill("")
      .dropDuplicates()
      .repartition(2)

    caseDf.count()

    val filterDf = caseDf
      .rdd
      .map(row => (row.getAs[Long]("case_id"), (row.getAs[String]("label"), row.getAs[String]("value"))))
      .groupByKey()
      .mapValues { iter =>
        // 直接将迭代器转换为列表，保留元素的顺序
        iter.toList
      }
      .map { case (caseId, labelValuesList) =>
        // 遍历列表，按需处理 label 和 value
        val custom_label_builder = new StringBuilder()
        val check_label_builder = new StringBuilder()
        var is_reask = "否"

        labelValuesList.foreach { case (label, value) =>
          label match {
            case "CUSTOM_LABEL" =>
              if (custom_label_builder.nonEmpty) custom_label_builder.append(",")
              custom_label_builder.append(value)
            case "API_TROUBLESHOOT_LABEL_PAICHATU_TAG_" =>
              if (check_label_builder.nonEmpty) check_label_builder.append(",")
              check_label_builder.append(value)
            case "CASE_CLAZZ" if value == "ASK" =>
              is_reask = "是"
            case _ => // 忽略其他标签
          }
        }

        val custom_label = custom_label_builder.toString()
        val check_label = check_label_builder.toString()

        (caseId, custom_label, check_label, is_reask)
      }
      //custom_label自定义标签, check_label排查标签, is_reask是否追问
      .toDF("case_id", "custom_label", "check_label", "is_reask")
      .na.fill("")
      .dropDuplicates("case_id")
      .repartition(2)

    filterDf.count()
    filterDf.dropDuplicates()
  }
}
