package com.baidu.sql.customized.keyproduct.commons

/**
 * <AUTHOR>
 * @date date 2024/2/5
 * @time 14:26
 * @package_name com.baidu.sql.customized.keyproduct.commons
 */
object EsConf {
  //follow地址
  val ImUser = "superuser"
  val ImPassword = "metis_MegSed"
  val ImHost = "************"
  val ImPort = 8200

  //Duke用户中心
  val DukeUser = "superuser"
  val DukePassword = "ZhiLian0308"
  val DukeHost = "***********"
  val DukePort = 8200

  //Duke版权系统
  val DukeBanQuanUser = "duke"
  val DukeBanQuanPassword = "duke123"
  val DukeBanQuanHost = "***********"
  val DukeBanQuanPort = 8200

  //迟滞UFO地址
  val UfoUser = "ufo"
  val UfoPassword = "Ufo!123apptest"
  val UfoHost = "*************"
  val UfoPort = 8020
}
