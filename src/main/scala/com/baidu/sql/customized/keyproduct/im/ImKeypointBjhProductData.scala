package com.baidu.sql.customized.keyproduct.im

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import com.baidu.sql.customized.keyproduct.im.ImKeypointFeedback.{robot_eva_sloved, robot_eva_stat}
import com.baidu.sql.utils.SparkUtils.{getHttpurl, unionIfNotEmpty}
import com.baidu.sql.utils.TimeFormat.SEC_FORMAT_MYSQL
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils, TimeOperateUtil}
import okhttp3._
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.SparkConf
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}

import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDateTime, ZoneId}
import java.util.Base64
import scala.collection.mutable
import scala.collection.mutable.{ArrayBuffer, ListBuffer}
import scala.util.control.Breaks.{break, breakable}

object ImKeypointBjhProductData extends Serializable {

  val indexName = "metis_access_rt_release"

  //产品线名称，处理前，处理后名称。
  var productNameMap: Map[Long, (String, String)] = Map(
    6L -> ("百家号作者服务平台","百家号"),
  )

  var contentMap: collection.Map[Long, String] = null

  //开始时间 20241228
  var startTimeStamp:Long = 0L
  //结束时间 20241229
  var endTimeStamp:Long = 0L
  //结束时间天数+1 20241230
  var endTomorrowTimeStamp :Long = 0L

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * */

    // 分区时间
    val YesterDay: String = args(0)
    // 获取YesterDay豪秒级时间戳
    startTimeStamp = TimeOperateUtil.getTimeStamp(YesterDay)
    endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))
    endTomorrowTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,2))

    println(s"开始时间戳为：$startTimeStamp")
    println(s"结束时间戳为：$endTimeStamp")

    val sparkConf = new SparkConf().setAppName("IMReadEs")
    //sparkSession配置
    val spark = new SparkSession
      .Builder()
      .master("local[*]")
      //.config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.debug.maxToStringFields", "200")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      //.enableHiveSupport()
      .getOrCreate()

    spark.sparkContext.setLogLevel("WARN")

    //解决follow_feedback_channel反馈渠道字段
    val resDfList = readFollowConf(spark)

    //转成id -> name的映射
    contentMap = getAutoContentIdMap(spark)

    //获取新建数据
    val createDf = dealWithEsData(spark,resDfList,"新增")
      .withColumn("icafe_current_status", lit("新增"))
    println("获取新建的数据为：" + createDf.count())
    /*createDf
      .filter(col("im_session_id") === "__METIS_IM__-2613783285_6-6_2001146_3000567_4000767_5001042-1755527518224x8QacI")
      .select("no_agent_online_flag")
      .show(false)*/

    //获取修改数据
    val updateDf = dealWithEsData(spark,resDfList,"修改")
      .withColumn("icafe_current_status", lit("修改"))
    println("获取修改的数据为：" + updateDf.count())

    val combinedDF = unionIfNotEmpty(createDf,updateDf)
    println(s"合并后的数据量为${combinedDF.count()}条")
    combinedDF.createOrReplaceTempView("combined_data")

    //获取ip和ip_province
    val urlDf = getIpUrl(spark,combinedDF)
    println("获取ip和ip_province的数据为：" + urlDf.count())
    urlDf.createOrReplaceTempView("url_province")

    val urlProvinceDf = spark.sql(
      """
        |select
        |   c.*,
        |   case when u.ip_province is null then '' else u.ip_province end as url_ip_province --   省份
        |from (select * from combined_data where ip_province = '') c
        |left join url_province u on c.ip = u.ip
        |""".stripMargin)
      .withColumn("ip_province",col("url_ip_province"))
      .drop("url_ip_province")

    //把所有ip_province本来就有值的数据和通过接口获取到ip_province的数据合并
    val resComDf = combinedDF.filter(col("ip_province") =!= "").unionByName(urlProvinceDf)
      //产品问题分类提前处理
      .withColumn("is_product_type1",when(col("product_type1") === "","机器人").otherwise("人工"))
      .withColumn("product_type",when(col("is_product_type1") === "机器人",col("product_type2")).otherwise(col("product_type1")))
      .withColumn("function_type",when(col("is_product_type1") === "机器人",col("function_type2")).otherwise(col("function_type1")))
      .withColumn("function_detail_type",when(col("is_product_type1") === "机器人",col("function_detail_type2")).otherwise(col("function_detail_type1")))
      .withColumn("customlv4",when(col("is_product_type1") === "机器人",col("customlv42")).otherwise(col("customlv41")))
      .withColumn("customlv5",when(col("is_product_type1") === "机器人",col("customlv52")).otherwise(col("customlv51")))
      .withColumn("channel",lit("follow"))
      .na.fill("")
      .repartition(20)
    resComDf.createOrReplaceTempView("res_com_data")
    println(s"res_com_data count: ${resComDf.count}")

    val sql =
      s"""
         |-- insert into table udw_ns.default.help_ods_bjh_productline_session partition (event_day = $YesterDay )
         |select
         |    channel,
         |    im_session_id,
         |    session_id,
         |    concat_ws('-',product_type,function_type,function_detail_type,,customlv4,customlv5) as product_type, -- 产品问题类型
         |    online_session_details,
         |    follow_feedback_channel,
         |    repeat_48_hour,
         |    is_transferred,
         |    robot_first_time,
         |    system_first_time,
         |    agent_first_response_time,
         |    agent_first_response_content,
         |    session_end_time,
         |    '' as transfer_time_1,
         |    '' as transfer_time_2,
         |    '' as transfer_service_name,
         |    follow_author_bid,
         |    follow_author_uid,
         |    follow_author_name,
         |    follow_author_type,
         |    follow_author_flag,
         |    follow_author_benefit_level,
         |    no_agent_online_flag,
         |    case when manual_queue_duration is null then '' else manual_queue_duration end as manual_queue_duration,
         |    service_staff_name,
         |    skill_group_name,
         |    is_transfered,
         |    close_type,
         |    case when first_handle_time is null or first_handle_time = NULL then '' else first_handle_time end as first_handle_time, -- 首次响应时长（秒）
         |    case when avg_handle_time is null or avg_handle_time = NULL then '' else avg_handle_time end as avg_handle_time, -- 平均响应时长（秒）
         |    case when manual_session_duration is null then '' else manual_session_duration end as manual_session_duration,  -- 会话时长（秒）
         |    follow_icafe_flag,
         |    follow_icafe_status,
         |    '' as follow_icafe_workflow_space,
         |    follow_icafe_update_time,
         |    manually_labeled_flag,
         |    labeled_user_name,
         |    '' as labeled_time,
         |    '' as labeled_details,
         |    user_feedback_status,
         |    auto_content_csi,
         |    auto_content_fcr,
         |    upvote_cnt,
         |    downvote_cnt,
         |    manual_content_csi,
         |    manual_content_fcr,
         |    manual_satisfaction_label,
         |    blacklist_user,
         |    user_risk_label,
         |    regulate_risk_user,
         |    regulate_commercial_risk_user,
         |    public_opinion_risk_user,
         |    public_opinion_commercial_risk_user,
         |    brand_commercial_risk_user,
         |    consumer_protection_commercial_risk_user,
         |    risk_level,
         |    '' as case_id,
         |    '' as case_type,
         |    '' as feedback_type,
         |    '' as priority,
         |    '' as feedback_content,
         |    '' as article_url,
         |    '' as evidence_pictures,
         |    '' as ufo_feedback_channel,
         |    '' as contacts,
         |    '' as contact_phone,
         |    '' as submit_time,
         |    '' as bailing_session_id,
         |    '' as last_replier,
         |    '' as ai_satisfaction_csi,
         |    '' as ai_issue_resolved_fcr,
         |    '' as ufo_author_bid,
         |    '' as ufo_author_uid,
         |    '' as ufo_author_name,
         |    '' as ufo_author_flag,
         |    '' as ufo_author_benefit_level,
         |    '' as ufo_author_mailbox,
         |    '' as mobile_app_version,
         |    '' as ip,
         |    '' as ip_location,
         |    '' as ip_carrier,
         |    '' as mobile_model,
         |    '' as platform,
         |    '' as mobile_os_version,
         |    '' as case_status,
         |    '' as latest_reply,
         |    '' as final_reply,
         |    '' as case_replier,
         |    '' as case_responsible_person,
         |    '' as robot_reply,
         |    '' as ufo_icafe_created_flag,
         |    '' as icafe_url,
         |    '' as ufo_icafe_status,
         |    '' as ufo_icafe_workflow_space,
         |    '' as ufo_icafe_update_time
         |from res_com_data
         |""".stripMargin

    /*val sql =
      s"""
         |-- insert into table udw_ns.default.help_ods_follow_map_productline_di partition (event_day = $YesterDay )
         |select
         |	follow_feedback_channel, --	反馈来源
         |	concat_ws('-',product_type,function_type,function_detail_type,,customlv4,customlv5) as product_type, -- 产品问题类型
         |	mobile_app_name, --    App包含名
         |	userid,	--  用户id
         |	ip, --   IP地域
         |	ip_province, --   区域
         |	feedback_type, -- 	反馈分类
         |	submit_time, -- 	提交时间
         |	risk_level, --  风险等级
         |	session_id, -- 会话id
         |	user_name,  --  用户名
         |	regexp_replace(regexp_replace(session_content, '\\n', ' '), '\\r', '') AS session_content, -- 反馈内容
         |	quesiton_desc as question_desc, -- 问题描述
         |	remark, -- 备注
         |	case when user_feedback_status = '' then '人工未邀请评价' else user_feedback_status end as user_feedback_status, -- 用户评价
         |	evaluate_csi, -- 人工客服CSI
         |	is_solve, -- 解决状态
         |	skill_group_name, -- 客服组
         |	is_transfered, -- 是否转接
         |	update_time, -- 更新时间
         |	custom_label, -- 自定义标签
         |	scene_type, -- 场景类型
         |	close_type, -- 结束类型
         |	im_session_id, -- 会话标识
         |	prod_type_list, -- 问题类型
         |	ernie_bot_flag, -- 是否文心一言回复
         |	case when manual_session_duration is null then '' else manual_session_duration end as manual_session_duration,  -- 会话时长（秒）
         |	case when first_handle_time is null or first_handle_time = NULL then '' else first_handle_time end as first_handle_time, -- 首次响应时长（秒）
         |	case when avg_handle_time is null or avg_handle_time = NULL then '' else avg_handle_time end as avg_handle_time, -- 平均响应时长（秒）
         |	not_accept, -- 接入失败
         |  service_staff_name, --  更新人
         |	case when im_session_status = "CLOSE"  then "关闭" else "开启" end  im_session_status, -- 是否关闭
         |  regexp_replace(concat(os,'-',sdkvn,'-',ipRegion), '--', '') as sys_info, -- 系统信息
         |  risk_type, -- 风险类型
         |  auto_content_id, -- 智能客服内容id
         |  auto_content_csi, -- 在线智能客服CSI
         |  auto_content_fcr, -- 在线智能客服FCR
         |  manual_content_csi, -- 在线人工客服CSI
         |  manual_content_fcr, -- 在线人工客服FCR
         |  auto_satisfaction_label, -- 在线智能评价满意度标签
         |  auto_advise_text, -- 在线智能评价建议文本
         |  manual_satisfaction_label, -- 在线人工评价满意度标签
         |  artificial_advise_text, -- 在线人工评价建议文本
         |  case when round_num is null then '' else round_num end as round_num, -- 交互轮次
         |  follow_icafe_update_time, -- icafe更新时间
         |  follow_icafe_status, -- icafe状态
         |  follow_icafe_flag, -- 是否创建icafe
         |  is_transferred, -- 是否转人工
         |  upvote_cnt, -- 点赞数
         |  downvote_cnt, -- 点踩数
         |  wen_correct_cnt, -- 文心回答正确数
         |  wen_error_cnt, -- 文心回答错误数
         |  wen_unanswered_cnt, -- 文心未回答数
         |  wen_error_detail, -- 文心回答错误消息明细
         |  wen_unanswered_detail, -- 文心未回答消息明细
         |  is_session_monitor, -- 是否触发会话监控
         |  sessionz_label_values, -- 会话监控人工接入情况
         |  hit_cheating_strategy, -- 作弊策略
         |  user_agent, -- UserAgent
         |  case
         |    when cuid_1 != '' then cuid_1
         |    else (case when cuid_2 != '' then cuid_2 else cuid_3 end)
         |  end as cuid, -- cuid
         |  channel_id, -- channel_id
         |  channel_id_map, -- 活动来源
         |  icafe_current_status, --icafe写入表时状态
         |  case when manual_queue_duration is null then '' else manual_queue_duration end as manual_queue_duration, -- 排队时长
         |  case when zx_sessionz_total_time is null then '' else zx_sessionz_total_time end as zx_sessionz_total_time, -- 人工会话时长
         |  zx_is_roll_out, --是否转出
         |  zx_roll_out_product_line, --转出产品线
         |  zx_is_now_prod_sessionz, -- 是否属于本产品线
         |  product_before_line, -- 在线处理前产品线
         |  app_vn, -- app版本
         |  zx_is_fill_form, -- 是否填写表单
         |  zx_form_name, -- 表单名称
         |  zx_form_content, -- 表单内容
         |  zx_user_num, -- 人工会话轮次
         |  zx_risk_label, -- 危险用户标记
         |  user_risk_label, -- 用户风险标记
         |  case when robot_evaluate = '' then '机器人未邀请评价' else robot_evaluate end as robot_evaluate -- 机器人邀评
         |from res_com_data
         |""".stripMargin*/

    spark.sql(sql)

    spark.close()
  }

  /**
   * 获取会话内容
   */
  val getSessionDetail = udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      var productLine = ""
      //机器人首次开始对话时间
      var botFirstTime = ""
      //系统客服首次开始对话时间
      var systemFirstTime = ""
      //人工客服首次开始对话时间
      var customFirstTime = ""
      //人工客服首次开始对话内容
      var customFirstContent = ""
      //对话结束时间
      var endTime = ""
      data.foreach(elem => {
        val normalTalk = JSON.parseObject(elem)
        //机器人时间获取判断
        var robotflag = false
        //系统时间获取判断
        var systemflag = false
        //人工时间获取判断
        var customflag = false
        //人工内容获取判断
        var customContentflag = false
        try {
          val talkType = normalTalk.getString("talkType")
          //记录类型
          var users = ""
          //记录时间/评价时间,格式：yyyy-MM-dd HH:mm:ss
          val time = getDateTime(normalTalk.getString("time"),SEC_FORMAT_MYSQL)
          //对话结束时间
          endTime = time
          //客服账号/满意度
          var userName = ""
          //客服组，解决状态
          var status = ""
          //回复内容
          var content = ""
          //获取用户对话记录数据NORMAL_TALK类型
          if (talkType != null && talkType != "" && ("NORMAL_TALK".equals(talkType) || "EVENT_TALK".equals(talkType))) {
            val userInfo = normalTalk.getJSONObject("userInfo")
            if (userInfo != null && !userInfo.isEmpty) {
              if ("__METIS_IM__".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") != true) {
                //if ("__METIS_IM__".equals(userInfo.getString("sysName")) && (userInfo.getBoolean("isBot") == false || userInfo.getString("isBot") == null)) {
                users = "【用户回复】"

              } else if ("metis-bot".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") == true) {
                users = "【metis-bot】"
                if (robotflag == false) {
                  botFirstTime = time
                  robotflag = true
                }
              } else if ("系统客服".equals(userInfo.getString("showName")) && userInfo.getBoolean("isBot") == true) {
                users = "【系统客服】"
                if (systemflag == false) {
                  systemFirstTime = time
                  systemflag = true
                }
              } else if ("follow".equals(userInfo.getString("sysName"))) {
                users = "【人工客服】"
                userName = userInfo.getString("userName")
                if (customflag == false) {
                  customFirstTime = time
                  customflag = true
                }
              }
            }

            //具体的对话内容直接硬解富卡，有的text是空,有的bailingRichCard是空，是个坑
            val bailingRichCard = normalTalk.getString("bailingRichCard")
            breakable {
              if (bailingRichCard == null || !JSON.parseObject(bailingRichCard).containsKey("content")) {
                content = if (normalTalk.getString("text") == null) "" else normalTalk.getString("text")
                break
              } else {
                val tent = JSON.parseObject(bailingRichCard).getString("content")
                if (tent != null || !(tent.isEmpty)) {
                  val contentJson = JSON.parseObject(tent)
                  val types = contentJson.getString("type")
                  if ("html".equals(types)) {
                    val data = contentJson.getString("data")
                    content = if (data == null) "" else data.replaceAll("<.*?>", "")
                  } else if ("card".equals(types)) {
                    val dataJson = contentJson.getJSONObject("data")
                    if (dataJson == null || dataJson.isEmpty) {
                      content = ""
                      break
                    }
                    val array = dataJson.getJSONArray("content")
                    if (array == null || array.isEmpty) {
                      content = ""
                      break
                    } else {
                      for (item <- 0 to array.size() - 1) {
                        val cardContent = array.getJSONObject(item)
                        val cardType = cardContent.getString("type")
                        if ("html".equals(cardType) || "text".equals(cardType)) {
                          val valuestr = cardContent.getString("value")
                          content = if (valuestr == null) "" else valuestr.replaceAll("<.*?>", "")
                        } else {
                          //其他类型富卡直接转json
                          content = cardContent.toString()
                        }
                      }
                    }
                  }
                }
              }
            }
          }else if (talkType != null && talkType != "" && "SESSION_EVALUATE".equals(talkType)){
            val evaluateInfo = normalTalk.getJSONObject("evaluateInfo")
            if (evaluateInfo != null && !evaluateInfo.isEmpty) {
              if (evaluateInfo.containsKey("evaluated")) {
                //是否被评价
                val evaluated = evaluateInfo.getString("evaluated")
                if ("true".equals(evaluated)) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    //评价结果
                    val evaluateResult = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResult != null && !evaluateResult.isEmpty) {
                      if (evaluateResult.containsKey("robot_eva_sloved")) {
                        //是否解决
                        val score = evaluateResult.getString("robot_eva_sloved")
                        status = robot_eva_sloved.getOrElse(score, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_stat")) {
                        //评价得分
                        val stat = evaluateResult.getString("robot_eva_stat")
                        userName = stat + robot_eva_stat.getOrElse(stat, "")
                      }
                      if (evaluateResult.containsKey("robot_eva_diss_content")) {
                        //服务评价
                        content = evaluateResult.getString("robot_eva_diss_content")
                      }
                      if (evaluateResult.containsKey("robot")) {
                        //机器人评价
                        if (evaluateResult.getBoolean("robot") == true) users = "【机器人评价】" else users = "【人工评价】"
                      }
                    }
                  }
                }
              }
            }
          }
          if (customContentflag == false){
            //人工客服首次开始对话内容
            customFirstContent = content
            customContentflag = true
          }
          //满足条件的数据才接入
          if (users != ""){
            if (users == "【机器人评价】" || users == "【人工评价】"){
              res.append(Seq(users,content).mkString(""))
            } else if(users != "【机器人评价】" && content != ""){
              res.append(Seq(users,content).mkString(""))
            }else{
              res
            }
          }
        } catch {
          //case e:Exception => println(s"buildExportSessionTalkContent fail imSessionId:" + normalTalk.getString("imSessionId") + "talkSeqId:"  + normalTalk.getString("seqId"))
          case e:Exception => println(s"session_id:" + normalTalk.getString("imSessionId") + ",error:"  + e.getMessage)
        }
      })
      Seq(res.mkString(" "),botFirstTime,systemFirstTime,customFirstTime,customFirstContent,endTime).mkString("=>")
    }
  })

  /**
   * 获取智能客服内容id
   */
  val getAutoContentId = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      val res = new ListBuffer[String]()
      data.foreach(elem => {
        try {
          val jsonObject = JSON.parseObject(elem)
          if (jsonObject != null && jsonObject.containsKey("extra")) {
            val extra = jsonObject.getJSONObject("extra")
            if (extra != null && !extra.isEmpty) {
              if (extra.containsKey("answerId")) {
                val answerIdStr: Long = extra.getLong("answerId")
                if (answerIdStr != null && answerIdStr >= 0) {
                  val content = contentMap.getOrElse(answerIdStr,answerIdStr).toString
                  if (!res.contains(content) && content != "") {
                    res.append(content)
                  }
                }
              }
            }
          }
        } catch {
          case e:Exception => println(" getAutoContentId error:" + e.getMessage())
        }
      })
      res.toSet.mkString(",")
    }
  })

  /**
   * 获取智能客服评价等内容
   */
  val getEvaluateData = functions.udf((data: mutable.WrappedArray[String],dataType:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(elem => {
        if (JSONUtil.isJson(elem)) {
          val jsonObject = JSON.parseObject(elem)
          if (jsonObject != null &&
            jsonObject.containsKey("talkType") &&
            jsonObject.getString("talkType").equals("SESSION_EVALUATE") &&
            jsonObject.containsKey("evaluateInfo") &&
            !jsonObject.getJSONObject("evaluateInfo").isEmpty) {
              val evaluateInfo = jsonObject.getJSONObject("evaluateInfo")
              try {
                if (dataType.equals("is_solve")) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    val evaluateResultObj = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResultObj != null && !evaluateResultObj.isEmpty && evaluateResultObj.containsKey("robot")) {
                      val robot = evaluateResultObj.getBoolean("robot")
                      if (!robot) {
                        val robot_eva_sloved = evaluateResultObj.getString("robot_eva_sloved")
                        res = robot_eva_sloved match { // 使用Java 12+的switch表达式
                          case "1" => "已解决"
                          case "2" => "未解决"
                          case "3" => "持续关注"
                          case _ => ""
                        }
                      }
                    }
                  }
                }else if(dataType.equals("evaluate_csi")) {
                  if (evaluateInfo.containsKey("evaluateResult")) {
                    val evaluateResultObj = evaluateInfo.getJSONObject("evaluateResult")
                    if (evaluateResultObj != null && !evaluateResultObj.isEmpty && evaluateResultObj.containsKey("robot")) {
                      val robot = evaluateResultObj.getBoolean("robot")
                      if (!robot) {
                        //用户评价
                        res = evaluateResultObj.getString("robot_eva_stat")
                      }
                    }
                  }
                }else if(dataType.equals("user_feedback_status")) {
                  if ((jsonObject.containsKey("from") &&
                    !jsonObject.getJSONObject("from").isEmpty &&
                    jsonObject.getJSONObject("from").getString("sysName").equals("follow"))
                    ||
                    (evaluateInfo.containsKey("evaluateResult") &&
                      !evaluateInfo.getJSONObject("evaluateResult").isEmpty &&
                      evaluateInfo.getJSONObject("evaluateResult") != null &&
                      evaluateInfo.getJSONObject("evaluateResult").containsKey("robot") &&
                      evaluateInfo.getJSONObject("evaluateResult").getBoolean("robot") == false)) {
                        val evaluated = evaluateInfo.getString("evaluated")
                        res = evaluated match {
                          case "true" => "已评价"
                          case "false" => "未评价"
                          case _ => ""
                        }
                      }
                } else if(dataType.equals("robot_evaluate")) {
                  if ((jsonObject.containsKey("from") &&
                    !jsonObject.getJSONObject("from").isEmpty &&
                    jsonObject.getJSONObject("from").getString("sysName").equals("__METIS_IM__"))
                    ||
                    (evaluateInfo.containsKey("evaluateResult") &&
                      !evaluateInfo.getJSONObject("evaluateResult").isEmpty &&
                      evaluateInfo.getJSONObject("evaluateResult") != null &&
                      evaluateInfo.getJSONObject("evaluateResult").containsKey("robot") &&
                      evaluateInfo.getJSONObject("evaluateResult").getBoolean("robot") == true)) {
                    val evaluated = evaluateInfo.getString("evaluated")
                    res = evaluated match {
                      case "true" => "已评价"
                      case "false" => "未评价"
                      case _ => ""
                    }
                  }
                }else {
                  res = ""
                }
              } catch {
                //case e:Exception => println("evaluateInfo:" + evaluateInfo.toString() + "getEvaluateData error:" + e.getMessage())
                case e:Exception => null
              }
           }
        }
      })
      res
    }
  })


  /**
   * 接入是否失败
   */
  val getNotAccept = functions.udf((data: mutable.WrappedArray[String],obuzFollow:String) => {
    if (data == null || data.isEmpty){
      ""
    }else{
      val obuzFollowJsonOpt = if (JSONUtil.isJson(obuzFollow)) Some(JSON.parseObject(obuzFollow)) else None
      val hasValidId = obuzFollowJsonOpt.exists(json => json.getString("id") != null && !json.getString("id").isEmpty)

      data.foldLeft("")((res, row) => {
        if (JSONUtil.isJson(row)) {
          val talksJson = JSON.parseObject(row)
          if (talksJson.containsKey("talkType") && talksJson.containsKey("text") &&
            talksJson.getString("talkType") != null && talksJson.getString("text") != null) {
            val talkType = talksJson.getString("talkType")
            val text = talksJson.getString("text")
            if (talkType.equals("SYS_PROMPT") && text.equals("客服接入会话")) {
              "否"
            } else if (res != "否" && hasValidId && !talkType.equals("SYS_PROMPT") && !text.equals("客服接入会话")) {
              "是"
            } else {
              res
            }
          } else {
            res
          }
        } else {
          res
        }
      })
    }
  })

  /**
   * 是否转出产品线
   */
  val getRollOutProduct = functions.udf((data: String,dataStr:String) => {
    if (data == null || data.isEmpty || data == "" || Option(data).getOrElse("") == ""){
      ""
    }else if (!JSONUtil.isJson(data)) {
      ""
    }else{
      val jsondata = JSON.parseObject(data)
      if (jsondata.containsKey("sessionzLabelValues")){
        val labelArr = JSON.parseArray(jsondata.getString("sessionzLabelValues"))
        val containsRollOutProductLine = labelArr.contains("SESSIONZ_ROLL_OUT_PRODUCT_LINE")
        dataStr match {
          case "是否转出" => if (containsRollOutProductLine) "是" else "否"
          case "转出产品线" => if (containsRollOutProductLine)
            jsondata.getString("productId") match {
              case "1" => "百度APP"
              case "3" => "百度文库"
              case "5" => "百度贴吧"
              case "6" => "百家号"
              case "8" => "本地生活"
              case "11" => "爱企查"
              case "12" => "内容产品-综合"
              case "17" => "用户增长"
              case "25" => "问一问"
              case "28" => "PASS账号"
              case "35" => "萝卜快跑"
              case "37" => "文心一言"
              case "40" => "手机助手C端"
              case "41" => "百度教育"
              case _ => "其他"
            }
           else ""
          case _ => "" // 可以根据需要添加默认情况的处理
        }
      }else{
        ""
      }
    }
  })

  /**
   * 获取智能客服 CSI  FCR
   */
  val getAutoContentRobot = functions.udf((data: String,types:String) => {
    if (data == null || data.isEmpty || data == "" || Option(data).getOrElse("") == "") {
      ""
    } else {
      var res = ""
      val jsonObject = JSON.parseObject(data)
      val array = jsonObject.keySet().toArray.sortBy(x => x.toString.toInt)
      for (key <- array){
        val sessionjSON = jsonObject.getJSONObject(key.toString)
        val evaluateResult = sessionjSON.getString("evaluateResult")
        //判断level==SESSION_LEVEL，evaluated为true，evaluateResult不为空
        if (sessionjSON.getString("level") == "SESSION_LEVEL" && sessionjSON.getBoolean("evaluated") == true && evaluateResult != null && evaluateResult != ""){
          val resultJson = JSON.parseObject(evaluateResult)
          if(resultJson.getBoolean("robot") == true){
            if (types.equals("智能CSI")){
              res = resultJson.getString("robot_eva_stat") match {
                case "1" => "非常不满意"
                case "2" => "不满意"
                case "3" => "一般"
                case "4" => "满意"
                case "5" => "非常满意"
                case _ => "其他"
              }
            }else if (types.equals("智能FCR")){
              res = resultJson.getString("robot_eva_sloved") match {
                case "1" => "解决"
                case "2" => "未解决"
                case "3" => "持续关注"
                case _ => "其他"
              }
            }
            else if (types.equals("智能标签")){
              if(resultJson.containsKey("robot_eva_stat_label") && resultJson.getJSONArray("robot_eva_stat_label").size() > 0){
                res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
              }
            }
            else if (types.equals("智能文本")){
              res = resultJson.getOrDefault("robot_eva_advise","").toString
            }
          }else if (resultJson.getBoolean("robot") == false){
            if (types.equals("人工标签")){
              if(resultJson.containsKey("robot_eva_stat_label") && resultJson.getJSONArray("robot_eva_stat_label").size() > 0) {
                res = resultJson.getJSONArray("robot_eva_stat_label").toArray.mkString(",")
              }
            }else if (types.equals("人工文本")){
              res = resultJson.getOrDefault("robot_eva_advise","").toString
            }else if (types.equals("人工CSI")){
              res = resultJson.getString("robot_eva_stat") match {
                case "1" => "非常不满意"
                case "2" => "不满意"
                case "3" => "一般"
                case "4" => "满意"
                case "5" => "非常满意"
                case _ => "其他"
              }
            }else if (types.equals("人工FCR")){
              res = resultJson.getString("robot_eva_sloved") match {
                case "1" => "解决"
                case "2" => "未解决"
                case "3" => "持续关注"
                case _ => "其他"
              }
            }
          }
        }
      }
      res
    }
  })

  /**
   * 点赞数、点踩数，文心回答数量等
   * types:传入的类型，普通是获取点赞数和点踩数，文心是获取关于文心的点赞数等等
   * value：json对应的值，11是点赞数，9是点踩数
   */
  val getEvaluateDesc = functions.udf((data: mutable.WrappedArray[String],types:String,value:String) => {
    //    println(data)
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = 0
      if(types.equals("普通")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          val evaluateOn = jsonObject.getString("evaluateOn")
          val evaluateInfo = jsonObject.getString("evaluateInfo")
          if (evaluateOn == "true" && evaluateInfo != null && evaluateInfo != "") {
            val extraObject = JSON.parseObject(evaluateInfo).getString("evaluateResult")
            if(extraObject != null && extraObject != ""){
              val eval = JSON.parseObject(extraObject).getString("evaluation")
              //计算11的数量是点赞数，9的数量是点踩数
              //println(s"evaluation的值是：${eval}")
              if (eval == value){
                res += 1
              }
            }else{
              ""
            }
          }else{
            ""
          }
        })
      }else if (types.equals("文心")){
        data.foreach(elem => {
          val jsonObject = JSON.parseObject(elem)
          if(jsonObject.containsKey("ouserEvaluateInfo")){
            val ouserEvaluateInfo = jsonObject.getString("ouserEvaluateInfo")
            if (ouserEvaluateInfo != null && ouserEvaluateInfo != "") {
              val ouserEvaluateInfoJson = JSON.parseObject(ouserEvaluateInfo)
              if(ouserEvaluateInfoJson.containsKey("ouserEvaluateRet")){
                val ouserEvaluateRet = ouserEvaluateInfoJson.getString("ouserEvaluateRet")
                //GOOD：文心回答正确数量，BAD：文心回答错误数量，未回答：文心未回答数量，点踩明细：文心回答错误数量
                if (ouserEvaluateRet.equals(value)){
                  res += 1
                }
              }
            }
          }
        })
      }
      if (res.toString == "0") "" else res.toString
    }
  })

  /**
   * 文心消息回答明细
   */
  val getEvaluateDescDetail = functions.udf((data: mutable.WrappedArray[String],value:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = new StringBuilder()
      data.foreach(elem => {
        val jsonObject = JSON.parseObject(elem)
        if (jsonObject.containsKey("ouserEvaluateInfo")){
          val ouserEvaluateInfo = jsonObject.getString("ouserEvaluateInfo")
          if (ouserEvaluateInfo != null && JSONUtil.isJson(ouserEvaluateInfo)) {
            val ouserEvaluateInfoJson = JSON.parseObject(ouserEvaluateInfo)
            if(ouserEvaluateInfoJson.containsKey("ouserEvaluateRet")){
              val ouserEvaluateRet = ouserEvaluateInfoJson.getString("ouserEvaluateRet")
              //GOOD：文心回答正确数量，BAD：文心回答错误数量，未回答：文心未回答数量，点踩明细：文心回答错误数量
              if (ouserEvaluateRet.equals(value)){
                if (res.length > 0){
                  res.append(";")
                }
                res.append("【metis-bot】")
                val bailing = jsonObject.getString("bailingRichCard")
                if(bailing == null || !JSON.parseObject(bailing).containsKey("content")) {
                  res.append(if (jsonObject.getString("text") == null || jsonObject.getString("text") == "") "" else jsonObject.getString("text"))
                }else if (JSON.parseObject(bailing).containsKey("content")){
                  val content = JSON.parseObject(bailing).getJSONObject("content")
                  val typestr = content.getString("type")
                  if("html".equals(typestr)){
                    val data = content.getString("data")
                    res.append(data.replaceAll("<.*?>",""))
                  }else if("card".equals(typestr)){
                    val dataJson = content.getJSONObject("data")
                    if (!dataJson.isEmpty  && dataJson.containsKey("content")){
                      val cardContents = dataJson.getJSONArray("content")
                      if (cardContents.size() > 0){
                        for (o <- 0 to cardContents.size() - 1){
                          val cardcont = cardContents.getJSONObject(o)
                          val cardType = cardcont.getString("type")
                          if (cardType.equals("html") || cardType.equals("text")){
                            val valuestr = cardcont.getString("value")
                            res.append(if (valuestr == null || valuestr == "") "" else valuestr.replaceAll("<.*?>",""))
                          }else{
                            res.append(cardcont.toString())
                          }
                        }
                      }
                    }
                  }
                }
                val evaluateReason = if (ouserEvaluateInfoJson.containsKey("evaluateReason")) ouserEvaluateInfoJson.getString("evaluateReason") else ""
                val evaluateNotes = if (ouserEvaluateInfoJson.containsKey("evaluateNotes")) ouserEvaluateInfoJson.getString("evaluateNotes") else ""
                val suggestAnswer = if (ouserEvaluateInfoJson.containsKey("suggestAnswer")) ouserEvaluateInfoJson.getString("suggestAnswer") else ""
                res.append(" 【原因：" + evaluateReason.replaceAll("[\\r|\\t]","").replaceAll("\\n"," "))
                res.append("】【备注：")
                res.append(if (evaluateNotes == null) "" else evaluateNotes.replaceAll("[\\r|\\t]","").replaceAll("\\n"," "))
                res.append("】【建议回复：")
                res.append(if (suggestAnswer == null) "" else suggestAnswer.replaceAll("[\\r|\\t]","").replaceAll("\\n"," "))
                res.append("】")
              }
            }
          }
        }
      })
      res.mkString("")
    }
  })


  /**
   * 是否属于当前产品线
   */
  val getIsNowProd = functions.udf((data:String,obuzFollow:String,productId:String) => {
    if (data == null || data.isEmpty || data == "" || Option(data).getOrElse("") == "") {
      "是"
    }else {
      var res = "是"
      val bailingInfoOnImSessionLevel = JSON.parseObject(data)
      //判断obuzFollow里面的productId和总的productId是否一样
      if (obuzFollow != null && obuzFollow != "") {
        val obuzFollowJson = JSON.parseObject(obuzFollow)
        if (obuzFollowJson != null && obuzFollowJson.containsKey("productId")) {
          val obuzFollowProductId = obuzFollowJson.getString("productId")
          res = if (obuzFollowProductId.equals(productId)) "是" else "否"
        }
      } else if (bailingInfoOnImSessionLevel != null && bailingInfoOnImSessionLevel.containsKey("productId")) {
        //判断bailingInfoOnImSessionLevel中的productId，与level下的containerProduct的productId对比
        //如果productId一致是属于当前产品线，反之不属于
        val levelProductId = bailingInfoOnImSessionLevel.getString("productId")
        res = if (levelProductId.equals(productId)) "是" else "否"
      } else {
        res
      }
      res
    }
  })

  /**
   * 会话监控人工接入情况
   */
  val getSessionzLabelValues = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(row => {
        res = row match {
          case "SESSIONZ_MONITOR_START" => "客服未主动接管"
          case "SESSIONZ_MONITOR_ABANDON" => "放弃监控"
          case "SESSIONZ_MONITOR_OUSER_IN" => "客服主动接管"
          case _ => ""
        }
      })
      res
    }
  })

  /**
   * 风险用户情况
   */
  val getRiskLabel = functions.udf((data: String,dataType:String) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      var res = ""
      val json = JSON.parseObject(data)
      if (json != null && json.containsKey("regulateRiskLabel") && dataType.equals("监管风险用户")){
        res = json.getString("regulateRiskLabel")
      }else if (json != null && json.containsKey("publicOpinionRiskLabel") && dataType.equals("舆情风险用户")){
        res = json.getString("publicOpinionRiskLabel")
      }else if (json != null && json.containsKey("regulateCommercialRiskUsers") && dataType.equals("监管商业风险用户")){
        res = json.getString("regulateCommercialRiskUsers")
      }else if (json != null && json.containsKey("brandCommercialRiskUsers") && dataType.equals("品牌商业风险用户")){
        res = json.getString("brandCommercialRiskUsers")
      }else if (json != null && json.containsKey("consumerProtectionCommercialRiskUsers") && dataType.equals("消保商业风险用户")){
        res = json.getString("consumerProtectionCommercialRiskUsers")
      }else if (json != null && json.containsKey("publicOpinionCommercialRiskUsers") && dataType.equals("舆情商业风险用户")){
        res = json.getString("publicOpinionCommercialRiskUsers")
      }else{
        res
      }
      res
    }
  })

  /**
   * 列表数据处理
   */
  val getListVal = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      data.foreach(row => {
        if(row.startsWith("[") && row.endsWith("]")){
          res.append(row.replaceAll("\"","").replaceAll("\\[","").replaceAll("]","").split(",").mkString("/"))
        }else{
          res.append(row.replaceAll("[\\t|\\r]","").replaceAll("\\n"," ").replaceAll("\"",""))
        }
      })
      res.mkString(",")
    }
  })

  /**
   * 获取无客服在线数据值
   */
  val getAbandonReason = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      var flag = false
      data.foreach(row => {
        val json = JSON.parseObject(row)
        if (json != null && json.getString("talkType").equals("ROUTE_CHNAGE_PROMPT")){
          if (json.containsKey("from")){
            val fromJson = json.getJSONObject("from")
            if (fromJson != null && fromJson.containsKey("sysName")){
              val sysName = fromJson.getString("sysName")
              if (sysName.equals("follow")){
                if (json.containsKey("abandonReason")){
                  val reason = json.getString("abandonReason")
                  if(reason.equals("暂无客服，请等待") && !flag){
                    flag = true
                    res = "是"
                  }else if (!flag){
                    res = "否"
                  }
                }
              }
            }
          }
        }
      })
      res
    }
  })

  /**
   * 技能组筛选过滤
   */
  val getSkillGroupName = functions.udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var skillGroupName = ""
      data.foreach(row => {
        val json = JSON.parseObject(row)
        if(json.containsKey("skillGroupName")){
          skillGroupName = json.getString("skillGroupName")
        }
      })
      skillGroupName
    }
  })

  /**
   * 标注人数据获取
   */
  val getArtificialdata = functions.udf((data: mutable.WrappedArray[String],dataType:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      var res = ""
      data.foreach(row => {
        val json = JSON.parseObject(row)
        if(json.containsKey("ouserEvaluateInfo")){
          val ouserEvaluateInfo = json.getString("ouserEvaluateInfo")
          if (ouserEvaluateInfo != null && JSONUtil.isJson(ouserEvaluateInfo)) {
            val ouserEvaluateInfoJson = JSON.parseObject(ouserEvaluateInfo)
            if (dataType.equals("是否人工标注") && ouserEvaluateInfoJson.containsKey("firstUserName")){
              val firstUserName = ouserEvaluateInfoJson.getString("firstUserName")
              if (firstUserName != null && firstUserName != ""){
                res = "是"
              }else{
                res = "否"
              }
            }
            if (dataType.equals("标注人名称") && ouserEvaluateInfoJson.containsKey("userName")){
              res = ouserEvaluateInfoJson.getOrDefault("userName","").toString
            }
          }
        }
      })
      res
    }
  })

  /**
   * 获取产品问题类型
   */
  val getProductPro = functions.udf((data: String,index: Int) => {
    if (data == null || data.isEmpty || data == ",," || data == "--") {
      ""
    } else {
      val str = data.replaceAll("\\[","").replaceAll("]","")
        .replaceAll(" ","")
        .replaceAll("\"","").replaceAll("[\\t|\\r]","")
        .replaceAll("\\n"," ").split(",").applyOrElse(index, (x: Int) => "")
      str
    }
  })

  /**
   * 处理ES字段逻辑
   * @param spark
   * @param resDfList follow_feedback_channel 数据
   * @param sessionDf 计算会话数据
   * @param isDefault 新增/修改
   * @return
   */
  def dealWithEsData(spark: SparkSession,resDfList:(DataFrame, DataFrame, DataFrame, DataFrame, DataFrame),isDefault:String): DataFrame = {
    import spark.implicits._
    val queryCol = isDefault match {
      case "新增" => ("createTime","create_time")
      case "修改" => ("obuzFollow.icafeLastPullTime","update_time")
    }

    val productChannel = resDfList._1
    val appChannel = resDfList._2
    val planChannel = resDfList._3
    val pageChannel = resDfList._4
    val pathChannel = resDfList._5
    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    //循环所有产品线
    for (product <- productNameMap) {
      val productId = product._1.toString
      //处理前的产品线名称
      val productBeforeName = product._2._1
      //处理后的产品线名称
      val productName = product._2._2
      //通过EsSpark读取Es数据
      val rdd = getEsData(spark, productId, queryCol._1)
      //定义读取Es后的数据schema
      val schema = new StructType()
        .add("data", StringType)

      // 使用SparkSession创建DataFrame，row._2是所有Json字符串的数据内容
      val df = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)

      // 解析字段sql  这里把接口无法取出的字段做处理
      val tempDf = df
        //产品线id
        .withColumn("product_id",lit(productId))
        //处理前产品线名称
        .withColumn("product_before_line",lit(productBeforeName))
        //product_id第一次入的产品线
        .withColumn("product_first_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.productId")
            .cast("String")))
        //app_id子产品线
        .withColumn("app_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.appId")
            .cast("String")))
        //app_id子产品线
        .withColumn("path_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.entryPathId")
            .cast("String")))
        //app_id子产品线
        .withColumn("plan_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.planId")
            .cast("String")))
        //app_id子产品线
        .withColumn("page_id",
          getNullVal(get_json_object(col("data"), "$.startInfo.bailingInfoOnImSessionLevel.clientPageId")
            .cast("String")))
        //在线的session_id
        .withColumn("im_session_id",
          getNullVal(get_json_object(col("data"), "$.imSessionId")
            .cast("String")))
        //系统信息ipRegion
        .withColumn("ipRegion",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.sysInfoMap.ipRegion")
            .cast("String")))
        //会话标识
        .withColumn("session_id",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.id")
            .cast("String")))
        //客服组
        .withColumn("skill_group_name",
          getNullVal(getSkillGroupName(from_json(get_json_object(col("data"), "$.obuzFollow.sessionzAllocationESDTOList"),ArrayType(StringType)))
            .cast("String")))
        //人工客服是否转接
        .withColumn("is_transfered",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.isChange")
            .cast("String")))
        //创建时间
        .withColumn("submit_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.createTime")
            .cast("String"))))
        //更新时间
        .withColumn("update_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.obuzFollow.updateTime")
            .cast("String"))))
        // 一级产品问题类型
        .withColumn("product_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[0]"))
            .cast("String"))
        // 二级产品问题类型
        .withColumn("function_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[1]"))
            .cast("String"))
        // 三级产品问题类型
        .withColumn("function_detail_type1",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[2]"))
            .cast("String"))
        // 四级产品问题类型
        .withColumn("customlv41",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[3]"))
            .cast("String"))
        // 五级产品问题类型
        .withColumn("customlv51",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_prod_type_产品问题分类[4]"))
            .cast("String"))
        // 产品问题分类
        // 一级产品问题类型
        .withColumn("product_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(0)))
        // 二级产品问题类型
        .withColumn("function_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(1)))
        // 三级产品问题类型
        .withColumn("function_detail_type2",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(2)))
        // 四级产品问题类型
        .withColumn("customlv42",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(3)))
        // 五级产品问题类型
        .withColumn("customlv52",
          getProductPro(get_json_object(col("data"), "$.sessionContext.questionCategory").cast("String"),lit(4)))
        // 智能客服内容id
        .withColumn("auto_content_id",
          getAutoContentId(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)))
            .cast("String"))
        // 智能客服CSI
        .withColumn("auto_content_csi",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("智能CSI"))
            .cast("String"))
        // 智能客服FCR
        .withColumn("auto_content_fcr",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("智能FCR"))
            .cast("String"))
        // 人工客服CSI
        .withColumn("manual_content_csi",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("人工CSI"))
            .cast("String"))
        // 人工客服FCR
        .withColumn("manual_content_fcr",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("人工FCR"))
            .cast("String"))
        //icafe状态
        .withColumn("follow_icafe_status",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.icafeStatus")).cast("String"))
        //是否转人工（包留言）
        .withColumn("is_transferred",
          when(getNullVal(get_json_object(col("data"), "$.routePoint.currentRouteInfoIndex")).cast("String") === "1", "是")
            .otherwise("否"))
        //点赞数
        .withColumn("upvote_cnt",
          getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("普通"),lit("11")))
        //点踩数
        .withColumn("downvote_cnt",
          getEvaluateDesc(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("普通"),lit("9")))
        //cuid
        .withColumn("cuid_1",
          get_json_object(col("data"), "$.sessionContext.prod_cuid")
            .cast("String"))
        .withColumn("cuid_2",
          get_json_object(col("data"), "$.startInfo.startContext.baiduCuid")
            .cast("String"))
        .withColumn("cuid_3",
          get_json_object(col("data"), "$.startInfo.startUserInfo.baiduCuid")
            .cast("String"))
        // 智能评价满意度标签
        .withColumn("auto_satisfaction_label",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("智能标签"))
            .cast("String"))
        // 人工评价满意度标签
        .withColumn("manual_satisfaction_label",
          getAutoContentRobot(get_json_object(col("data"), "$.sessionEvaluatesJson"),lit("人工标签"))
            .cast("String"))
        // 作者bid
        .withColumn("follow_author_bid",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_TEXT_baijiahao_user_id_百家号ID"))
            .cast("String"))
        // 作者昵称
        .withColumn("follow_author_name",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_TEXT_baijiahao_name_百家号名称"))
            .cast("String"))
        // 作者类型
        .withColumn("follow_author_type",
          getListVal(from_json(get_json_object(col("data"), "$.obuzFollow.formField_SELECT_baijiahao_type_百家号类型"), ArrayType(StringType)))
            .cast("String"))
        //作者uid
        .withColumn("follow_author_uid",
          get_json_object(col("data"), "$.sessionContext.uid")
            .cast("String"))
        //是否黑名单用户
        .withColumn("blacklist_user",
          get_json_object(col("data"), "$.sessionContext.interceptBlackUser")
            .cast("String"))
        // 人工客服CSI
        .withColumn("evaluate_csi",
          getEvaluateData(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("evaluate_csi"))
            .cast("String"))
        // 用户评价（启用）
        .withColumn("user_feedback_status",
          getEvaluateData(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("user_feedback_status"))
            .cast("String"))
        // 是否标注人
        .withColumn("manually_labeled_flag",
          getArtificialdata(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("是否标注人"))
            .cast("String"))
        // 标注人姓名
        .withColumn("labeled_user_name",
          getArtificialdata(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("标注人名称"))
            .cast("String"))
        // 是否无客服在线
        .withColumn("no_agent_online_flag",
          getAbandonReason(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)))
            .cast("String"))
        //更新人
        .withColumn("service_staff_name",
          getListVal(from_json(get_json_object(col("data"), "$.obuzFollow.updateUserNameList"), ArrayType(StringType)))
            .cast("String"))
        .withColumn("quesiton_desc",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_TEXT_quesiton_desc_问题描述"))
            .cast("String"))
        .withColumn("remark",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_TEXT_remark_备注"))
            .cast("String"))
        .withColumn("custom_label",
          getListVal(from_json(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_custom_label_自定义标签"), ArrayType(StringType)))
            .cast("String"))
        .withColumn("risk_type",
          getListVal(from_json(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_risk_type_风险类型"), ArrayType(StringType)))
            .cast("String"))
        //风险等级
        .withColumn("risk_level",
          getListVal(from_json(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_risk_level_风险等级"), ArrayType(StringType)))
            .cast("String"))
        //结束类型
        .withColumn("close_type",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.closeType"))
            .cast("String"))
        //48小时重复进线
        .withColumn("repeat_48_hour",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.sysInfoMap.repeat_48_hour"))
            .cast("String"))
        //icafe更新时间
        .withColumn("follow_icafe_update_time",
          getDateTimeVal(get_json_object(col("data"), "$.obuzFollow.icafeLastPullTime"))
            .cast("String"))
        //是否创建icafe
        .withColumn("follow_icafe_flag",
          when(getNullVal(get_json_object(col("data"), "$.obuzFollow.icafeStatus")) === "","否").otherwise("是")
            .cast("String"))
        //用户风险标签
        .withColumn("user_risk_label",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.formField_CASCADER_user_identity_用户风险标签[0]"))
            .cast("String"))
        //会话标记，true为没用，null或者false为有用
        .withColumn("session_flag",
          getNullVal(get_json_object(col("data"), "$.startInfo.toServiceTalkSessionFlag"))
            .cast("String"))
        //会话内容长字符串
        .withColumn("session_details_list",split(getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType))).cast("string"),"=>"))
        //会话内容
        .withColumn("online_session_details",getNullVal(col("session_details_list").getItem(0).cast("string")))
        //AI对话开始时间
        .withColumn("robot_first_time",getNullVal(col("session_details_list").getItem(1).cast("string")))
        //转人工排队开始时间
        .withColumn("system_first_time",getNullVal(col("session_details_list").getItem(2).cast("string")))
        //转人工接起时间
        .withColumn("agent_first_response_time",getNullVal(col("session_details_list").getItem(3).cast("string")))
        //人工客服回复第一句话
        .withColumn("agent_first_response_content",getNullVal(col("session_details_list").getItem(4).cast("string")))
        //对话结束时间
        .withColumn("session_end_time",getNullVal(col("session_details_list").getItem(5).cast("string")))
        //会话内容(带文心分类标签)
        //.withColumn("session_details_label",getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心分类标签")))
        //监管风险用户标签
        .withColumn("regulate_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("监管风险用户"))))
        //舆情风险用户标签
        .withColumn("public_opinion_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("舆情风险用户"))))
        //监管商业风险用户标签
        .withColumn("regulate_commercial_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("监管商业风险用户"))))
        //品牌商业风险用户标签
        .withColumn("brand_commercial_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("品牌商业风险用户"))))
        //消保商业风险用户标签
        .withColumn("consumer_protection_commercial_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("消保商业风险用户"))))
        //舆情商业风险用户标签
        .withColumn("public_opinion_commercial_risk_user", getNullVal(getRiskLabel(get_json_object(col("data"), "$.sessionContext.initStartContext").cast("String"),lit("舆情商业风险用户"))))
        //百家号是否鼓励层用户
        .withColumn("follow_author_flag", getNullVal(get_json_object(col("data"), "$.sessionContext.initStartContext.isEncouragement").cast("String")))
        //百家号权益等级
        .withColumn("follow_author_benefit_level", getNullVal(get_json_object(col("data"), "$.obuzFollow.sysInfoMap.bjhEquityLevel").cast("String")))
        .drop("data","session_details_list")
        .dropDuplicates(Seq("im_session_id", "product_id","product_before_line"))

      resultDf.append(tempDf)
    }

    //将用户增长的手百主入口（任务中台）过滤掉
    val esDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2))
      //过滤掉会话标记为true的会话，即没用会话
      .filter($"session_flag" =!= "true")
      .filter(col("im_session_id") === "__METIS_IM__-2613783285_6-6_2001146_3000567_4000767_5001042-1755527518224x8QacI")
      .dropDuplicates(Seq("im_session_id", "product_id","product_before_line"))
      .repartition(20)

    val esCnt = esDF.count()
    println("esDF的数量为：" + esCnt)

    if (esCnt > 0){
      //获取计算会话数
      val sessionDf = getSessionCount(spark,isDefault,esDF)

      //获取计算交互轮次，平均响应时间，会话时长表
      val roundNum = sessionDf._1
      val avgHandleTimes = sessionDf._2
      val sessionzTimeDf = sessionDf._3

      // es获取到的数据 join url获取到的数据
      var combinedDF = esDF.as("E")
        .join(productChannel.as("P"),col("E.product_first_id") === col("P.id"),"left_outer")
        .join(appChannel.as("A"),col("E.app_id") === col("A.id"),"left_outer")
        .join(planChannel.as("C"),col("E.plan_id") === col("C.id"),"left_outer")
        .join(pageChannel.as("G"),col("E.page_id") === col("G.id"),"left_outer")
        .join(pathChannel.as("T"),col("E.path_id") === col("T.id"),"left_outer")
        .join(roundNum.as("R"),$"E.session_id" === $"R.mysql_session_id", "left")
        .join(avgHandleTimes.as("A"),$"E.session_id" === $"A.avg_session_id", "left")
        .join(sessionzTimeDf.as("Z"),$"E.session_id" === $"Z.sessiontime_id", "left")
        .withColumn("follow_feedback_channel", concat_ws("-",col("product_name"),col("app_name"),col("plan_name"),col("page_name"),col("breadcrumb_name")).cast("String"))
        .drop("im_session_id_url","mysql_session_id","avg_session_id","id")
        .dropDuplicates("im_session_id","session_id","product_before_line")
        .repartition(20)

      if (isDefault == "修改") {
        //如果是“修改”的数据，取修改时间和创建时间不为当天的
        combinedDF = combinedDF.filter(substring($"follow_icafe_update_time",1,11) =!= substring($"submit_time",1,11) )
      }
      combinedDF
    }else{
      spark.emptyDataFrame
    }
  }

  /**
   *  读取ES数据库数据
   * @param spark
   * @param productId 产品线ID
   * @param queryCol 查询过滤字段
   * @return
   */
  def getEsData(spark: SparkSession,
                        productId: String,
                        queryCol:String): RDD[(String, String)] = {

    val query =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "must": [
         |        {"bool":{
         |              "should": [
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
         |                    {"term": {"obuzFollow.productId": "${productId}"}}
         |              ]
         |            }
         |         },
         |        {
         |          "bool": {
         |            "must": [
         |              {
         |                "range": {
         |                  "${queryCol}": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    CommonUtils.ElasticSearchOperate(spark,indexName,PropertiesUtils.ESImProperties,query)
  }

  //从接口获取数据
  def postRequest(productId: String, imSessionIds: Array[String]): String = {
    val url = "http://follow.baidu-int.com/data/follow-common-api/querySessionzFiledList"
    //请求体拼接
    val body = new JSONObject
    body.put("productId", productId)
    body.put("imSessionIds", imSessionIds)
    //口令获取
    var response: Response = null
    val APP_KEY = "dashboard_udw_sync_data_interface"
    val APP_TOKEN = "d55cc05f-a8a0-4b8a-85f8-8c02b67ffaf8"
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val localDateTime = LocalDateTime
      .ofInstant(Instant.ofEpochMilli(System.currentTimeMillis), ZoneId.systemDefault)
    val createTime = localDateTime.format(formatter)
    val sign = DigestUtils.md5Hex(APP_KEY + APP_TOKEN + createTime)

    val requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON
      .toString), body.toString())
    val okHttpClient = new OkHttpClient
    val request = new Request.Builder().url(url).addHeader("appKey", APP_KEY).addHeader("createTime", s"""$createTime""")
      .addHeader("sign", s"""$sign""").post(requestBody).build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }


  /**
   * 通过接口获取ip匹配的地址信息
   *
   * @param df ip地址，只支持ipv4，不支持ipv6
   * @return
   */
  def getIpUrl(spark: SparkSession, df: DataFrame): DataFrame = {
    //将所有的ip获取出来
    val ipDf =
      df.filter(col("ip_province") === "" && !col("ip").contains(":") && col("ip") =!= "")
        .select("ip")
        .dropDuplicates()

    val ipList = ipDf.rdd
        .map(x => x.getAs[String]("ip"))
        .collect
    println("ip的数量为：" + ipList.length)

    //分批获取，每次最大100个一批
    val batchSize = 100
    val numBatches = Math.ceil(ipList.length / batchSize.toDouble).toInt
    val requestBatches = ipList.grouped(batchSize).toSeq

    //接口地址，用户名和密码
    val url = "http://api.ip.baidu.com/ip"
    val name = "ufo"
    val pwd = "3gjk9053auqskscgcckockcoc"

    val Authorization = "Basic " + Base64.getUrlEncoder().encodeToString((name + ":" + pwd).getBytes())

    //口令获取
    val okHttpClient = new OkHttpClient
    var responses = new ArrayBuffer[Seq[String]]()

    for (i <- 0 until numBatches) {
      //拼接url
      val batchUrl = url + "?ip=" + requestBatches(i).mkString(",")

      //创建request请求
      val request = new Request.Builder().url(batchUrl)
        .addHeader("Authorization", Authorization)
        .get()
        .build

      var response: Response = null

      //发送请求
      try {
        response = okHttpClient.newCall(request).execute
        if (response.isSuccessful) {
          //获取结果
          responses.append(Seq(response.body.string))
        } else {
          "Empty response"
        }
      } catch {
        case _: Exception =>
          "Empty response"
      } finally if (response != null) response.close()

      //等待100毫秒
      Thread.sleep(100) // 控制请求 QPS
    }

    println("responses长度为：" + responses.length)

    //将结果封装成RDD
    val rddResponses = spark.sparkContext.parallelize(responses)
    // 接口返回结果处理
    val finalResponseArray: RDD[String] =
      rddResponses
        .flatMap {
          response =>
            response.flatMap { jsonStr =>
              var res = Seq.empty[String]
              val jsonObject = JSON.parseObject(jsonStr)
              if (!jsonObject.isEmpty) {
                val dataArray: Option[JSONArray] = Option(jsonObject.getJSONArray("data"))
                res = dataArray match {
                  case Some(array) => (0 until array.size()).map(i => array.getString(i))
                  case None => Seq.empty[String]
                }
              }
              res
            }
        }

    import spark.implicits._

    //获取ip和地址信息
    val urlDF = finalResponseArray
      .toDF("url_data")
      .withColumn("ip", getNullVal(get_json_object(col("url_data"), "$.query_ip").cast("String")))
      .withColumn("ip_province",
        getNullVal(
          concat_ws(
            "",
            get_json_object(col("url_data"), "$.prov").cast("String"),
            get_json_object(col("url_data"), "$.city-full").cast("String")
          )
        )
      )
      .filter(!col("ip_province").contains("None"))
      .drop("url_data")
      .dropDuplicates()
      .na.fill("")
      .repartition(20)
      .cache()

    urlDF
  }

  /**
   * 计算交互轮次，平均响应时间，会话时长
   * @param spark
   * @return
   */
  def getSessionCount( spark: SparkSession,
                               isDefault:String,
                               esDf:DataFrame)   = {
    import spark.implicits._

    var roundNum = spark.emptyDataFrame
    var avgHandleTimes = spark.emptyDataFrame
    var sessionzTime = spark.emptyDataFrame

    if (isDefault.equals("新建")){
      //获取交互轮次round_num,sessionz_total_time人工会话时长,queue_total_time排队时长
      val commonSql =
        s"""
           |select
           |   session_id as mysql_session_id,
           |   round_num,
           |   IF ( first_reply_time > '0000-00-00 00:00:00', timestampdiff( SECOND, manual_time, first_reply_time ), 0) AS first_handle_time,
           |   sum(IF (last_talk_time > '0000-00-00 00:00:00' AND sessionz_start_time > '0000-00-00 00:00:00', timestampdiff(SECOND, sessionz_start_time, last_talk_time ), 0)) AS zx_sessionz_total_time,
           |   sum(IF (queue_start_time > '0000-00-00 00:00:00' AND queue_end_time > '0000-00-00 00:00:00' AND queue_status = 2, timestampdiff(SECOND,queue_start_time, queue_end_time ), 0)) AS manual_queue_duration
           |from data_online_dialogue
           |where session_id is not null
           |and is_delete = 0
           |and unix_timestamp(create_time) >= '${startTimeStamp / 1000}'
           |and unix_timestamp(create_time) <= '${endTomorrowTimeStamp / 1000}'
           |group by session_id
           |""".stripMargin

      roundNum = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,commonSql)

      print(s"roundNum读取的数量有${roundNum.count()}条")

      //平均响应时间
      val avg_time =
        s"""
           |select
           |   session_id as avg_session_id,
           |   count(*) as zx_user_num,
           |   round(avg(reply_diff)) as avg_handle_time
           |from data_online_dialogue_time
           |WHERE is_delete = 0 and session_id is not null
           |and unix_timestamp(create_time) >= '${startTimeStamp / 1000}'
           |and unix_timestamp(create_time) <= '${endTomorrowTimeStamp / 1000}'
           |group by session_id
           |""".stripMargin

      avgHandleTimes = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,avg_time)

      //获取会话时长
      val session_time =
        s"""
           |SELECT
           |   sessionz_id,
           |   id,
           |	  unix_timestamp(create_time) as create_time,
           |	  op_type,
           |	  sessionz_status
           |FROM
           |	sessionz_op_log
           |WHERE
           |	is_delete = 0
           |	and unix_timestamp(create_time) >= '${startTimeStamp / 1000}'
           |	and unix_timestamp(create_time) <= '${endTomorrowTimeStamp / 1000}'
           |""".stripMargin

      sessionzTime = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,session_time)
    }else{
      //修改状态的数据，需要根据ES的数据session_id来获取数据，不根据create_time字段来过滤数据
      val sessionIds = esDf.filter($"session_id" =!= "")
        .select($"session_id")
        .distinct()
        .rdd
        .map(row => row.getAs[String]("session_id"))
        .collect()

      //获取交互轮次round_num,sessionz_total_time人工会话时长,queue_total_time排队时长
      val commonSql =
        s"""
           |select
           |   session_id as mysql_session_id,
           |   round_num,
           |   IF ( first_reply_time > '0000-00-00 00:00:00', timestampdiff( SECOND, manual_time, first_reply_time ), 0) AS first_handle_time,
           |   -- sum(IF (manual_time > '0000-00-00 00:00:00' AND end_time > '0000-00-00 00:00:00', timestampdiff(SECOND, manual_time, end_time ), 0)) AS zx_sessionz_artificial_time, -- 人工会话总时长,
           |   sum(IF (last_talk_time > '0000-00-00 00:00:00' AND sessionz_start_time > '0000-00-00 00:00:00', timestampdiff(SECOND, sessionz_start_time, last_talk_time ), 0)) AS zx_sessionz_total_time,
           |   sum(IF (queue_start_time > '0000-00-00 00:00:00' AND queue_end_time > '0000-00-00 00:00:00' AND queue_status = 2, timestampdiff(SECOND,queue_start_time, queue_end_time ), 0)) AS manual_queue_duration
           |from data_online_dialogue
           |where session_id is not null
           |and is_delete = 0
           |and session_id in (${sessionIds.mkString(",")})
           |group by session_id
           |""".stripMargin

      roundNum = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,commonSql)
      println(s"roundNum读取的数量有${roundNum.count()}条")

      //平均响应时间
      val avg_time =
        s"""
           |select
           |   session_id as avg_session_id,
           |   count(*) as zx_user_num,
           |   round(avg(reply_diff)) as avg_handle_time
           |from data_online_dialogue_time
           |WHERE is_delete = 0 and session_id is not null
           |and session_id in (${sessionIds.mkString(",")})
           |group by session_id
           |""".stripMargin

      avgHandleTimes = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,avg_time)

      //获取会话时长
      val session_time =
        s"""
           |SELECT
           |   sessionz_id,
           |   id,
           |	  unix_timestamp(create_time) as create_time,
           |	  op_type,
           |	  sessionz_status
           |FROM
           |	sessionz_op_log
           |WHERE
           |	is_delete = 0
           |	and sessionz_id in (${sessionIds.mkString(",")})
           |""".stripMargin

      sessionzTime = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,session_time)
    }

    //计算会话时长字段
    val sessionzTimeDf = sessionzTime.orderBy("sessionz_id","id")
      .select($"sessionz_id",$"create_time".cast("long"),$"op_type".cast("string"),$"sessionz_status".cast("string"))
      .rdd.map(row => (row.getAs[Long]("sessionz_id"),(row.getAs[Long]("create_time"),row.getAs[String]("op_type"),row.getAs[String]("sessionz_status"))))
      .groupByKey()
      .mapValues(_.toArray)
      .map({case (sessionId, imSessionIdArray) =>
        var last_time = 0L
        var session_time = 0L
        var isIngSession = false
        //遍历每个sessionId的数组
        imSessionIdArray.foreach(tuple => {
          val create_time = tuple._1
          val op_type = tuple._2
          val sessionz_status = tuple._3
          if (!isIngSession){
            breakable{
              if (!op_type.contains("50","304","5")){
                break
              }else{
                isIngSession = true
              }
            }
          }
          if (last_time == 0L) {
            if (((op_type == "50" || op_type == "304" || op_type == "5") && sessionz_status == "1") || op_type == "3") {
              last_time = create_time
            }
          }else {
            //累加session_time，计算会话时长
            session_time += (create_time - last_time)
            last_time = create_time
            if (op_type == "4" || op_type == "11" || op_type == "6" || op_type == "200") {
              last_time = 0L
            }
          }
        })
        if (last_time != 0L) {
          session_time += (endTimeStamp/1000 - last_time)
        }
        (sessionId.toString,session_time)
      }).toDF("sessiontime_id","manual_session_duration")
      .repartition(20)
      .toDF()
      .cache()
    println(s"计算会话时长字段总数据量为：${sessionzTimeDf.count()}")

    (roundNum,avgHandleTimes,sessionzTimeDf)
  }

  /**
   * follow_feedback_channel渠道反馈拼接字段获取
   * product_name,app_name,plan_name,page_name,breadcrumb_name
   * @param spark
   * @return
   */
  def readFollowConf(spark: SparkSession) = {
    //product_name
    val cfgSql1 = """select id,product_name from access_cfg_l1_product """.stripMargin
    //app_name
    val cfgSql2 = """select id,app_name from access_cfg_l2_app """.stripMargin
    //entry_path
    val cfgSql3 = """select id,bread_crumb_id from access_cfg_l3_entry_path """.stripMargin
    //plan_name
    val cfgSql4 = """select id,plan_name from access_cfg_l4_plan """.stripMargin
    //page_name
    val cfgSql5 = """select id,page_name from access_cfg_l5_page """.stripMargin
    //page_id
    val cfgSql6 = """select id,page_id from access_cfg_l6_client_config """.stripMargin
    //breadcrumb_name
    val cfgSql7 = """select id,breadcrumb_name from access_cfg_breadcrumb """.stripMargin

    val productDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql1).cache()
    val appDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql2).cache()
    val planDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql4).cache()
    val pathDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql3).cache()
    val pageDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql5).cache()
    val clientDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql6).cache()
    val breadDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,cfgSql7).cache()

    //获取pagename
    val pageNameDf:DataFrame = clientDf.as("C").join(pageDf.as("B"),col("C.page_id") === col("B.id"))
      .select(
        col("C.id"),
        col("B.page_name")
      )
      .toDF()
      .repartition(20)
      .cache()

    //获取breadcrumb_name
    val breadNameDf:DataFrame = pathDf.as("C").join(breadDf.as("B"),col("C.bread_crumb_id") === col("B.id"))
      .select(
        col("C.id"),
        col("B.breadcrumb_name")
      )
      .repartition(20)
      .cache()
    println(s"pageNameDf数据量为：${pageNameDf.count()}")
    (productDf,appDf,planDf,pageNameDf,breadNameDf)
  }

  /**
   * 获取自动回复内容
   * @return
   */
  def getAutoContentIdMap(spark:SparkSession): collection.Map[Long, String] = {
    val querySql =
      s"""
         |select
         |  distinct
         |  id,
         |  content
         |from standard """.stripMargin

    val contentDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowIdProperties,querySql)
    //转换成id->content的map
    val contentMap: collection.Map[Long, String] = contentDf.rdd.map(row => {
      row.getAs[Long]("id") -> row.getAs[String]("content")
    }).collectAsMap()
    contentMap
  }

  //从接口获取数据
  def postRequestTest(SessionId: String): String = {
    var httpurl = getHttpurl()
    httpurl = httpurl + s"/data/follow-common-api/findSessionzBuzOpLog?sessionzId=${SessionId}"
    //println(httpurl)

    //口令获取
    var response: Response = null
    val APP_KEY = "dashboard_udw_sync_data_interface"
    val APP_TOKEN = "d55cc05f-a8a0-4b8a-85f8-8c02b67ffaf8"
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val localDateTime = LocalDateTime
      .ofInstant(Instant.ofEpochMilli(System.currentTimeMillis), ZoneId.systemDefault)
    val createTime = localDateTime.format(formatter)
    val sign = DigestUtils.md5Hex(APP_KEY + APP_TOKEN + createTime)

    val okHttpClient = new OkHttpClient
    val request = new Request.Builder()
      .url(httpurl)
      .addHeader("appKey", APP_KEY)
      .addHeader("createTime", s"""$createTime""")
      .addHeader("sign", s"""$sign""")
      //.post(requestBody)
      .get()
      .build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }
}

