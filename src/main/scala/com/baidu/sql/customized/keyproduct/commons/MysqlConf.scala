package com.baidu.sql.customized.keyproduct.commons

/**
 * <AUTHOR>
 * @date date 2024/2/6
 * @time 17:25
 * @package_name com.baidu.sql.customized.keyproduct.commons
 */
object MysqlConf {

  val Driver = "com.mysql.jdbc.Driver"
//  val Driver = "com.mysql.cj.jdbc.Driver"

  //热线数据库
  val HuccaCustomerUrl = "*************************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val HuccaCustomerUser = "work"
  val HuccaCustomerPassword = "serv@123"

  val WorkFlowUrl = "*******************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val WorkFlowUser = "work"
  val WorkFlowPassword = "serv@123"

  val RobotNewUrl = "********************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val RobotNewUser = "work"
  val RobotNewPassword = "serv@123"

  val HyccaUrl = "****************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val HyccaUser = "work"
  val HyccaPassword = "serv@123"

  //在线数据库
  val FollowUrl = "********************************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val FollowUser = "follow_on_read"
  val FollowPassword = "2834hndf7834fnq3o7gbv478"

  //风铃1服务库查询follow的产品线，appid等
  val FollowIdUrl = "*******************************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  val FollowIdUser = "work"
  val FollowIdPassword = "Asd123"

  //客服数据库
  var KeFuUrl: String = "*************************************?" +
    "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
    "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
  var KeFuUser = "monitor_write"
  var KeFuPassword = "monitor@kefu123"

  //风铃3服务库
  var FengLingUrl: String = "********************************************************?" +
    "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
    "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
  var FengLingUser = "fl_read"
  var FengLingPassword = "fl_read_Asd!23"

  //QA的数仓对应的数据库
  val QaUrl = "************************************?" +
    "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
    "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
  val QaUser = "work"
  val QaPassword = "Asd123"

  //阿拉丁的数仓对应的数据库
  val ALaDingUrl = "*****************************************?" +
    "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
    "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
  val ALaDingUser = "work"
  val ALaDingPassword = "Alading123!"

  //阿拉丁线上库
  var ALaDingOnlineUrl: String = "********************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  var ALaDingOnlineUser = "work"
  var ALaDingOnlinePassword = "Asd123"

  //迟滞线上代理库（迟滞和Duke版权系统数据）
  var DukeOnlineUrl: String = "*************************************************************************************************************************************" +
    "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
  var DukeOnlineUser = "chizhi_read_cyy"
  var DukeOnlinePassword = "aguGL2659"

  //电商线上数据库
  val DianShangUrl = "***********************************************************************************************************************************************************************************************************"
  val DianShangUser = "feedback_admin"
  val DianShangPassword = "c942e54d88243bc79468fdc79913259c"

  //新回声数据库，查询热线ES工单的百度教育，百度账号，爱企查的问题分类使用
  val XinHuiShengUrl = "********************************************************************************************************************************************************************************************************************"
  val XinHuiShengUser = "beacon_dataio"
  val XinHuiShengPassword = "ubvagwdIsx=gx"

  //风控策略引擎线上库，只读权限
  val FengKongUrl = "**************************************************************************************************************************************************************************************************************************"
  val FengKongUser = "silent_read"
  val FengKongPassword = "0keuE3R8k,EMu1rp"

  //风控风险召回线上库，可写权限
  val FengKongZhaoHuiUrl = "******************************************************************************************************************************************************************************************************"
  val FengKongZhaoHuiUser = "work"
  val FengKongZhaoHuiPassword = "v:@@ESL+k:_j"

  //风控feed模型数据库，可写权限
  val FeedAuditUrl = "*****************************************************************************************************************************************************************************************************************************************************************************"
  val FeedAuditUser = "work"
  val FeedAuditPassword = "vickyOnlineWr"

  //风控feed模型数据库，只读权限
  val FeedAuditReadUrl = "**********************************************************************************************************************************************************************************************************************************************************************************************"
  val FeedAuditReadUser = "work"
  val FeedAuditReadPassword = "12345Asd827763aa"

}
