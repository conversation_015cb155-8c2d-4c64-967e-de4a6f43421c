package com.baidu.sql.customized.dianshang

import com.baidu.sql.utils.TimeOperateUtil
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import java.time.LocalDateTime


/**
 * <AUTHOR>
 *         输入电商底表udw_ns.default.help_ods_risk_event
 *         输出电商黑名单表 udw_ns.default.help_ods_risk_black_id_day
 * @Date 2024-11-25
 */
object DianShangShuaDanUser {

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_im_2_mysql")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 昨天日期
    val dt_yd = YesterDay
    // 当前日期
    val dt_td = TimeOperateUtil.calcnDate(YesterDay,1)
    // 黑名单过期时间
    //val dt_7d = TimeOperateUtil.calcnDate(dt_td,-7)
    // 取近三个月
    val dt_90d = TimeOperateUtil.calcnDate(dt_td,90)
    //获取当前时间的小时数
    val currentTime = LocalDateTime.now()
    val dt_hour = currentTime.getHour

    //抽取赔付数据
    val dataDf = spark.sql(
      s"""
        |select 
        |    passid,
        |    zid,
        |    ip,
        |    round(servertime/3600) as serverhour,
        |    extra['shop_id'] as shop_id,
        |    extra['telNumber'] as telNumber,
        |    unchecked_mid['rqv_userName'] as rqv_userName,
        |    extra['countyName'] as countyName,
        |    unchecked_mid['rqv_telNumber'] as rqv_telNumber, 
        |    unchecked_mid['rqv_telNumberAes'] as rqv_telNumberAes,
        |    unchecked_mid['rqv_detailInfo'] as rqv_detailInfo,
        |    phone_sha1,
        |    phone,
        |    unchecked_mid['imv_geoh_l8'] as imv_geoh_l8,
        |    event_day
        |from udw_ns.default.help_ods_risk_event
        |where event_day='${dt_yd}'
        |and activity_id = '5680'
        """.stripMargin)

    // 第一部分转换
    // 分组并计算每个shop_id的passid数量（即pv）
    val shop_id_stats = dataDf.groupBy($"shop_id").agg(count($"passid").alias("pv"))
    //过滤出pv大于100的shop_id
    val filteredShopStats = shop_id_stats.filter($"pv" > 100).select($"shop_id")
    val t_joined = filteredShopStats.join(dataDf, Seq("shop_id"), "left")

    // 第二部分转换
    // 分组并计算每个zid，shop_id的passid数量（即uv）
    val zid_stats = t_joined.filter($"zid" =!= "").groupBy($"zid", $"shop_id").agg(countDistinct($"passid").alias("uv"))
    // 过滤出uv大于6的数据
    val filteredZidStats = zid_stats.filter($"uv" > 6)
    // 关联pv数据
    val zid_passid = filteredZidStats.join(t_joined.select($"passid", $"zid", $"shop_id"), Seq("zid", "shop_id"), "left")
    //对passid去重
    val uniqueZidPassid = zid_passid.select($"passid").dropDuplicates()

    // 第三部分转换
    // 先删除imv_geoh_l8空null的数据，分组并计算每个imv_geoh_l8，shop_id的passid数量（即uv）
    val geo_stats = t_joined.na.drop(Seq("imv_geoh_l8")).groupBy($"imv_geoh_l8", $"shop_id").agg(countDistinct($"passid").alias("uv"))
    // 过滤出uv大于50的数据
    val filteredGeoStats = geo_stats.filter($"uv" > 50)
    // 关联passid数据
    val geo_passid = filteredGeoStats.join(t_joined.select($"imv_geoh_l8", $"passid", $"shop_id"), Seq("imv_geoh_l8", "shop_id"), "left")
    //对passid去重
    val uniqueGeoPassid = geo_passid.select($"passid").dropDuplicates()

    // 最终结果
    val result = uniqueZidPassid.union(uniqueGeoPassid).dropDuplicates()
    result.createOrReplaceTempView("tmp_passid")

    // 写入黑名单表
    spark.sql(
      s"""
         |insert overwrite table udw_ns.default.help_ods_risk_black_id_day
         |partition (event_day='${dt_yd}')
         |select
         |    passid,
         |    'N_B_BN6' as tag,
         |    'passid',
         |    '5680',
         |    '1',
         |    unix_timestamp('${dt_td}','yyyyMMdd'),
         |    unix_timestamp('${dt_90d}','yyyyMMdd'),
         |    '-1',
         |    'shuadan',
         |    'UT'
         |from tmp_passid
         """.stripMargin)

    spark.close()
  }
}
