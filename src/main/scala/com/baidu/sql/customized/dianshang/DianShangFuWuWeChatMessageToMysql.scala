package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/02/25
 * @description: 电商服务数据(微信留言明细)到MySQL的同步逻辑
 */
object DianShangFuWuWeChatMessageToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._
    // 读取Hive表
    val hiveSql =
      s"""
         |select
         |  /*+ COALESCE(10) */ -- 小文件合并
         |  distinct left(t1.feedback_time, 10) as event_day, --发送时间
         |  t1.case_id, --反馈id
         |  t1.feedback_time, --反馈时间
         |  case
         |    when t1.len_end > t1.len_start then substring(t1.online_feedback_content, t1.len_start, t1.len_end - t1.len_start -1)
         |    else substring(t1.online_feedback_content, t1.len_start, t1.len_f_end - t1.len_start -1)
         |  end as online_feedback_content, --反馈内容
         |  ifnull (t1.intent_recognition_results, '其他') as intent_recognition_results, --意图识别结果：用户意图(一级)
         |  ifnull (t1.logistics_identification_results, '其他') as logistics_identification_results, --物流识别结果:用户意图(二级)
         |  --   case session_id
         |  --     when sheet_type = '服务单' then '自动化识别-人工处理'
         |  --     when sheet_type = '默认服务单' is not null then '自动化识别-处理'
         |  --     else '自动化未识别-人工处理'
         |  --   end as sheet_type,
         |  t1.order_id, --订单id
         |  t1.uid, --用户id
         |  t1.user_value_label, --用户标签
         |  t1.new_fengling_default_fuwu_case_id, --服务单id
         |  t1.send_msg_flag, --是否发送短信
         |  t1.service_progress_click_status, --服务链接点击情况
         |  t1.review_link_clicks, --评价链接点击情况
         |  --   t2.send_time, --微信发送时间
         |  --   t2.send_user, --微信发送人
         |  --   t2.send_user_type, --微信发送人类别
         |  --replace(replace(replace(t2.send_content, CHAR(9), ''), '\\n', ''), '\\N', '') as send_content,
         |  --t2.session_rank, --微信发送顺序
         |  t2.session_first_time, --微信发送第一次时间
         |  t2.session_first_sys_time, --微信发送系统第一次时间
         |  t2.session_first_rg_time, --微信客服回复第一时间
         |  t2.user_first_answer_time,
         |  if (
         |    (unix_timestamp(t2.user_first_answer_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(t2.session_first_sys_time, 'yyyy-MM-dd HH:mm:ss')) / 60 <= 1440,
         |    1,
         |    0
         |  ) if_user_24Hin_reply,
         |  --max(t2.if_24Hin_label) as if_24Hin_label,
         |  max(t2.user_last_answer_time) as user_last_answer_time, --微信用户最晚时间
         |  max(
         |    if (
         |      (
         |        unix_timestamp(t2.user_last_answer_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(t2.session_first_sys_time, 'yyyy-MM-dd HH:mm:ss')
         |      ) / 60 > 1,
         |      1,
         |      0
         |    )
         |  ) if_user_reply, --是否用户再次回复
         |  sheet_all.close_time as sheet_close_time,
         |  case
         |    when (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time <> t2.session_first_time
         |      and t2.session_first_sys_time < t2.session_first_rg_time
         |    )
         |    or (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time = t2.session_first_time
         |    ) then '是'
         |    else '否'
         |  end as is_auto_reply, --是否自动回复
         |  case
         |    when (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time <> t2.session_first_time
         |      and t2.session_first_sys_time < t2.session_first_rg_time
         |    )
         |    or (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time = t2.session_first_time
         |    ) then t2.session_first_sys_time
         |  end as if_session_first_sys_time, --首次自动回复时间
         |  case
         |    when t2.session_first_rg_time <> t2.session_first_time then '是'
         |    else '否'
         |  end as is_rengong_reply, --是否人工回复
         |  case
         |    when t2.session_first_rg_time <> t2.session_first_time then t2.session_first_rg_time
         |  end as if_session_first_rg_time, --首次人工回复时间
         |  case
         |    when ! (
         |      (
         |        t2.session_first_sys_time != t2.session_first_time
         |        and t2.session_first_rg_time != t2.session_first_time
         |        and t2.session_first_sys_time < t2.session_first_rg_time
         |      )
         |      or (
         |        t2.session_first_sys_time != t2.session_first_time
         |        and t2.session_first_rg_time = t2.session_first_time
         |      )
         |    ) --非自动回复
         |    and (t2.session_first_rg_time != t2.session_first_time) --人工回复
         |    then (
         |      unix_timestamp(t2.session_first_rg_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(t2.session_first_time, 'yyyy-MM-dd HH:mm:ss')
         |    ) / 3600
         |  end as first_reply_dura, --人工客服首次响应时长
         |  concat_ws('&', SORT_ARRAY(t2.session_type)) as session_type, --会话参与角色
         |  --t2.spilt_message_id, --对话ID
         |  --concat_ws('&', SORT_ARRAY(t2.spilt_message_type)) as spilt_message_type, --对话参与角色
         |  --   concat_ws('&', SORT_ARRAY(t2.send_content)) as concat_send_content,
         |  --SORT_ARRAY(replace(replace(replace(t2.send_content, CHAR(9), ''), '\\n', ''), '\\N', ''))
         |  --内容合并
         |  t3.eva_time, --评价时间
         |  t3.eva_source, --评价来源
         |  ifnull (t3.eva_star, '-') as eva_star, --评价星级
         |  t3.eva_solve_status, --问题解决状态
         |  t3.eva_star_label, --评价标签
         |  replace(replace(replace(t3.eva_advise, CHAR(9), ''), '\\n', ''), '\\N', '') as eva_advise --评价内容
         |from
         |  (
         |    SELECT distinct
         |      feedback_time, --反馈时间
         |      im_session_id,
         |      case_id, --反馈id
         |      order_id, --订单id
         |      uid, --用户id
         |      user_value_label, --用户标签
         |      replace(replace(replace(online_feedback_content, CHAR(9), ''), '\\n', ''), '\\N', '') as online_feedback_content, --反馈内容
         |      locate(
         |        '&',
         |        replace(
         |          replace(
         |            replace(
         |              replace(replace(replace(online_feedback_content, CHAR(9), ''), '\\n', ''), '\\N', ''),
         |              'channelFeedBackId',
         |              '$$channelFeedBackId'
         |            ),
         |            '投诉内容',
         |            '&投诉内容'
         |          ),
         |          '支付方式',
         |          '^支付方式'
         |        )
         |      ) as len_start,
         |      locate(
         |        '$$',
         |        replace(
         |          replace(
         |            replace(
         |              replace(replace(replace(online_feedback_content, CHAR(9), ''), '\\n', ''), '\\N', ''),
         |              'channelFeedBackId',
         |              '$$channelFeedBackId'
         |            ),
         |            '投诉内容',
         |            '&投诉内容'
         |          ),
         |          '支付方式',
         |          '^支付方式'
         |        )
         |      ) as len_end,
         |      locate(
         |        '^',
         |        replace(
         |          replace(
         |            replace(
         |              replace(replace(replace(online_feedback_content, CHAR(9), ''), '\\n', ''), '\\N', ''),
         |              'channelFeedBackId',
         |              '$$channelFeedBackId'
         |            ),
         |            '投诉内容',
         |            '&投诉内容'
         |          ),
         |          '支付方式',
         |          '^支付方式'
         |        )
         |      ) as len_f_end,
         |      case
         |        when fengling_default_fuwu_case_id IS NULL
         |        OR fengling_default_fuwu_case_id = '' THEN fengling_fuwu_case_id
         |        ELSE fengling_default_fuwu_case_id
         |      END AS new_fengling_default_fuwu_case_id,
         |      intent_recognition_results, --意图识别结果
         |      logistics_identification_results, --物流识别结果
         |      send_msg_flag, --是否发送短信
         |      case
         |        when service_progress_click_status = '已点击' then '是'
         |        else '否'
         |      end as service_progress_click_status, --服务链接点击情况
         |      review_link_clicks --评价链接点击情况
         |    from
         |      udw_ns.default.help_ods_feedback_online_online_retailers_feedback_only_df
         |    where
         |      1 = 1
         |      and event_day = ${YesterDay} --date_format(date_sub(current_date, 1), 'yyyyMMdd')
         |      and from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241209' -- 业务开始时间
         |      --   and from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241220'
         |      --   and from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') <= '20250217'
         |      and question_type = '度小店-留言渠道'
         |  ) t1
         |  left join (
         |    select
         |      z1.session_id,
         |      z1.send_time,
         |      z1.send_user,
         |      z1.send_user_type,
         |      z1.send_content,
         |      z1.session_rank,
         |      z1.session_first_time,
         |      z1.session_first_sys_time,
         |      z1.session_first_rg_time,
         |      z1.user_last_answer_time,
         |      MIN(
         |        CASE
         |          WHEN send_user REGEXP '^[0-9]+$$'
         |          AND (unix_timestamp(send_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(z1.session_first_sys_time, 'yyyy-MM-dd HH:mm:ss'))/60>1 THEN send_time
         |        END
         |      ) OVER (
         |        PARTITION BY
         |          z1.session_id
         |      ) AS user_first_answer_time,
         |      --if(unix_timestamp(z1.user_last_answer_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(z1.session_first_sys_time, 'yyyy-MM-dd HH:mm:ss')<86400 and unix_timestamp(z1.user_last_answer_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(z1.session_first_sys_time, 'yyyy-MM-dd HH:mm:ss')>0,1,0) as if_24Hin_label,
         |      concat(z1.session_id, z2.split_rank) as spilt_message_id,
         |      collect_set(z1.send_user_type) over (
         |        partition by
         |          z1.session_id
         |      ) as session_type, -- 会话粒度类型
         |      collect_set(z1.send_user_type) over (
         |        partition by
         |          concat(z1.session_id, z2.split_rank)
         |      ) as spilt_message_type
         |    from
         |      (
         |        select distinct
         |          session_id,
         |          send_time,
         |          send_user,
         |          case
         |            when send_user = '系统客服'
         |            and send_content like '%您好，已收到您的反馈，我们将在1-3个工作日内为您查询处理%' then '系统欢迎语'
         |            when send_user = '系统客服' then '系统客服'
         |            when send_user = '短信内容' then '短信内容'
         |            when send_user regexp '^[0-9]+$$' then '用户'
         |            when send_user REGEXP '^[A-Za-z].*' then '人工客服'
         |            else 'Other'
         |          end as send_user_type,
         |          regexp_replace(regexp_replace(regexp_replace(send_content, '\\n', ''), '\\t', ''), '\\r', '') as send_content,
         |          row_number() over (
         |            partition by
         |              session_id
         |            order by
         |              send_time asc,
         |              send_content
         |          ) as session_rank,
         |          first_value(send_time) over (
         |            partition by
         |              session_id
         |            order by
         |              send_time asc,
         |              send_content
         |          ) as session_first_time,
         |          first_value(send_time) over (
         |            partition by
         |              session_id
         |            order by
         |              case
         |                when send_user = '系统客服'
         |                and send_content not like '%您好，已收到您的反馈，我们将在1-3个工作日内为您查询处理%' then 1
         |                else 0
         |              end desc,
         |              send_time asc
         |          ) as session_first_sys_time, --系统客服最早回复时间
         |          MAX(
         |            CASE
         |              WHEN send_user REGEXP '^[0-9]+$$' THEN send_time
         |            END
         |          ) OVER (
         |            PARTITION BY
         |              session_id
         |          ) AS user_last_answer_time,
         |          first_value(send_time) over (
         |            partition by
         |              session_id
         |            order by
         |              case
         |                when send_user REGEXP '^[A-Za-z].*' then 1
         |                else 0
         |              end desc,
         |              send_time asc
         |          ) as session_first_rg_time --人工客服最早回复时间
         |        from
         |          udw_ns.default.help_ods_feedback_wechat_conversation
         |        where
         |          1 = 1
         |          and event_day = ${YesterDay}
         |          and send_time >= '2024-12-09' -- 数据起始时间
         |      ) z1
         |      left join (
         |        select -- 处理切割范围
         |          session_id,
         |          LPAD(session_rank_user, 4, '0') as split_rank,
         |          session_rank_user as spilt_session_start,
         |          ifnull (
         |            lead(session_rank_user) over (
         |              partition by
         |                session_id
         |              order by
         |                session_rank
         |            ),
         |            10000
         |          ) as spilt_session_end
         |        from
         |          (
         |            select -- 提取切割节点
         |              session_id,
         |              send_time,
         |              send_user,
         |              send_user_type,
         |              send_content,
         |              session_rank,
         |              case -- 处理第一个信息
         |                when lag(send_user_type) over (
         |                  partition by
         |                    session_id
         |                  order by
         |                    session_rank
         |                ) is null then session_rank
         |                -- 处理用户连续追问
         |                when send_user_type = '用户'
         |                and lag(send_user_type) over (
         |                  partition by
         |                    session_id
         |                  order by
         |                    session_rank
         |                ) = '用户' then null
         |                -- 处理非用户追问
         |                when send_user_type <> '用户' then null
         |                -- 处理用户对话中情况
         |                else session_rank
         |              end as session_rank_user
         |            from
         |              (
         |                select -- 提取顺序
         |                  distinct session_id,
         |                  send_time,
         |                  send_user,
         |                  case
         |                    when send_user = '系统客服'
         |                    and send_content like '%您好，已收到您的反馈，我们将在1-3个工作日内为您查询处理%' then '系统欢迎语'
         |                    when send_user = '系统客服' then '系统客服'
         |                    when send_user = '短信内容' then '短信内容'
         |                    when send_user regexp '^[0-9]+$$' then '用户'
         |                    when send_user REGEXP '^[A-Za-z].*' then '人工客服'
         |                    else 'Other'
         |                  end as send_user_type,
         |                  regexp_replace(regexp_replace(regexp_replace(send_content, '\\n', ''), '\\t', ''), '\\r', '') as send_content,
         |                  row_number() over (
         |                    partition by
         |                      session_id
         |                    order by
         |                      send_time asc,
         |                      send_content
         |                  ) as session_rank
         |                from
         |                  udw_ns.default.help_ods_feedback_wechat_conversation
         |                where
         |                  1 = 1
         |                  and event_day = ${YesterDay}
         |                  and send_time >= '2024-12-' -- 数据起始时间
         |              ) x1
         |          ) n1
         |        where
         |          session_rank_user is not null
         |      ) z2 on z1.session_id = z2.session_id
         |      and z1.session_rank >= z2.spilt_session_start
         |      and z1.session_rank < z2.spilt_session_end
         |  ) t2 on t1.case_id = t2.session_id
         |  left join (
         |    SELECT distinct
         |      case_id,
         |      eva_time, --评价时间
         |      eva_source, --评价来源
         |      eva_star, --评价星级
         |      eva_solve_status, --问题解决状态
         |      eva_star_label, --评价标签
         |      eva_advise --评价内容
         |    from
         |      udw_ns.default.help_ods_fengling_case_evaluate_df
         |    where
         |      1 = 1
         |      and event_day = ${YesterDay}
         |  ) t3 on t1.new_fengling_default_fuwu_case_id = t3.case_id
         |  left join (
         |    SELECT
         |      cur_status,
         |      case_id,
         |      im_session_id,
         |      order_id,
         |      case_content,
         |      refund_id,
         |      sheet_type,
         |      max(create_time) as create_time,
         |      max(first_reply_time) as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |      max(outbound_call_contact_time) as outbound_call_contact_time, --外呼联系用户时间
         |      max(customer_service_first_processing_time) as customer_service_first_processing_time, --二线客服首次处理时间
         |      max(merchant_re_processing_time) as merchant_re_processing_time, --商家再次处理时间
         |      max(close_time) as close_time,
         |      max(from_wechat_flag) as from_wechat_flag, --是否微信提交
         |      max(intent_recognition_results) as intent_recognition_results, --意图识别结果
         |      max(match_scene_results) as match_scene_results, --匹配场景结果
         |      max(automatic_shutdown_reason) as automatic_shutdown_reason, --自动关闭原因
         |      max(wait_customer_confirm_fast_refund) as wait_customer_confirm_fast_refund, --待用户确认快通售后
         |      max(wait_customer_confirm_fast_refund_type) as wait_customer_confirm_fast_refund_type, --待用户确认快通类型
         |      max(execute_refund_type) as execute_refund_type, --执行售后类型
         |      max(merchants_estimated_delivery_time) as merchants_estimated_delivery_time --商家预计发货时间
         |    from
         |      (
         |        SELECT
         |          cur_status,
         |          case_id,
         |          im_session_id,
         |          order_id,
         |          refund_id,
         |          replace(replace(case_content, char(10), '|'), char(13), '|') as case_content,
         |          create_time,
         |          '' as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |          '' as outbound_call_contact_time, --外呼联系用户时间
         |          '' as customer_service_first_processing_time, --二线客服首次处理时间
         |          '' as merchant_re_processing_time, --商家再次处理时间
         |          '' as delegation_guaranteed_processing_time, --二线客服再次处理
         |          close_time,
         |          from_wechat_flag, --是否微信提交
         |          intent_recognition_results, --意图识别结果
         |          match_scene_results, --匹配场景结果
         |          automatic_shutdown_reason, --自动关闭原因
         |          wait_customer_confirm_fast_refund, --待用户确认快通售后
         |          wait_customer_confirm_fast_refund_type, --待用户确认快通类型
         |          execute_refund_type, --执行售后类型
         |          merchants_estimated_delivery_time, --商家预计发货时间
         |          '默认服务单' as sheet_type
         |        from
         |          udw_ns.default.help_ods_tb_fengling_morenfuwu_info_online
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241209'
         |        union all
         |        SELECT
         |          cur_status,
         |          case_id,
         |          im_session_id,
         |          order_id,
         |          refund_id,
         |          replace(replace(case_content, char(10), '|'), char(13), '|') as case_content,
         |          create_time,
         |          first_reply_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |          outbound_call_contact_time as outbound_call_contact_time, --外呼联系用户时间
         |          customer_service_first_processing_time as customer_service_first_processing_time, --二线客服首次处理时间
         |          merchant_re_processing_time as merchant_re_processing_time, --商家再次处理时间
         |          delegation_guaranteed_processing_time as delegation_guaranteed_processing_time, --二线客服再次处理
         |          kefu_close_time as close_time,
         |          from_wechat_flag,
         |          intent_recognition_results,
         |          match_scene_results,
         |          automatic_shutdown_reason,
         |          wait_customer_confirm_fast_refund,
         |          wait_customer_confirm_fast_refund_type,
         |          execute_refund_type,
         |          merchants_estimated_delivery_time,
         |          '服务单' as sheet_type
         |        from
         |          udw_ns.default.help_ods_fengling_fuwu_detail
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241209'
         |      )
         |    group by
         |      cur_status,
         |      case_id,
         |      im_session_id,
         |      order_id,
         |      case_content,
         |      refund_id,
         |      sheet_type
         |  ) sheet_all on t1.im_session_id = sheet_all.im_session_id
         |group by
         |  left(t1.feedback_time, 10), --发送时间
         |  t1.case_id, --反馈id
         |  t1.feedback_time, --反馈时间
         |  case
         |    when t1.len_end > t1.len_start then substring(t1.online_feedback_content, t1.len_start, t1.len_end - t1.len_start -1)
         |    else substring(t1.online_feedback_content, t1.len_start, t1.len_f_end - t1.len_start -1)
         |  end, --反馈内容
         |  ifnull (t1.intent_recognition_results, '其他'), --意图识别结果：用户意图(一级)
         |  ifnull (t1.logistics_identification_results, '其他'), --物流识别结果:用户意图(二级)
         |  case
         |    when sheet_type = '服务单' then '自动化识别-人工处理'
         |    when sheet_type = '默认服务单' is not null then '自动化识别-处理'
         |    else '自动化未识别-人工处理'
         |  end,
         |  t1.order_id, --订单id
         |  t1.uid, --用户id
         |  t1.user_value_label, --用户标签
         |  t1.new_fengling_default_fuwu_case_id, --服务单id
         |  t1.send_msg_flag, --是否发送短信
         |  t1.service_progress_click_status, --服务链接点击情况
         |  t1.review_link_clicks, --评价链接点击情况
         |  --   t2.send_time, --微信发送时间
         |  --   t2.send_user, --微信发送人
         |  --   t2.send_user_type, --微信发送人类别
         |  --replace(replace(replace(t2.send_content, CHAR(9), ''), '\\n', ''), '\\N', '') as send_content, --微信发送内容
         |  --t2.session_rank, --微信发送顺序
         |  t2.session_first_time, --微信发送第一次时间
         |  t2.session_first_sys_time, --微信发送系统第一次时间
         |  t2.session_first_rg_time, --微信客服回复第一时间
         |  t2.user_first_answer_time,
         |  t2.user_last_answer_time, --微信用户最晚时间
         |  --t2.if_24Hin_label,
         |  sheet_all.close_time,
         |  case
         |    when (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time <> t2.session_first_time
         |      and t2.session_first_sys_time < t2.session_first_rg_time
         |    )
         |    or (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time = t2.session_first_time
         |    ) then '是'
         |    else '否'
         |  end, --是否自动回复
         |  case
         |    when (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time <> t2.session_first_time
         |      and t2.session_first_sys_time < t2.session_first_rg_time
         |    )
         |    or (
         |      t2.session_first_sys_time <> t2.session_first_time
         |      and t2.session_first_rg_time = t2.session_first_time
         |    ) then t2.session_first_sys_time
         |  end, --首次自动回复时间
         |  case
         |    when t2.session_first_rg_time <> t2.session_first_time then '是'
         |    else '否'
         |  end, --是否人工回复
         |  case
         |    when t2.session_first_rg_time <> t2.session_first_time then t2.session_first_rg_time
         |  end, --首次人工回复时间
         |  case
         |    when ! (
         |      (
         |        t2.session_first_sys_time != t2.session_first_time
         |        and t2.session_first_rg_time != t2.session_first_time
         |        and t2.session_first_sys_time < t2.session_first_rg_time
         |      )
         |      or (
         |        t2.session_first_sys_time != t2.session_first_time
         |        and t2.session_first_rg_time = t2.session_first_time
         |      )
         |    ) --非自动回复
         |    and (t2.session_first_rg_time != t2.session_first_time) --人工回复
         |    then (
         |      unix_timestamp(t2.session_first_rg_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(t2.session_first_time, 'yyyy-MM-dd HH:mm:ss')
         |    ) / 3600
         |  end, --人工客服首次响应时长
         |  concat_ws('&', SORT_ARRAY(t2.session_type)), --会话参与角色
         |  --t2.spilt_message_id, --对话ID
         |  --concat_ws('&', SORT_ARRAY(t2.spilt_message_type)), --对话参与角色
         |  --   concat_ws('&', SORT_ARRAY(t2.send_content)) as concat_send_content,
         |  --SORT_ARRAY(replace(replace(replace(t2.send_content, CHAR(9), ''), '\\n', ''), '\\N', ''))
         |  --内容合并
         |  t3.eva_time, --评价时间
         |  t3.eva_source, --评价来源
         |  ifnull (t3.eva_star, '-'), --评价星级
         |  t3.eva_solve_status, --问题解决状态
         |  t3.eva_star_label, --评价标签
         |  replace(replace(replace(t3.eva_advise, CHAR(9), ''), '\\n', ''), '\\N', '') --评价内容
         |""".stripMargin

    println(hiveSql)

    //类型转换，缺失值填充
    val hiveDf = spark.sql(hiveSql)
      .select(
        $"event_day".cast("date"),
        $"case_id".cast("long"),
        $"feedback_time".cast("timestamp"),
        $"online_feedback_content".cast("string"),
        $"intent_recognition_results".cast("string"),
        $"logistics_identification_results".cast("string"),
        $"order_id".cast("long"),
        $"uid".cast("int"),
        $"user_value_label".cast("string"),
        $"new_fengling_default_fuwu_case_id".cast("int"),
        $"send_msg_flag".cast("string"),
        $"service_progress_click_status".cast("string"),
        $"review_link_clicks".cast("string"),
        $"session_first_time".cast("timestamp"),
        $"session_first_sys_time".cast("timestamp"),
        $"session_first_rg_time".cast("timestamp"),
        $"user_first_answer_time".cast("timestamp"),
        $"if_user_24Hin_reply".cast("int"),
        $"user_last_answer_time".cast("timestamp"),
        $"if_user_reply".cast("int"),
        $"sheet_close_time".cast("timestamp"),
        $"is_auto_reply".cast("string"),
        $"if_session_first_sys_time".cast("timestamp"),
        $"is_rengong_reply".cast("string"),
        $"if_session_first_rg_time".cast("timestamp"),
        $"first_reply_dura".cast("float"),
        $"session_type".cast("string"),
        $"eva_time".cast("timestamp"),
        $"eva_source".cast("string"),
        $"eva_star".cast("string"),
        $"eva_solve_status".cast("string"),
        $"eva_star_label".cast("string"),
        $"eva_advise".cast("string"),
      )
      .na.fill("",Seq("online_feedback_content","intent_recognition_results","logistics_identification_results","user_value_label","send_msg_flag","service_progress_click_status","review_link_clicks","is_auto_reply","is_rengong_reply","is_rengong_reply","session_type","eva_source","eva_star","eva_solve_status","eva_star_label","eva_advise"))
      .na.fill(0,Seq("case_id","order_id","uid","new_fengling_default_fuwu_case_id","if_user_24Hin_reply","if_user_reply"))
      .na.fill(0.0,Seq("first_reply_dura"))
      .dropDuplicates()
      .repartition(20)
    println(s"获取微信留言${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(10,false)
    hiveDf.printSchema()

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_wechat_message")

    spark.close()
  }
}
