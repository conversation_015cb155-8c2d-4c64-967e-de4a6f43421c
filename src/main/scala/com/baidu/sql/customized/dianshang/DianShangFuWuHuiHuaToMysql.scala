package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/12/23
 * @description: 电商服务数据(会话明细)到MySQL的同步逻辑
 */
object DianShangFuWuHuiHuaToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._
    // 读取Hive表
    val hiveSql =
      s"""
         |select
         |  service_data.create_day,
         |  service_data.case_id,
         |  order_table.ucid,
         |  service_data.order_id as service_order_id,
         |  service_data.sub_order_id as service_sub_order_id,
         |  service_data.newcase_type_level1,
         |  service_data.newcase_type_level2,
         |  service_data.newcase_type_level3,
         |  service_data.platform_arbitration,
         |  service_data.biao_type,
         |  service_data.source_from,
         |  pay_time,
         |  shop_id,
         |  shop_name,
         |  product_id,
         |  product_name,
         |  order_status as status, -- status字段来源替换
         |  gmv as total_amount,
         |  refund_create_time,
         |  refund_finish_time,
         |  -- refund_reason, -- 剔除
         |  -- refund_desc, -- 剔除
         |  -- categoryNameList,
         |  first_cate_name as categoryNameLevel1, -- 字段来源替换
         |  second_cate_name as categoryNameLevel2, -- 字段来源替换
         |  third_cate_name as categoryNameLevel3, -- 字段来源替换
         |  -- express_status, -- 剔除
         |  -- express_data, -- 剔除
         |  shop_type, -- 店铺类型-新增
         |  uc_shop_type, -- 商家UC分类--新增
         |  order_table.refund_id,--新增
         |  is_digital_human,--新增
         |  origin_refund_type,--新增
         |  order_refund_status,--新增
         |  if (fast.refund_id is null, 1, 0) is_fast,
         |  fast.refund_id as fast_refund_id,
         |  fast.create_day as fast_create_day,
         |  fast.risk_user_flag as fast_risk_user_flag,
         |  fast.fast_after_sale_flag as fast_fast_after_sale_flag,
         |  fast.fast_after_sale_type as fast_fast_after_sale_type,
         |  fast.quick_pass_source as fast_quick_pass_source,
         |  fast.quick_pass_after_sale_type as fast_quick_pass_after_sale_type,
         |  fast.negative_commodity_flag as fast_negative_commodity_flag,
         |  evaluate_day,
         |  eva_time,
         |  eva_solve_status,
         |  eva_star,
         |  eva_star_label,
         |  eva_advise
         |--   nps.order_id as nps_order,--新增，暂无权限
         |--   nps.nps_score,--新增
         |--   nps.nps_labels,--新增
         |--   nps.nps_content,--新增
         |--   nps.nps_time,--新增
         |--   nps.nps_type--新增
         |from
         |  (
         |    SELECT
         |      order_id,
         |      sub_order_id,
         |      case_id,
         |      left(create_time, 10) as create_day,
         |      create_time,
         |      kefu_close_time,
         |      newcase_type_level1,
         |      newcase_type_level2,
         |      newcase_type_level3,
         |      platform_arbitration,
         |      biao_type,
         |      source_from
         |    from
         |      (
         |        SELECT
         |          order_id,
         |          sub_order_id,
         |          case_id,
         |          create_time as create_time,
         |          kefu_close_time,
         |          case
         |            when newcase_type_level1 is null
         |            or newcase_type_level1 = '' then 'unknown'
         |            else newcase_type_level1
         |          end as newcase_type_level1,
         |          case
         |            when newcase_type_level2 is null
         |            or newcase_type_level2 = '' then 'unknown'
         |            else newcase_type_level2
         |          end as newcase_type_level2,
         |          case
         |            when newcase_type_level3 is null
         |            or newcase_type_level3 = '' then 'unknown'
         |            else newcase_type_level3
         |          end as newcase_type_level3,
         |          liability_arbitration as platform_arbitration,
         |          '仲裁单' as biao_type,
         |          source_from
         |        FROM
         |          udw_ns.default.help_ods_fengling_zhongcai_detail
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >='20240101'
         |        UNION ALL
         |        SELECT
         |          order_id,
         |          sub_order_id,
         |          case_id,
         |          feedback_time as create_time,
         |          '' as kefu_close_time,
         |          case
         |            when newcase_type_level1 is null
         |            or newcase_type_level1 = '' then 'unknown'
         |            else newcase_type_level1
         |          end as newcase_type_level1,
         |          case
         |            when newcase_type_level2 is null
         |            or newcase_type_level2 = '' then 'unknown'
         |            else newcase_type_level2
         |          end as newcase_type_level2,
         |          case
         |            when newcase_type_level3 is null
         |            or newcase_type_level3 = '' then 'unknown'
         |            else newcase_type_level3
         |          end as newcase_type_level3,
         |          '' as platform_arbitration,
         |          'feedback' as biao_type,
         |          case when question_type='度小店-留言渠道' then '留言' else feedback_mode end as source_from
         |        FROM
         |          udw_ns.default.help_ods_feedback_online_online_retailers_feedback_only_df
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >='20240101' -- AND feedback_type = '投诉'
         |          AND (
         |        -- 当 feedback_time 在 2025-01-10 之前，取 feedback_mode = '留言'
         |        (
         |          from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') < '20241210'
         |          AND (feedback_mode in ('在线', '电话', '留言') AND space_or_skill_id > 0)
         |          or feedback_mode = '电话'
         |        )
         |        -- 当 feedback_time 在 2025-01-10 之后，取 question_type='度小店-留言渠道'
         |        OR (
         |          from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241210'
         |          AND (
         |            feedback_mode in ('在线', '电话')
         |            AND space_or_skill_id > 0
         |            and feedback_mode != '留言'
         |          )
         |          or feedback_mode = '电话'
         |          and question_type = '度小店-留言渠道'
         |        )
         |      )
         |          -- and space_or_skill_id > 0
         |          -- and question_type !='度小店-离线留言'
         |        UNION ALL
         |        SELECT
         |          order_id,
         |          sub_order_id,
         |          case_id,
         |          create_time as create_time,
         |          kefu_close_time,
         |          case
         |            when newcase_type_level1 is null
         |            or newcase_type_level1 = '' then 'unknown'
         |            else newcase_type_level1
         |          end as newcase_type_level1,
         |          case
         |            when newcase_type_level2 is null
         |            or newcase_type_level2 = '' then 'unknown'
         |            else newcase_type_level2
         |          end as newcase_type_level2,
         |          case
         |            when newcase_type_level3 is null
         |            or newcase_type_level3 = '' then 'unknown'
         |            else newcase_type_level3
         |          end as newcase_type_level3,
         |          '' as platform_arbitration,
         |          '服务单' as biao_type,
         |          source_from
         |        FROM
         |          udw_ns.default.help_ods_fengling_fuwu_detail
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd')>='20240101' -- AND service_type='投诉'
         |        UNION ALL
         |        select
         |          order_id,
         |          sub_order_id,
         |          case_id,
         |          create_time as create_time,
         |          kefu_close_time,
         |          case_type_level1 as newcase_type_level1,
         |          case_type_level2 as newcase_type_level2,
         |          '' as newcase_type_level3,
         |          complaint_result_b as platform_arbitration,
         |          '投诉单' as biao_type,
         |          source_from
         |        from
         |          udw_ns.default.help_ods_fengling_tousu_detail_df
         |        WHERE
         |          event_day = ${YesterDay}
         |          and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >='20240101'
         |        union all
         |        select
         |          order_id,
         |          sub_order_id,
         |          case_id,
         |          create_time as create_time,
         |          close_time as kefu_close_time,
         |          newcase_type_level1,
         |          newcase_type_level2,
         |          newcase_type_level3,
         |          -- intent_recognition_results,
         |          -- match_scene_results,
         |          '' as platform_arbitration,
         |          '默认服务单' as biao_type,
         |          --   passport_id,
         |          source_from
         |          -- question_categorys,
         |          -- 'moren' as biao_type
         |        from
         |          udw_ns.default.help_ods_tb_fengling_morenfuwu_info_online
         |        WHERE
         |          event_day = ${YesterDay}
         |          and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd')>='20250101'
         |      )
         |  ) service_data
         |  left join (
         |    SELECT
         |      create_time,
         |      pay_time,
         |      left(pay_time, 10) as pay_day,
         |      cancel_time,
         |      update_time,
         |      CAST(SUBSTR(update_time, 1, 10) AS DATE) AS update_day,
         |      content_type,
         |      content_id,
         |      product_create_time, -- 商品创建时间
         |      product_on_sell_time, -- 商品上架时间
         |      product_update_time,
         |      identify_day, -- 作弊订单识别日期
         |      ucid,
         |      shop_id,
         |      shop_name,
         |      passport_id,
         |      order_id,
         |      sub_order_id,
         |      sku_id,
         |      product_id,
         |      replace(replace(product_name, char(10), '|'), char(13), '|') product_name,
         |      business_attribution_new,
         |      buy_num,
         |      business_discount,
         |      platform_discount,
         |      detail_freight_amount,
         |      detail_payment_amount,
         |      detail_settle_amount,
         |      detail_total_amount,
         |      rake_channel_amount,
         |      rake_split_amount,
         |      rake_finish_time,
         |      rebate_channel_amount,
         |      rebate_split_amount,
         |      rebate_finish_time,
         |      evaluate_status,
         |      first_cate_name,
         |      second_cate_name,
         |      third_cate_name,
         |      gmv,
         |      is_consumed, -- 订单是否核销
         |      is_digital_human, -- 直播是否数字人直播
         |      order_refund_status, -- 订单售后状态
         |      origin_refund_type,
         |      refund_create_time,
         |      refund_finish_time,
         |      order_status,
         |      pay_status,
         |      pay_type,
         |      platform_type, -- 商品平台
         |      product_type,
         |      shop_type, -- 店铺类型
         |      refund_id,
         |      refund_status,
         |      submit_type,
         |      trade_type, -- 交易类型
         |      transaction_mode,
         |      uc_shop_type, -- 商家UC分类
         |      policy_type, -- 反作弊策略类型
         |      policyid, -- 反作弊策略id
         |      antitag -- 作弊订单标识，使用当天反作弊订单表的数
         |    FROM
         |      yinhe.ecommerce_dws_zbzy_order_detail_df
         |    WHERE
         |      event_day = ${YesterDay} -- 取最近一天
         |      AND pay_type = 0 --在线支付
         |      AND pay_status IN (2, 3) -- 支付或退款订单
         |      AND app_id IN (5, 100012) -- 度小店&招财猫
         |  ) as order_table on order_table.sub_order_id = service_data.sub_order_id
         |  left join (
         |    SELECT
         |      create_time,
         |      from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyy-MM-dd') as create_day,
         |      order_id,
         |      sub_order_id,
         |      refund_id,
         |      risk_user_flag,
         |      fast_after_sale_flag,
         |      fast_after_sale_type,
         |      quick_pass_source,
         |      quick_pass_after_sale_type,
         |      negative_commodity_flag
         |    from
         |      udw_ns.default.help_ods_tb_data_kuaitong_all
         |    where
         |      event_day = ${YesterDay}
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      and fast_after_sale_flag = '快通'
         |      -- and quick_pass_source in ('服务单', '投诉单', '仲裁单')
         |  ) fast on service_data.sub_order_id = fast.sub_order_id
         |  and fast.create_time > service_data.create_time
         |  left join (
         |    select
         |      case_id,
         |      eva_source,
         |      left(eva_time, 10) as evaluate_day,
         |      eva_time,
         |      eva_solve_status,
         |      eva_star,
         |      eva_star_label,
         |      replace(replace(eva_advise, char(10), '|'), char(13), '|') eva_advise,
         |      passport_id,
         |      order_id,
         |      source_id
         |    from
         |      udw_ns.default.help_ods_fengling_case_evaluate_df
         |    where
         |      event_day =  ${YesterDay}
         |      and eva_time is not null
         |      and from_unixtime(unix_timestamp(eva_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20250101'
         |      and case_id not in ('583086', '583063')
         |    order by
         |      eva_time
         |  ) evaluate on service_data.case_id = evaluate.case_id
         |--   left join (
         |--     SELECT
         |--       pass_id,
         |--       order_id,
         |--       nps_score,
         |--       nps_labels,
         |--       nps_content,
         |--       create_time as nps_time,
         |--       nps_type
         |--     from
         |--       ec_alg.ec_ads_user_nps_df
         |--     where
         |--       event_day = ${YesterDay}
         |--   ) nps
         |--   on nps.order_id = service_data.order_id
         |-- limit
         |--   1000
         |""".stripMargin

    println(hiveSql)

    //类型转换，缺失值填充
    val hiveDf = spark.sql(hiveSql)
      .select(
        $"create_day".cast("date"),
        $"case_id".cast("long"),
        $"ucid".cast("string"),
        $"service_order_id".cast("long"),
        $"service_sub_order_id".cast("long"),
        $"newcase_type_level1".cast("string"),
        $"newcase_type_level2".cast("string"),
        $"newcase_type_level3".cast("string"),
        $"platform_arbitration".cast("string"),
        $"biao_type".cast("string"),
        $"source_from".cast("string"),
        $"pay_time".cast("timestamp"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"product_id".cast("long"),
        $"product_name".cast("string"),
        $"status".cast("int"),
        $"total_amount".cast("double"),
        $"refund_create_time".cast("timestamp"),
        $"refund_finish_time".cast("timestamp"),
        //$"refund_reason".cast("long"),
        //$"refund_desc".cast("string"),
        //$"categoryNameList".cast("string"),
        $"categoryNameLevel1".cast("string"),
        $"categoryNameLevel2".cast("string"),
        $"categoryNameLevel3".cast("string"),
        //$"express_data".cast("string"),
        $"shop_type".cast("string"),
        $"uc_shop_type".cast("string"),
        $"refund_id".cast("long"),
        $"is_digital_human".cast("int"),
        $"origin_refund_type".cast("int"),
        $"order_refund_status".cast("string"),
        $"is_fast".cast("int"),
        $"fast_refund_id".cast("long"),
        $"fast_create_day".cast("date"),
        $"fast_risk_user_flag".cast("string"),
        $"fast_fast_after_sale_flag".cast("string"),
        $"fast_fast_after_sale_type".cast("string"),
        $"fast_quick_pass_source".cast("string"),
        $"fast_quick_pass_after_sale_type".cast("string"),
        $"fast_negative_commodity_flag".cast("string"),
        $"evaluate_day".cast("date"),
        $"eva_time".cast("timestamp"),
        $"eva_solve_status".cast("string"),
        $"eva_star".cast("string"),
        $"eva_star_label".cast("string"),
        $"eva_advise".cast("string")
      )
      .na.fill("",Seq("ucid","newcase_type_level1","newcase_type_level2","newcase_type_level3","platform_arbitration","biao_type","source_from","shop_name","product_name","categoryNameLevel1","categoryNameLevel2","categoryNameLevel3","fast_risk_user_flag","fast_fast_after_sale_flag","fast_fast_after_sale_type","fast_quick_pass_source","fast_quick_pass_after_sale_type","fast_negative_commodity_flag","shop_type","uc_shop_type","order_refund_status","eva_solve_status","eva_star","eva_star_label"))
      .na.fill(0,Seq("case_id","service_order_id","service_sub_order_id","shop_id","product_id","status","is_fast","fast_refund_id","refund_id","origin_refund_type","is_digital_human"))
      .na.fill(0.0,Seq("total_amount"))
      .dropDuplicates()
      .repartition(200)
    println(s"获取会话明细${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(10,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_session_details")

    spark.close()
  }
}
