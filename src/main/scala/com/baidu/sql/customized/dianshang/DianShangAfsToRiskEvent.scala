package com.baidu.sql.customized.dianshang

import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import scala.collection.mutable
import scala.collection.mutable.ArrayBuffer


/**
 * 电商Kafka数据入Hive底表（afs上txt文件）
 */
object DianShangAfsToRiskEvent {
  //运行日期
  var YesterDay: String = ""

  //读取类型，全量till还是增量increase
  var readType:String = ""
  //前一个小时值
  var hour:String = ""

  // 定义struct的类型。这里假设JSON有两个字段：field1和field2。
  val structSchema = new StructType()
    .add("activity_id",StringType)
    .add("ser_time",StringType)
    .add("time_stamp",StringType)
    .add("app",StringType)
    .add("sc",StringType)
    .add("ev",StringType)
    .add("username",StringType)
    .add("ip",StringType)
    .add("passid",StringType)
    .add("userid",StringType)
    .add("reg_time",StringType)
    .add("cuid",StringType)
    .add("z",StringType)
    .add("gdid",StringType)
    .add("xid",StringType)
    .add("secure_mobile",StringType)
    .add("rule_id_hit",ArrayType(StringType))
    .add("activity_type",ArrayType(StringType))
    .add("aid",StringType)
    .add("app_version",StringType)
    .add("appid",StringType)
    .add("appkey",StringType)
    .add("baiduid",StringType)
    .add("bid_info",MapType(StringType, StringType))
    .add("brand",StringType)
    .add("caller",StringType)
    .add("channel_id",IntegerType)
    .add("device_type",StringType)
    .add("event_hash",StringType)
    .add("external_username",StringType)
    .add("extra",MapType(StringType, StringType))
    .add("fcv_ios_zid_idfa",StringType)
    .add("header",MapType(StringType, StringType))
    .add("idfa",StringType)
    .add("idfv",StringType)
    .add("imei",StringType)
    .add("imv_acv_xid",StringType)
    .add("imv_acv_zid",StringType)
    .add("imv_displayname",StringType)
    .add("imv_email",StringType)
    .add("imv_ip_cy",StringType)
    .add("imv_mobile",StringType)
    .add("imv_reg_ip",StringType)
    .add("imv_reg_time",StringType)
    .add("imv_secureemail",StringType)
    .add("imv_trust_ip",StringType)
    .add("imv_ut_ban_dtl",StringType)
    .add("imv_ut_spm_dtl",StringType)
    .add("imv_ut_vlb_dtl",StringType)
    .add("imv_zid2xid",StringType)
    .add("inviterid",StringType)
    .add("ip_ar",StringType)
    .add("ip_c",StringType)
    .add("ip_ci",StringType)
    .add("ip_cu",StringType)
    .add("ip_pr",StringType)
    .add("is_old_customer",StringType)
    .add("is_user_ban",StringType)
    .add("is_user_high_value",StringType)
    .add("is_user_spam",StringType)
    .add("is_user_stolen",StringType)
    .add("jsv_lid",StringType)
    .add("jsv_machine",StringType)
    .add("jzid",StringType)
    .add("likedid",StringType)
    .add("lsv_imv_zid",StringType)
    .add("lsv_ip_tags",StringType)
    .add("lsv_passid_tags",StringType)
    .add("lsv_rqv_a",StringType)
    .add("lsv_rqv_ip",StringType)
    .add("lsv_zid_tags",StringType)
    .add("mac",StringType)
    .add("matched",StringType)
    .add("mid_bid",StringType)
    .add("mid_bid_info",StringType)
    .add("mid_biz",StringType)
    .add("mid_fcv_ios_zid_activesecs",StringType)
    .add("mid_fp",StringType)
    .add("mid_fphash",StringType)
    .add("mid_http_header",StringType)
    .add("mid_imv_br_chk",StringType)
    .add("mid_imv_br_tag",StringType)
    .add("mid_imv_br_tkn",StringType)
    .add("mid_imv_cuid2xid",StringType)
    .add("mid_imv_zid",StringType)
    .add("mid_jsv_factor",StringType)
    .add("mid_rqv_biz",StringType)
    .add("mid_rqv_biz_ev",StringType)
    .add("mid_rqv_header",StringType)
    .add("mid_rqv_jt",StringType)
    .add("mid_st",StringType)
    .add("mid_taker_st",StringType)
    .add("mid_token",StringType)
    .add("mid_tp",StringType)
    .add("mid_username",StringType)
    .add("ml_feature",MapType(StringType, StringType))
    .add("ml_models",MapType(StringType, StringType))
    .add("model",StringType)
    .add("msg_version",StringType)
    .add("ocr_time",StringType)
    .add("other_input",MapType(StringType, StringType))
    .add("passid1",StringType)
    .add("phone_sha1",StringType)
    .add("playback",StringType)
    .add("position",StringType)
    .add("referer",StringType)
    .add("reg_ip",StringType)
    .add("reg_timestamp",StringType)
    .add("register_type",StringType)
    .add("related_passid",MapType(StringType, StringType))
    .add("relation",StringType)
    .add("relation_id",StringType)
    .add("req_a",StringType)
    .add("req_bid",StringType)
    .add("req_biz",MapType(StringType, StringType))
    .add("req_brand",StringType)
    .add("req_bssid",StringType)
    .add("req_c",StringType)
    .add("req_ev",StringType)
    .add("req_fp",MapType(StringType, StringType))
    .add("req_http_header",StringType)
    .add("req_jt",StringType)
    .add("req_lt",StringType)
    .add("req_mf",StringType)
    .add("req_mobile",StringType)
    .add("req_model",StringType)
    .add("req_net",StringType)
    .add("req_option",StringType)
    .add("req_reg_ip",StringType)
    .add("req_reg_time",StringType)
    .add("req_sid",StringType)
    .add("req_ssid",StringType)
    .add("req_test",StringType)
    .add("req_to",StringType)
    .add("req_token",MapType(StringType, StringType))
    .add("req_username",StringType)
    .add("req_vw",StringType)
    .add("reserve1",StringType)
    .add("reserve2",StringType)
    .add("reserve3",StringType)
    .add("reserve4",StringType)
    .add("reserve5",ArrayType(StringType))
    .add("reserve6",MapType(StringType, StringType))
    .add("resp_action",StringType)
    .add("resp_ban_reason",StringType)
    .add("resp_c",StringType)
    .add("resp_s",StringType)
    .add("resp_sid",StringType)
    .add("resp_x",StringType)
    .add("resp_z",StringType)
    .add("ret_label",StringType)
    .add("ret_level",StringType)
    .add("reward_type",StringType)
    .add("reward_value",StringType)
    .add("rule_id_all",ArrayType(IntegerType))
    .add("rule_id_list",ArrayType(StringType))
    .add("rule_id_observe",ArrayType(IntegerType))
    .add("rules",ArrayType(StringType))
    .add("ser_time_hour",StringType)
    .add("servertime",StringType)
    .add("sid",StringType)
    .add("system_version",StringType)
    .add("ua",StringType)
    .add("ua_info",MapType(StringType, StringType))
    .add("unchecked_mid",MapType(StringType, StringType))
    .add("unchecked_req",MapType(StringType, StringType))
    .add("unchecked_resp",MapType(StringType, StringType))
    .add("unchecked_total",MapType(StringType, StringType))
    .add("zid",StringType)
    .add("zid_fc",StringType)
    .add("phone",StringType)
    .add("stat_hour",StringType)


  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)
    //Hour = args(1)
    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    //读取数据类型，全量还是增量
    readType = args(2)
    println(s"读取数据类型：${readType}")
    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    //定义读取和写入的hdfs文件名
    val fileName = "risk_event"

    val df = readAfs(spark,fileName).repartition(20)
    //val df = readCsvData(spark,"risk_event_20241205.txt")
    println(s"读取risk_event的hdfs数据文件共："+ df.count())

    if (df.count() != 0) {
      //处理数据
      val result = dealData(spark,df)
      result.show(5,false)
      println(result.count())
      result.createOrReplaceTempView("res_risk_event")

      spark.sql(
        s"""
          |insert overwrite table udw_ns.default.help_ods_risk_event partition(event_day,event_hour)
          |select
          |   activity_id,
          |   ser_time,
          |   time_stamp,
          |   app,
          |   sc,
          |   ev,
          |   username,
          |   ip,
          |   passid,
          |   userid,
          |   reg_time,
          |   cuid,
          |   z,
          |   gdid,
          |   xid,
          |   secure_mobile,
          |   rule_id_hit,
          |   activity_type,
          |   aid,
          |   app_version,
          |   appid,
          |   appkey,
          |   baiduid,
          |   bid_info,
          |   brand,
          |   caller,
          |   channel_id,
          |   device_type,
          |   event_hash,
          |   external_username,
          |   extra,
          |   fcv_ios_zid_idfa,
          |   header,
          |   idfa,
          |   idfv,
          |   imei,
          |   imv_acv_xid,
          |   imv_acv_zid,
          |   imv_displayname,
          |   imv_email,
          |   imv_ip_cy,
          |   imv_mobile,
          |   imv_reg_ip,
          |   imv_reg_time,
          |   imv_secureemail,
          |   imv_trust_ip,
          |   imv_ut_ban_dtl,
          |   imv_ut_spm_dtl,
          |   imv_ut_vlb_dtl,
          |   imv_zid2xid,
          |   inviterid,
          |   ip_ar,
          |   ip_c,
          |   ip_ci,
          |   ip_cu,
          |   ip_pr,
          |   is_old_customer,
          |   is_user_ban,
          |   is_user_high_value,
          |   is_user_spam,
          |   is_user_stolen,
          |   jsv_lid,
          |   jsv_machine,
          |   jzid,
          |   likedid,
          |   lsv_imv_zid,
          |   lsv_ip_tags,
          |   lsv_passid_tags,
          |   lsv_rqv_a,
          |   lsv_rqv_ip,
          |   lsv_zid_tags,
          |   mac,
          |   matched,
          |   mid_bid,
          |   mid_bid_info,
          |   mid_biz,
          |   mid_fcv_ios_zid_activesecs,
          |   mid_fp,
          |   mid_fphash,
          |   mid_http_header,
          |   mid_imv_br_chk,
          |   mid_imv_br_tag,
          |   mid_imv_br_tkn,
          |   mid_imv_cuid2xid,
          |   mid_imv_zid,
          |   mid_jsv_factor,
          |   mid_rqv_biz,
          |   mid_rqv_biz_ev,
          |   mid_rqv_header,
          |   mid_rqv_jt,
          |   mid_st,
          |   mid_taker_st,
          |   mid_token,
          |   mid_tp,
          |   mid_username,
          |   ml_feature,
          |   ml_models,
          |   model,
          |   msg_version,
          |   ocr_time,
          |   other_input,
          |   passid1,
          |   phone_sha1,
          |   playback,
          |   position,
          |   referer,
          |   reg_ip,
          |   reg_timestamp,
          |   register_type,
          |   related_passid,
          |   relation,
          |   relation_id,
          |   req_a,
          |   req_bid,
          |   req_biz,
          |   req_brand,
          |   req_bssid,
          |   req_c,
          |   req_ev,
          |   req_fp,
          |   req_http_header,
          |   req_jt,
          |   req_lt,
          |   req_mf,
          |   req_mobile,
          |   req_model,
          |   req_net,
          |   req_option,
          |   req_reg_ip,
          |   req_reg_time,
          |   req_sid,
          |   req_ssid,
          |   req_test,
          |   req_to,
          |   req_token,
          |   req_username,
          |   req_vw,
          |   reserve1,
          |   reserve2,
          |   reserve3,
          |   reserve4,
          |   reserve5,
          |   reserve6,
          |   resp_action,
          |   resp_ban_reason,
          |   resp_c,
          |   resp_s,
          |   resp_sid,
          |   resp_x,
          |   resp_z,
          |   ret_label,
          |   ret_level,
          |   reward_type,
          |   reward_value,
          |   rule_id_all,
          |   rule_id_list,
          |   rule_id_observe,
          |   rules,
          |   ser_time_hour,
          |   servertime,
          |   sid,
          |   system_version,
          |   ua,
          |   ua_info,
          |   unchecked_mid,
          |   unchecked_req,
          |   unchecked_resp,
          |   unchecked_total,
          |   zid,
          |   zid_fc,
          |   phone,
          |   '${YesterDay}' as event_day,
          |   stat_hour as event_hour
          |from res_risk_event
          |""".stripMargin)
    }

    // 停止SparkSession
    spark.stop()
  }

  /**
   * 自定义测试数据集
   * @param spark
   * @return
   */
  def readTestData(spark: SparkSession): DataFrame = {
    // 定义DataFrame的模式
    val schema = StructType(Array(
      StructField("ID", StringType, true),
      StructField("URL", StringType, true),
      StructField("TypeVal", StringType, true),
      StructField("Site", StringType, true),
      StructField("Content", StringType, true),
      StructField("IntoDbTime", StringType, true)
    ))

    // 创建一些示例数据
    val data = Seq(
      Row("1002","http://www.pc6.com/az/830555.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:08"),
      Row("1010","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:10"),
      Row("1010","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:15"),
      Row("1009","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-09 00:00:00"),
      Row("1008","http://91pmpc6.com/?ch=ocki1cy", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-10 00:00:00"),
      Row("1001","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "delete", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1001","http://m.pc6.com/s/499066", "add", "http://m.pc6.com", "*.pc6.com","2024-08-04 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:20"),
      Row("1007","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-03 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1005","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-02 00:00:00"),
      Row("1004","http://570pc6.com/", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00")
      // 添加更多数据行...

    )
     // 使用模式和数据创建DataFrame
    spark.createDataFrame(spark.sparkContext.parallelize(data), schema)
  }

  /**
   * 读取AFS格式的数据
   * @param spark
   * @param fileName 读取的文件名
   * @return
   */
  def readAfs(spark: SparkSession, fileName: String): DataFrame = {
    var res :DataFrame = null
    //判断是全量还是增量,till为全量，其他为增量
    if (readType.equals("till")) {
      //全量数据
      res = spark
        .read
        .json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/order_risk/htjpf/${fileName}*.txt")
        .dropDuplicates()
    }else{
      try {
        val resDf =
          spark
          .read
          .json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/order_risk/htjpf/${fileName}_${YesterDay}.txt")
          .dropDuplicates()
        if (resDf.count() > 0){
          res = resDf
        }
      } catch {
        case e:Exception => println(s"${fileName}_${YesterDay}文件不存在," + e.getMessage)
      }
    }
    res
  }

  /**
   * 读取txt格式的数据
   * @param spark
   * @param fileName
   * @return
   */
  def readCsvData(spark: SparkSession,fileName:String): DataFrame = {
    //根据ID是最新状态的数据 /Users/<USER>/Desktop/zrj_files/MIS数据/url_shield_new_total.csv
    //没有根据ID过滤去重文件 /Users/<USER>/Desktop/zrj_files/python/csvToExcel/
    //文件名 site_mask_new_total,suited_website_total,url_shield_new_total
    val filePath = s"/Users/<USER>/Desktop/zrj_files/电商/${fileName}"
    spark
      .read
      //.option("header", "true")
      .json(filePath)
  }

  /**
   * 将Row转换为Map
   * @param row
   * @return
   */
  def rowToMap(row: Row): Map[String, String] = {
    val schema = row.schema
    var map = Map[String,String]()

    for (i <- 0 until row.size) {
      // 对于row中的每一个元素，创建一个新的Map，它包含了之前的所有数据以及当前的新数据
      // 然后将这个新的Map赋值给map变量
      val value = row.getAs[Any](i)
      map += (schema(i).name -> ( if (value == null) "" else value.toString))
    }
    map
  }

  /**
   * 将Row转换为Array[STRING]
   * @param row
   * @return
   */
  def rowToArrayString(wrappedArray: scala.collection.mutable.WrappedArray[Any]): Array[String] = {
    val array = new ArrayBuffer[String](wrappedArray.length) // 预设大小以减少扩容开销

    // 遍历WrappedArray中的每个GenericRowWithSchema元素
    wrappedArray.foreach {
      case row: Row => // 使用模式匹配直接处理Row类型
        val values = row.toSeq.map { value =>
          s"${value.toString}" // 将值转换为字符串，处理null值的情况
        }
        val pairs = row.schema.fieldNames.zip(values).map { case (name, value) =>
          s"$name:$value" // 构建字段名和值的对
        }
        // 使用mkString简化字符串连接逻辑，例如"{key1:value1,key2:value2}"
        val jsonString = pairs.mkString("{", ",", "}")
        array += jsonString
      case other =>
        array += other.toString // 处理非Row类型的情况
    }

    array.toArray
  }


  /**
   * 将Row转换为Array[INT]
   * @param row
   * @return
   */
  def rowToArrayInt(wrappedArray: scala.collection.mutable.WrappedArray[Any]): Array[Int] = {
    val array = ArrayBuffer[Int]()
    // 遍历WrappedArray中的每个GenericRowWithSchema元素
    wrappedArray.foreach { row =>
      array += row.toString.toInt
    }
    array.toArray
  }


  /**
   * 自定义转换函数，根据目标类型转换值
   * @param value 数据
   * @param targetType 目标类型
   * @return
   */
  def convertValue(value: Any, targetType: DataType): Any = {
    targetType match {
      case _: StringType => if (value == null) getDefaultValue(StringType) else value.toString
      case _: IntegerType => if (value == null) getDefaultValue(IntegerType) else value.toString.toInt // 将字符串转换为整数
      case _: LongType => if (value == null) getDefaultValue(LongType) else value.toString.toLong // 将字符串转换为长整型
      case _: ArrayType =>  targetType.asInstanceOf[ArrayType].elementType match {
        case _: StringType => value match {
          case _ : mutable.WrappedArray[Any] => rowToArrayString(value.asInstanceOf[mutable.WrappedArray[Any]]) // 将GenericRowWithSchema转换为Array
          case null => Array.empty[String]
          case _ => value.asInstanceOf[Seq[String]]
        }
        // 根据需要添加其他类型的转换逻辑
        case _: IntegerType => value match {
          case _ : mutable.WrappedArray[Any] => rowToArrayInt(value.asInstanceOf[mutable.WrappedArray[Any]]) // 将GenericRowWithSchema转换为Array
          case null => Array.empty[Int]
          case _ => value.asInstanceOf[Seq[Int]]
        }
      }
      case _: MapType => value match {
        case _ : GenericRowWithSchema => rowToMap(value.asInstanceOf[GenericRowWithSchema]) // 将GenericRowWithSchema转换为Map
      }// 将Map转换为Map
      case _: StructType => value.asInstanceOf[Row] // 将Struct转换为Row
      case _ => getDefaultValue(targetType)
    }

  }

  /**
   * 自定义函数，根据类型返回默认值
   * @param dataType 数据类型
   * @return
   */
  def getDefaultValue(dataType: DataType): Any = {
    dataType match {
      case _: StringType => ""
      case _: IntegerType => null.asInstanceOf[Integer] // 使用null或适当的默认值
      case _: LongType => null.asInstanceOf[Long] // 使用null或适当的默认值
      case _: ArrayType => Array.empty[Any]
      case _: MapType => Map.empty[String, Any]
      case _: StructType => Row.empty // 或使用具体的空Row与StructType匹配
      case _ => null
      // 可以根据需要添加其他类型的默认值逻辑
    }
  }


  /**
   * 处理读取的数据进行判断
   * @param spark
   * @param df
   * @return
   */
  def dealData(spark: SparkSession,df:DataFrame): DataFrame = {

    val colllist = df
      .rdd
      .map(row => {
      val parsed_data = row.getStruct(0)
      val buffer = ArrayBuffer.empty[Any]

      //循环hive底表所有字段数据
      structSchema.fields.foreach(field => {
        //hive底表字段和数据类型
        val fieldName = field.name
        val fieldType = field.dataType
        //判断字段是否存在
        val maybeField = parsed_data.schema.fields.find(_.name == fieldName)
        var parsedValue :Any = null
        // 安全地处理maybeField
        maybeField match {
          case Some(parsedField) =>
            // 读取数据的名称和字段类型
            val parsedName = parsedField.name
            //val parsedType = parsedField.dataType
            //获取字段索引
            val fieldindex = parsed_data.fieldIndex(parsedName)
            //根据字段索引获取数据值
            parsedValue = parsed_data.get(fieldindex)
            buffer += convertValue(parsedValue, fieldType) // 调用自定义的转换函数
          case None =>
            // 字段不存在，可以根据需要进行处理，例如记录日志、跳过该字段等
            println(s"Field ${fieldName} does not exist in parsed_data.")
            buffer += getDefaultValue(fieldType) // 字段不存在时添加默认值
        }
      })
      println(s"buffer:${buffer}")
      Row.fromSeq(buffer)
    })

    val filterRes = spark.createDataFrame(colllist, structSchema)
      .repartition(2)

    filterRes.count()
    filterRes
  }
}
