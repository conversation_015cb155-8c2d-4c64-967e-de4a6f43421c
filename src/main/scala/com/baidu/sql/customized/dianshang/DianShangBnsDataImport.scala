package com.baidu.sql.customized.dianshang

import com.alibaba.fastjson.JSONObject
import com.baidu.bailing.lib.ral.util.UrlTargetUtil
import com.baidu.sql.utils.SparkUtils.{dianShangUrl, getHttpurl}
import okhttp3.{MediaType, OkHttpClient, Request, RequestBody, Response}
import org.apache.commons.codec.digest.DigestUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.types.{StringType, StructField, StructType}

import scala.collection.JavaConverters.mapAsJavaMapConverter
import scala.collection.convert.ImplicitConversions.`map AsScala`
import scala.collection.mutable.ListBuffer
import scala.math.ceil


/**
 * <AUTHOR>
 * @Date 2024-11-27
 */
object DianShangBnsDataImport {

  //bns接口地址
  //val url = "group.opera-risk-riskChannelEcom-000-bj.ECommerceRisk.all"
  //val url = "bns://group.epbravo-riskChannelEcom.EP-BRAVO.all"

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_hive_to_bns")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 昨天日期
    val dt_yd = YesterDay
    // 当前日期
    val dt_td = calcnDate(YesterDay, -1)
    // 黑名单过期时间
    val dt_exp = calcnDate(dt_td, 90)
    // 取近三个月
    val dt_90d = calcnDate(dt_td, -90)
    // 赔付次数
    val times = 7
    //赔付金额
    val money = 3000

    //当前时间戳
    val creSec = getTimeStampTake(dt_td, 10).toString
    //过期时间戳
    val expSec = getTimeStampTake(dt_exp, 10).toString
    //0-无效标签 1-名单类标签 2-安全因子类标签
    val status = "1"
    //风险级别,L-低风险，M-中风险，H-高风险
    val level = "H"
    val listStr = List(status, expSec, level, creSec).mkString(",")
    val resultList = ListBuffer[DataFrame]()

    //抽取赔付数据
    val peiFuDf = spark.sql(
      s"""
         |select distinct
         |    extra['pay_for_id'] as pay_for_id,
         |    extra['pay_for_money']/100 as pay_for_money,
         |    passid,
         |    zid,
         |    phone_sha1
         |from udw_ns.default.help_ods_risk_event
         |where event_day>=${dt_90d}
         |and event_day<=${dt_yd}
         |and activity_id='13142'
         |and req_ev='pay_for_callback'
         |and extra['pay_for_enum']='1' -- 代表已赔付
        """.stripMargin)

    val peiFuFount = peiFuDf.count()
    println(s"读取${dt_90d} - ${dt_yd}赔付数据一共：" + peiFuFount)
    peiFuDf.show(2, false)
    peiFuDf.createOrReplaceTempView("pei_data")

    if (peiFuFount > 0) {
      //薅羊毛手机号
      val phoneDf = spark.sql(
        s"""
           |select
           |    t.phone_sha1 as id,
           |    'B_0XP' as tag,
           |    'phone' as type,
           |    '薅羊毛手机号' as content
           |from
           |(select
           |    phone_sha1,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where phone_sha1 != ''
           |group by phone_sha1) t
           |where t.nums_of_orders>${times} or t.pay_for_money>${money}
         """.stripMargin)

      if (phoneDf.count() > 0) {
        resultList += phoneDf
      }

      //薅羊毛账号
      val passidDf = spark.sql(
        s"""
           |select
           |    t.passid as id,
           |    'B_JIW' as tag,
           |    'passid' as type,
           |    '薅羊毛账号' as content
           |from
           |(select
           |    passid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where passid != ''
           |group by passid) t
           |where t.nums_of_orders>${times} or t.pay_for_money>${money}
         """.stripMargin)

      if (passidDf.count() > 0) {
        resultList += passidDf
      }

      //薅羊毛设备
      val zidDf = spark.sql(
        s"""
           |select
           |    t.zid as id,
           |    'B_08F' as tag,
           |    'zid' as type,
           |    '薅羊毛设备' as content
           |from
           |(select
           |    zid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where zid != ''
           |group by zid) t
           |where t.nums_of_orders>${times} or t.pay_for_money>${money}
         """.stripMargin)

      if (zidDf.count() > 0) {
        resultList += zidDf
      }
    }

    //申请赔付阶段抽取数据
    val payForDf = spark.sql(
      s"""
         |select distinct
         |    extra['pay_for_id'] as pay_for_id,
         |    extra['pay_for_money']/100 as pay_for_money,
         |    passid,
         |    zid,
         |    phone_sha1
         |from udw_ns.default.help_ods_risk_event
         |where event_day>=${dt_90d}
         |and event_day<=${dt_yd}
         |and activity_id in ('13142','13576')
         |and req_ev in ('pay_for','auto_system_wfjp')
        """.stripMargin)

    val payForFount = payForDf.count()
    println(s"读取${dt_90d} - ${dt_yd}申请赔付阶段抽取数据一共：" + payForFount)
    payForDf.show(2, false)
    payForDf.createOrReplaceTempView("peifu_pay_for")

    if (payForFount > 0) {
      //申请赔付阶段薅羊毛设备
      val payForZidDf = spark.sql(
        s"""
           |select
           |    t.zid as id,
           |    'B_C48' as tag,
           |    'zid' as type,
           |    '申请赔付阶段薅羊毛设备' as content
           |from
           |(select
           |    zid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from peifu_pay_for
           |where zid != ''
           |group by zid) t
           |where t.nums_of_orders>${times} or t.pay_for_money>${money}
         """.stripMargin)

      if (payForZidDf.count() > 0) {
        resultList += payForZidDf
      }

      //申请赔付阶段薅羊毛账号
      val payForPassidDf = spark.sql(
        s"""
           |select
           |    t.passid as id,
           |    'B_EV5' as tag,
           |    'passid' as type,
           |    '申请赔付阶段薅羊毛账号' as content
           |from
           |(select
           |    passid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from peifu_pay_for
           |where passid != ''
           |group by passid) t
           |where nums_of_orders>${times} or pay_for_money>${money}
         """.stripMargin)

      if (payForPassidDf.count() > 0) {
        resultList += payForPassidDf
      }
    }

    //如何有数据的话
    if (resultList.nonEmpty) {
      //合并数据
      val unionDf = resultList.reduce((x, y) => x.unionByName(y))
        .dropDuplicates()
        .filter($"id" =!= "")
        .na.fill("")
        .repartition(2)

      unionDf.show(5, false)
      println("合并后的数据量为：" + unionDf.count())

      if (unionDf.count() > 0) {
        println("开始解析bns地址：")
        val httpUrl = getHttpurl(url=dianShangUrl,lastUrl = "/list/import")

        //按照业务名单类型进行分组遍历
        val dataCntDf = unionDf.rdd.map(x => {
            val objKeyVal = x.getAs[String]("type") + "_" + x.getAs[String]("id")
            val tag = x.getAs[String]("tag")
            //按照业务名单来分组
            val content = x.getAs[String]("content")
            (content, (objKeyVal, tag))
          })
          .groupByKey()
          .mapValues(_.toArray)
          .map({ case (content, objArray) =>
            var jsonObj = new JSONObject
            var dataCnt = 0L
            //遍历同一个content下的多个objKeyVal和tag
            objArray.foreach(x => {
              val objKeyVal = x._1
              val tag = x._2
              val jsonTag = new JSONObject
              //{tag:"status,expSec,level,creSec,ext"}
              jsonTag.put(tag, listStr)
              //{passid_10001:{tag:"status,expSec,level,creSec,ext"}}
              jsonObj.put(objKeyVal, jsonTag)
              dataCnt = dataCnt + 1
            })
            //{passid_10001:{tag:"status,expSec,level,creSec,ext"},content:"薅羊毛账号"}
            jsonObj.put("content", content)
            jsonObj
          })
          .collect()

        println("打印业务名单长度" + dataCntDf.length)

        //数据量大于1000时，分批发送
        val dataLen = 1000

        dataCntDf.foreach { json =>
          //获取当前JSON的长度
          val len_json = json.size()
          if (len_json > dataLen) {
            val content = json.get("content").asInstanceOf[String]
            var removeJson = json
            val num = ceil(len_json / dataLen).toInt
            println("num:" + num)
            for (i <- 1 to num) {
              val splitIndex = if (num != 1 && i == num) removeJson.size() else dataLen * i
              val (firstPart, secondPart) = removeJson.splitAt(splitIndex)

              val jsonObject: JSONObject = new JSONObject(firstPart.asJava) // 使用Java转换，因为org.json主要支持Java集合
              jsonObject.put("content", content)
              //println("jsonObject:" + jsonObject.toJSONString)
              //发送数据
              postRequest(httpUrl, jsonObject)

              // 更新removeJson为剩余的部分，准备下一次循环
              removeJson = new JSONObject(secondPart.asJava)

              // 如果是最后一次循环，或者剩余的部分小于dataLen个元素，则直接添加content并结束循环
              if (i == num || removeJson.size() < dataLen) {
                removeJson.put("content", content)
                //println("jsonObject:" + removeJson.toJSONString)
                //发送数据
                postRequest(httpUrl, removeJson)
              }
            }
          } else {
            //println("jsonObject:" + json.toJSONString)
            //发送数据
            postRequest(httpUrl, json)
          }
        }
      }

      println("推送数据成功,程序结束")

      spark.close()
    }


    /**
     * 从接口获取数据
     * @param httpUrl
     * @param dataJson
     */
    def postRequest(httpUrl: String, dataJson: JSONObject): Unit = {

      //请求体拼接
      val body = new JSONObject
      body.put("data", dataJson)
      //口令获取
      var response: Response = null
      val APP_KEY = "9999"
      val APP_TOKEN = "4ee0e2c2cabab107411f9f2b04497759"
      //当前时间戳
      val createTime = currentTimestampMillis(10)
      val sign = DigestUtils.md5Hex(APP_KEY + createTime + APP_TOKEN)

      val requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body.toString())
      val okHttpClient = new OkHttpClient
      val request = new Request.Builder()
        .url(httpUrl + "?sign=" + sign + "&timestamp=" + createTime + "&appkey=" + APP_KEY)
        //.addHeader("sign", s"""$sign""")
        //.addHeader("timestamp", s"""$createTime""")
        //.addHeader("appkey", APP_KEY)
        .addHeader("Content-Type", "application/json")
        .post(requestBody)
        .build

      try {
        response = okHttpClient.newCall(request).execute
        if (response.isSuccessful) {
          //response.body.string
          println("返回response" + response.body.string)
        } else {
          println("Empty response")
        }
      } catch {
        case e: Exception =>
          println("Empty response:" + e.getMessage)
      } finally if (response != null) response.close()
    }

    /**
     * * 解析bns地址,获取httpurl
     */
    /*def getHttpurl(): String = {
      var httpurl = ""
      val ipPattern =
        "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}:\\d{4}";

      val addressList = new ListBuffer[String]
      //获取ip,如果ip则直接使用，否则通过bns获取
      if (url.matches(ipPattern)) {
        addressList.append(url)
        httpurl = "http://" + addressList.head + "/list/import"
      } else {
        var urlhttp = ""
        try {
          urlhttp = UrlTargetUtil.getHttpPath(url)
        } catch {
          case e: Exception => println("load bns exp." + e.getMessage)
        }

        if (urlhttp.length > 0) {
          httpurl = urlhttp + "/list/import"
        }
      }
      println("解析后的httpurl:" + httpurl)
      httpurl
    }*/

    /**
     * 自定义测试数据集
     *
     * @param spark
     * @return
     */
    def readTestData(spark: SparkSession): DataFrame = {
      // 定义DataFrame的模式
      val schema = StructType(Array(
        StructField("id", StringType, true),
        StructField("tag", StringType, true),
        StructField("type", StringType, true),
        StructField("content", StringType, true)
      ))

      // 创建一些示例数据
      val data = Seq(
        Row("1002", "B_0XP", "phone", "薅羊毛手机号"),
        Row("1003", "B_0XP", "phone", "薅羊毛手机号"),
        Row("1010", "B_0XP", "phone", "薅羊毛手机号"),
        Row("1010", "B_JIW", "passid", "薅羊毛账号"),
        Row("1009", "B_JIW", "passid", "薅羊毛账号"),
        Row("1008", "B_JIW", "passid", "薅羊毛账号"),
        Row("1001", "B_JIW", "passid", "薅羊毛账号"),
        Row("1006", "B_08F", "zid", "薅羊毛设备"),
        Row("1001", "B_08F", "zid", "薅羊毛设备"),
        Row("1003", "B_08F", "zid", "薅羊毛设备"),
        Row("1007", "B_08F", "zid", "薅羊毛设备"),
        Row("1008", "B_08F", "zid", "薅羊毛设备"),
        Row("1009", "B_08F", "zid", "薅羊毛设备"),
        Row("1010", "B_08F", "zid", "薅羊毛设备"),
        Row("1011", "B_08F", "zid", "薅羊毛设备"),
        Row("1012", "B_08F", "zid", "薅羊毛设备"),
        Row("1013", "B_08F", "zid", "薅羊毛设备"),
        Row("1014", "B_08F", "zid", "薅羊毛设备"),
        Row("1015", "B_08F", "zid", "薅羊毛设备"),
        Row("1016", "B_08F", "zid", "薅羊毛设备"),
        Row("1017", "B_08F", "zid", "薅羊毛设备"),
        Row("1006", "B_C48", "zid", "申请赔付阶段薅羊毛设备"),
        Row("1005", "B_C48", "zid", "申请赔付阶段薅羊毛设备"),
        Row("1009", "B_C48", "zid", "申请赔付阶段薅羊毛设备"),
        Row("1010", "B_C48", "zid", "申请赔付阶段薅羊毛设备"),
        Row("1012", "B_EV5", "passid", "申请赔付阶段薅羊毛账号"),
        Row("1011", "B_EV5", "passid", "申请赔付阶段薅羊毛账号"),
        Row("1004", "B_EV5", "passid", "申请赔付阶段薅羊毛账号")
        // 添加更多数据行...

      )
      // 使用模式和数据创建DataFrame
      spark.createDataFrame(spark.sparkContext.parallelize(data), schema)
    }
  }
}
