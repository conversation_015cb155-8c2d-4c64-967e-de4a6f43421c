package com.baidu.sql.customized.dianshang

import com.baidu.sql.utils.TimeOperateUtil
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, SparkSession}
import scala.collection.mutable.ListBuffer


/**
 * <AUTHOR>
 *         输出电商黑名单表 udw_ns.default.help_ods_risk_black_id_day
 * @Date 2024-11-25
 */
object DianShangHymData {

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_im_2_mysql")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()

    // 昨天日期
    val dt_yd = YesterDay
    // 当前日期
    val dt_td = TimeOperateUtil.calcnDate(YesterDay,-1)
    // 黑名单过期时间
    val dt_exp = TimeOperateUtil.calcnDate(dt_td,90)
    // 取近三个月
    val dt_90d = TimeOperateUtil.calcnDate(dt_td,-90)
    // 赔付次数
    val times = 7
    //赔付金额
    val money = 3000

    //抽取赔付数据
    val peiFuDf = spark.sql(
      s"""
        |select distinct
        |    extra['pay_for_id'] as pay_for_id,
        |    extra['pay_for_money']/100 as pay_for_money,
        |    passid,
        |    zid,
        |    phone_sha1
        |from udw_ns.default.help_ods_risk_event
        |where event_day>=${dt_90d}
        |and event_day<=${dt_yd}
        |and activity_id='13142'
        |and req_ev='pay_for_callback'
        |and extra['pay_for_enum']='1' -- 代表已赔付
        """.stripMargin)

    println(s"读取${dt_90d} - ${dt_yd}赔付数据一共：" + peiFuDf.count())
    peiFuDf.show(2,false)
    peiFuDf.createOrReplaceTempView("pei_data")
    val resultList = new ListBuffer[DataFrame]()

    if (peiFuDf.count() > 0){
      //薅羊毛手机号
      val phoneDf = spark.sql(
        s"""
           |select
           |    phone_sha1,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where phone_sha1 != ''
           |group by phone_sha1
         """.stripMargin)

      println(s"读取薅羊毛手机号数据一共：" + phoneDf.count())
      phoneDf.createOrReplaceTempView("peifu_phone")

      val phoneResDf = spark.sql(
        s"""
           |select
           |    phone_sha1 as id,
           |    'B_0XP' as tag,
           |    'phone' as id_type,
           |    '13142' as activity_id,
           |    '1' as channel_id,
           |    unix_timestamp('${dt_td}','yyyyMMdd') as generation_time,
           |    unix_timestamp('${dt_exp}','yyyyMMdd') as expire_time,
           |    '-1' as value,
           |    'nums_of_orders>${times} or rqv_total_amount>${money}' as reason,
           |    'UT' as operator
           |from peifu_phone
           |where nums_of_orders>${times} or pay_for_money>${money}
           |""".stripMargin)

      if (phoneResDf.count() > 0){
        resultList += phoneResDf
      }

      //薅羊毛账号
      val passidDf = spark.sql(
        s"""
           |select
           |    passid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where passid != ''
           |group by passid
         """.stripMargin)

      println(s"读取薅羊毛账号数据一共：" + passidDf.count())
      passidDf.createOrReplaceTempView("peifu_passid")

      val passidResDf = spark.sql(
        s"""
           |select
           |    passid as id,
           |    'B_JIW' as tag,
           |    'passid' as id_type,
           |    '13142' as activity_id,
           |    '1' as channel_id,
           |    unix_timestamp('${dt_td}','yyyyMMdd') as generation_time,
           |    unix_timestamp('${dt_exp}','yyyyMMdd') as expire_time,
           |    '-1' as value,
           |    'nums_of_orders>${times} or rqv_total_amount>${money}' as reason,
           |    'UT' as operator
           |from peifu_passid
           |where nums_of_orders>${times} or pay_for_money>${money}
           |""".stripMargin)

      if (passidResDf.count() > 0){
        resultList += passidResDf
      }

      //薅羊毛设备
      val zidDf = spark.sql(
        s"""
           |select
           |    zid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from pei_data
           |where zid != ''
           |group by zid
         """.stripMargin)

      println(s"读取薅羊毛设备数据一共：" + zidDf.count())
      zidDf.createOrReplaceTempView("peifu_zid")

      val zidResDf = spark.sql(
        s"""
           |select
           |    zid as id,
           |    'B_08F' as tag,
           |    'zid' as id_type,
           |    '13142' as activity_id,
           |    '1' as channel_id,
           |    unix_timestamp('${dt_td}','yyyyMMdd') as generation_time,
           |    unix_timestamp('${dt_exp}','yyyyMMdd') as expire_time,
           |    '-1' as value,
           |    'nums_of_orders>${times} or rqv_total_amount>${money}' as reason,
           |    'UT' as operator
           |from peifu_zid
           |where nums_of_orders>${times} or pay_for_money>${money}
           |""".stripMargin)

      if (zidResDf.count() > 0) {
        resultList += zidResDf
      }
    }

    //申请赔付阶段抽取数据
    val payForDf = spark.sql(
      s"""
         |select distinct
         |    extra['pay_for_id'] as pay_for_id,
         |    extra['pay_for_money']/100 as pay_for_money,
         |    passid,
         |    zid,
         |    phone_sha1
         |from udw_ns.default.help_ods_risk_event
         |where event_day>=${dt_90d}
         |and event_day<=${dt_yd}
         |and activity_id in ('13142','13576')
         |and req_ev in ('pay_for','auto_system_wfjp')
        """.stripMargin)

    println(s"读取申请赔付阶段抽取数据一共：" + payForDf.count())
    payForDf.createOrReplaceTempView("peifu_pay_for")

    if (payForDf.count() > 0){
      //申请赔付阶段薅羊毛设备
      val payForZidDf = spark.sql(
        s"""
           |select
           |    zid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from peifu_pay_for
           |where zid != ''
           |group by zid
         """.stripMargin)
      println(s"读取申请赔付阶段薅羊毛设备数据一共：" + payForZidDf.count())
      payForZidDf.createOrReplaceTempView("pay_for_zid")

      val payForZidResDf = spark.sql(
        s"""
           |select
           |    zid as id,
           |    'B_C48' as tag,
           |    'zid' as id_type,
           |    '13142' as activity_id,
           |    '1' as channel_id,
           |    unix_timestamp('${dt_td}','yyyyMMdd') as generation_time,
           |    unix_timestamp('${dt_exp}','yyyyMMdd') as expire_time,
           |    '-1' as value,
           |    'nums_of_orders>${times} or rqv_total_amount>${money}' as reason,
           |    'UT' as operator
           |from pay_for_zid
           |where nums_of_orders>${times} or pay_for_money>${money}
           |""".stripMargin)

      if (payForZidResDf.count() > 0){
        resultList += payForZidResDf
      }

      //申请赔付阶段薅羊毛账号
      val payForPassidDf = spark.sql(
        s"""
           |select
           |    passid,
           |    count(distinct pay_for_id) as nums_of_orders,
           |    sum(pay_for_money) as pay_for_money
           |from peifu_pay_for
           |where passid != ''
           |group by passid
         """.stripMargin)

      println(s"读取申请赔付阶段薅羊毛账号数据一共：" + payForPassidDf.count())
      payForPassidDf.createOrReplaceTempView("pay_for_passid")

      val payForPassidResDf =  spark.sql(
        s"""
           |select
           |    passid as id,
           |    'B_EV5' as tag,
           |    'passid' as id_type,
           |    '13142' as activity_id,
           |    '1' as channel_id,
           |    unix_timestamp('${dt_td}','yyyyMMdd') as generation_time,
           |    unix_timestamp('${dt_exp}','yyyyMMdd') as expire_time,
           |    '-1' as value,
           |    'nums_of_orders>${times} or rqv_total_amount>${money}' as reason,
           |    'UT' as operator
           |from pay_for_passid
           |where nums_of_orders>${times} or pay_for_money>${money}
           |""".stripMargin)

      if (payForPassidResDf.count() > 0){
        resultList += payForPassidResDf
      }
    }

    if (resultList.nonEmpty){
      //合并数据
      val resultDf = resultList.reduce((x, y) => x.union(y))
        .dropDuplicates()
        .na.fill("")

      println(s"最终筛选薅羊毛数据一共：" + resultDf.count())
      resultDf.show(5,false)
      resultDf.createOrReplaceTempView("result")

      //写入黑名单
      spark.sql(
        s"""
           |insert overwrite udw_ns.default.help_ods_risk_black_id_day partition (event_day='${dt_yd}')
           |select
           |    id,
           |    tag,
           |    id_type,
           |    activity_id,
           |    channel_id,
           |    generation_time,
           |    expire_time,
           |    value,
           |    reason,
           |    operator
           |from result
           |""".stripMargin)
    }
    spark.close()
  }
}
