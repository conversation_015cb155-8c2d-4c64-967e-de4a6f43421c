package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/12/23
 * @description: 电商服务数据(重复进线数据)到MySQL的同步逻辑
 */
object DianShangFuWuJinXianToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    // 读取Hive表
    val hiveSql =
      s"""
         |SELECT
         |      create_day,
         |      feedback_time,
         |      feedback_mode,
         |      service_data.case_id,
         |      service_data.order_id,
         |      if(peifu.order_id is not null,1,0) as is_peifu,
         |      lag(feedback_time, 1) over (
         |        partition by
         |          service_data.order_id
         |        order by
         |          feedback_time
         |      ) as lag_1,
         |      lag(service_data.case_id, 1) over (
         |        partition by
         |          service_data.order_id
         |        order by
         |          feedback_time
         |      ) as lag_1_case,
         |      lag(feedback_time, 3) over (
         |        partition by
         |          service_data.order_id
         |        order by
         |          feedback_time
         |      ) as lag_3,
         |      lag(service_data.case_id, 3) over (
         |        partition by
         |          service_data.order_id
         |        order by
         |          feedback_time
         |      ) as lag_3_case,
         |      lag(feedback_time, 5) over (
         |        partition by
         |          service_data.order_id
         |        order by
         |          feedback_time
         |      ) as lag_5,
         |      sheet.order_id as sheet_order_id,
         |      sheet.case_id as sheet_case_id, --工单id
         |      sheet.biao_type, --工单类型
         |      sheet.from_case_id,
         |      sheet.source_from, --来源
         |      sheet.node_status, --处理节点
         |      sheet.case_label,
         |      sheet.label_rnk,
         |      sheet.is_cs_in,
         |      sheet.create_time,
         |      sheet.first_turn_time,
         |      sheet.first_reply_time,
         |      sheet.quick_compensation_process_time,
         |      sheet.quick_compensation_pay_time,
         |      sheet.experience_compensation_process_time,
         |      sheet.experience_compensation_pay_time,
         |      sheet.outbound_call_contact_time,
         |      sheet.customer_service_first_processing_time,
         |      sheet.merchant_re_processing_time,
         |      sheet.delegation_guaranteed_processing_time,
         |      sheet.guaranteed_processing_time,
         |      sheet.merchant_third_processing_time,
         |      sheet.guarantee_circulation_time,
         |      sheet.customer_service_third_circulation_time,
         |      sheet.customer_service_end_investigation_time,
         |      sheet.guarantee_end_investigation_time,
         |      sheet.platform_customer_service_accountability_time,
         |      sheet.kefu_close_time,
         |      sheet.cs_judge_close_time
         |    FROM
         |      (
         |        SELECT
         |        distinct 
         |          from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyy-MM-dd') as create_day,
         |          feedback_time,
         |          feedback_mode,
         |          user_value_label,
         |          uid,
         |          robot_sat,
         |          to_manual,
         |          space_or_skill_id,
         |          im_session_id,
         |          case_id,
         |          order_id,
         |          shop_name,
         |          manual_sat
         |        FROM
         |          udw_ns.default.help_ods_feedback_online_online_retailers_feedback_only_df
         |        WHERE
         |          event_day = ${YesterDay}
         |          AND from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '********'
         |          and
         |          (
         |              (to_manual=1
         |              and space_or_skill_id > 0
         |              and space_or_skill_id != 225
         |              and feedback_mode = '在线')
         |              or feedback_mode in ('留言','热线','电话')
         |          )
         |      ) service_data
         |      left join (
         |    SELECT
         |      create_time, --投诉发起时间
         |      case
         |        WHEN source_from in ('IM机器人', 'IM人工', '客服工作台') then '人工IM'
         |        WHEN source_from in ('电商C端', '商家IM') then '投诉单'
         |        WHEN source_from in ('热线400', '微信') then '其他'
         |        when case_type = '仲裁单' then '仲裁单'
         |        else '暂无法识别（7.18号前来源未必填）'
         |      end as source_from_label,
         |      order_id, --订单id
         |      case_id,
         |      compensation_reason, --赔付原因
         |      carriage_quick_pass_flag --是否快通
         |    from
         |      udw_ns.default.help_ods_tb_fengling_compensation_all_info_online
         |    where
         |      event_day = ${YesterDay}
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '********'
         |      --and carriage_quick_pass_flag = '是'
         |    group by
         |      create_time, --投诉发起时间
         |      source_from_label,
         |      order_id, --订单id
         |      case_id,
         |      compensation_reason,
         |      carriage_quick_pass_flag
         |  ) peifu on service_data.order_id = peifu.order_id
         |  left join (
         |    select
         |      order_id,
         |      case_id, --工单id
         |      biao_type, --工单类型
         |      from_case_id,
         |      source_from, --来源
         |      node_status, --处理节点
         |      case_label,
         |      label_rnk,
         |      is_cs_in,
         |      create_time,
         |      first_turn_time,
         |      first_reply_time,
         |      quick_compensation_process_time,
         |      quick_compensation_pay_time,
         |      experience_compensation_process_time,
         |      experience_compensation_pay_time,
         |      outbound_call_contact_time,
         |      customer_service_first_processing_time,
         |      merchant_re_processing_time,
         |      delegation_guaranteed_processing_time,
         |      guaranteed_processing_time,
         |      merchant_third_processing_time,
         |      guarantee_circulation_time,
         |      customer_service_third_circulation_time,
         |      customer_service_end_investigation_time,
         |      guarantee_end_investigation_time,
         |      platform_customer_service_accountability_time,
         |      kefu_close_time,
         |      case
         |        when biao_type in ('fuwu', 'zhongcai') then kefu_close_time
         |        when biao_type in ('tousu') then platform_customer_service_accountability_time
         |        else null
         |      end as cs_judge_close_time
         |    from
         |      (
         |        select
         |          order_id,
         |          case_id, --工单id
         |          biao_type, --工单类型
         |          from_case_id,
         |          source_from,
         |          case_label,
         |          case
         |            when case_label = '售后纠纷' then 1
         |            when case_label = '售后服务问题' then 2
         |            when case_label = '发货物流问题' then 3
         |            when case_label = '平台服务问题' then 4
         |            when case_label = '商家服务问题' then 5
         |            when case_label = '其他' then 6
         |            else null
         |          end as label_rnk,
         |          if (
         |            (
         |              biao_type = 'fuwu'
         |              and outbound_call_contact_time is not null
         |            )
         |            or (
         |              biao_type = 'zhongcai'
         |              and first_reply_time is not null
         |            )
         |            or (
         |              biao_type = 'tousu'
         |              and create_time is not null
         |            ),
         |            '是',
         |            '否'
         |          ) as is_cs_in,
         |          node_status, --处理节点
         |          create_time,
         |          first_turn_time,
         |          first_reply_time,
         |          quick_compensation_process_time,
         |          quick_compensation_pay_time,
         |          experience_compensation_process_time,
         |          experience_compensation_pay_time,
         |          outbound_call_contact_time,
         |          customer_service_first_processing_time,
         |          merchant_re_processing_time,
         |          delegation_guaranteed_processing_time,
         |          guaranteed_processing_time,
         |          merchant_third_processing_time,
         |          guarantee_circulation_time,
         |          customer_service_third_circulation_time,
         |          customer_service_end_investigation_time,
         |          guarantee_end_investigation_time,
         |          platform_customer_service_accountability_time,
         |          kefu_close_time
         |        from
         |          (
         |            select
         |              order_id,
         |              case_id, --工单id
         |              biao_type, --工单类型
         |              from_case_id,
         |              case
         |                when source_from is not null then source_from
         |                when source_from is null
         |                and biao_type = 'zhongcai' then '售后仲裁纠纷来源'
         |                else '其他'
         |              end as source_from, --来源
         |              node_status, --处理节点
         |              case
         |                when biao_type = 'zhongcai' then '售后纠纷'
         |                when newcase_type_level1 = '商家-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '买家-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '平台-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '商家-商品质量' then '售后服务问题'
         |                when newcase_type_level1 = '商家-商品发布' then '售后服务问题'
         |                when newcase_type_level1 = '商品问题' then '售后服务问题'
         |                when newcase_type_level1 = '钱款纠纷' then '售后服务问题'
         |                when newcase_type_level1 = '舆情-舆情外溢' then '售后服务问题'
         |                when newcase_type_level1 = '发货物流问题' then '发货物流问题'
         |                when newcase_type_level1 = '商家-发货服务' then '发货物流问题'
         |                when newcase_type_level1 = '商家-快递问题' then '发货物流问题'
         |                when newcase_type_level1 = '物流-快递问题' then '发货物流问题'
         |                when newcase_type_level1 = '平台-平台服务' then '平台服务问题'
         |                when newcase_type_level1 = '平台-订单相关' then '平台服务问题'
         |                when newcase_type_level1 = '平台-平台功能' then '平台服务问题'
         |                when newcase_type_level1 = '平台-平台福利活动' then '平台服务问题'
         |                when newcase_type_level1 = '商家服务' then '商家服务问题'
         |                when newcase_type_level1 = '发票问题' then '商家服务问题'
         |                when newcase_type_level1 = '商家-发票相关' then '商家服务问题'
         |                when newcase_type_level1 = '商家-商家服务' then '商家服务问题'
         |                when newcase_type_level1 = '商家-商家信息' then '商家服务问题'
         |                when newcase_type_level1 = '其他' then '其他'
         |                else '其他'
         |              end as case_label,
         |              -- unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(first_cs_deal_time, 'yyyy-MM-dd HH:mm:ss') as diff,
         |              max(create_time) as create_time, --工单创建时间
         |              max(first_turn_time) as first_turn_time, --工单首次到商家时间
         |              max(first_reply_time) as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |              max(quick_compensation_process_time) as quick_compensation_process_time, --快赔保障处理时间
         |              max(quick_compensation_pay_time) as quick_compensation_pay_time, --快赔保障打款时间
         |              max(experience_compensation_process_time) as experience_compensation_process_time, --体验赔保障处理时间
         |              max(experience_compensation_pay_time) as experience_compensation_pay_time, --体验赔保障打款时间
         |              max(outbound_call_contact_time) as outbound_call_contact_time, --外呼联系用户时间
         |              max(customer_service_first_processing_time) as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |              max(merchant_re_processing_time) as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |              max(delegation_guaranteed_processing_time) as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |              max(guaranteed_processing_time) as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |              max(merchant_third_processing_time) as merchant_third_processing_time, --商家第三次处理时间
         |              max(guarantee_circulation_time) as guarantee_circulation_time, --保障流转时间
         |              max(customer_service_third_circulation_time) as customer_service_third_circulation_time, --客服第三次流转时间
         |              max(customer_service_end_investigation_time) as customer_service_end_investigation_time, --客服结束排查时间
         |              max(guarantee_end_investigation_time) as guarantee_end_investigation_time, --保障结束排查时间
         |              max(platform_customer_service_accountability_time) as platform_customer_service_accountability_time, --平台判责时间
         |              max(kefu_close_time) as kefu_close_time
         |            from
         |              (
         |                SELECT
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  from_case_id,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  first_reply_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  outbound_call_contact_time as outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_processing_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  merchant_re_processing_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  delegation_guaranteed_processing_time as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  null as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |                  null as merchant_third_processing_time, --商家第三次处理时间
         |                  null as guarantee_circulation_time, --保障流转时间
         |                  null as customer_service_third_circulation_time, --客服第三次流转时间
         |                  null as customer_service_end_investigation_time, --客服结束排查时间
         |                  null as guarantee_end_investigation_time, --保障结束排查时间
         |                  null as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                  case
         |                    when newcase_type_level1 is null
         |                    or newcase_type_level1 = '' then 'unknown'
         |                    else newcase_type_level1
         |                  end as newcase_type_level1,
         |                  case
         |                    when newcase_type_level2 is null
         |                    or newcase_type_level2 = '' then 'unknown'
         |                    else newcase_type_level2
         |                  end as newcase_type_level2,
         |                  case
         |                    when newcase_type_level3 is null
         |                    or newcase_type_level3 = '' then 'unknown'
         |                    else newcase_type_level3
         |                  end as newcase_type_level3,
         |                  'zhongcai' as biao_type,
         |                  source_from, --来源
         |                  node_status
         |                FROM
         |                  udw_ns.default.help_ods_fengling_zhongcai_detail
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '********' AND ${YesterDay}
         |                  --and outbound_call_contact_time is not null 
         |                  --and first_reply_time is not null --限制有商家流转则客服介入
         |                UNION ALL
         |                SELECT
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  from_case_id,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  first_reply_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  outbound_call_contact_time as outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_processing_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  merchant_re_processing_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  delegation_guaranteed_processing_time as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  guaranteed_processing_time as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |                  null as merchant_third_processing_time, --商家第三次处理时间
         |                  null as guarantee_circulation_time, --保障流转时间
         |                  null as customer_service_third_circulation_time, --客服第三次流转时间
         |                  null as customer_service_end_investigation_time, --客服结束排查时间
         |                  null as guarantee_end_investigation_time, --保障结束排查时间
         |                  null as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                  case
         |                    when newcase_type_level1 is null
         |                    or newcase_type_level1 = '' then 'unknown'
         |                    else newcase_type_level1
         |                  end as newcase_type_level1,
         |                  case
         |                    when newcase_type_level2 is null
         |                    or newcase_type_level2 = '' then 'unknown'
         |                    else newcase_type_level2
         |                  end as newcase_type_level2,
         |                  case
         |                    when newcase_type_level3 is null
         |                    or newcase_type_level3 = '' then 'unknown'
         |                    else newcase_type_level3
         |                  end as newcase_type_level3,
         |                  'fuwu' as biao_type,
         |                  source_from, --来源
         |                  node_status
         |                FROM
         |                  udw_ns.default.help_ods_fengling_fuwu_detail
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') between '********' and ${YesterDay}
         |                  --and outbound_call_contact_time is not null --限制有客服介入
         |                UNION ALL
         |                select
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  from_case_id,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  business_first_circulation_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  null outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_circulation_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  business_second_circulation_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  null as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  customer_service_second_circulation_time as guaranteed_processing_time, --客服再次处理时间
         |                  null as merchant_third_processing_time, --商家第三次处理时间
         |                  guarantee_circulation_time as guarantee_circulation_time, --保障流转时间
         |                  customer_service_third_circulation_time as customer_service_third_circulation_time, --客服第三次流转时间
         |                  customer_service_end_investigation_time as customer_service_end_investigation_time, --客服结束排查时间
         |                  guarantee_end_investigation_time as guarantee_end_investigation_time, --保障结束排查时间
         |                  platform_customer_service_accountability_time as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                  case_type_level1 as newcase_type_level1,
         |                  case_type_level2 as newcase_type_level2,
         |                  '' as newcase_type_level3,
         |                  --complaint_result_b,
         |                  'tousu' as biao_type,
         |                  source_from, --来源
         |                  complaint_status as node_status
         |                from
         |                  udw_ns.default.help_ods_fengling_tousu_detail_df
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '********' AND ${YesterDay}
         |                  and (
         |                    complaint_result_b in ('成立', '不成立')
         |                    or complaint_result_b is null
         |                  )
         |                  --   and platform_customer_service_accountability_time is not null
         |              ) --工单数据合并
         |            group by
         |              order_id,
         |              case_id, --工单id
         |              biao_type, --工单类型
         |              from_case_id,
         |              source_from, --来源
         |              node_status, --处理节点
         |              case_label
         |          ) --取最近一条时间节点
         |      ) --场景排序
         |  ) sheet on service_data.case_id = sheet.from_case_id
         |""".stripMargin

    // 执行Hive SQL查询，类型转换
    val hiveDf = spark.sql(hiveSql)
      .select(
        $"create_day".cast("date"),
        $"feedback_time".cast("timestamp"),
        $"feedback_mode".cast("string"),
        $"case_id".cast("long"),
        $"order_id".cast("long"),
        $"is_peifu".cast("int"),
        $"lag_1".cast("timestamp"),
        $"lag_1_case".cast("long"),
        $"lag_3".cast("timestamp"),
        $"lag_3_case".cast("long"),
        $"lag_5".cast("timestamp"),
        $"sheet_order_id".cast("long"),
        $"sheet_case_id".cast("long"),
        $"biao_type".cast("string"),
        $"from_case_id".cast("long"),
        $"source_from".cast("string"),
        $"node_status".cast("string"),
        $"case_label".cast("string"),
        $"label_rnk".cast("int"),
        $"is_cs_in".cast("string"),
        $"create_time".cast("timestamp"),
        $"first_turn_time".cast("timestamp"),
        $"first_reply_time".cast("timestamp"),
        $"quick_compensation_process_time".cast("timestamp"),
        $"quick_compensation_pay_time".cast("timestamp"),
        $"experience_compensation_process_time".cast("timestamp"),
        $"experience_compensation_pay_time".cast("timestamp"),
        $"outbound_call_contact_time".cast("timestamp"),
        $"customer_service_first_processing_time".cast("timestamp"),
        $"merchant_re_processing_time".cast("timestamp"),
        $"delegation_guaranteed_processing_time".cast("timestamp"),
        $"guaranteed_processing_time".cast("timestamp"),
        $"merchant_third_processing_time".cast("timestamp"),
        $"guarantee_circulation_time".cast("timestamp"),
        $"customer_service_third_circulation_time".cast("timestamp"),
        $"customer_service_end_investigation_time".cast("timestamp"),
        $"guarantee_end_investigation_time".cast("timestamp"),
        $"platform_customer_service_accountability_time".cast("timestamp"),
        $"kefu_close_time".cast("timestamp"),
        $"cs_judge_close_time".cast("timestamp"),
      )
      .na.fill("",Seq("feedback_mode","biao_type","source_from","node_status","case_label","is_cs_in"))
      .na.fill(0,Seq("case_id","order_id","is_peifu","lag_1_case","lag_3_case","sheet_order_id","sheet_case_id","from_case_id","label_rnk"))
      .dropDuplicates()
      .repartition(100)
    println(s"获取重复进线数据${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入数据到MySQL数据库,覆盖写
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_repeat_entry")

    spark.close()
  }
}
