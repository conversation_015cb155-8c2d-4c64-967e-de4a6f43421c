package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.util.Properties


/**
 * @author: zhang<PERSON><PERSON>e
 * @date: 2025/5/27
 * @description: 电商商家服务数据到MySQL的同步逻辑
 */
object DianShangEcAlgToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._

    // 读取MySQL配置信息
    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")
    
    // 读取会话表ec_alg.ec_ods_load_tb_emi_session_df
    val sessionSql =
      s"""
         |select
         |  id,
         |  user_id,
         |  ucid,
         |  shop_id,
         |  shop_name,
         |  shop_logo,
         |  customer_id,
         |  uuap,
         |  start_time,
         |  end_time,
         |  end_from,
         |  status,
         |  evaluate_satisfied,
         |  evaluate_star,
         |  is_delete,
         |  create_time,
         |  update_time,
         |  evaluate_content,
         |  classify_ids
         |from ec_alg.ec_ods_load_tb_emi_session_df
         |where event_day = '$YesterDay'
         |""".stripMargin

    val sessionDf = spark.sql(sessionSql)
      .select(
        $"id".cast("long"),
        $"user_id".cast("long"),
        $"ucid".cast("long"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"shop_logo".cast("string"),
        $"customer_id".cast("long"),
        $"uuap".cast("string"),
        $"start_time".cast("string"),
        $"end_time".cast("string"),
        $"end_from".cast("int"),
        $"status".cast("int"),
        $"evaluate_satisfied".cast("int"),
        $"evaluate_star".cast("int"),
        $"is_delete".cast("int"),
        $"create_time".cast("string"),
        $"update_time".cast("string"),
        $"evaluate_content".cast("string"),
        $"classify_ids".cast("string")
      )
      .na.fill(0L, Seq("id", "user_id", "ucid", "shop_id", "customer_id"))  // 填充BIGINT类型的空值
      .na.fill("", Seq("shop_name", "shop_logo", "uuap", "start_time", "end_time", "create_time", "update_time", "evaluate_content", "classify_ids"))  // 填充VARCHAR类型的空值
      .na.fill(0, Seq("end_from", "status", "evaluate_satisfied", "evaluate_star", "is_delete"))  // 填充INT类型的空值
      .dropDuplicates()
      .repartition(10)
      .cache()
    println(s"获取会话表ec_alg.ec_ods_load_tb_emi_session_df的${YesterDay}数据共：${sessionDf.count()}条数据")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,sessionDf,"ec_ods_load_tb_emi_session_df")

    // 读取会话表ec_alg.ec_ods_load_tb_emi_message_df
    val messageSql =
      s"""
         |select
         |  id,
         |  session_id,
         |  user_id,
         |  ucid,
         |  shop_id,
         |  shop_name,
         |  customer_id,
         |  uuap,
         |  type,
         |  sub_type,
         |  msg_from,
         |  msg_to,
         |  message,
         |  time_must_show,
         |  status,
         |  read_time,
         |  like_status,
         |  support_like,
         |  is_delete,
         |  create_time,
         |  update_time
         |from ec_alg.ec_ods_load_tb_emi_message_df
         |where event_day = '$YesterDay'
         |""".stripMargin

    val messageDf = spark.sql(messageSql)
      .select(
        $"id".cast("long"),
        $"session_id".cast("long"),
        $"user_id".cast("long"),
        $"ucid".cast("long"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"customer_id".cast("long"),
        $"uuap".cast("string"),
        $"type".cast("int"),
        $"sub_type".cast("int"),
        $"msg_from".cast("int"),
        $"msg_to".cast("int"),
        $"message".cast("string"),
        $"time_must_show".cast("int"),
        $"status".cast("int"),
        $"read_time".cast("string"),
        $"like_status".cast("int"),
        $"support_like".cast("int"),
        $"is_delete".cast("int"),
        $"create_time".cast("string"),
        $"update_time".cast("string")
      )
      .na.fill(0L, Seq("id", "session_id", "user_id", "ucid", "shop_id", "customer_id"))  // 填充BIGINT类型的空值
      .na.fill("", Seq("shop_name", "uuap", "read_time", "create_time", "update_time", "message"))  // 填充VARCHAR类型的空值
      .na.fill(0, Seq("type", "sub_type", "msg_from", "msg_to", "time_must_show", "status", "like_status", "support_like", "is_delete"))  // 填充INT类型的空值
      .dropDuplicates()
      .repartition(10)
      .cache()
    println(s"获取消息表ec_alg.ec_ods_load_tb_emi_message_df的${YesterDay}数据共：${messageDf.count()}条数据")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,messageDf,"ec_ods_load_tb_emi_message_df")

    // 读取问题标签映射表ec_alg.ec_ods_load_tb_emi_classify_dict_df
    val dictSql =
      s"""
         |select
         |  classify_name,
         |  create_time,
         |  id,
         |  is_delete,
         |  level,
         |  parent_id,
         |  update_time
         |from ec_alg.ec_ods_load_tb_emi_classify_dict_df
         |where event_day = '$YesterDay'
         |""".stripMargin

    val dictDf = spark.sql(dictSql)
      .select(
        $"classify_name".cast("string"),
        $"create_time".cast("string"),
        $"id".cast("long"),
        $"is_delete".cast("int"),
        $"level".cast("int"),
        $"parent_id".cast("long"),
        $"update_time".cast("string")
      )
      .na.fill(0L, Seq("id", "parent_id"))  // 填充BIGINT类型的空值
      .na.fill("", Seq("classify_name", "create_time", "update_time"))  // 填充VARCHAR类型的空值
      .na.fill(0, Seq("is_delete", "level"))  // 填充INT类型的空值
      .dropDuplicates()
      .repartition(10)
      .cache()
    println(s"获取问题标签映射表ec_alg.ec_ods_load_tb_emi_classify_dict_df的${YesterDay}数据共：${dictDf.count()}条数据")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,dictDf,"ec_ods_load_tb_emi_classify_dict_df")

    spark.close()
  }
}
