package com.baidu.sql.customized.dianshang

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.{JSON, JSONObject}
import com.baidu.sql.utils.TimeFormat.SEC_FORMAT_MYSQL
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.UdfUtils._
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils, TimeOperateUtil}
import org.apache.spark.rdd.RDD
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import com.baidu.sql.customized.keyproduct.im.ImKeypointProductFromUrlEsSpark.productMap

import java.text.SimpleDateFormat
import java.util.Calendar
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.util.control.Breaks.{break, breakable}


/**
 * 电商微信对话记录数据（follow的度小店-留言渠道）
 */
object DianShangWeChatConversation extends Serializable {

  val indexName = "metis_access_rt_release"

  // 机器人评价满意度
  val robot_eva_stat = Map(
    "1" -> "(极不满意)",
    "2" -> "(不满意)",
    "3" -> "(一般)",
    "4" -> "(满意)",
    "5" -> "(非常满意)"
  )

  // 机器人解决状态
  val robot_eva_sloved = Map(
    "1" -> "已解决",
    "2" -> "未解决",
    "3" -> "持续关注",
  )

  //用户id和用户名对应关系
  val id_uname = mutable.Map[String, String]()
  //bns转换后的http地址
  var url = ""

  //昨天日期 yyyyMMdd
  var YesterDay :String = ""
  //昨天日期 yyyy-MM-dd
  var yesterDay :String = ""
  //前天日期 yyyyMMdd
  var beforeYesterDay :String = ""
  var startTimeStamp:Long = 0L
  var endTimeStamp:Long = 0L

  //区域回溯数据的起始时间 yyyyMMdd
  var PartitionDay:String = ""
  //区域回溯数据的起始时间 yyyy-MM-dd
  var partitionDay:String = ""

  //需要获取的follow电商产品线
  val productNameMap = Map(44L -> "度小店-留言渠道")

  def main(args: Array[String]): Unit = {
    /*
    * 从在线follow的ES库取所需字段
    * 根据createTime字段查询一天的数据,然后根据提供的字段做结构化处理
    * 将会话内容写入help_ods_feedback_wechat_conversation数仓表中
    * */

    // 分区时间 yyyyMMdd
    YesterDay = args(0)
    // 昨天日期 yyyy-MM-dd
    yesterDay = calcnDateFormat(YesterDay)
    //前天日期 yyyyMMdd
    beforeYesterDay = calcnDate(YesterDay,-1)

    // 操作类型,delete,partition_delete
    val is_delete = args(1)
    // 区域回溯数据的起始时间 yyyyMMdd
    PartitionDay = args(2)
    // 区域回溯数据的起始时间 yyyy-MM-dd
    partitionDay = calcnDateFormat(PartitionDay)

    //风铃短信数据的获取起始时间
    var startTime = ""

    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    if (is_delete.equals("delete")){
      // 获取yesterday豪秒级时间戳
      startTimeStamp = TimeOperateUtil.getTimeStamp(YesterDay)
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))
      startTime = YesterDay
    }else if (is_delete.equals("partition_delete")) {
      // 获取partitionDay区域豪秒级时间戳
      startTimeStamp = TimeOperateUtil.getTimeStamp(PartitionDay)
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,2))
      startTime = PartitionDay
    }else{
      //执行删除分区的操作后直接关闭程序
      spark.sql(s"ALTER TABLE udw_ns.default.help_ods_feedback_wechat_conversation DROP IF EXISTS PARTITION (event_day = " + YesterDay + " )")
      spark.stop()
      sys.exit(1)
    }

    //读取客服组信息
    /*val commonSql =
      """
        |select
        |   id,  -- 客服id
        |   baidu_uname  -- 客服账号
        |from rbac_user
        |""".stripMargin

    val idUnameDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFollowProperties,commonSql)
    //将客服组信息存入id_uname
    idUnameDf.rdd.foreach(row => {
      val id = row.getAs[Long]("id").toString.trim
      val baidu_uname = row.getAs[String]("baidu_uname").trim
      id_uname.put(id,baidu_uname)
    })*/

    println("startTime:" + startTimeStamp + " endTime:" + endTimeStamp)

    //获取新增数据
    val createDF = dealData(spark,"insert")
    //获取修改数据
    val updateDF = dealData(spark,"update")

    val combinedDF = createDF.unionByName(updateDF)
    combinedDF.createOrReplaceTempView("temp_data")
    //combinedDF.show(5,false)

    //获取风铃case数据,短信数据
    val querySql =
      s"""
        |select
        |   case_id,
        |   actual_send_time,
        |   sms_info
        |from tb_from_wechat_msg_task
        |where DATE_FORMAT(update_time,'%Y%m%d') >= '${startTime}'
        |and case_id is not null
        |and sms_info is not null
        |""".stripMargin
    val caseDf = CommonUtils.mysqlOperate(spark,PropertiesUtils.MysqlFenglingCaseProperties,querySql)
    caseDf.createOrReplaceTempView("case_data")

    //关联风铃和follow数据
    val fenglingDf = spark.sql(
      """
        |select
        | t.session_id as session_id,
        | -- t.case_id,
        | c.actual_send_time as send_time,
        | '短信内容' as send_user,
        | c.sms_info as send_content
        |from (select session_id,case_id from temp_data where case_id != '') t
        |left join case_data c
        |on t.case_id = c.case_id
        |""".stripMargin
    )
      .filter($"send_content".isNotNull)
      .dropDuplicates()
    println("关联风铃后的数据条数：" + fenglingDf.count())
    //fenglingDf.show(5,false)

    //多轮对话数据拆分
    val explodeDf = spark.sql(
      """
        |select
        |   product_id,
        |   -- product_line,
        |   userid,
        |   session_id,
        |   im_session_id,
        |   accept_group,
        |   -- submit_time,
        |   explode(split(session_details,'<=>')) as session_details
        |from temp_data
        |where session_details != ''
        |""".stripMargin)

    val splitDf = explodeDf
      //记录类型
      .withColumn("msg_type",getNullVal(split(col("session_details"),"=>").getItem(0).cast("string")))
      //发送时间
      .withColumn("send_time",getNullVal(split(col("session_details"),"=>").getItem(1).cast("string")))
      //客服账号/操作人/满意度CSI
      .withColumn("service_name_csi",getNullVal(split(col("session_details"),"=>").getItem(2).cast("string")))
      //客服组/解决状态（后面删除）
      //.withColumn("group_status",getNullVal(split(col("session_details"),"=>").getItem(3)))
      //如果group_status为空，则取客服组（总的客服组/解决状态）
      //.withColumn("service_group_status",when($"group_status" === "",getGroupStatus($"service_name_csi",$"accept_group")).otherwise($"group_status"))
      //发送内容
      .withColumn("send_content",getNullVal(split(col("session_details"),"=>").getItem(4).cast("string")))
      //转接产品线id（后面删除）
      .withColumn("skill_productId",getNullVal(split(col("session_details"),"=>").getItem(5).cast("string")))
      //发送人
      .withColumn("send_user",when(col("msg_type") === "用户回复",col("userid")).when(col("msg_type") === "人工客服",col("service_name_csi")).otherwise(lit("系统客服")))
      .filter($"msg_type" =!= "")
      .filter($"product_id" === $"skill_productId")
      .drop("session_details","product_id","accept_group","skill_productId","msg_type","service_name_csi","userid","im_session_id")
      .dropDuplicates()

    println("对话记录拆分后的数据条数：" + splitDf.count())
    splitDf.show(5,false)

    //定时例行任务，写入T-1的数据逻辑
    if (is_delete.equals("delete")){
      //对话记录
      val writeDf = fenglingDf.unionByName(splitDf)
        .na.fill("")
        .dropDuplicates()
        .repartition(60)

      println("T-1的数量为：" + writeDf.count())
      writeDf.show(5,false)

      //读取hive的T-2的数据
      val yesterdayDf = spark.sql(
          s"""
             |select
             |   session_id,
             |   send_time,
             |   send_user,
             |   send_content
             |from udw_ns.default.help_ods_feedback_wechat_conversation
             |where event_day = $beforeYesterDay
             |""".stripMargin)
        .repartition(60)
      println("T-2的数据条数：" + yesterdayDf.count())

      //合并T-1和T-2的数据
      if (yesterdayDf.count() > 0){
        //先对T-1数据做left_anti操作，去掉T-2中已经存在的数据
        val combinedDf = writeDf.as("a").join(yesterdayDf.as("b"),
            $"a.session_id" === $"b.session_id" &&
            $"a.send_time" === $"b.send_time" &&
            $"a.send_user" === $"b.send_user" &&
            $"a.send_content" === $"b.send_content"
            , "left_anti")
          .unionByName(yesterdayDf)
          .na.fill("")
          .dropDuplicates()
          .repartition(60)
        println("最终合并的数量为：" + combinedDf.count())
        combinedDf.createOrReplaceTempView("combined_data")
      }else{
        writeDf.createOrReplaceTempView("combined_data")
      }
      //写入T-1的数据
      val sql =
        s"""
           |insert overwrite table udw_ns.default.help_ods_feedback_wechat_conversation partition (event_day = $YesterDay )
           |select
           |  session_id, -- 会话id
           |	send_time, -- 发送时间
           |	send_user,	-- 发送人
           |	send_content, -- 发送内容
           |	'' as message_send_time, -- 短信发送时间
           |  '' as message_content -- 短信内容
           |from combined_data
           |""".stripMargin

      spark.sql(sql)

    }else if (is_delete.equals("partition_delete")){
      //partition_delete,一次性读取多天数据写入T-1的数据逻辑，不用读取T-2的hive数据
      val writeDf = fenglingDf.unionByName(splitDf)
        .na.fill("")
        .dropDuplicates()
        .repartition(60)

      println("最终合并的数量为：" + writeDf.count())
      writeDf.createOrReplaceTempView("combined_data")

      val sql =
        s"""
           |insert overwrite table udw_ns.default.help_ods_feedback_wechat_conversation partition (event_day = $YesterDay )
           |select
           |  session_id, -- 会话id
           |	send_time, -- 发送时间
           |	send_user,	-- 发送人
           |	send_content, -- 发送内容
           |	'' as message_send_time, -- 短信发送时间
           |  '' as message_content -- 短信内容
           |from combined_data
           |""".stripMargin

      spark.sql(sql)
    }else{
      println("请输入正确的参数，例如：delete或者partition_delete")
    }

    spark.close()
  }

  /**
   * 通过传入的json串获取产品线名称和技能组名称
   */
  /*val getGroupStatus = udf((userName: String,jsonData:String) => {
    if (userName == null || userName == "") {
      ""
    } else {
      val json = JSON.parseObject(jsonData)
      //用户名
      var res = ""
      // 使用keySet获取所有的键，并遍历它们
      for (d <- 0 until json.size()) {
        val jSONObject = json.getJSONObject(d.toString)
        val ouserName = jSONObject.getString("ouserName")
        if (ouserName.equals(userName)) {
          res = jSONObject.getString("skillGroup")
        }
      }
      res
    }
  })*/

  /**
   * 获取风铃caseId
   */
  val getFengLingCase = udf((url:String) => {
    //"https:/fenglingb.baidu.com/#/process/detail?caseId=642932
    if (url == null || url == "") {
      ""
    }else{
      url.split("=")(1)
    }
  })

  /**
   * 获取会话内容
   */
  val getSessionDetail = udf((data: mutable.WrappedArray[String],jsonData:String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      val res = ListBuffer[String]()
      var json :JSONObject = null
      if(jsonData != null && !jsonData.isEmpty && !jsonData.equals("")){
        json = JSON.parseObject(jsonData)
      }
      var index = 0
      var productLine = ""
      data.foreach(elem => {
        val normalTalk = JSON.parseObject(elem)
        try {
          val talkType = normalTalk.getString("talkType")
          //初始产品线id，转接产品线后id会改变
          if (json != null){
            productLine = json.getJSONObject(index.toString).getOrDefault("productId","").toString
          }
          //记录类型
          var users = ""
          //记录时间/评价时间,格式：yyyy-MM-dd HH:mm:ss
          val time = getDateTime(normalTalk.getString("time"),SEC_FORMAT_MYSQL)
          //客服账号/满意度
          var userName = ""
          //客服组，解决状态
          var status = ""
          //回复内容
          var content = ""
          //获取用户对话记录数据NORMAL_TALK类型
          if (talkType != null && talkType != "" && ("NORMAL_TALK".equals(talkType) || "EVENT_TALK".equals(talkType))) {
            val userInfo = normalTalk.getJSONObject("userInfo")
            if (userInfo != null && !userInfo.isEmpty) {
              if ("__METIS_IM__".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") == false) {
                users = "用户回复"
              } /*else if ("metis-bot".equals(userInfo.getString("sysName")) && userInfo.getBoolean("isBot") == true) {
                users = "metis-bot"
              } */else if ("系统客服".equals(userInfo.getString("showName")) && userInfo.getBoolean("isBot") == true) {
                users = "系统客服"
              } else if ("follow".equals(userInfo.getString("sysName"))) {
                users = "人工客服"
                userName = userInfo.getString("userName")
              }
            }

            //具体的对话内容直接硬解富卡，有的text是空,有的bailingRichCard是空，是个坑
            val bailingRichCard = normalTalk.getString("bailingRichCard")
            val talkContext = normalTalk.getString("talkContext")

            breakable {
              if (bailingRichCard == null || !JSON.parseObject(bailingRichCard).containsKey("content")) {
                content = if (normalTalk.getString("text") == null) "" else normalTalk.getString("text")
                break
              } else if(normalTalk.getString("text") == "[订单]" && talkContext != null && talkContext != "" && JSONUtil.isJson(talkContext)){
                content = "订单ID:" + JSON.parseObject(talkContext).getString("orderId")
              } else {
                val tent = JSON.parseObject(bailingRichCard).getString("content")
                if (tent != null || !(tent.isEmpty)) {
                  val contentJson = JSON.parseObject(tent)
                  val types = contentJson.getString("type")
                  if ("html".equals(types)) {
                    val data = contentJson.getString("data")
                    content = if (data == null) "" else data.replaceAll("<.*?>", "")
                  } else if ("card".equals(types)) {
                    val dataJson = contentJson.getJSONObject("data")
                    if (dataJson == null || dataJson.isEmpty) {
                      content = ""
                      break
                    }
                    val array = dataJson.getJSONArray("content")
                    if (array == null || array.isEmpty) {
                      content = ""
                      break
                    } else {
                      for (item <- 0 to array.size() - 1) {
                        val cardContent = array.getJSONObject(item)
                        val cardType = cardContent.getString("type")
                        if ("html".equals(cardType) || "text".equals(cardType)) {
                          val valuestr = cardContent.getString("value")
                          content =
                            if (valuestr == null) {
                              ""
                            /*else if (valuestr.contains("投诉内容")){
                              var filter = valuestr.replaceAll("、|.|;|。", ",").replaceAll("=|：", ":").replaceAll(" ", "")
                              filter = if (filter.endsWith(",")) filter.substring(0, filter.length() - 1) else filter
                              "{" + filter + "}"*/
                            }else{
                              valuestr.replaceAll("<.*?>", "")
                            }
                        }else if ("image".equals(cardType)) {
                          val image = cardContent.getString("value")
                          if (image != null && image != "") {
                            content = image
                          }else{
                            content = ""
                          }
                        }
                        else {
                          //其他类型富卡直接转json
                          content = cardContent.toString()
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          /*else if (talkType != null && talkType != "" && "SESSION_EVALUATE".equals(talkType)){
            val evaluateInfo = normalTalk.getJSONObject("evaluateInfo")
            if (evaluateInfo != null && !evaluateInfo.isEmpty) {
              if (evaluateInfo.containsKey("evaluated")) {
                //是否被评价
                val evaluated = evaluateInfo.getString("evaluated")
                if ("true".equals(evaluated)) {
                  users = "机器人评价"
                }
              }
              if (evaluateInfo.containsKey("evaluateResult")) {
                //评价结果
                val evaluateResult = evaluateInfo.getJSONObject("evaluateResult")
                if (evaluateResult != null && !evaluateResult.isEmpty) {
                  if (evaluateResult.containsKey("robot_eva_sloved")) {
                    //是否解决
                    val score = evaluateResult.getString("robot_eva_sloved")
                    status = robot_eva_sloved.getOrElse(score, "")
                  }
                  if (evaluateResult.containsKey("robot_eva_stat")) {
                    //评价得分
                    val stat = evaluateResult.getString("robot_eva_stat")
                    userName = stat + robot_eva_stat.getOrElse(stat, "")
                  }
                  if (evaluateResult.containsKey("robot_eva_diss_content")) {
                    //服务评价
                    content = evaluateResult.getString("robot_eva_diss_content")
                  }
                }
              }
            }
          }*/
          //满足条件的数据才接入
          if (users != ""){
            if (content.trim.equals("百度客服为您服务")){
              index += 1
              if (json != null){
                productLine = json.getJSONObject(index.toString).getOrDefault("productId","").toString
              }
            }
            if (users == "机器人评价"){
              res.append(Seq(users,time,userName,status,content,productLine).mkString("=>"))
            } else if(users != "机器人评价" && content != ""){
              res.append(Seq(users,time,userName,status,content,productLine).mkString("=>"))
            }else{
              res
            }
          }
        } catch {
          //case e:Exception => println(s"buildExportSessionTalkContent fail imSessionId:" + normalTalk.getString("imSessionId") + "talkSeqId:"  + normalTalk.getString("seqId"))
          case e:Exception => println(s"session_id:" + normalTalk.getString("imSessionId") + ",error:"  + e.getMessage)
        }
      })
      res.mkString("<=>")
    }
  })

  /**
   * 获取业务线
   */
  val getProductLine = udf((data: String) => {
    if (data == null || data.isEmpty) {
      ""
    } else {
      productMap.getOrElse(data, "")
    }
  })

  /**
   *  读取ES数据库数据
   * @param spark
   * @param productId 产品线ID
   * @param isDefault 是新增数据还是修改数据
   * @return
   */
  private def getEsData(spark: SparkSession,
                        productId: String,
                        isDefault:String): RDD[(String, String)] = {

    //新建数据查询语句
    val createQuery =
      s"""
         |{
         |  "query": {
         |    "bool": {
         |      "must": [
         |        {"bool":{
         |              "should": [
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
         |                    {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
         |                    {"term": {"obuzFollow.productId": "${productId}"}}
         |              ]
         |            }
         |         },
         |        {
         |          "bool": {
         |            "must": [
         |              {
         |                "range": {
         |                  "startInfo.startTime": {
         |                    "gte": "${startTimeStamp}",
         |                    "lt": "${endTimeStamp}"
         |                  }
         |                }
         |              }
         |            ]
         |          }
         |        }
         |      ]
         |    }
         |  }
         |}
         |""".stripMargin

    //修改数据查询语句
    val updateQuery =
      s"""
        |{
        |  "query": {
        |    "bool": {
        |      "must": [
        |        {
        |          "bool": {
        |            "should": [
        |              {"term": {"startInfo.bailingInfoOnImSessionLevel.productId": "${productId}"}},
        |              {"term": {"startInfo.bailingInfoOnImSessionLevel.containerProductId": "${productId}"}},
        |              {"term": {"obuzFollow.productId": "${productId}"}}
        |            ]
        |          }
        |        },
        |        {
        |          "nested": {
        |            "path": "talks",
        |            "query": {
        |              "range": {
        |                "talks.time": {
        |                  "gte": "${startTimeStamp}",
        |                  "lte": "${endTimeStamp}"
        |                }
        |              }
        |            }
        |          }
        |        }
        |      ]
        |    }
        |  }
        |}
        |""".stripMargin

    val query = isDefault match {
      case "insert" => createQuery
      case "update" => updateQuery
    }

    CommonUtils.ElasticSearchOperate(spark,indexName,PropertiesUtils.ESImProperties,query)
  }


  /**
   *
   * @param spark
   * @return
   */
  def dealData(spark:SparkSession,isDefault:String): DataFrame = {
    val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()

    if (isDefault == "insert") {
      endTimeStamp = TimeOperateUtil.getTimeStamp(calcnDate(YesterDay,1))
    }

    for (product <- productNameMap) {
      val productId = product._1.toString
      //产品线名称
      val productName = product._2
      //通过EsSpark读取Es数据
      val rdd = getEsData(spark, productId,isDefault)
      //定义读取Es后的数据schema
      val schema = new StructType()
        .add("data", StringType)

      // 使用SparkSession创建DataFrame，row._2是所有Json字符串的数据内容
      val df = spark.createDataFrame(rdd.map(row => Row(row._2)), schema)

      // 解析字段sql  这里把接口无法取出的字段做处理
      val tempDf = df
        //产品线id
        .withColumn("product_id",lit(productId))
        .withColumn("product_line",lit(productName))
        //产品线名称
        /*.withColumn("product_line",
          when(getProductLine(col("app_id")) === "", lit(productName)).otherwise(getProductLine(col("app_id")))
            .cast("String"))*/
        //在线的session_id
        .withColumn("im_session_id",
          getNullVal(get_json_object(col("data"), "$.imSessionId")
            .cast("String")))
        //会话标识
        .withColumn("session_id",
          getNullVal(get_json_object(col("data"), "$.obuzFollow.id")
            .cast("String")))
        //创建时间
        .withColumn("submit_time",
          getNullVal(getDateTimeVal(get_json_object(col("data"), "$.startInfo.startTime")
            .cast("String"))))
        .withColumn("userid", getNullVal(get_json_object(col("data"), "$.startInfo.startUserInfo.userId").cast("String")))
        //客服组
        .withColumn("accept_group",
          getSkillGroupName(from_json(get_json_object(col("data"), "$.obuzFollow.sessionzAllocationESDTOList"),ArrayType(StringType)))
            .cast("string"))
        //风铃短信case_id
        .withColumn("case_id",
          getNullVal(getFengLingCase(get_json_object(col("data"), "$.obuzFollow.sysInfoMap.fenglingCaseUrl"))
            .cast("string")))
        //会话内容
        .withColumn("session_details",getNullVal(getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),col("accept_group"))))
        //会话内容(带文心分类标签)
        //.withColumn("session_details_label",getSessionDetail(from_json(get_json_object(col("data"), "$.talks"), ArrayType(StringType)),lit("文心分类标签")))
        .drop("data")
        .dropDuplicates(Seq("session_id", "product_id","product_line"))

      resultDf.append(tempDf)
    }

    //将用户增长的手百主入口（任务中台）过滤掉
    val esDF: DataFrame = resultDf.reduce((df1, df2) => df1.union(df2))
      .dropDuplicates(Seq("session_id", "product_id","product_line"))
      .toDF()
      .repartition(60)

    println("esDF数据量：" + esDF.count())

    esDF
  }

  /**
   * 技能组筛选过滤
   */
  val getSkillGroupName = udf((data: mutable.WrappedArray[String]) => {
    if (data == null || data.isEmpty || data == "") {
      ""
    } else {
      val resMap = new JSONObject()
      //给转接产品线的加个顺序索引
      var index = 0
      data.foreach(row => {
        var skillGroupName = ""
        //var ouserName = ""
        var productId = ""
        val json = JSON.parseObject(row)
        if(json.containsKey("skillGroupName")) {
          //客服组
          skillGroupName = json.getOrDefault("skillGroupName", "").toString
        }
        /*if(json.containsKey("ouserId")){
          val maybeString = id_uname.get(json.getOrDefault("ouserId", "").toString)
          //用户id获取用户名
          ouserName = if (maybeString.nonEmpty) maybeString.get else ""
        }*/

        if(json.containsKey("productId")){
          productId = json.getOrDefault("productId","").toString
        }
        //构建json串
        resMap.put(index.toString,JSON.parseObject(s"{'skillGroup':'${skillGroupName}','productId':'${productId}'}"))
        index += 1
      })
      resMap.toJSONString
    }
  })

  /**
   * 生成删除分区语句
   * @param startDateStr
   * @param endDateStr
   * @return
   */
  def generateDropPartitionStatements(startDateStr: String, endDateStr: String): Seq[String] = {
    val dateFormat = new SimpleDateFormat("yyyyMMdd")
    val cal = Calendar.getInstance()
    val startDate = dateFormat.parse(startDateStr)
    val endDate = dateFormat.parse(endDateStr)

    val statements = scala.collection.mutable.ListBuffer.empty[String]
    var currentDate = startDate

    while (currentDate.before(endDate) || currentDate.equals(endDate)) {
      val formattedDate = dateFormat.format(currentDate)
      //statements += s"ALTER TABLE udw_ns.default.help_ods_feedback_wechat_conversation DROP IF EXISTS PARTITION (event_day='$formattedDate')"
      statements += formattedDate
      cal.setTime(currentDate)
      cal.add(Calendar.DAY_OF_MONTH, 1)
      currentDate = cal.getTime()
    }
    statements.toSeq
  }
}
