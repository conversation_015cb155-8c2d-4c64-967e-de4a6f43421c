package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/12/23
 * @description: 电商服务数据(快通数据)到MySQL的同步逻辑
 */
object DianShangFuWuKuaiTongToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    
    import spark.implicits._

    // 读取Hive表
    val hiveSql =
      s"""
         |SELECT
         |  a.create_time,
         |  a.create_day,
         |  a.order_id,
         |  a.sub_order_id,
         |  a.refund_id,
         |  a.risk_user_flag,
         |  a.fast_after_sale_flag,
         |  a.fast_after_sale_type,
         |  a.quick_pass_source,
         |  a.quick_pass_after_sale_type,
         |  a.negative_commodity_flag,
         |   pay_time,
         |  shop_id,
         |  shop_name,
         |  product_id,
         |  product_name,
         |  status,
         |  total_amount,
         |  refund_create_time,
         |  refund_finish_time,
         |  refund_reason,
         |  refund_desc,
         |  categoryNameList,
         |  categoryNameLevel1,
         |  categoryNameLevel2,
         |  categoryNameLevel3,
         |  express_status,
         |  express_data
         |from
         |  (
         |    SELECT
         |      create_time,
         |      from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyy-MM-dd') as create_day,
         |      order_id,
         |      sub_order_id,
         |      refund_id,
         |      risk_user_flag,
         |      fast_after_sale_flag,
         |      fast_after_sale_type,
         |      quick_pass_source,
         |      quick_pass_after_sale_type,
         |      negative_commodity_flag
         |    from
         |      udw_ns.default.help_ods_tb_data_kuaitong_all
         |    where
         |      event_day = ${YesterDay}
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      and fast_after_sale_flag = '快通'
         |  ) a
         |    left join 
         |  (
         |    select
         |      order_id,
         |      sub_order_id,
         |      cuid,
         |      baiduid,
         |      passport_id,
         |      ucid,
         |      shop_id,
         |      shop_name,
         |      product_id,
         |      product_name,
         |      sku_id,
         |      pay_type,
         |      pay_channel,
         |      total_amount / 100 as total_amount,
         |      payment_amount,
         |      freight_amount,
         |      cheap_amount,
         |      refund_create_time,
         |      refund_finish_time,
         |      refund_reason,
         |      refund_desc,
         |      ip,
         |      name,
         |      mobile,
         |      province,
         |      city,
         |      area,
         |      address,
         |      status,
         |      express_name,
         |      tracking_number,
         |      express_status,
         |      express_data,
         |      create_time,
         |      pay_time,
         |      cancel_time,
         |      consign_time,
         |      confirm_time,
         |      evaluate_time,
         |      pay_passport_id,
         |      buy_num,
         |      product_score,
         |      shop_service_score,
         |      shop_logistics_score,
         |      original_content,
         |      product_json,
         |      get_json_object(product_json, '$$.serviceTel') as serviceTel,
         |      get_json_object(product_json, '$$.categoryNameList') as categoryNameList,
         |      get_json_object(product_json, '$$.categoryNameLevel1') as categoryNameLevel1,
         |      get_json_object(product_json, '$$.categoryNameLevel2') as categoryNameLevel2,
         |      get_json_object(product_json, '$$.categoryNameLevel3') as categoryNameLevel3
         |    from
         |      udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
         |    where
         |      event_day = ${YesterDay}
         |      and app_id in (5, 100012)
         |      and substr(pay_time, 1, 10) >='2024-01-01'
         |      and pay_type = 0
         |      and get_json_object(cashier_pay_info, '$$.status') in (2, 3)
         |  ) as order_table on order_table.sub_order_id = a.sub_order_id
         |""".stripMargin

    //类型转换，缺失值填充，去重
    val hiveDf = spark.sql(hiveSql)
      .select(
        $"create_time".cast("timestamp"),
        $"create_day".cast("date"),
        $"order_id".cast("long"),
        $"sub_order_id".cast("long"),
        $"refund_id".cast("long"),
        $"risk_user_flag".cast("string"),
        $"fast_after_sale_flag".cast("string"),
        $"fast_after_sale_type".cast("string"),
        $"quick_pass_source".cast("string"),
        $"quick_pass_after_sale_type".cast("string"),
        $"negative_commodity_flag".cast("string"),
        $"pay_time".cast("timestamp"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"product_id".cast("long"),
        $"product_name".cast("string"),
        $"status".cast("long"),
        $"total_amount".cast("double"),
        $"refund_create_time".cast("timestamp"),
        $"refund_finish_time".cast("timestamp"),
        $"refund_reason".cast("long"),
        $"refund_desc".cast("string"),
        $"categoryNameList".cast("string"),
        $"categoryNameLevel1".cast("string"),
        $"categoryNameLevel2".cast("string"),
        $"categoryNameLevel3".cast("string"),
        $"express_status".cast("int"),
        $"express_data".cast("string"),
      )
      .na.fill("",Seq("risk_user_flag","fast_after_sale_flag","fast_after_sale_type","quick_pass_source","quick_pass_after_sale_type","negative_commodity_flag","shop_name","product_name","refund_desc","categoryNameList","categoryNameLevel1","categoryNameLevel2","categoryNameLevel3"))
      .na.fill(0,Seq("order_id","sub_order_id","refund_id","shop_id","product_id","status","refund_reason","express_status"))
      .na.fill(0.0,Seq("total_amount"))
      .dropDuplicates()
      .repartition(50)
    println(s"获取快通数据${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    //数据写入，覆盖写
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_fast_channel")

    spark.close()
  }
}
