package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils._
import com.baidu.sql.utils.PropertiesUtils
import com.baidu.sql.utils.TimeOperateUtil.{calcnDate, calcnDateFormat}
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON>e
 * @date: 2025/6/18
 * @description: 爆量店铺细分商品数据到MySQL的同步逻辑
 */
object DianShangShopProductToMysql {
  //运行日期
  var YesterDay: String = ""
  //前一天日期
  var BeForeDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)
    BeForeDay = calcnDate(YesterDay, -1)
    val YesterDayFormat = calcnDateFormat(YesterDay)
    val BeForeDayFormat = calcnDateFormat(BeForeDay)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._

    //订单爆量店铺数据
    val shopDf = spark.sql(
      s"""
        |select
        |  q.shop_id, --店铺ID
        |  q.shop_name, --店铺名称
        |  q.open_shop_date, --开店日期
        |  q.create_date, --下单日期
        |  q.ordcnt, --订单数
        |  q.uidcnt, --用户数
        |  q.anti_ordcnt, --反作弊订单数
        |  q.anti_uidcnt, --反作弊用户数
        |  q.anti_gmv, --反作弊GMV
        |  q.anti_pbgmv, --套利金额
        |  q.event_day, --T-1
        |  (q.ordcnt - nvl(q.ordcnt_last, 0)) / nvl(q.ordcnt_last, 0)  as shop_ordcnt_huanbi --店铺订单环比
        |from
        |  (select distinct
        |      m.shop_id,
        |      n.shop_name,
        |      m.open_shop_date,
        |      n.create_date,
        |      n.ordcnt,
        |      n.gmv,
        |      n.uidcnt,
        |      n.anti_ordcnt,
        |      n.anti_uidcnt,
        |      n.anti_gmv,
        |      n.anti_pbgmv, --套利金额
        |      datediff(day, m.open_shop_date, n.create_date) as date_diff,
        |      '${YesterDay}' as event_day,
        |      row_number() over (
        |        partition by
        |          n.shop_id
        |        order by
        |          n.create_date asc
        |      ) as rank,
        |      lead(n.ordcnt, 1) over (
        |        partition by
        |          n.shop_id
        |        order by
        |          n.create_date desc
        |      ) as ordcnt_last
        |    from
        |      (select
        |          shop_id,
        |          substr(open_shop_time, 1, 10) as open_shop_date
        |        from
        |          udw_ns.default.fengchao_biz_ecom_dwd_shop_risk_control_info
        |        where
        |          event_day = '${BeForeDay}'
        |          and app_id in (5)
        |          and shop_status in (1, 3) --已开启、豁免
        |        group by
        |          shop_id,
        |          substr(open_shop_time, 1, 10)
        |      ) m
        |      left join (
        |        select
        |          a.shop_id,
        |          a.shop_name,
        |          substr(a.create_time, 1, 10) as create_date,
        |          count(distinct b.order_id) as ordcnt,
        |          sum(b.gmv) as gmv,
        |          count(distinct b.passport_id) as uidcnt,
        |          count(
        |            distinct if (
        |              b.antitag = 1
        |              and b.policy_type is null,
        |              b.order_id,
        |              null
        |            )
        |          ) as anti_ordcnt,
        |          count(
        |            distinct if (
        |              b.antitag = 1
        |              and b.policy_type is null,
        |              b.passport_id,
        |              null
        |            )
        |          ) as anti_uidcnt,
        |          sum(
        |            if (
        |              b.antitag = 1
        |              and b.policy_type is null,
        |              b.gmv,
        |              0
        |            )
        |          ) as anti_gmv,
        |          sum(
        |            if (
        |              b.antitag = 1
        |              and b.policy_type is null,
        |              b.platform_discount,
        |              0
        |            )
        |          ) as anti_pbgmv --套利金额
        |        from
        |          (select
        |              order_id,
        |              sub_order_id,
        |              cuid,
        |              shop_id,
        |              shop_name,
        |              status,
        |              create_time
        |            from
        |              udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
        |            where
        |              event_day = '${YesterDay}'
        |              and substr(pay_time, 1, 10) >= '${BeForeDayFormat}'
        |              and app_id in (5)
        |              and pay_type = 0 --在线支付
        |              and get_json_object(cashier_pay_info, '$$.status') in (2, 3)
        |            group by
        |              order_id,
        |              sub_order_id,
        |              cuid,
        |              shop_id,
        |              shop_name,
        |              status,
        |              create_time
        |          ) a
        |          left join (
        |            select
        |              order_id,
        |              sub_order_id,
        |              passport_id,
        |              cast(gmv as BIGINT) as gmv,
        |              cast(platform_discount as BIGINT) as platform_discount,
        |              antitag,
        |              policy_type, --反作弊策略类型
        |              policyid --反作弊策略ID
        |            from
        |              yinhe.ecommerce_dws_zbzy_order_detail_df
        |            where
        |              event_day = '${YesterDay}'
        |              and substr(pay_time, 1, 10) >= '${BeForeDayFormat}'
        |              and substr(pay_time, 1, 10) <= '${YesterDayFormat}'
        |              and pay_type = 0 --在线支付
        |              and pay_status in (2, 3) --支付或退款订单
        |              and app_id in (5) --度小店
        |            group by
        |              order_id,
        |              sub_order_id,
        |              passport_id,
        |              cast(gmv as BIGINT),
        |              cast(platform_discount as BIGINT),
        |              antitag,
        |              policy_type, --反作弊策略类型
        |              policyid
        |          ) b on a.sub_order_id = b.sub_order_id
        |        group by
        |          a.shop_id,
        |          a.shop_name,
        |          substr(a.create_time, 1, 10)
        |      ) n on m.shop_id = n.shop_id
        |  ) q
        |group by
        |  q.shop_id,
        |  q.shop_name,
        |  q.open_shop_date, --开店日期
        |  q.create_date, --交易日期
        |  q.ordcnt,
        |  q.gmv,
        |  q.uidcnt,
        |  q.anti_ordcnt,
        |  q.anti_uidcnt,
        |  q.anti_gmv,
        |  q.anti_pbgmv,
        |  q.event_day,
        |  (q.ordcnt - nvl(q.ordcnt_last, 0)) / nvl(q.ordcnt_last, 0)
        |having -- 环比订单量>=80% 且 订单量>=100
        |  (q.ordcnt - nvl(q.ordcnt_last, 0)) / nvl(q.ordcnt_last, 0) >= 0.8
        |  and ordcnt >= 100
        |""".stripMargin)
      .select(
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"open_shop_date".cast("date"),
        $"create_date".cast("date"),
        $"ordcnt".cast("long"),
        $"uidcnt".cast("long"),
        $"anti_ordcnt".cast("long"),
        $"anti_uidcnt".cast("long"),
        $"anti_gmv".cast("long"),
        $"anti_pbgmv".cast("long"),
        $"shop_ordcnt_huanbi".cast("decimal(12,2)"),
        $"event_day".cast("string")
      )
      .na.fill("", Seq("shop_name")) // 填充字符串列的空值
      .na.fill(0, Seq("shop_id","ordcnt","uidcnt","anti_ordcnt","anti_uidcnt","anti_gmv","anti_pbgmv"))  // 填充数值列的空值
      .na.fill(0.0, Seq("shop_ordcnt_huanbi"))  // 填充数值列的空值
      .dropDuplicates()
      .repartition(10)
      .cache()
    println(s"获取订单爆量店铺数据的${YesterDay}数据共：${shopDf.count()}条数据")
    shopDf.createOrReplaceTempView("tmp_data")


    // 读取Hive表
    val hiveSql =
      s"""
         |--爆量店铺细分商品数据
         |select
         |  o1.shop_id, -- 店铺ID
         |  o1.shop_name, -- 店铺名称
         |  o1.shop_ordcnt_huanbi as ratio, -- 这里用 shop_ordcnt_huanbi 替换 ratio
         |  o2.order_first_cate_name, -- 一级类目
         |  o2.product_id, -- 商品ID
         |  o2.product_name, -- 商品名称
         |  o2.spu_ordcnt, -- SPU订单数
         |  o2.spu_gmv, -- SPU GMV
         |  o2.spu_uidcnt, -- SPU用户数
         |  o2.spu_anti_ordcnt, -- SPU反作弊订单数
         |  o2.spu_anti_uidcnt, -- SPU反作弊用户数
         |  o2.spu_anti_gmv, -- SPU反作弊GMV
         |  o2.spu_anti_pbgmv, -- SPU套利金额
         |  (o2.spu_ordcnt - nvl(o2.spu_ordcnt_last, 0)) / nvl(o2.spu_ordcnt_last, 0) as spu_ordcnt_huanbi, -- SPU订单环比
         |  o1.event_day -- T-1
         |from
         |  tmp_data o1
         |  left join (
         |    select
         |      h.shop_id,
         |      h.shop_name,
         |      h.create_date,
         |      h.order_first_cate_name,
         |      h.product_id,
         |      h.product_name,
         |      h.spu_ordcnt,
         |      h.spu_gmv,
         |      h.spu_uidcnt,
         |      h.spu_anti_ordcnt,
         |      h.spu_anti_uidcnt,
         |      h.spu_anti_gmv,
         |      h.spu_anti_pbgmv, --套利金额
         |      lead(h.spu_ordcnt, 1) over (
         |        partition by
         |          h.shop_id,
         |          h.product_id
         |        order by
         |          h.create_date desc
         |      ) as spu_ordcnt_last
         |    from
         |      (
         |        select
         |          a.shop_id,
         |          a.shop_name,
         |          substr(a.create_time, 1, 10) as create_date,
         |          a.order_first_cate_name,
         |          a.product_id,
         |          a.product_name,
         |          count(distinct a.order_id) as spu_ordcnt,
         |          sum(a.gmv) as spu_gmv,
         |          count(distinct a.passport_id) as spu_uidcnt,
         |          count(
         |            distinct if (
         |              a.antitag = 1
         |              and a.policy_type is null,
         |              a.order_id,
         |              null
         |            )
         |          ) as spu_anti_ordcnt,
         |          count(
         |            distinct if (
         |              a.antitag = 1
         |              and a.policy_type is null,
         |              a.passport_id,
         |              null
         |            )
         |          ) as spu_anti_uidcnt,
         |          sum(
         |            if (
         |              a.antitag = 1
         |              and a.policy_type is null,
         |              a.gmv,
         |              0
         |            )
         |          ) as spu_anti_gmv,
         |          sum(
         |            if (
         |              a.antitag = 1
         |              and a.policy_type is null,
         |              a.platform_discount,
         |              0
         |            )
         |          ) as spu_anti_pbgmv --套利金额
         |        from
         |          (
         |            select
         |              shop_id,
         |              shop_name,
         |              create_time,
         |              order_first_cate_name,
         |              order_id,
         |              sub_order_id,
         |              product_id,
         |              product_name,
         |              passport_id,
         |              cast(gmv as BIGINT) as gmv,
         |              cast(platform_discount as BIGINT) as platform_discount,
         |              antitag,
         |              policy_type, --反作弊策略类型
         |              policyid --反作弊策略ID 
         |            from
         |              yinhe.ecommerce_dws_zbzy_order_detail_df
         |            where
         |              event_day = '${YesterDay}'
         |              and substr(pay_time, 1, 10) >= '${BeForeDayFormat}'
         |              and substr(pay_time, 1, 10) <= '${YesterDayFormat}'
         |              and pay_type = 0 --在线支付
         |              and pay_status in (2, 3) --支付或退款订单
         |              and app_id in (5) --度小店
         |            group by
         |              shop_id,
         |              shop_name,
         |              create_time,
         |              order_first_cate_name,
         |              order_id,
         |              sub_order_id,
         |              product_id,
         |              product_name,
         |              passport_id,
         |              cast(gmv as BIGINT),
         |              cast(platform_discount as BIGINT),
         |              antitag,
         |              policy_type, --反作弊策略类型
         |              policyid --反作弊策略ID 
         |          ) a
         |        group by
         |          a.shop_id,
         |          a.shop_name,
         |          substr(a.create_time, 1, 10),
         |          a.order_first_cate_name,
         |          a.product_id,
         |          a.product_name
         |      ) h
         |    -- 去掉多余的 group by
         |  ) o2 on o1.shop_id = o2.shop_id
         |  and o1.create_date = o2.create_date
         |
         |""".stripMargin

    val shopDetailsDf = spark.sql(hiveSql)
      .select(
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"ratio".cast("decimal(12,2)"),
        $"order_first_cate_name".cast("string"),
        $"product_id".cast("long"),
        $"product_name".cast("string"),
        $"spu_ordcnt".cast("long"),
        $"spu_gmv".cast("long"),
        $"spu_uidcnt".cast("long"),
        $"spu_anti_ordcnt".cast("long"),
        $"spu_anti_uidcnt".cast("long"),
        $"spu_anti_gmv".cast("long"),
        $"spu_anti_pbgmv".cast("long"),
        $"spu_ordcnt_huanbi".cast("decimal(12,2)"),
        $"event_day".cast("string")
      )
      .na.fill("", Seq("shop_name","order_first_cate_name","product_name")) // 填充字符串列的空值
      .na.fill(0, Seq("product_id","spu_ordcnt","spu_gmv","spu_uidcnt","shop_id","spu_anti_ordcnt","spu_anti_uidcnt","spu_anti_gmv","spu_anti_pbgmv"))  // 填充数值列的空值
      .na.fill(0.0, Seq("ratio","spu_ordcnt_huanbi"))  // 填充数值列的空值
      .dropDuplicates()
      .repartition(10)
    println(s"获取店铺细分商品数据的${YesterDay}数据共：${shopDetailsDf.count()}条数据")
    shopDetailsDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    val deleteSql1 = s"delete from ec_ods_order_hot_shops where event_day = '${YesterDay}' "
    //将当天的数据先删除再写入
    deleteMysql(properties,deleteSql1)
    println(s"删除ec_ods_order_hot_shops历史${YesterDay}数据成功")
    // 写入MySQL数据库,订单爆量店铺数据
    mysqlWriteOperate(spark,properties,shopDf,"ec_ods_order_hot_shops",SaveMode.Append)

    val deleteSql2 = s"delete from ec_ods_shop_product_analysis where event_day = '${YesterDay}' "
    //将当天的数据先删除再写入
    deleteMysql(properties,deleteSql2)
    println(s"删除ec_ods_shop_product_analysis历史${YesterDay}数据成功")
    // 写入MySQL数据库,店铺细分商品数据
    mysqlWriteOperate(spark,properties,shopDetailsDf,"ec_ods_shop_product_analysis",SaveMode.Append)

    spark.close()
  }
}
