package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.{CommonUtils, JDBCUtils, PropertiesUtils}
import org.apache.spark.sql.functions.to_date
import org.apache.spark.sql.types.TimestampType
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: zhang<PERSON>jie
 * @date: 2024/12/23
 * @description: 电商服务数据(工单时长)到MySQL的同步逻辑
 */
object DianShangFuWuGongDanToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._
    
    // 读取Hive表
    val hiveSql =
      s"""
         |SELECT
         |*,
         |    GREATEST(
         |        CASE WHEN guaranteed_processing_time > customer_service_first_processing_time THEN customer_service_first_processing_time END,
         |        CASE WHEN guarantee_circulation_time > guaranteed_processing_time THEN guaranteed_processing_time END,
         |        CASE WHEN kefu_close_time > guaranteed_processing_time THEN kefu_close_time END
         |    ) AS nearest_guaranteed_deal_time
         |from 
         |(
         |select
         |  a.order_id,
         |  a.case_id, --工单id
         |  biao_type, --工单类型
         |  source_from, --来源
         |  node_status, --处理节点
         |  case_label, --问题场景标签,
         |  is_delegate_guaranteed,
         |  is_cs_in, --判断客服是否介入
         |  if (b.order_id is not null, '是', '否') is_fast_pass,
         |  a.create_time, --工单创建时间
         |  first_turn_time, --工单首次到商家时间
         |  first_reply_time, --工单商家回复时间
         |  quick_compensation_process_time, --快赔保障处理时间
         |  quick_compensation_pay_time, --快赔保障打款时间
         |  experience_compensation_process_time, --体验赔保障处理时间
         |  experience_compensation_pay_time, --体验赔保障打款时间
         |  outbound_call_contact_time, --外呼联系用户时间
         |  customer_service_first_processing_time, --二线客服首次处理时间
         |  merchant_re_processing_time, --商家再次处理时间
         |  delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间 
         |  guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |  merchant_third_processing_time, --商家第三次处理时间
         |  guarantee_circulation_time, --保障流转时间
         |  customer_service_third_circulation_time, --客服第三次流转时间
         |  customer_service_end_investigation_time, --客服结束排查时间
         |  guarantee_end_investigation_time, --保障结束排查时间
         |  platform_customer_service_accountability_time, --平台判责时间
         |  kefu_close_time, --工单关闭时间
         |  cs_judge_close_time,
         |  customer_service_final_handler,
         |  customer_service_final_transfer_handler,
         |  case
         |    when biao_type = 'tousu'
         |    and cs_judge_close_time > first_reply_time then unix_timestamp(first_reply_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(customer_service_first_processing_time, 'yyyy-MM-dd HH:mm:ss')
         |    when biao_type = 'tousu'
         |    and cs_judge_close_time < first_reply_time then 0
         |    when biao_type in ('fuwu', 'zhongcai') then unix_timestamp(first_reply_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')
         |    else null
         |  end as shop_first_deal_timediff_second,
         |  case
         |    when biao_type = 'tousu'
         |    and customer_service_first_processing_time is not null then unix_timestamp(customer_service_first_processing_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')
         |    when customer_service_first_processing_time is not null
         |    and biao_type in ('fuwu', 'zhongcai') then unix_timestamp(customer_service_first_processing_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(first_reply_time, 'yyyy-MM-dd HH:mm:ss')
         |    when customer_service_first_processing_time is null
         |    and biao_type = 'tousu' then unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')
         |    when customer_service_first_processing_time is null
         |    and biao_type in ('fuwu', 'zhongcai') then unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(first_reply_time, 'yyyy-MM-dd HH:mm:ss')
         |    else null
         |  end as cs_first_deal_timediff_second,
         |   case
         |    when cs_judge_close_time is not null 
         |    then unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')
         |    else null
         |  end as sheet_close_time,
         |    case
         |    when cs_judge_close_time is not null and 
         |    unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')> 259200 then '超过72H'
         |    when cs_judge_close_time is not null and 
         |    unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(a.create_time, 'yyyy-MM-dd HH:mm:ss')<=259200 then '72H内'
         |    else '未完结'
         |  end as is_over_72H
         |from
         |  (
         |    select
         |      order_id,
         |      case_id, --工单id
         |      is_delegate_guaranteed,
         |      biao_type, --工单类型
         |      source_from, --来源
         |      node_status, --处理节点
         |      case_label,
         |      label_rnk,
         |      is_cs_in,
         |      create_time,
         |      first_turn_time,
         |      first_reply_time,
         |      quick_compensation_process_time,
         |      quick_compensation_pay_time,
         |      experience_compensation_process_time,
         |      experience_compensation_pay_time,
         |      outbound_call_contact_time,
         |      customer_service_first_processing_time,
         |      merchant_re_processing_time,
         |      delegation_guaranteed_processing_time,
         |      guaranteed_processing_time,
         |      merchant_third_processing_time,
         |      guarantee_circulation_time,
         |      customer_service_third_circulation_time,
         |      customer_service_end_investigation_time,
         |      guarantee_end_investigation_time,
         |      platform_customer_service_accountability_time,
         |      kefu_close_time,
         |        customer_service_final_handler,
         |  customer_service_final_transfer_handler,
         |      case
         |        when biao_type in ('fuwu', 'zhongcai') then kefu_close_time
         |        when biao_type in ('tousu') and platform_customer_service_accountability_time is not null then platform_customer_service_accountability_time
         |        when biao_type in ('tousu') and platform_customer_service_accountability_time is null then kefu_close_time
         |        else null
         |      end as cs_judge_close_time,
         |      case_label,
         |      row_number() over (
         |        partition by
         |          case_id
         |        order by
         |          label_rnk
         |      ) as label_rnk_order
         |    from
         |      (
         |        select
         |          order_id,
         |          case_id, --工单id
         |          is_delegate_guaranteed,
         |          biao_type, --工单类型
         |          source_from,
         |          case_label,
         |          case
         |            when case_label = '售后纠纷' then 1
         |            when case_label = '售后服务问题' then 2
         |            when case_label = '发货物流问题' then 3
         |            when case_label = '平台服务问题' then 4
         |            when case_label = '商家服务问题' then 5
         |            when case_label = '其他' then 6
         |            else null
         |          end as label_rnk,
         |          if (
         |            (
         |              biao_type = 'fuwu'
         |              and outbound_call_contact_time is not null
         |            )
         |            or (
         |              biao_type = 'zhongcai'
         |              and first_reply_time is not null
         |            )
         |            or (
         |              biao_type = 'tousu'
         |              and create_time is not null
         |            ),
         |            '是',
         |            '否'
         |          ) as is_cs_in,
         |          node_status, --处理节点
         |          create_time,
         |          first_turn_time,
         |          first_reply_time,
         |          quick_compensation_process_time,
         |          quick_compensation_pay_time,
         |          experience_compensation_process_time,
         |          experience_compensation_pay_time,
         |          outbound_call_contact_time,
         |          customer_service_first_processing_time,
         |          merchant_re_processing_time,
         |          delegation_guaranteed_processing_time,
         |          guaranteed_processing_time,
         |          merchant_third_processing_time,
         |          guarantee_circulation_time,
         |          customer_service_third_circulation_time,
         |          customer_service_end_investigation_time,
         |          guarantee_end_investigation_time,
         |          platform_customer_service_accountability_time,
         |          kefu_close_time,
         |            customer_service_final_handler,
         |  customer_service_final_transfer_handler
         |        from
         |          (
         |            select
         |              order_id,
         |              case_id, --工单id
         |              is_delegate_guaranteed,
         |              biao_type, --工单类型
         |              case
         |                when source_from is not null then source_from
         |                when source_from is null
         |                and biao_type = 'zhongcai' then '售后仲裁纠纷来源'
         |                else '其他'
         |              end as source_from, --来源
         |              node_status, --处理节点
         |              case
         |                when biao_type = 'zhongcai' then '售后纠纷'
         |                when newcase_type_level1 = '商家-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '买家-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '平台-售后服务' then '售后服务问题'
         |                when newcase_type_level1 = '商家-商品质量' then '售后服务问题'
         |                when newcase_type_level1 = '商家-商品发布' then '售后服务问题'
         |                when newcase_type_level1 = '商品问题' then '售后服务问题'
         |                when newcase_type_level1 = '钱款纠纷' then '售后服务问题'
         |                when newcase_type_level1 = '舆情-舆情外溢' then '售后服务问题'
         |                when newcase_type_level1 = '发货物流问题' then '发货物流问题'
         |                when newcase_type_level1 = '商家-发货服务' then '发货物流问题'
         |                when newcase_type_level1 = '商家-快递问题' then '发货物流问题'
         |                when newcase_type_level1 = '物流-快递问题' then '发货物流问题'
         |                when newcase_type_level1 = '平台-平台服务' then '平台服务问题'
         |                when newcase_type_level1 = '平台-订单相关' then '平台服务问题'
         |                when newcase_type_level1 = '平台-平台功能' then '平台服务问题'
         |                when newcase_type_level1 = '平台-平台福利活动' then '平台服务问题'
         |                when newcase_type_level1 = '商家服务' then '商家服务问题'
         |                when newcase_type_level1 = '发票问题' then '商家服务问题'
         |                when newcase_type_level1 = '商家-发票相关' then '商家服务问题'
         |                when newcase_type_level1 = '商家-商家服务' then '商家服务问题'
         |                when newcase_type_level1 = '商家-商家信息' then '商家服务问题'
         |                when newcase_type_level1 = '其他' then '其他'
         |                else '其他'
         |              end as case_label,
         |                customer_service_final_handler,
         |  customer_service_final_transfer_handler,
         |              -- unix_timestamp(cs_judge_close_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(first_cs_deal_time, 'yyyy-MM-dd HH:mm:ss') as diff,
         |              max(create_time) as create_time, --工单创建时间
         |              max(first_turn_time) as first_turn_time, --工单首次到商家时间
         |              max(first_reply_time) as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |              max(quick_compensation_process_time) as quick_compensation_process_time, --快赔保障处理时间
         |              max(quick_compensation_pay_time) as quick_compensation_pay_time, --快赔保障打款时间
         |              max(experience_compensation_process_time) as experience_compensation_process_time, --体验赔保障处理时间
         |              max(experience_compensation_pay_time) as experience_compensation_pay_time, --体验赔保障打款时间
         |              max(outbound_call_contact_time) as outbound_call_contact_time, --外呼联系用户时间
         |              max(customer_service_first_processing_time) as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |              max(merchant_re_processing_time) as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |              max(delegation_guaranteed_processing_time) as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |              max(guaranteed_processing_time) as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |              max(merchant_third_processing_time) as merchant_third_processing_time, --商家第三次处理时间
         |              max(guarantee_circulation_time) as guarantee_circulation_time, --保障流转时间
         |              max(customer_service_third_circulation_time) as customer_service_third_circulation_time, --客服第三次流转时间
         |              max(customer_service_end_investigation_time) as customer_service_end_investigation_time, --客服结束排查时间
         |              max(guarantee_end_investigation_time) as guarantee_end_investigation_time, --保障结束排查时间
         |              max(platform_customer_service_accountability_time) as platform_customer_service_accountability_time, --平台判责时间
         |              max(kefu_close_time) as kefu_close_time
         |            from
         |              (
         |                SELECT
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  is_delegate_guaranteed,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  first_reply_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  outbound_call_contact_time as outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_processing_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  merchant_re_processing_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  delegation_guaranteed_processing_time as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  null as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |                  null as merchant_third_processing_time, --商家第三次处理时间
         |                  null as guarantee_circulation_time, --保障流转时间
         |                  null as customer_service_third_circulation_time, --客服第三次流转时间
         |                  null as customer_service_end_investigation_time, --客服结束排查时间
         |                  null as guarantee_end_investigation_time, --保障结束排查时间
         |                  null as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                    customer_service_final_handler,
         |  customer_service_final_transfer_handler,
         |                  case
         |                    when newcase_type_level1 is null
         |                    or newcase_type_level1 = '' then 'unknown'
         |                    else newcase_type_level1
         |                  end as newcase_type_level1,
         |                  case
         |                    when newcase_type_level2 is null
         |                    or newcase_type_level2 = '' then 'unknown'
         |                    else newcase_type_level2
         |                  end as newcase_type_level2,
         |                  case
         |                    when newcase_type_level3 is null
         |                    or newcase_type_level3 = '' then 'unknown'
         |                    else newcase_type_level3
         |                  end as newcase_type_level3,
         |                  'zhongcai' as biao_type,
         |                  source_from, --来源
         |                  node_status
         |                FROM
         |                  udw_ns.default.help_ods_fengling_zhongcai_detail
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '********' AND ${YesterDay}
         |                  --and outbound_call_contact_time is not null 
         |                  --and first_reply_time is not null --限制有商家流转则客服介入
         |                UNION ALL
         |                SELECT
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  is_delegate_guaranteed,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  first_reply_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  outbound_call_contact_time as outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_processing_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  merchant_re_processing_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  delegation_guaranteed_processing_time as delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  guaranteed_processing_time as guaranteed_processing_time, --委派保障处理时间/保障处理时间
         |                  null as merchant_third_processing_time, --商家第三次处理时间
         |                  null as guarantee_circulation_time, --保障流转时间
         |                  null as customer_service_third_circulation_time, --客服第三次流转时间
         |                  null as customer_service_end_investigation_time, --客服结束排查时间
         |                  null as guarantee_end_investigation_time, --保障结束排查时间
         |                  null as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                    customer_service_final_handler,
         |  customer_service_final_transfer_handler,
         |                  case
         |                    when newcase_type_level1 is null
         |                    or newcase_type_level1 = '' then 'unknown'
         |                    else newcase_type_level1
         |                  end as newcase_type_level1,
         |                  case
         |                    when newcase_type_level2 is null
         |                    or newcase_type_level2 = '' then 'unknown'
         |                    else newcase_type_level2
         |                  end as newcase_type_level2,
         |                  case
         |                    when newcase_type_level3 is null
         |                    or newcase_type_level3 = '' then 'unknown'
         |                    else newcase_type_level3
         |                  end as newcase_type_level3,
         |                  'fuwu' as biao_type,
         |                  source_from, --来源
         |                  node_status
         |                FROM
         |                  udw_ns.default.help_ods_fengling_fuwu_detail
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') between '********' and ${YesterDay}
         |                  --and outbound_call_contact_time is not null --限制有客服介入
         |                UNION ALL
         |                select
         |                  order_id,
         |                  case_id,
         |                  id,
         |                  is_delegate_guaranteed,
         |                  create_time as create_time, --工单创建时间
         |                  first_turn_time as first_turn_time, --工单首次到商家时间
         |                  business_first_circulation_time as first_reply_time, --工单商家回复时间  --business_first_circulation_time
         |                  quick_compensation_process_time as quick_compensation_process_time, --快赔保障处理时间
         |                  quick_compensation_pay_time as quick_compensation_pay_time, --快赔保障打款时间
         |                  experience_compensation_process_time as experience_compensation_process_time, --体验赔保障处理时间
         |                  experience_compensation_pay_time as experience_compensation_pay_time, --体验赔保障打款时间
         |                  outbound_call_contact_time, --外呼联系用户时间
         |                  customer_service_first_circulation_time as customer_service_first_processing_time, --二线客服首次处理时间  投诉：customer_service_first_circulation_time
         |                  business_second_circulation_time as merchant_re_processing_time, --商家再次处理时间  投诉：business_second_circulation_time
         |                  delegation_guaranteed_processing_time, --委派保障时间/二线客服再次处理时间  --customer_service_second_circulation_time
         |                  customer_service_second_circulation_time as guaranteed_processing_time, --客服再次处理时间
         |                  '' as merchant_third_processing_time, --商家第三次处理时间
         |                  guarantee_circulation_time as guarantee_circulation_time, --保障流转时间
         |                  customer_service_third_circulation_time as customer_service_third_circulation_time, --客服第三次流转时间
         |                  customer_service_end_investigation_time as customer_service_end_investigation_time, --客服结束排查时间
         |                  guarantee_end_investigation_time as guarantee_end_investigation_time, --保障结束排查时间
         |                  platform_customer_service_accountability_time as platform_customer_service_accountability_time, --平台判责时间
         |                  kefu_close_time as kefu_close_time,
         |                  customer_service_final_handler,
         |                  customer_service_final_transfer_handler,
         |                  case_type_level1 as newcase_type_level1,
         |                  case_type_level2 as newcase_type_level2,
         |                  '' as newcase_type_level3,
         |                  --complaint_result_b,
         |                  'tousu' as biao_type,
         |                  source_from, --来源
         |                  complaint_status as node_status
         |                from
         |                  udw_ns.default.help_ods_fengling_tousu_detail_df
         |                WHERE
         |                  event_day = ${YesterDay}
         |                  and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '********' AND ${YesterDay}
         |                  and (
         |                    complaint_result_b in ('成立', '不成立')
         |                    or complaint_result_b is null
         |                  )
         |                  --   and platform_customer_service_accountability_time is not null
         |              ) --工单数据合并
         |            group by
         |              order_id,
         |              case_id, --工单id
         |              is_delegate_guaranteed,
         |              biao_type, --工单类型
         |              source_from, --来源
         |              node_status, --处理节点
         |              case_label,
         |                customer_service_final_handler,
         |  customer_service_final_transfer_handler
         |          ) --取最近一条时间节点
         |      ) --场景排序
         |  ) a --窗口函数限制唯一场景
         |  left join (
         |    SELECT
         |      create_time,
         |      from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyy-MM-dd') as create_day,
         |      order_id,
         |      sub_order_id,
         |      refund_id,
         |      risk_user_flag,
         |      fast_after_sale_flag,
         |      fast_after_sale_type,
         |      quick_pass_source,
         |      quick_pass_after_sale_type,
         |      negative_commodity_flag
         |    from
         |      udw_ns.default.help_ods_tb_data_kuaitong_all
         |    where
         |      event_day = ${YesterDay}
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      and fast_after_sale_flag = '快通'
         |      and quick_pass_source in ('服务单', '投诉单', '仲裁单')
         |  ) b on a.order_id = b.order_id
         |  and b.create_time > a.create_time
         |where
         |  label_rnk_order = 1
         |  )
         |""".stripMargin

    val hiveDf = spark.sql(hiveSql)
      .select(
        $"order_id".cast("long"),
        $"case_id".cast("long"),
        $"biao_type".cast("string"),
        $"source_from".cast("string"),
        $"node_status".cast("string"),
        $"case_label".cast("string"),
        $"is_delegate_guaranteed".cast("string"),
        $"is_cs_in".cast("string"),
        $"is_fast_pass".cast("string"),
        $"create_time".cast("timestamp"),
        $"first_turn_time".cast("timestamp"),
        $"first_reply_time".cast("timestamp"),
        $"quick_compensation_process_time".cast("timestamp"),
        $"quick_compensation_pay_time".cast("timestamp"),
        $"experience_compensation_process_time".cast("timestamp"),
        $"experience_compensation_pay_time".cast("timestamp"),
        $"outbound_call_contact_time".cast("timestamp"),
        $"customer_service_first_processing_time".cast("timestamp"),
        $"merchant_re_processing_time".cast("timestamp"),
        $"delegation_guaranteed_processing_time".cast("timestamp"),
        $"guaranteed_processing_time".cast("timestamp"),
        $"merchant_third_processing_time".cast("timestamp"),
        $"guarantee_circulation_time".cast("timestamp"),
        $"customer_service_third_circulation_time".cast("timestamp"),
        $"customer_service_end_investigation_time".cast("timestamp"),
        $"guarantee_end_investigation_time".cast("timestamp"),
        $"platform_customer_service_accountability_time".cast("timestamp"),
        $"kefu_close_time".cast("timestamp"),
        $"cs_judge_close_time".cast("timestamp"),
        $"customer_service_final_handler".cast("string"),
        $"customer_service_final_transfer_handler".cast("string"),
        $"shop_first_deal_timediff_second".cast("long"),
        $"cs_first_deal_timediff_second".cast("long"),
        $"sheet_close_time".cast("long"),
        $"is_over_72H".cast("string"),
        $"nearest_guaranteed_deal_time".cast("timestamp"),
      )
      .na.fill("", Seq("biao_type","source_from","node_status","case_label","is_delegate_guaranteed","is_cs_in","is_fast_pass","customer_service_final_handler","customer_service_final_transfer_handler","is_over_72H")) // 填充字符串列的空值
      .na.fill(0, Seq("order_id","case_id","shop_first_deal_timediff_second","cs_first_deal_timediff_second","sheet_close_time"))  // 填充数值列的空值
      .dropDuplicates()
    println(s"获取工单时长${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_order_duration")

    spark.close()
  }
}
