package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/4/29
 * @description: 电商智能IM明细到MySQL的同步逻辑
 */
object DianShangZhiNengIMToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    import spark.implicits._
    
    // 读取Hive表
    val hiveSql =
      s"""
         |select
         |  service_data.feedback_day,
         |  service_data.feedback_time,
         |  service_data.case_id,
         |  im_session_id,
         |  service_data.order_id as service_order_id,
         |  service_data.sub_order_id as service_sub_order_id,
         |  service_data.newcase_type_level1,
         |  service_data.newcase_type_level2,
         |  service_data.newcase_type_level3,
         |  service_data.feedback_channel,
         |  robot_sat,
         |  manual_sat,
         |  robot_feedback_type, --机器人反馈类型标签
         |  online_feedback_content, --反馈内容
         |  user_value_label, --用户标签
         |  robot_evaluation_score, --智能服务评价得分
         |  robot_evaluation_label, --智能服务评价标签
         |  robot_whether_solve, --智能服务解决问题与否
         |  robot_advice, --智能服务建议
         |  smart_initiate_quick_pass, --是否触发快通（智能）
         |  smart_emit_quick_pass_card, --是否下发快通介入卡（智能）
         |  smart_generate_quick_pass_case, --是否生成快通售后单（智能）
         |  smart_quick_pass_type, --快通类型（智能）
         |  pay_time,
         |  shop_id,
         |  shop_name,
         |  product_name,
         |  order_status, --status字段来源替换
         |  gmv as total_amount,
         |  refund_create_time,
         |  first_cate_name, --字段来源替换
         |  second_cate_name, --字段来源替换
         |  shop_type, --店铺类型-新增
         |  uc_shop_type, --商家UC分类--新增
         |  order_table.refund_id, --新增
         |  origin_refund_type, --新增
         |  order_refund_status, --新增
         |  to_manual,
         |  space_or_skill_id,
         |  kuaitong.fast_after_sale_type,
         |  kuaitong.quick_pass_source as fast_quick_pass_source
         |from
         |  (
         |    SELECT
         |      order_id,
         |      sub_order_id,
         |      case_id,
         |      im_session_id,
         |      feedback_time,
         |      left(feedback_time, 10) as feedback_day,
         |      feedback_channel,
         |      robot_sat,
         |      manual_sat,
         |      robot_feedback_type, --机器人反馈类型标签
         |      online_feedback_content, --反馈内容
         |      user_value_label, --用户标签
         |      robot_evaluation_score, --智能服务评价得分
         |      robot_evaluation_label, --智能服务评价标签
         |      robot_whether_solve, --智能服务解决问题与否
         |      robot_advice, --智能服务建议
         |      smart_initiate_quick_pass, --是否触发快通（智能）
         |      smart_emit_quick_pass_card, --是否下发快通介入卡（智能）
         |      smart_click_need_flag, --是否点击确认/需要（智能）
         |      smart_emit_quick_pass_reason_card, --是否下发快通原因选择卡（智能）
         |      smart_click_reason_flag, --是否点击快通原因（智能）
         |      smart_click_reason_detail, --点击快通原因（智能）
         |      smart_generate_quick_pass_case, --是否生成快通售后单（智能）
         |      smart_quick_pass_type, --快通类型（智能）
         |      smart_show_door_pickup_card, --是否展示预约上门取件卡片（智能）
         |      smart_click_door_pickup, --是否点击预约上门取件（智能）
         |      smart_refund_id, -- 售后单id（智能快通）  
         |      smart_refund_type, --售后类型（智能快通）
         |      carriage_quick_pass_flag, --是否快通运费人工
         |      compensation_type, --赔付类型
         |      experience_compensation_type, --体验赔方式
         |      compensation_amount, --赔付金额
         |      compensation_reason, --赔付原因
         |      to_manual,
         |      space_or_skill_id,
         |      case
         |        when newcase_type_level1 is null
         |        or newcase_type_level1 = '' then 'unknown'
         |        else newcase_type_level1
         |      end as newcase_type_level1,
         |      case
         |        when newcase_type_level2 is null
         |        or newcase_type_level2 = '' then 'unknown'
         |        else newcase_type_level2
         |      end as newcase_type_level2,
         |      case
         |        when newcase_type_level3 is null
         |        or newcase_type_level3 = '' then 'unknown'
         |        else newcase_type_level3
         |      end as newcase_type_level3
         |    FROM
         |      udw_ns.default.help_ods_feedback_online_online_retailers_feedback_only_df
         |    WHERE
         |      event_day = '${YesterDay}'
         |      AND from_unixtime(unix_timestamp(feedback_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      AND question_type = '度小店-实时对话'
         |  ) service_data
         |  left join (
         |    SELECT
         |      create_time,
         |      pay_time,
         |      left(pay_time, 10) as pay_day,
         |      cancel_time,
         |      update_time,
         |      CAST(SUBSTR(update_time, 1, 10) AS DATE) AS update_day,
         |      content_type,
         |      content_id,
         |      product_create_time, --商品创建时间
         |      product_on_sell_time, --商品上架时间
         |      product_update_time,
         |      identify_day, --作弊订单识别日期
         |      ucid,
         |      shop_id,
         |      shop_name,
         |      passport_id,
         |      order_id,
         |      sub_order_id,
         |      sku_id,
         |      product_id,
         |      replace(replace(product_name, char(10), '|'), char(13), '|') product_name,
         |      business_attribution_new,
         |      buy_num,
         |      business_discount,
         |      platform_discount,
         |      detail_freight_amount,
         |      detail_payment_amount,
         |      detail_settle_amount,
         |      detail_total_amount,
         |      rake_channel_amount,
         |      rake_split_amount,
         |      rake_finish_time,
         |      rebate_channel_amount,
         |      rebate_split_amount,
         |      rebate_finish_time,
         |      evaluate_status,
         |      first_cate_name,
         |      second_cate_name,
         |      third_cate_name,
         |      gmv,
         |      is_consumed, --订单是否核销
         |      is_digital_human, --直播是否数字人直播
         |      order_refund_status, --订单售后状态
         |      origin_refund_type,
         |      refund_create_time,
         |      refund_finish_time,
         |      order_status,
         |      pay_status,
         |      pay_type,
         |      platform_type, --商品平台
         |      product_type,
         |      shop_type, --店铺类型
         |      refund_id,
         |      refund_status,
         |      submit_type,
         |      trade_type, --交易类型
         |      transaction_mode,
         |      uc_shop_type, --商家UC分类
         |      policy_type, --反作弊策略类型
         |      policyid, --反作弊策略id
         |      antitag --作弊订单标识，使用当天反作弊订单表的数
         |    FROM
         |      yinhe.ecommerce_dws_zbzy_order_detail_df
         |    WHERE
         |      event_day = '${YesterDay}' --取最近一天
         |      and from_unixtime(unix_timestamp(pay_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      AND pay_type = 0 --在线支付
         |      AND pay_status IN (2, 3) --支付或退款订单
         |      AND app_id IN (5, 100012) --度小店&招财猫
         |  ) as order_table on order_table.sub_order_id = service_data.sub_order_id
         |  left join (
         |    SELECT
         |      create_time,
         |      from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyy-MM-dd') as create_day,
         |      order_id,
         |      sub_order_id,
         |      refund_id,
         |      risk_user_flag,
         |      fast_after_sale_flag,
         |      fast_after_sale_type,
         |      quick_pass_source,
         |      quick_pass_after_sale_type,
         |      negative_commodity_flag
         |    from
         |      udw_ns.default.help_ods_tb_data_kuaitong_all
         |    where
         |      event_day = '${YesterDay}'
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20240501'
         |      and fast_after_sale_flag = '快通'
         |  ) kuaitong on service_data.order_id = kuaitong.order_id
         |  and kuaitong.create_time > service_data.feedback_time
         |""".stripMargin

    val hiveDf = spark.sql(hiveSql)
      .select(
        $"feedback_day".cast("date"),
        $"feedback_time".cast("timestamp"),
        $"case_id".cast("long"),
        $"im_session_id".cast("string"),
        $"service_order_id".cast("long"),
        $"service_sub_order_id".cast("long"),
        $"newcase_type_level1".cast("string"),
        $"newcase_type_level2".cast("string"),
        $"newcase_type_level3".cast("string"),
        $"feedback_channel".cast("string"),
        $"robot_sat".cast("string"),
        $"manual_sat".cast("string"),
        $"robot_feedback_type".cast("string"),
        $"online_feedback_content".cast("string"),
        $"user_value_label".cast("string"),
        $"robot_evaluation_score".cast("string"),
        $"robot_evaluation_label".cast("string"),
        $"robot_whether_solve".cast("string"),
        $"robot_advice".cast("string"),
        $"smart_initiate_quick_pass".cast("string"),
        $"smart_emit_quick_pass_card".cast("string"),
        $"smart_generate_quick_pass_case".cast("string"),
        $"smart_quick_pass_type".cast("string"),
        $"pay_time".cast("timestamp"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"product_name".cast("string"),
        $"order_status".cast("int"),
        $"total_amount".cast("double"),
        $"refund_create_time".cast("timestamp"),
        $"first_cate_name".cast("string"),
        $"second_cate_name".cast("string"),
        $"shop_type".cast("string"),
        $"uc_shop_type".cast("string"),
        $"refund_id".cast("long"),
        $"origin_refund_type".cast("string"),
        $"order_refund_status".cast("string"),
        $"to_manual".cast("int"),
        $"space_or_skill_id".cast("string"),
        $"fast_after_sale_type".cast("string"),
        $"fast_quick_pass_source".cast("string")
      )
      .na.fill("", Seq("im_session_id","newcase_type_level1","newcase_type_level2","newcase_type_level3",
        "feedback_channel","robot_sat","manual_sat","robot_feedback_type","user_value_label","robot_evaluation_score",
        "robot_evaluation_label","robot_advice","smart_initiate_quick_pass","smart_emit_quick_pass_card",
        "smart_quick_pass_type","shop_name", "product_name","first_cate_name", "second_cate_name","shop_type",
        "order_refund_status","online_feedback_content","robot_whether_solve","smart_generate_quick_pass_case",
      "uc_shop_type","origin_refund_type","space_or_skill_id","fast_after_sale_type","fast_quick_pass_source")) // 填充字符串列的空值
      .na.fill(0, Seq("case_id","service_order_id","service_sub_order_id","order_status","shop_id","refund_id","to_manual"))  // 填充数值列的空值
      .na.fill(0.0, Seq("total_amount"))  // 填充数值列的空值
      .dropDuplicates()
      .repartition(50)
    println(s"获取智能IM的${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    // 写入MySQL数据库
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_intelligent_im")

    spark.close()
  }
}
