package com.baidu.sql.customized.dianshang

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.CommonUtils.mysqlWriteOperate
import com.baidu.sql.utils.PropertiesUtils
import org.apache.spark.sql.types.DecimalType
import org.apache.spark.sql.{SaveMode, SparkSession}


/**
 * @author: z<PERSON><PERSON>jie
 * @date: 2024/12/23
 * @description: 电商服务数据(延迟满意度)到MySQL的同步逻辑
 */
object DianShangFuWuYanChiToMysql {
  //运行日期
  var YesterDay: String = ""

  def main(args: Array[String]): Unit = {
    YesterDay = args(0)

    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()
    import spark.implicits._

    // 读取Hive表
    val hiveSql =
      s"""
         |SELECT
         |distinct
         |  evaluate.case_id,
         |  eva_source,
         |  evaluate_day,
         |  eva_time,
         |  eva_solve_status,
         |  eva_star,
         |  eva_star_label,
         |  eva_advise,
         |  evaluate.passport_id,
         |  evaluate.order_id,
         |  source_id,
         |  question_categorys,
         |  replace(replace(case_content, char(10), '|'), char(13), '|') as case_content,
         |  biao_type,
         |  source_from,
         |  shop_id,
         |  shop_name,
         |  product_id,
         |  product_name,
         |  categoryNameList,
         |  status,
         |  total_amount,
         |  refund_create_time,
         |  refund_finish_time,
         |  refund_reason,
         |  express_data
         |from
         |  (
         |    select
         |      case_id,
         |      eva_source,
         |      left(eva_time, 10) as evaluate_day,
         |      eva_time,
         |      eva_solve_status,
         |      eva_star,
         |      eva_star_label,
         |      replace(replace(eva_advise, char(10), '|'), char(13), '|') eva_advise,
         |      passport_id,
         |      order_id,
         |      source_id
         |    from
         |      udw_ns.default.help_ods_fengling_case_evaluate_df
         |    where
         |      event_day = ${YesterDay}
         |      and eva_time is not null
         |      and from_unixtime(unix_timestamp(eva_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') >= '20241101'
         |      and case_id not in ('583086', '583063')
         |    order by
         |      eva_time
         |  ) evaluate
         |  left join (
         |    SELECT
         |      order_id,
         |      sub_order_id,
         |      case_id,
         |      passport_id,
         |      source_from,
         |      create_time as create_time,
         |      newcase_type_level1,
         |      newcase_type_level2,
         |      newcase_type_level3,
         |      question_categorys,
         |      question_desc as case_content,
         |      'zhongcai' as biao_type
         |    FROM
         |      udw_ns.default.help_ods_fengling_zhongcai_detail
         |    WHERE
         |      event_day = ${YesterDay}
         |      AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '20241001' AND ${YesterDay}
         |    UNION ALL
         |    SELECT
         |      order_id,
         |      sub_order_id,
         |      case_id,
         |      passport_id,
         |      source_from,
         |      create_time as create_time,
         |      newcase_type_level1,
         |      newcase_type_level2,
         |      newcase_type_level3,
         |      question_categorys,
         |      case_content,
         |      'fuwu' as biao_type
         |    FROM
         |      udw_ns.default.help_ods_fengling_fuwu_detail
         |    WHERE
         |      event_day = ${YesterDay}
         |      AND from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') between '20241001' and ${YesterDay} -- AND service_type='投诉'
         |    UNION ALL
         |    select
         |      order_id,
         |      sub_order_id,
         |      case_id,
         |      passport_id,
         |      source_from,
         |      create_time as create_time,
         |      newcase_type_level1,
         |      newcase_type_level2,
         |      newcase_type_level3,
         |      question_categorys,
         |      case_content,
         |      --complaint_result_b,
         |      'tousu' as biao_type
         |    from
         |      udw_ns.default.help_ods_fengling_tousu_detail_df
         |    WHERE
         |      event_day = ${YesterDay}
         |      and from_unixtime(unix_timestamp(create_time, 'yyyy-MM-dd HH:mm:ss'), 'yyyyMMdd') BETWEEN '20241001' AND ${YesterDay}
         |  ) sheet on evaluate.case_id = sheet.case_id
         |  left join (
         |    select
         |      order_id,
         |      sub_order_id,
         |      cuid,
         |      baiduid,
         |      passport_id,
         |      ucid,
         |      shop_id,
         |      shop_name,
         |      product_id,
         |      product_name,
         |      sku_id,
         |      pay_type,
         |      pay_channel,
         |      total_amount / 100 as total_amount,
         |      payment_amount,
         |      freight_amount,
         |      cheap_amount,
         |      refund_create_time,
         |      refund_finish_time,
         |      refund_reason,
         |      ip,
         |      name,
         |      mobile,
         |      province,
         |      city,
         |      area,
         |      address,
         |      status,
         |      express_name,
         |      tracking_number,
         |      express_status,
         |      express_data,
         |      create_time,
         |      pay_time,
         |      cancel_time,
         |      consign_time,
         |      confirm_time,
         |      evaluate_time,
         |      pay_passport_id,
         |      buy_num,
         |      product_score,
         |      shop_service_score,
         |      shop_logistics_score,
         |      original_content,
         |      get_json_object(product_json, '$$.categoryNameList') as categoryNameList,
         |      get_json_object(product_json, '$$.serviceTel') as serviceTel
         |    from
         |      udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
         |    where
         |      event_day = ${YesterDay}
         |      and app_id in (5, 100012)
         |      and substr(pay_time, 1, 10) >='2024-01-01'
         |      and pay_type = 0
         |      and get_json_object(cashier_pay_info, '$$.status') in (2, 3)
         |  ) as order_table on order_table.sub_order_id = sheet.sub_order_id
         |""".stripMargin

    //类型转换
    val hiveDf = spark.sql(hiveSql)
      .select(
        $"case_id".cast("long"),
        $"eva_source".cast("string"),
        $"evaluate_day".cast("date"),
        $"eva_time".cast("timestamp"),
        $"eva_solve_status".cast("string"),
        $"eva_star".cast("string"),
        $"eva_star_label".cast("string"),
        $"eva_advise".cast("string"),
        $"passport_id".cast("long"),
        $"order_id".cast("long"),
        $"source_id".cast("long"),
        $"question_categorys".cast("string"),
        $"case_content".cast("string"),
        $"biao_type".cast("string"),
        $"source_from".cast("string"),
        $"shop_id".cast("long"),
        $"shop_name".cast("string"),
        $"product_id".cast("long"),
        $"product_name".cast("string"),
        $"categoryNameList".cast("string"),
        $"status".cast("int"),
        $"total_amount".cast(DecimalType(10, 1)),
        $"refund_create_time".cast("timestamp"),
        $"refund_finish_time".cast("timestamp"),
        $"refund_reason".cast("string"),
        $"express_data".cast("string")
      )
      .na.fill("",Seq("eva_source","eva_solve_status","eva_star","eva_advise","biao_type","source_from","shop_name","product_name","categoryNameList"))
      .na.fill(0,Seq("case_id","passport_id","order_id","source_id","shop_id","product_id","status"))
      .na.fill(0.0,Seq("total_amount"))
      .dropDuplicates()
      .repartition(50)
    println(s"获取延迟满意度${YesterDay}数据共：${hiveDf.count()}条数据")
    hiveDf.show(5,false)

    val properties = PropertiesUtils.MysqlDianShangProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    //写入数据，覆盖写
    mysqlWriteOperate(spark,properties,hiveDf,"ecom_service_delay_satisfacing")

    spark.close()
  }
}
