package com.baidu.sql.customized.dianshang

import cn.hutool.json.JSONUtil
import com.alibaba.fastjson.JSON
import com.baidu.sql.utils.TimeOperateUtil
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType}

import java.time.LocalDateTime
import scala.collection.mutable


/**
 * <AUTHOR>
 *         输入电商底表udw_ns.default.help_ods_risk_event
 *         输出电商黑名单表 udw_ns.default.help_ods_risk_black_id_day
 * @Date 2024-11-25
 */
object DianShangDealDataStatistics {

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_im_2_mysql")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 昨天日期
    val dt_yd = YesterDay
    // 当前日期
    val dt_td = TimeOperateUtil.calcnDate(YesterDay,1)
    // 黑名单过期时间
    val dt_90d = TimeOperateUtil.calcnDate(dt_td,90)
    //获取当前时间的小时数
    val currentTime = LocalDateTime.now()
    val dt_hour = currentTime.getHour

    //抽取近三十天数据
    val data = spark.sql(
      s"""
        |select
        |    passid,
        |    zid,
        |    ip,
        |    extra['telNumber'] as telNumber,
        |    servertime,
        |    unchecked_mid['rqv_userName'] as rqv_userName,
        |    extra['countyName'] as countyName,
        |    unchecked_mid['rqv_telNumber'] as rqv_telNumber,
        |    unchecked_mid['rqv_telNumberAes'] as rqv_telNumberAes,
        |    unchecked_mid['rqv_detailInfo'] as rqv_detailInfo,
        |    phone_sha1,
        |    phone,
        |    unchecked_mid['imv_geoh_l8'] as imv_geoh_l8,
        |    extra['skues'] as skues,
        |    event_day
        |from udw_ns.default.help_ods_risk_event
        |where event_day = '${dt_yd}'
        |and activity_id = '5680'
        """.stripMargin)
      .withColumn("spuid",getSpu($"skues".cast(ArrayType(StringType)), lit("spuId")))
      .withColumn("spu_count",getSpu($"skues".cast(ArrayType(StringType)), lit("count")))

    data.createOrReplaceTempView("tmp_spu")

    //爆款squ统计
    val spuDf = spark.sql(
      """
        |select
        |   b.*
        |from
        |   (select
        |         spuid,
        |         count(distinct passid) as uv,
        |         count(passid) as pv
        |    from tmp_spu
        |    group by 1
        |    having uv>1000) a
        |left join tmp_spu b
        |on b.spuid=a.spuid
        |""".stripMargin)

    spuDf.createOrReplaceTempView("ds_bkspu")

    //爆款SPU  zid 统计 召回黄牛
    val ds_bkspu_groupbyzid = spark.sql(
      """
        |select distinct
        |   b.passid
        |from
        | (select
        |     zid,
        |     count(distinct passid) as uv
        |  from ds_bkspu
        |  where zid != ''
        |  group by 1
        |  having uv>5) a
        |left join ds_bkspu b
        |on b.zid=a.zid
        |""".stripMargin)

    //爆款SPU  PASSID 地址统计  召回 黄牛
    val ds_bkspu_groupbygeoh = spark.sql(
      """
        |select distinct
        |b.passid
        |from
        | (select
        |     imv_geoh_l8,
        |     count(distinct passid) as uv
        |  from ds_bkspu
        |  where imv_geoh_l8 is not null
        |  group by 1
        |  having uv>50) a
        |left join ds_bkspu b
        |on b.imv_geoh_l8=a.imv_geoh_l8
        |""".stripMargin)

    val ds_result = ds_bkspu_groupbyzid.union(ds_bkspu_groupbygeoh)
    ds_result.count()
    ds_result.createOrReplaceTempView("ds_result")

    // 写入黑名单表
    spark.sql(
      s"""
         |insert overwrite table udw_ns.default.help_ods_risk_black_id_day
         |partition (event_day = '${dt_yd}')
         |select distinct
         |    passid,
         |    'B_XDL' as tag,
         |    'passid',
         |    '13142',
         |    '1',
         |    unix_timestamp('${dt_td}','yyyyMMdd'),
         |    unix_timestamp('${dt_90d}','yyyyMMdd'),
         |    '-1',
         |    'huangniu',
         |    'UT'
         |from ds_result
         """.stripMargin)

    spark.close()
  }

  /**
   * 获取skues中的字段数据
   */
  val getSpu = udf ((data: mutable.WrappedArray[String],colname:String) =>{
    if (data == null || data.isEmpty){
      ""
    }else{
      var res = ""
      val jsonStr = data(0)
      if (JSONUtil.isJson(jsonStr)){
        val json = JSON.parseObject(jsonStr)
        res = json.get(colname).toString
      }
      res
    }
  })
}
