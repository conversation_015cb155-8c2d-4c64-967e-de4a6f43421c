package com.baidu.sql.customized.dianshang

import com.baidu.sql.utils.TimeOperateUtil
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

import java.time.LocalDateTime


/**
 * <AUTHOR>
 *         输入电商底表udw_ns.default.help_ods_risk_event，店铺IP表udw.default.help_ods_huaxiang_fengkong_login_info_df
 *         输出电商黑名单表 udw_ns.default.help_ods_risk_black_id_day
 * @Date 2024-11-25
 */
object DianShangTiDanFalseDeal {

  def main(args: Array[String]): Unit = {
    // 分区时间
    val YesterDay: String = args(0)
    val conf = new SparkConf()

    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_udw_im_2_mysql")
      //.master("local")
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 昨天日期
    val dt_yd = YesterDay
    // 当前日期
    val dt_td = TimeOperateUtil.calcnDate(YesterDay,1)
    // 黑名单过期时间
    val dt_30d = TimeOperateUtil.calcnDate(dt_td,-30)
    // 三个月后日期
    val dt_90d = TimeOperateUtil.calcnDate(dt_td,90)
    //获取当前时间的小时数
    val currentTime = LocalDateTime.now()
    val dt_hour = currentTime.getHour

    //店铺IP近30天数据
    val data = spark.sql(
      s"""
        |select distinct
        |   uc_id,
        |   login_ip_last,
        |   event_day
        |from udw.default.help_ods_huaxiang_fengkong_login_info_df
        |where event_day > '${dt_30d}'
        |and app_id in (5,100012)
        |and ip != 'error'
        |""".stripMargin)

    //抽取近三十天数据
    val order = spark.sql(
      s"""
        |select 
        |    passid,
        |    ip,
        |    event_day
        |from udw_ns.default.help_ods_risk_event
        |where event_day >= '${dt_30d}'
        |and event_day <= '${dt_yd}'
        |and activity_id = '5680'
        """.stripMargin)

    // 执行内连接
    val joinedData = order.join(data, Seq("ip", "event_day"), "inner")

    // 分组并计算统计信息
    val dataStats = joinedData.groupBy($"passid", $"uc_id").agg(countDistinct($"ip", $"event_day").alias("ip_inner_nums"))
    val passidIpcnts = joinedData.groupBy($"passid").agg(countDistinct($"ip", $"event_day").alias("ip_cnts"))
    val ucIdIpcnts = joinedData.groupBy($"uc_id").agg(countDistinct($"ip", $"event_day").alias("ip_cnts2"))

    // 连接结果并计算分数
    val result = dataStats.join(passidIpcnts, Seq("passid"), "left")
      .join(ucIdIpcnts, Seq("uc_id"), "left")
      .withColumn("score", $"ip_inner_nums" / ($"ip_cnts" + $"ip_cnts2" - $"ip_inner_nums"))

    // 过滤并选择最终的passid，同时去除重复项
    val output = result.filter($"score" >= 0.5 && $"ip_inner_nums" > 8)
      .select($"passid")
      .dropDuplicates()

    // 创建临时视图
    output.createOrReplaceTempView("tmp_passid")

    // 写入黑名单表
    spark.sql(
      s"""
         |insert overwrite table udw_ns.default.help_ods_risk_black_id_day
         |partition (event_day='${dt_yd}')
         |select
         |    passid,
         |    'N_B_ALN' as tag,
         |    'passid',
         |    '5680',
         |    '1',
         |    unix_timestamp('${dt_td}','yyyyMMdd'),
         |    unix_timestamp('${dt_90d}','yyyyMMdd'),
         |    '-1',
         |    'zimai_zimai',
         |    'UT'
         |from tmp_passid
         """.stripMargin)

    spark.close()
  }
}
