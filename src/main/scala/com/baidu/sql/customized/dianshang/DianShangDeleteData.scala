package com.baidu.sql.customized.dianshang

import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

object DianShangDeleteData {


  /**
   * 获取电商赔付udw表现有的数据，将需要删除的数据删除
   */
  def main(args: Array[String]): Unit = {
    // 分区时间
    val yesterDay: String = args(0)
    //第二个参数，将除了此渠道的数据，数仓数据读取需要的数据覆盖原分区
    val tableName = args(1) match {
      //电商底表
      case "risk_event" => "udw_ns.default.help_ods_risk_event"
      //电商黑名单表
      case "risk_black" => "udw_ns.default.help_ods_risk_black_id_day"
    }

    //sparkSession配置
    val sparkConf = new SparkConf().setAppName("udw_overwrite")
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    val sc = spark.sparkContext
    sc.setLogLevel("WARN")

    println(s"开始删除${tableName}表下的${yesterDay}分区数据：")

    if (tableName.equals("udw_ns.default.help_ods_risk_event")) {
      for (event_hour <- 0 to 23) {
        spark.sql(s"ALTER TABLE ${tableName} DROP IF EXISTS PARTITION (event_day = " + yesterDay+ ", event_hour = " + event_hour.toString + ")")
      }
    }else{
      spark.sql(s"ALTER TABLE ${tableName} DROP IF EXISTS PARTITION (event_day = " + yesterDay+ " )")
    }
  }
}
