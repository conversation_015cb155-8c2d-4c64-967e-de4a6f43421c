package com.baidu.sql.customized

import com.baidu.sql.utils.CommonUtils
import com.baidu.sql.utils.TimeOperateUtil._
import com.baidu.sql.utils.TimeFormat._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}

import scala.collection.mutable.ListBuffer

/**
 * MIS干预数据导出（afs上txt文件）
 */
object MisDataFilter {
  //运行日期
  var YesterDay: String = ""
  //读取类型，全量till还是增量increase
  var readType:String = ""

  val filterUrl = Array(
    "101jk.com",
    "10city.net",
    "189pp.com",
    "345mm.com",
    "350pp.com",
    "365azw.com",
    "8x8m.com",
    "99nv.net",
    "chengdutv.com",
    "chinatopbrands.org",
    "cpirc.org.cn",
    "digish.net",
    "dlwjfk.com",
    "doctorsky.cn",
    "febay.com.cn",
    "guilinnet.com",
    "haolady.com",
    "ivsky.com",
    "jkzl.org",
    "jwb.com.cn",
    "kt250.com",
    "kzzj023.com",
    "mm099.com",
    "muzhiwan.com",
    "nvsheng.com",
    "pksex.cn",
    "sportscn.com",
    "tibetonline.net",
    "tupianworld.net",
    "yoti.cn",
    "zcuu.com"
  )

  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)
    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    //读取数据类型，全量还是增量
    readType = args(1)
    println(s"读取数据类型：${readType}")
    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    //site_mask_new_total,suited_website_total,url_shield_new_total

    //定义读取和写入的hdfs文件名，读取站点和URL
    val fileNames = Seq("site_mask_new","url_shield_new","suited_website")
    //val fileNames = Seq("site_mask_new")

    for (fileName <- fileNames) {
      val colName = fileName match {
        case "suited_website" => "Content"
        case "site_mask_new" => "Site"
        case "url_shield_new" => "URL"
      }

      val df = readAfs(spark,fileName).repartition(20)
      //val df = readCsvData(spark,"site_mask_new_till_20240723.txt")
      println(s"读取hdfs数据文件${fileName}共："+ df.count())

      if (df.count() != 0) {
        val filterCondition = filterUrl.map(url => col(colName).contains(url)).reduce(_ || _)
        val filterDf = df.filter(filterCondition)
        writeAfsData(filterDf,fileName)
      }
    }

    // 停止SparkSession
    spark.stop()
  }

  /**
   * Mis数据操作过于频繁
   *
   * @param dataFrame 传入的数据
   * @param dataType  统计数据维度：主域，站点，URL
   * @param dataDate  统计时间维度：1天内，7天内
   */
  def operationFrequentData(spark: SparkSession, dataFrame: DataFrame, dataType: String, dataDate: String): DataFrame = {

    //统计数据维度和需要输出的字段
    val dataTypeToInfo = Map(
      "主域" -> ("Content", Seq("ID", "Content","TypeVal", "DataType","User", "IntoDbTime", "MaskTime", "DelTime", "Comment","TimeDuration").map(elem => s"t.$elem").mkString(",")),
      "站点" -> ("Site", Seq("ID", "Site", "TypeVal", "DataType", "User","FunctionStyle", "IntoDbTime", "ShieldCircle", "DelTime", "Comment","TimeDuration").map(elem => s"t.$elem").mkString(",")),
      "URL" -> ("URL", Seq("ID", "URL", "TypeVal", "DataType","User", "IntoDbTime","ShieldCircle", "ShieldSource", "DelTime", "ShieldStyle", "Note","TimeDuration").map(elem => s"t.$elem").mkString(","))
    )

    //统计日期维度，1天内：满足条件的数量>=3;7天内满足条件的数量>=4
    val dayConditionInfo = Map(
      "1" -> ("3", "= t1.event_day"),
      "7" -> ("4", "BETWEEN date_sub(t1.event_day,6) AND t1.event_day")
    )

    val (colDataType, colName) = dataTypeToInfo.getOrElse(dataType, "")
    val (dayNum, dayCondition) = dayConditionInfo.getOrElse(dataDate, "")

    //对满足条件的数据进行过滤
    val res = dataFrame
      .filter(col("TypeVal") === "add" or col("TypeVal") === "delete")
      .withColumn("event_day", date_format(col("IntoDbTime"), "yyyy-MM-dd"))
      //统计时间区间
      .withColumn("TimeDuration",
        when(lit(dataDate) === "1",col("event_day"))
          .otherwise(concat(date_sub(col("event_day"),6),lit(" - "),col("event_day")))
      )
    res.createOrReplaceTempView("tempTable")

    val sqlResult =
      s"""
         | select
         |     t1_${colDataType} as ${colDataType},
         |     t1_event_day as event_day,
         |     count(*) as value_7_days
         | from
         |    (SELECT
         |        t1.${colDataType} as t1_${colDataType},
         |        t1.event_day as t1_event_day,
         |        t2.event_day as t2_event_day,
         |        t2.TypeVal as TypeVal
         |     FROM
         |         tempTable t1
         |     INNER JOIN
         |         tempTable t2 ON t1.${colDataType} = t2.${colDataType}
         |     WHERE t2.event_day ${dayCondition}
         |     GROUP BY t1.${colDataType},t1.event_day,t2.event_day,t2.TypeVal
         |     ) t
         |  group by t1_${colDataType},t1_event_day
         |  having count(*) >= ${dayNum}
         |  order by t1_event_day
         |
         |""".stripMargin

    val df = spark.sql(sqlResult)
    //df.show(10,false)
    df.repartition(20).createOrReplaceTempView("resTable")

    //输出最后符合条件的数据
    var resDf = spark.sql(
      s"""
         |select
         |   ${colName}
         |from resTable r
         |inner join tempTable t
         |on r.event_day = t.event_day
         |and r.${colDataType} = t.${colDataType}
         |""".stripMargin
    ) //时间字段空的是null， 字符串字段空的是 无
      .withColumn("IntoDbTime", when(col("IntoDbTime") === " " || col("IntoDbTime") === "", null).otherwise(col("IntoDbTime")))
      .withColumn("DelTime", when(col("DelTime") === " " || col("DelTime") === "", null).otherwise(col("DelTime")))

    var resultDf: DataFrame = null
    if (dataType.equals("主域") ) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "replace(Content, '*.', '') as url",
        "User as user",
        "MaskTime as shield_circle",
        "'无' as shield_source ",
        "'无' as shield_style",
        "Comment as remark",
        "'主域' as shield_data_type",
        "DataType as data_type",
        "'操作过于频繁' as abnormal_reason"
      )
    } else if (dataType.equals("站点") ) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "Site as url",
        "User as user",
        "TypeVal as abnormal_operation_type",
        "ShieldCircle as shield_circle",
        "FunctionStyle as effectiveness_type",
        "IntoDbTime as insert_time",
        "DelTime as release_time",
        "'无' as shield_source ",
        "'无' as shield_style",
        "Comment as remark",
        "'站点' as shield_data_type",
        "DataType as data_type",
        "'操作过于频繁' as abnormal_reason"
      )
    } else if (dataType.equals("URL")) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "URL as url",
        "User as user",
        "TypeVal as abnormal_operation_type",
        "ShieldCircle as shield_circle",
        "'无' as effectiveness_type",
        "ShieldSource as shield_source ",
        "ShieldStyle as shield_style",
        "IntoDbTime as insert_time",
        "DelTime as release_time",
        "Note as remark",
        "'URL' as shield_data_type",
        "DataType as data_type",
        "'操作过于频繁' as abnormal_reason"
      )
    }
    println(s"${dataType}数据操作频繁共："+ resultDf.count())
    //加入写入时间后返回
    resultDf
      .withColumn("create_time",lit(getCurrentTimeStr(SEC_FORMAT_MYSQL)).cast("timestamp"))
      .withColumn("update_time",lit(getCurrentTimeStr(SEC_FORMAT_MYSQL)).cast("timestamp"))
      .dropDuplicates()
  }

  /**
   * 自定义测试数据集
   * @param spark
   * @return
   */
  def readTestData(spark: SparkSession): DataFrame = {
    // 定义DataFrame的模式
    val schema = StructType(Array(
      StructField("ID", StringType, true),
      StructField("URL", StringType, true),
      StructField("TypeVal", StringType, true),
      StructField("Site", StringType, true),
      StructField("Content", StringType, true),
      StructField("IntoDbTime", StringType, true)
    ))

    // 创建一些示例数据
    val data = Seq(
      Row("1002","http://www.pc6.com/az/830555.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:08"),
      Row("1010","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:10"),
      Row("1010","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:15"),
      Row("1009","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-09 00:00:00"),
      Row("1008","http://91pmpc6.com/?ch=ocki1cy", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-10 00:00:00"),
      Row("1001","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "delete", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1001","http://m.pc6.com/s/499066", "add", "http://m.pc6.com", "*.pc6.com","2024-08-04 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:20"),
      Row("1007","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-03 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1005","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-02 00:00:00"),
      Row("1004","http://570pc6.com/", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00")
      // 添加更多数据行...

    )
     // 使用模式和数据创建DataFrame
    spark.createDataFrame(spark.sparkContext.parallelize(data), schema)
  }

  /**
   * 读取AFS格式的数据
   * @param spark
   * @param fileName 读取的文件名
   * @return
   */
  def readAfs(spark: SparkSession, fileName: String): DataFrame = {
    var res :DataFrame = null
    //判断是全量还是增量,till为全量，其他为增量
    if (readType.equals("till")) {
      //全量数据
      res = spark.read.json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/${fileName}*.txt").dropDuplicates()
    }else{
      val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
      //增量数据
      for (i <- 1 to 7) {
        //减日期
        val subtractDate = calcnDate(YesterDay,-i)
        val resDf = spark.read
          .json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/${fileName}_${subtractDate}.txt")
          .dropDuplicates()
        if (resDf.count() > 0){
          resultDf.append(resDf)
        }
      }
      res = if (resultDf.isEmpty) {
        // 如果resultDf为空，你可以根据需求返回一个空的DataFrame或者抛出异常等
        spark.emptyDataFrame
      } else {
        resultDf.reduce((df1, df2) => df1.union(df2))
      }
    }
    res
  }

  /**
   * 读取txt格式的数据
   * @param spark
   * @param fileName
   * @return
   */
  def readCsvData(spark: SparkSession,fileName:String): DataFrame = {
    //根据ID是最新状态的数据 /Users/<USER>/Desktop/zrj_files/MIS数据/url_shield_new_total.csv
    //没有根据ID过滤去重文件 /Users/<USER>/Desktop/zrj_files/python/csvToExcel/
    //文件名 site_mask_new_total,suited_website_total,url_shield_new_total
    val filePath = s"/Users/<USER>/Desktop/zrj_files/MIS数据/${fileName}"
    spark.read
      //.option("header", "true")
      .json(filePath)
  }

  /**
   * 处理读取的数据进行判断
   * @param spark
   * @param df
   * @return
   */
  def dealData(spark: SparkSession,df:DataFrame,filename:String): DataFrame = {

    val dataType =  filename match {
      case "site_mask_new" => "站点"
      case "suited_website" => "主域"
      case "url_shield_new" => "URL"
    }
    println("统计1天内")
    val result1 = operationFrequentData(spark,df,dataType,"1")
    println("统计7天内")
    val result2 = operationFrequentData(spark,df,dataType,"7")
    result1.unionByName(result2)
  }

  /**
   * 写入本地csv格式文件
   * @param df
   * @param fileName
   */
  def writeLocalData(df:DataFrame,fileName:String): Unit = {
    df
      .repartition(1)
      .write
      .option("header","true")
      .mode(SaveMode.Overwrite)
      .csv(s"/Users/<USER>/Desktop/zrj_files/MIS数据/result_${fileName}")
  }

  /**
   * 写入afs上csv格式文件
   * @param df
   * @param fileName
   */
  def writeAfsData( df:DataFrame, fileName:String): Unit = {
    //先判断是否存在目标文件
    val existPath = s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/misresult/result_${fileName}_${YesterDay}"
    //CommonUtils.checkFileExists(hadoopConf,existPath)

    //写入AFS
    df
      .repartition(1)
      .write
      .option("header","true")
      .mode(SaveMode.Overwrite)
      .csv(existPath)
  }

}
