package com.baidu.sql.customized

import com.baidu.sql.customized.keyproduct.commons.MysqlConf
import com.baidu.sql.utils.TimeFormat.SEC_FORMAT_MYSQL
import com.baidu.sql.utils.{CommonUtils, PropertiesUtils}
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}

import scala.collection.mutable.ListBuffer

/**
 * MIS干预数据导出7天内数据（afs上txt文件）
 */
object MisDataQuery7Days {
  //运行日期
  var YesterDay: String = ""
  var ToDay: String = ""
  //读取类型，全量till还是增量increase
  var readType:String = ""

  //统计数据维度和需要输出的字段
  val dataTypeToInfo = Map(
    "主域" ->  Seq( "ID", "TypeVal","StatusCode", "Content", "DataType", "User", "MaskTime", "ReviewUser", "Comment", "IntoDbTime", "DelUser", "DelReviewUser", "DelNote", "DelTime", "AddAuditTime", "DelAuditTime", "IsDeleted"),
    "站点" ->  Seq("ID","TypeVal", "StatusCode", "Site", "IntoDbTime", "FunctionStyle", "User", "DelUser", "DelNote", "ReviewUser", "ShieldCircle", "Comment", "DataType", "DelReviewUser", "DelTime", "AddAuditTime", "DelAuditTime", "IsDeleted", "CmdID"),
    "URL" -> Seq("ID","TypeVal", "State", "URL","URLPrefix", "DataType", "User", "IntoDbTime", "ShieldCircle", "ReviewUser", "ShieldSource", "ShieldStyle", "Note", "DelReviewUser", "DelNote", "DelUser", "DelTime", "AddAuditTime", "DelAuditTime", "IsDeleted", "CmdID"),
    "Query" -> Seq("ID","TypeVal", "StatusCode", "Query", "URL", "FunctionStyle", "DataType", "User", "IntoDbTime", "ShieldCircle", "ReviewUser", "Note", "DelUser", "DelReviewUser", "DelNote", "DelTime", "AddAuditTime", "DelAuditTime", "IsDeleted")
  )

  def main(args: Array[String]): Unit = {
    // 分区时间
    YesterDay = args(0)
    //当天时间
    ToDay = calcnDate(YesterDay,1)
    // 创建一个SparkSession
    val spark: SparkSession = SparkSession.builder()
      .appName("Create DataFrame Example")
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      //.master("local[*]")
      .getOrCreate()

    //读取数据类型，全量还是增量
    readType = args(1)
    println(s"读取数据类型：${readType}")
    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    //定义读取和写入的hdfs文件名
    val fileNames = Seq("site_mask_new","suited_website","query_url","url_shield_new")
    //val fileNames = Seq("url_shield_new")

    //写入线上数据库数据
    //val properties = PropertiesUtils.MysqlALaDingProperties
    val properties = PropertiesUtils.MysqlALaDingOnlineProperties
    properties.setProperty("driver", MysqlConf.Driver)
    properties.setProperty("characterEncoding", "UTF-8")
    properties.setProperty("connection.characterEncoding", "UTF-8")
    properties.setProperty("charset", "utf8mb4")

    for (fileName <- fileNames) {
      val mysqlTable = fileName match {
        case "site_mask_new" => "t_search_mis_site_mask"
        case "url_shield_new" => "t_search_mis_url_shield"
        case "suited_website" => "t_search_mis_suited_website"
        case "query_url" => "t_search_mis_query_url"
      }

      val df = readAfs(spark,fileName).repartition(20)
      //val df = readCsvData(spark,"url_shield_new_20241214.txt")
      println(s"读取hdfs数据文件${fileName}共："+ df.count())
      //df.groupBy("TypeVal").count().show()

      if (df.count() != 0) {
        //处理数据
        val result = dealData(spark,df,fileName)
        println(result.count())

        val deleteSql = s"delete from ${mysqlTable} where event_day >= ${YesterDay} "
        //将当天的数据先删除再写入
        CommonUtils.deleteMysql(properties,deleteSql)
        println(s"删除历史${YesterDay}数据成功")

        //mysql是覆盖写入
        result
          .repartition(2)
          .write
          .mode(SaveMode.Append)
          .jdbc(properties.getProperty("url"), mysqlTable, properties)
      }
    }

    // 停止SparkSession
    spark.stop()
  }

  /**
   * Mis数据处理
   *
   * @param dataFrame 传入的数据
   * @param dataType  统计数据维度：主域，站点，URL
   */
  def operationFrequentData(spark: SparkSession, dataFrame: DataFrame, dataType: String): DataFrame = {

    val colName = dataTypeToInfo.get(dataType).get
    dataFrame.createOrReplaceTempView(s"res_data")

    //输出最后符合条件的数据
    var resDf = spark.sql(
        s"""
           |select
           |   ${colName.map(elem => s"$elem").mkString(",")}
           |from res_data
           |""".stripMargin
      ) //时间字段空的是null， 字符串字段空的是 无
      .withColumn("IntoDbTime", when(col("IntoDbTime") === " " || col("IntoDbTime") === "--", null).otherwise(col("IntoDbTime")))
      .withColumn("DelTime", when(col("DelTime") === " " || col("DelTime") === "--", null).otherwise(col("DelTime")))

    var resultDf: DataFrame = null
    if (dataType.equals("主域") ) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "TypeVal as type_val",
        "StatusCode as status_code",
        "Content as content",
        "DataType as data_type",
        "User as user",
        "MaskTime as mask_time",
        "ReviewUser as review_user",
        "Comment as comment",
        "IntoDbTime as intodb_time",
        "DelUser as del_user",
        "DelReviewUser as del_review_user",
        "DelNote as del_note",
        "DelTime as del_time",
        "AddAuditTime as add_audit_time",
        "DelAuditTime as del_audit_time",
        "IsDeleted as is_deleted"
      )
        .withColumn("del_time",when(col("del_time") === "--" || col("del_time") === "",null).otherwise(col("del_time")))

    } else if (dataType.equals("站点") ) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "TypeVal as type_val",
        "StatusCode as status_code",
        "Site as site",
        "IntoDbTime as intodb_time",
        "FunctionStyle as function_style",
        "User as user",
        "ReviewUser as review_user",
        "ShieldCircle as shield_circle",
        "Comment as comment",
        "DataType as data_type",
        "DelReviewUser as del_review_user",
        "DelNote as del_note",
        "DelUser as del_user",
        "DelTime as del_time",
        "AddAuditTime as add_audit_time",
        "DelAuditTime as del_audit_time",
        "IsDeleted as is_deleted",
        "CmdID as cmd_id"
      ).withColumn("cmd_id",when(col("cmd_id") === "--" || col("cmd_id") === "","0").otherwise(col("cmd_id")))
      .withColumn("del_time",when(col("del_time") === "--" || col("del_time") === "",null).otherwise(col("del_time")))

    } else if (dataType.equals("URL")) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "TypeVal as type_val",
        "State as state",
        "URL as url",
        "URLPrefix as url_prefix",
        "DataType as data_type",
        "User as user",
        "IntoDbTime as intodb_time",
        "ShieldCircle as shield_circle",
        "ReviewUser as review_user",
        "ShieldSource as shield_source",
        "ShieldStyle as shield_style",
        "Note as note",
        "DelReviewUser as del_review_user",
        "DelNote as del_note",
        "DelUser as del_user",
        "DelTime as del_time",
        "AddAuditTime as add_audit_time",
        "DelAuditTime as del_audit_time",
        "IsDeleted as is_deleted",
        "CmdID as cmd_id"
      )
        .withColumn("cmd_id",when(col("cmd_id") === "--" || col("cmd_id") === "","0").otherwise(col("cmd_id")))
        .withColumn("del_time",when(col("del_time") === "--" || col("del_time") === "",null).otherwise(col("del_time")))

    }else if (dataType.equals("Query")) {
      resultDf = resDf.selectExpr(
        "ID as mis_id",
        "TypeVal as type_val",
        "StatusCode as status_code",
        "Query as query",
        "URL as url",
        "IntoDbTime as intodb_time",
        "FunctionStyle as function_style",
        "User as user",
        "ReviewUser as review_user",
        "ShieldCircle as shield_circle",
        "Note as note",
        "DataType as data_type",
        "DelReviewUser as del_review_user",
        "DelNote as del_note",
        "DelUser as del_user",
        "DelTime as del_time",
        "AddAuditTime as add_audit_time",
        "DelAuditTime as del_audit_time",
        "IsDeleted as is_deleted"
      )
        .withColumn("del_time",when(col("del_time") === "--" || col("del_time") === "","0000-00-00 00:00:00").otherwise(col("del_time")))

    }
    //加入写入时间后返回
    resultDf
      .withColumn("review_user",when(col("review_user") === "--" || col("review_user") === "","").otherwise(col("review_user")))
      .withColumn("del_review_user",when(col("del_review_user") === "--" || col("del_review_user") === "","").otherwise(col("del_review_user")))
      .withColumn("del_note",when(col("del_note") === "--" || col("del_note") === "","").otherwise(col("del_note")))
      .withColumn("del_user",when(col("del_user") === "--" || col("del_user") === "","").otherwise(col("del_user")))
      //.withColumn("del_time",when(col("del_time") === "--" || col("del_time") === "",null).otherwise(col("del_time")))
      .withColumn("add_audit_time", when(col("add_audit_time") === " " || col("add_audit_time") === "--", null).otherwise(col("add_audit_time")))
      .withColumn("del_audit_time", when(col("del_audit_time") === " " || col("del_audit_time") === "--", null).otherwise(col("del_audit_time")))
      .withColumn("create_time",lit(getCurrentTimeStr(SEC_FORMAT_MYSQL)).cast("timestamp"))
      .withColumn("update_time",lit(getCurrentTimeStr(SEC_FORMAT_MYSQL)).cast("timestamp"))
      .withColumn("event_day",lit(ToDay).cast("string"))
      .dropDuplicates()
  }

  /**
   * 自定义测试数据集
   * @param spark
   * @return
   */
  def readTestData(spark: SparkSession): DataFrame = {
    // 定义DataFrame的模式
    val schema = StructType(Array(
      StructField("ID", StringType, true),
      StructField("URL", StringType, true),
      StructField("TypeVal", StringType, true),
      StructField("Site", StringType, true),
      StructField("Content", StringType, true),
      StructField("IntoDbTime", StringType, true)
    ))

    // 创建一些示例数据
    val data = Seq(
      Row("1002","http://www.pc6.com/az/830555.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:08"),
      Row("1010","http://www.pc6.com/az/1042529.html", "add", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:10"),
      Row("1010","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-10 07:00:15"),
      Row("1009","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-09 00:00:00"),
      Row("1008","http://91pmpc6.com/?ch=ocki1cy", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-10 00:00:00"),
      Row("1001","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-10 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "delete", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1001","http://m.pc6.com/s/499066", "add", "http://m.pc6.com", "*.pc6.com","2024-08-04 00:00:00"),
      Row("1003","http://www.pc6.com/az/1042529.html", "delete", "http://www.pc6.com", "*.pc6.com","2024-08-11 07:00:20"),
      Row("1007","http://m.pc6.com/s/917041", "add", "http://m.pc6.com", "*.pc6.com","2024-08-03 00:00:00"),
      Row("1006","http://91pmpc6.com/?ch=ciyself01", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00"),
      Row("1005","http://m.pc6.com/s/1357412", "add", "http://m.pc6.com", "*.pc6.com","2024-08-02 00:00:00"),
      Row("1004","http://570pc6.com/", "add", "http://91pmpc6.com", "*.91pmpc6.com","2024-08-06 00:00:00")
      // 添加更多数据行...

    )
     // 使用模式和数据创建DataFrame
    spark.createDataFrame(spark.sparkContext.parallelize(data), schema)
  }

  /**
   * 读取AFS格式的数据
   * @param spark
   * @param fileName 读取的文件名
   * @return
   */
  def readAfs(spark: SparkSession, fileName: String): DataFrame = {
    var res :DataFrame = null
    //判断是全量还是增量,till为全量，其他为增量
    if (readType.equals("till")) {
      //全量数据
      res = spark.read.json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/${fileName}*.txt").dropDuplicates()
    }else{
      val resultDf: ListBuffer[DataFrame] = ListBuffer[DataFrame]()
      val dataType = fileName match {
        case "site_mask_new" => "站点"
        case "suited_website" => "主域"
        case "url_shield_new" => "URL"
        case "query_url" => "Query"
      }
      val colName = dataTypeToInfo.get(dataType).get

      //增量数据
      for (i <- 1 to 7) {
        //减日期
        val subtractDate = calcnDate(ToDay,-i)
        try {
          val resDf = spark.read
            .json(s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/${fileName}_${subtractDate}.txt")
            .dropDuplicates()
          if (resDf.count() > 0){
            resDf.createOrReplaceTempView(s"${fileName}_${subtractDate}")
            val hiveSql = s"""select ${colName.mkString(",")} from ${fileName}_${subtractDate}""".stripMargin
            //println(hiveSql)
            val filterDf = spark.sql(hiveSql)
            filterDf.count()
            resultDf.append(filterDf)
          }
        } catch {
          case e:Exception => println(s"${fileName}_${subtractDate}文件不存在," + e.getMessage)
        }
      }
      res = if (resultDf.isEmpty) {
        // 如果resultDf为空，你可以根据需求返回一个空的DataFrame或者抛出异常等
        spark.emptyDataFrame
      } else {
        resultDf.reduce((df1, df2) => df1.union(df2))
      }
    }
    res
  }

  /**
   * 读取txt格式的数据
   * @param spark
   * @param fileName
   * @return
   */
  def readCsvData(spark: SparkSession,fileName:String): DataFrame = {
    //根据ID是最新状态的数据 /Users/<USER>/Desktop/zrj_files/MIS数据/url_shield_new_total.csv
    //没有根据ID过滤去重文件 /Users/<USER>/Desktop/zrj_files/python/csvToExcel/
    //文件名 site_mask_new_total,suited_website_total,url_shield_new_total
    val filePath = s"/Users/<USER>/Desktop/zrj_files/MIS数据/${fileName}"
    spark.read
      //.option("header", "true")
      .json(filePath)
  }

  /**
   * 处理读取的数据进行判断
   * @param spark
   * @param df
   * @return
   */
  def dealData(spark: SparkSession,df:DataFrame,filename:String): DataFrame = {

    val dataType =  filename match {
      case "site_mask_new" => "站点"
      case "suited_website" => "主域"
      case "url_shield_new" => "URL"
      case "query_url" => "Query"
    }
    operationFrequentData(spark,df,dataType)
  }

  /**
   * 写入本地csv格式文件
   * @param df
   * @param fileName
   */
  def writeLocalData(df:DataFrame,fileName:String): Unit = {
    df
      .repartition(1)
      .write
      .option("header","true")
      .mode(SaveMode.Overwrite)
      .csv(s"/Users/<USER>/Desktop/zrj_files/MIS数据/result_${fileName}")
  }

  /**
   * 写入afs上csv格式文件
   * @param df
   * @param fileName
   */
  def writeAfsData( df:DataFrame, fileName:String): Unit = {
    //先判断是否存在目标文件
    val existPath = s"afs://pegasus.afs.baidu.com:9902/user/baisheng/search/misanti/misresult/result_${fileName}_${YesterDay}"
    //CommonUtils.checkFileExists(hadoopConf,existPath)

    //写入AFS
    df
      .repartition(1)
      .write
      .option("header","true")
      .mode(SaveMode.Overwrite)
      .csv(existPath)
  }

}
