package com.baidu.sql.customized

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 * @date date 2023/8/23
 * @time 16:14
 * @package_name com.baidu.sql.lib
 */
object BlackListQuiry {
  def main(args: Array[String]): Unit = {

    // 填写传入的csv文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)

    // 获取程序启动时间
    val startTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)
      .set("spark.jars", "/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")
      .setAppName(sql_name)
      //本地运行测试
      //.setMaster("local")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    val url: String = JDBCUtils.url
    val user: String = JDBCUtils.user
    val password: String = JDBCUtils.password
    val driver: String = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)


    // 读取现有的黑名单文件
    //直接从目录下读取CSV文件
    val csvData: DataFrame = spark.read.format("csv")
      .option("header", "true")
      .load("file:///home/<USER>/bigdata_sql_executor/"+sql_file)

    //建立视图
    csvData.createOrReplaceTempView("black_list_view")

    //展示临时视图
    spark.sql("SELECT * FROM black_list_view").show()
    //刷新表数据
    //spark.sql("REFRESH TABLE udw_ns.default.help_ods_shop_huaxiang_fengkong_wide_df")
    // 查询画像表中相应字段
    val wideTable: DataFrame = spark.sql(
      """
        |SELECT
        |shop_id,
        |legal_number_rcopaudit,
        |legal_name_rcopaudit,
        |org_code_rcopaudit
        |FROM udw_ns.default.help_ods_shop_huaxiang_fengkong_wide_df
        |WHERE event_day = '20220819'
        |""".stripMargin)
    //建立hive表临时视图
    wideTable.createOrReplaceTempView("wide_table_view")
    //打印hive表临时视图
    spark.sql("SELECT * FROM wide_table_view limit 20").show()
    //现有黑名单 left join 店铺画像维度表  关联字段  shop_id
    //val result: DataFrame = csvData.join(wideTable, Seq("shop_id"), "left")
    val result : DataFrame = spark.sql(
      """
        |select
        |t1.shop_id,
        |t1.shop_name,
        |t1.clear_reason,
        |t1.begin_time,
        |t1.close_time,
        |t1.uc_id,
        |t1.first_catalog,
        |t1.second_catalog,
        |t1.main_catalog,
        |t1.url,
        |t1.shop_type,
        |t1.shop_status,
        |t1.company_entity
        |t2.legal_number_rcopaudit,
        |t2.legal_name_rcopaudit,
        |t2.org_code_rcopaudit
        |from
        |(
        |select *
        |from black_list_view
        |) t1
        |left join
        |(
        |select *
        |from wide_table_view
        |) t2
        |on t1.shop_id = t2.shop_id
        |""".stripMargin)
    // 将每条数据转换为 JSON
    val jsonDf: DataFrame = result.limit(100).selectExpr("to_json(struct(*)) AS data")

    // 获取程序结束时间
    val endTime: Long = System.currentTimeMillis()

    // 将程序启动时间，结束时间，还有字段描述，sql文件名加入DataFrame
    val res: DataFrame = jsonDf.withColumn("start_time", lit(formatter.format(new Date(startTime))).cast("string"))
      .withColumn("end_time", lit(formatter.format(new Date(endTime))).cast("string"))
      .withColumn("sql_file", lit(sql_file).cast("string"))
      .withColumn("desc", lit(sql_desc).cast("string"))
    // 打印Schema
    res.printSchema()

    // 将数据写入最终落地表
    res.repartition(50).write.option("truncate", value = false)
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    spark.close()
  }

}
