package com.baidu.sql.lib

import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

import java.net.URI

object Sink2AfsTest {
  def main(args: Array[String]): Unit = {
    val conf = new SparkConf()
      .setAppName("SearchData2Afs")

    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      //      .master("local")
      .enableHiveSupport()
      .getOrCreate()
    spark.sparkContext.hadoopConfiguration.set("hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")

    val fs = FileSystem.get(new URI("afs://pegasus.afs.baidu.com:9902"), spark.sparkContext.hadoopConfiguration)
    var json = "nihao, hahhaha"
    // afs输出路径
    val outputPath = "afs://pegasus.afs.baidu.com:9902/user/baisheng/tethysEngine/profile/detail/"
    val outputAfsPath = new Path(outputPath, s"test.txt")
    // 设置为true 如果有数据覆盖掉
    val outputStream = fs.create(outputAfsPath)
    outputStream.write(json.getBytes("UTF-8"))
    outputStream.close()
  }
}
