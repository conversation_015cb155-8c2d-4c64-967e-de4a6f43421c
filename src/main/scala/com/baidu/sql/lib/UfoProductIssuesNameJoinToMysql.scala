package com.baidu.sql.lib

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql._

import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 */
object UfoProductIssuesNameJoinToMysql {
  def main(args: Array[String]): Unit = {
    //读取udw数据、匹配名称数据 =》每个问题级别的中文映射分别与udw数据join（使用空间ID和问题ID两个字段join）
    // sql文件名
    val sql_file: String = args(0)
    // 日期
    val event_day: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)

    // 获取程序启动时间
    val startTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)
      .set("spark.jars", "/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")
      .setAppName(sql_name)

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    // 获取mysql的配置
    val url: String = JDBCUtils.url
    val user: String = JDBCUtils.user
    val password: String = JDBCUtils.password
    val driver: String = JDBCUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)


    //读取udw底表数据
//    val udwDf: DataFrame = spark.sql(
//      s"""
//        |select
//        |*
//        |from
//        |udw_ns.default.help_ods_ufo_key_point_productline_di
//        |where event_day <= $event_day
//        |""".stripMargin)

    // 读取映射中文字段信息
    val csvResourcePath = getClass.getResourceAsStream("/sqlfiles/ll/ufo/product_issues_name.csv")
    var lines: Iterator[String] = scala.io.Source.fromInputStream(csvResourcePath).getLines()

    //将数据流转换为dataset
    val linesDS: Dataset[String] = spark.createDataset(lines.toSeq)(Encoders.kryo[String])

    //中文字段信息
    val csvDf = spark.read
      .option("header", "true")
      .option("inferSchema", "true")
      .csv(linesDS)

    //将需要join的df转换为临时表，方便处理
//    udwDf.createOrReplaceTempView("udw_temp_view")
    csvDf.createOrReplaceTempView("tmp_csv_temp_view")

    // 读取resources下面的sql文件
    val resourcePath = getClass.getResourceAsStream("/sqlfiles/ll/ufo/" + sql_file)
    var sql: String = scala.io.Source.fromInputStream(resourcePath).mkString
    // 检查文件内容是否包含 LIMIT 500 不包含就添加
    sql = if (!sql.toUpperCase.contains("LIMIT 500")) {
      s"$sql limit 500"
    } else {
      sql
    }

    //拼接join sql
    val result = spark.sql(sql)


    // 将每条数据转换为 JSON
    val jsonDf: DataFrame = result.selectExpr("to_json(struct(*)) AS data")

    // 获取程序结束时间
    val endTime: Long = System.currentTimeMillis()

    // 将程序启动时间，结束时间，还有字段描述，sql文件名加入DataFrame
    val res: DataFrame = jsonDf.withColumn("start_time", lit(formatter.format(new Date(startTime))).cast("string"))
      .withColumn("end_time", lit(formatter.format(new Date(endTime))).cast("string"))
      .withColumn("sql_file", lit(sql_file).cast("string"))
      .withColumn("desc", lit(event_day).cast("string"))
    // 打印Schema
    res.printSchema()

    // 打印数据
    //    res.show(20)

    // 将数据写入最终落地表
    res.repartition(50).write.option("truncate", value = false)
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    spark.close()
  }

}
