package com.baidu.sql.lib

import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.io.InvalidClassException
import java.text.SimpleDateFormat
import java.util.{Date, Properties}


/**
 * <AUTHOR>
 */
object VidioUrlMatchToMysql {
  def main(args: Array[String]): Unit = {
    /*
    * 1.engine_gpt_content_store.resource_id 匹配 udw_ns.default.bjh_feed_resource_rf.nid
    * 2.获取 rmb_self_build_url_http字段
    * 3.拼接engine_gpt_bjh_video_info中所需字段
    * 4.回写engine_gpt_bjh_video_info表
    *
    * mysqlDf ----->
    *                join on resource_id =  nid  ----> resultDf ---> mysql
    * udwDf   ---->
    * */

    // udw源表
    val sourceTable: String = args(0)
    // 最终落地表
    val writeBackTable: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)

    //分区日期
    val event_day: String = "20231204"
    //分区时间
    val event_hour: String = "19"
    //分区日期格式处理
    val timeStr: String = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6, 8) + " " + event_hour
    // 获取程序启动时间
    val creatTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")


    //sparkConf
    val sparkConf = new SparkConf().setAppName("vidio_url_match_to_mysql")
//      .setMaster("local[*]")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      //      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.cores.max", cores)
      .config("spark.task.maxFailures", maxFailures)
      .config("spark.driver.allowMultipleContexts", true)
      // 设置广播连接阈值为100MB
//      .config("spark.sql.autoBroadcastJoinThreshold", "100000000")
      // 禁用排序合并连接
//      .config("spark.sql.join.preferSortMergeJoin", "false")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .enableHiveSupport()
      .getOrCreate()

    //mysql表配置
    val url: String = "jdbc:mysql://*************:8653/tethysEngine?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull" +
      "&noAccessToProcedureBodies=true&useSSL=false&allowPublicKeyRetrieval=true"
    val username: String = "work"
    val password: String = "Asd123"
    val driver: String = "com.mysql.jdbc.Driver"
    val properties = new Properties()
    properties.setProperty("user", username)
    properties.setProperty("password", password)
    val sqlStatement = s"select resource_id from $sourceTable where create_time like '$timeStr%'"

    //构建mysqlDf
    // 从 MySQL 读取数据
    val mysqlDf = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", username)
      .option("password", password)
      .option("query", sqlStatement)
      .load()

    mysqlDf.show(20)
    //生成临时视图
    mysqlDf.createOrReplaceTempView("mysql_view")

    //从udw表中拉取视频url
    val joinDf = spark.sql(
      """
        |select
        |t1.resource_id as nid,
        |t2.rmb_self_build_url_http as video_url
        |from
        |mysql_view t1
        |left join
        |(select
        |nid,
        |rmb_self_build_url_http
        |from
        |udw_ns.default.bjh_feed_resource_rf
        |where event_day = '20231205') t2 on  t1.resource_id = t2.nid """.stripMargin)

    val schemaDf = joinDf
      .withColumn("creator", lit(0))
      .withColumn("updator", lit(0))
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("version", lit(0))
      .withColumn("is_delete", lit(0))
      .withColumn("del_unique_key", lit(0))
      .withColumn("engine_content_id", lit(0))

    schemaDf.createOrReplaceTempView("joined_view")

    val result = spark.sql(
      """
        |select
        |creator,
        |updator,
        |create_time,
        |update_time,
        |version,
        |is_delete,
        |del_unique_key,
        |nid,
        |video_url,
        |engine_content_id
        |from joined_view""".stripMargin)



      result.repartition(50).write
        .mode(SaveMode.Overwrite).jdbc(url, writeBackTable, properties)



  }

}
