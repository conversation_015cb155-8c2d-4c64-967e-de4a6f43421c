package com.baidu.sql.lib

import com.baidu.sql.utils.JDBCHxUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}

import java.util.{Date, Properties}


/**
 * <AUTHOR>
 */
object BottomInquiry {
  def main(args: Array[String]): Unit = {

    // sql文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores : String = args(2)
    // spark.task.maxFailures
    val maxFailures : String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = "tb_get_bottom"


    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc","false")
      .set("spark.sql.hive.convertMetastoreParquet","false")
      .set("spark.cores.max",cores)
      .set("spark.task.maxFailures",maxFailures)
      .set("spark.jars","/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")
      .setAppName(sql_name)

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 获取mysql的配置
    val url: String = JDBCHxUtils.url
    val user: String = JDBCHxUtils.user
    val password: String = JDBCHxUtils.password
    val driver: String = JDBCHxUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver",driver)

    var mSql =
      s"""
         |SELECT
         |   nid
         |FROM tb_nid_getBottom
         |""".stripMargin


    // 获取Mysql数据
    val jdbcDF = spark.read.format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", mSql)
      .load()

    var passIdArray: Array[Row] = jdbcDF.select("nid").collect()
    var allNid = "(" + passIdArray.mkString(",").replaceAll("\\[", "'").replaceAll("\\]", "'") + ")"


    var sql =
      s"""
         |
         |SELECT
         |  nid,
         |  rmb_self_build_url_http,
         |  url,
         |  get_json_object(video_tag_api_result, '$$.location_ocr_result.bottom') as bottom
         |FROM
         |  udw_ns.default.bjh_feed_resource_rf
         |WHERE
         |  event_day = '20231129'
         |  AND nid in ${allNid}
         |""".stripMargin

    val df = spark.sql(sql)


    // 将数据写入最终落地表
    df.repartition(50).write.option("truncate", value = false)
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)


    spark.close()
  }
}
