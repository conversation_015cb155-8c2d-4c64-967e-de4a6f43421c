package com.baidu.sql.lib

import com.baidu.sql.utils.{DevTethysJDBC, MarioJDBC}
import org.apache.log4j.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.util.Properties

/**
 * <AUTHOR>
 */
object HdfJoinJdbcDf {
  def main(args: Array[String]): Unit = {
    val conf = new SparkConf().setAppName("HdfJoinJdbcDf")

    val spark = SparkSession.builder()
      .config(conf)
//      .master("local")
      .enableHiveSupport()
      .getOrCreate()

    val logger = Logger.getLogger(getClass.getName)

    var driver = DevTethysJDBC.driver
    var url = DevTethysJDBC.url
    var username = DevTethysJDBC.user
    var password = DevTethysJDBC.password


    var sourceMysql =
      """
        |select
        |	author_pass_id,
        |	risk_level
        |from(
        |select
        |	author_pass_id,
        |	risk_level,
        |	count(1)
        |from tethys_assess_records
        |group by author_pass_id,risk_level
        |) t1
        |""".stripMargin

    // 查找MySQL的数据
    var jdbcDf = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", username)
      .option("password", password)
      .option("query", sourceMysql).load()

    val properties = new Properties()
    properties.setProperty("driver", driver)
    properties.setProperty("url", url)
    properties.setProperty("user", username)
    properties.setProperty("password", password)

    var hSql =
      """
        |SELECT
        |  user_id,
        |  view_count,
        |  article,
        |  article_id,
        |  article_like_pv,
        |  article_num,
        |  audit_status,
        |  author_name,
        |  author_total_fans,
        |  avatar,
        |  bstatus,
        |  check_type,
        |  collect_pv,
        |  comment_pv,
        |  cover_images,
        |  cstatus,
        |  es_id,
        |  img_str,
        |  insert_time,
        |  interest,
        |  m_content,
        |  nid,
        |  publish_time,
        |  quality_reject_rate,
        |  recommend_count,
        |  reject_reason,
        |  safe_reject_rate,
        |  self_del_rate,
        |  share_pv
        |FROM
        |  udw_ns.default.help_ods_bjh_base_info_df
        |WHERE
        |  event_day = '20231210'
        |  AND (
        |    (
        |      all_first_publish_time > "2023-12-01 22:41:01"
        |      and is_ugc_now = "1"
        |      and is_dongtai_active > "1"
        |      and status in ("unpass", "pass", "audit")
        |    )
        |  )
        |""".stripMargin

    var hdf = spark.sql(hSql)

    logger.info("start hdf: " + hdf.count())

    hdf = hdf.join(jdbcDf, jdbcDf("author_pass_id") === hdf("user_id"), "left_anti")
      .limit(10000)

    logger.info("end hdf: " + hdf.count())

    hdf.write.mode(SaveMode.Overwrite).jdbc(url, "tethys_assess_records_join_hdf", properties)

  }
}
