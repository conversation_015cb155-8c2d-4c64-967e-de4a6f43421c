package com.baidu.sql.lib

import com.baidu.sql.utils.JDBCUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}
import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import okhttp3.{MediaType, OkHttpClient, Request, RequestBody, Response}
import org.apache.commons.codec.digest.DigestUtils

import java.sql.{Connection, DriverManager, Statement}
import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 * @date date 2024/1/24
 * @time 14:53
 * @package_name com.baidu.sql.customized.author
 */
object AuthorUidUdw2Mysql {
  def main(args: Array[String]): Unit = {
    // 日期
    val eventDay = args(0)
    // 环境 online/test
    val environment = args(1)
    // 源表
    val sourceTable = "engine_copyright_bid"
    // 最终落地表
    val writeBackTable = "engine_copyright_uid"

    //时间字符串
    val timeStr: String = eventDay.substring(0, 4) + "-" + eventDay.substring(4, 6) + "-" + eventDay.substring(6, 8)

    // sparkConf
    val sparkConf = new SparkConf().setAppName("AuthorUidUdw2Mysql")

    // sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    // 目标表mysql的配置
    val driver: String = JDBCUtils.driver
    val (url, user, password) = environment match {
      case "online" => (JDBCUtils.strategyUrl, JDBCUtils.strategyUser, JDBCUtils.strategyPassword)
      case "test" => (JDBCUtils.strategyTestUrl, JDBCUtils.strategyTestUser, JDBCUtils.strategyTestPassword)
      case _ => throw new IllegalArgumentException(s"Invalid configType: $environment")
    }

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    //mysql 查询语句
    val sqlStatement =
      s"""
         |select
         |*
         |from $sourceTable
         |where create_time like '$timeStr%'
         |""".stripMargin

    // 读取B端id数据
    val jdbcDF: DataFrame = spark.read
      .format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", sqlStatement)
      .load()
      .selectExpr("bid","task_id")

    val bidDF = jdbcDF.selectExpr("bid")

    //更新表中status状态为1
    updateMysqlData(sourceTable, timeStr, url: String, user, password)

    // 拼接bid in条件字符串
    var bidArr = "( " + bidDF.collect().mkString(",").replaceAll("\\[", "'").replaceAll("\\]", "'") + " )"

    if (bidArr.equals("(  )")) {
      bidArr = "('0')"
    }

    val udwSql =
      s"""
         |select
         |  author_user_id as uid,
         |  author_name as uname,
         |  transform_id
         |from
         |  udw_ns.default.bjh_feed_resource_rf
         |where
         |  event_day = '$eventDay'
         |and transform_id in ${bidArr}
         |""".stripMargin

    println(udwSql)

    //udw表查询
    val udwDF = spark.sql(udwSql)

    // 匹配taskid到udw查出的结果数据
    val resultDF = jdbcDF.join(udwDF, jdbcDF("bid") === udwDF("transform_id"), "left").selectExpr("uid", "uname", "bid", "task_id")

    // mysql结果表数据写入
    resultDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    //taskid获取，存入数组
    val taskIds:Array[String] = resultDF.select("task_id").distinct().rdd.map(row => row(0).toString).collect()

    //更新数据的taskid告知后端
    postRequest(taskIds,environment)
    spark.close()
  }

  //完成数据同步后调用接口告知后端已完成数据同步的taskid
  def postRequest(taskIds: Array[String],environment:String): String = {
    val (url) = environment match {
      case "online" => ("https://tethys.baidu-int.com/api/tethysEngine/copyright/completed")
      case "test" => ("https://*************:8660/api/tethysEngine/copyright/completed")
      case _ => throw new IllegalArgumentException(s"Invalid configType: $environment")
    }

    val body = new JSONObject
    body.put("taskIdList",taskIds)

    var response: Response = null
    val sysName = "copyright"
    val token = "sefrgdfwergjf"
    val timestamp = System.currentTimeMillis
    val sign = DigestUtils.md5Hex(sysName + timestamp + token)

    val requestBody = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON
      .toString), body.toString())
    val okHttpClient = new OkHttpClient
    val request = new Request.Builder().url(url).addHeader("SYS-NAME", "copyright").addHeader("SYS-CALL-TIME", s"""$timestamp""").addHeader("SYS-SIGN", s"""$sign""").post(requestBody).build
    try {
      response = okHttpClient.newCall(request).execute
      if (response.isSuccessful) {
        response.body.string
      } else {
        "Empty response"
      }
    } catch {
      case _: Exception =>
        "Empty response"
    } finally if (response != null) response.close()
  }

  def updateMysqlData(sourceTable: String,
                      timeStr: String,
                      url: String,
                      username: String,
                      password: String): Unit = {
    var connection: Connection = null
    var statement: Statement = null

    try {
      //获取连接
      connection = DriverManager.getConnection(url, username, password)
      statement = connection.createStatement()
      //sql语句
      var sql =
        s"""
           |UPDATE
           | ${sourceTable}
           | SET status = 1
           |WHERE create_time  LIKE "$timeStr%"
           |""".stripMargin
      //打印sql
      println(sql)
      val rowsAffected: Int = statement.executeUpdate(sql)
      println("更新条数为：" + rowsAffected)
    } finally {
      // 关闭 statement 和 connection
      if (statement != null) {
        statement.close()
      }
      if (connection != null) {
        connection.close()
      }
    }
  }


}
