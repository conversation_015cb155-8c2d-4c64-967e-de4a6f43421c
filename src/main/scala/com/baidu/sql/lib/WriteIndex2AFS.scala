package com.baidu.sql.lib

import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

import java.text.SimpleDateFormat
import java.util.Calendar

/**
 * <AUTHOR>
 * @date 2023-08-03
 * @description
 */
object WriteIndex2AFS {

  def main(args: Array[String]): Unit = {

    val conf = new SparkConf()
    val spark: SparkSession = SparkSession
      .builder
      .config(conf)
      .appName("help_write_index_2_afs")
      .getOrCreate()

    spark.sparkContext.hadoopConfiguration.set("hadoop.job.ugi", "help_majia2,wuzijian02majia2")

    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")

    val cal2 = Calendar.getInstance()
    cal2.add(Calendar.DATE, -1)
    val date = dateFormat.format(cal2.getTime)

    val hdfsFilePath = "afs://baihua.afs.baidu.com:9902/user/help_majia2/success_index/" + args(0) + "_" + date

    val modelNames = Array("aa", "bb", "cc", "dd", "ee", "ff")
    val modelNamesRdd = spark.sparkContext.parallelize(modelNames, 1)
    modelNamesRdd.saveAsTextFile(hdfsFilePath)
  }
}
