package com.baidu.sql.lib

import com.baidu.sql.utils.JDBCHxUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.first
import org.apache.spark.sql.{SaveMode, SparkSession, functions}

import java.util.Properties


/**
 * <AUTHOR>
 */
object ReadPassId2Mysql2 {
  def main(args: Array[String]): Unit = {

    // 最终落地表
    val writeBackTable: String = "tb_res_data_wgy"

    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    import spark.implicits._

    // 获取mysql的配置
    val url: String = JDBCHxUtils.url
    val user: String = JDBCHxUtils.user
    val password: String = JDBCHxUtils.password
    val driver: String = JDBCHxUtils.driver

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver",driver)

    var mSql =
      s"""
         |SELECT
         |   uid,
         |   rid as nid
         |FROM tb_source_data_wgy
         |""".stripMargin


    // 获取Mysql数据
    val jdbcDF = spark.read.format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", user)
      .option("password", password)
      .option("query", mSql)
      .load()


    val result = jdbcDF.agg(functions.concat_ws(", ", functions.collect_list($"nid")).as("all_nid"))
      .select(first($"all_nid").as("result"))
    val allNid = "(" + result.first().getString(0) + ")"
    var sql =
      s"""
         |SELECT
         |  nid,
         |  title,
         |  url,
         |  origin_url,
         |  rmb_self_build_url_http
         |FROM
         |  udw_ns.default.bjh_feed_resource_rf
         |WHERE
         |  event_day = '20240110'
         |  AND nid in ${allNid}
         |""".stripMargin

    var df = spark.sql(sql)

    df = jdbcDF.join(df, jdbcDF("nid") === df("nid"), "left")
      .drop(df("nid"))

    // 将数据写入最终落地表
    df.repartition(20).write.option("truncate", value = false)
      .mode(SaveMode.Overwrite).jdbc(url, writeBackTable, properties)

    spark.close()
  }
}
