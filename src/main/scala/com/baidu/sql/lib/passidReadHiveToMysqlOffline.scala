package com.baidu.sql.lib

import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.sql.{Connection, DriverManager, Statement}
import java.text.SimpleDateFormat
import java.util.{Date, Properties}

/**
 * <AUTHOR>
 */
object passidReadHiveToMysqlOffline {
  def main(args: Array[String]): Unit = {
    // sql文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)

    //分区日期
    val event_day: String = "20231103"
    //分区时间
    val event_hour: String = "19"
    //分区日期格式处理
    val timeStr: String = event_day.substring(0, 4) + "-" + event_day.substring(4, 6) + "-" + event_day.substring(6, 8) + " " + event_hour
    // 获取程序启动时间
    val creatTime: Long = System.currentTimeMillis()
    // 格式化时间戳
    var formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    //回写表配置
    val url: String = "***********************************************?" +
      "useUnicode=true&useSSL=false&characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
    val username: String = "root"
    val password: String = "PlatTestDB@7!"
    val driver: String = "com.mysql.jdbc.Driver"

    val properties = new Properties()
    properties.setProperty("user", username)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    //sparkConf
    val sparkConf = new SparkConf().setAppName("passid_hive_2_mysql")
    //sparkSession配置
    val spark = SparkSession
      .builder()
      .config(sparkConf)
      .config("hive.exec.dynamic.partition", true)
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.cores.max", cores)
      .config("spark.task.maxFailures", maxFailures)
      .config("spark.driver.allowMultipleContexts", true)
      .enableHiveSupport()
      .getOrCreate()

    //把mysql中createtime为当天的数据删除,读取数据插入mysql
    //删除mysql所需日期数据
    deleteMysqlData(writeBackTable,timeStr,url,username,password)

    //读取数据
    val udwDF = spark.sql(
      s"""
         |select
         |phone,
         |sms_send_time,
         |case when pass_id is null then '0' else pass_id end pass_id
         |from udw_ns.default.help_ods_pass_id_sync_di_v1
         |where event_day = $event_day and event_hour = $event_hour
         |group by
         |phone,
         |sms_send_time,
         |pass_id
         |""".stripMargin)

    //读取数据拼接mysql中常规字段信息
    val tableDF = udwDF
      .withColumn("creator", lit(0))
      .withColumn("updator", lit(0))
      .withColumn("create_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("update_time", lit(formatter.format(new Date(creatTime))))
      .withColumn("version", lit(0))
      .withColumn("is_delete", lit(0))
      .withColumn("del_unique_key", lit(0))


    //调整顺序
    tableDF.createTempView("passid_view_temp")
    val resultDF = spark.sql(
      """
        |select
        |creator,
        |updator,
        |create_time,
        |update_time,
        |version,
        |is_delete,
        |del_unique_key,
        |phone,
        |sms_send_time,
        |pass_id
        |from
        |passid_view_temp
        |""".stripMargin)

    resultDF.show()

    resultDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)


  }

  def deleteMysqlData(writeBackTable: String,
                      timeStr: String,
                      url: String,
                      username: String,
                      password: String): Unit = {
    var connection: Connection = null
    var statement: Statement = null
    //    val yesterday = LocalDateTime.parse(YESTERDAY + " 00:00:00", DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"))
    //    val currentday = yesterday.plusDays(1)
    //    val yesterdayTimestamp = java.sql.Timestamp.valueOf(yesterday)
    //    val currentdayTimestamp = java.sql.Timestamp.valueOf(currentday)
    //    println("当前日期为"+currentdayTimestamp)
    //    println("昨天日期为"+yesterdayTimestamp)

    try {
      //获取连接
      val connection = DriverManager.getConnection(url, username, password)
      val statement = connection.createStatement()
      //sql语句
      var sql =
        s"""
           |DELETE
           |FROM ${writeBackTable}
           |WHERE create_time LIKE "$timeStr%"
           | and pass_id is null""".stripMargin
      //打印sql
      println(sql)
      val rowsAffected: Int = statement.executeUpdate(sql)
      println("删除条数为：" + rowsAffected)
    } finally {
      // 关闭 statement 和 connection
      if (statement != null) {
        statement.close()
      }
      if (connection != null) {
        connection.close()
      }
    }
  }
}
