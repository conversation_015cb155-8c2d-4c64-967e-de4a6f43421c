package com.baidu.sql.lib

import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.util.Properties

/**
 * <AUTHOR>
 */
object PassidMysqlToMysql {
  def main(args: Array[String]): Unit = {
    // sql文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)
    //日期
   val YESTERDAY = "20231103"
    //分区日期格式处理
    val yesterdayStr: String = YESTERDAY.substring(0, 4) + "-" + YESTERDAY.substring(4, 6) + "-" + YESTERDAY.substring(6, 8)

    // 源表
    val sourceTable: String = "engine_profile_sms"

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)
      .set("spark.jars", "/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
//      .master("local[*]")
      .appName("passid_mysql_2_mysql")
      .config(conf)
      .getOrCreate()


    // 数据源mysql的配置
    val url: String = "************************************************************?" +
      "zeroDateTimeBehavior=convertToNull&autoReconnect=true&useUnicode=true&useSSL=false&" +
      "characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
    val user: String = "readonly"
    val password: String = "Asd123"
    val driver: String = "com.mysql.jdbc.Driver"

    val properties = new Properties()
    properties.setProperty("user", user)
    properties.setProperty("password", password)
    properties.setProperty("driver", driver)

    //写回库mysql配置
    val writeBackUrl: String = "***********************************************?"+
      "useUnicode=true&useSSL=false&characterEncoding=utf8&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8"
    val writeBackUser: String = "root"
    val writeBackPassword: String = "PlatTestDB@7!"
    val writeBackDriver: String = "com.mysql.jdbc.Driver"

    val writeBackProperties = new Properties()
    writeBackProperties.setProperty("user", writeBackUser)
    writeBackProperties.setProperty("password", writeBackPassword)
    writeBackProperties.setProperty("driver", writeBackDriver)

    //读取源数据
    val soureDF: DataFrame = spark.read.jdbc(url, sourceTable, properties).where(s"""create_time like "$yesterdayStr%" """)
    soureDF.show()

    //    // 将数据写入最终落地表
    soureDF.repartition(50).write
      .mode(SaveMode.Append).jdbc(writeBackUrl, writeBackTable, writeBackProperties)

    spark.close()
  }

}
