package com.baidu.sql.lib

import com.baidu.sql.utils.MarioJDBC
import org.apache.spark.SparkConf
import org.apache.spark.sql.{SaveMode, SparkSession, functions}

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Properties

/**
 * <AUTHOR>
 */
object ReadMysql2MysqlUrl {

    def main(args: Array[String]): Unit = {
      /*
      * 1.engine_gpt_content_store.resource_id 匹配 udw_ns.default.bjh_feed_resource_rf.nid
      * 2.获取 rmb_self_build_url_http字段
      * 3.拼接engine_gpt_bjh_video_info中所需字段
      * 4.回写engine_gpt_bjh_video_info表
      *
      * mysqlDf ----->
      *                join on resource_id =  nid  ----> resultDf ---> mysql
      * udwDf   ---->
      * */

      // udw源表
      val sourceTable: String = "engine_gpt_content_store"
      // 最终落地表
      val writeBackTable: String = "engine_gpt_bjh_video_info_1"

      //分区日期 传入当前日期
      val event_day: String = args(0)
      //分区时间 传入上一小时
      val event_hour: String = args(1)
      //hive分区日期计算
      val yesterday = LocalDate.parse(event_day).minusDays(1)
      //分区日期格式处理
      val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
      val yesterdayString = yesterday.format(formatter)

      //分区日期格式处理
      val timeStr: String = event_day+ " " + event_hour


      var driver = MarioJDBC.driver
      var url = MarioJDBC.url
      var username = MarioJDBC.user
      var password = MarioJDBC.password


      val properties = new Properties()
      properties.setProperty("driver", driver)
      properties.setProperty("url", url)
      properties.setProperty("user", username)
      properties.setProperty("password", password)

      // 创建conf对象，指定参数
      val conf = new SparkConf()
        .setAppName("GetNidUrl")

      // SparkSession
      val spark: SparkSession = SparkSession
        .builder()
        .config(conf)
        .enableHiveSupport()
        .getOrCreate()

      val sqlStatement =
        s"""
          |select
          | resource_id as nid
          |from $sourceTable
          |where create_time like '$timeStr%'
          |""".stripMargin

      // 从 MySQL 读取数据
      val jdbcDf = spark.read
        .format("jdbc")
        .option("url", url)
        .option("driver", driver)
        .option("user", username)
        .option("password", password)
        .option("query", sqlStatement)
        .load()

      var allResourceId = "( " + jdbcDf.collect().mkString(", ").replaceAll("\\[", "'").replaceAll("\\]", "'") + " )"

      if (allResourceId.equals("(  )")){
        allResourceId = "('0')"
      }


      var hSql =
        s"""
           |select
           |	nid,
           |	rmb_self_build_url_http as video_url
           |from
           |udw_ns.default.bjh_feed_resource_rf
           |where event_day = $yesterdayString
           |and nid in ${allResourceId}
           |""".stripMargin


      //从udw表中拉取视频url
      var df = spark.sql(hSql)

      df = df
        .withColumn("create_time", functions.current_timestamp())
        .withColumn("update_time", functions.current_timestamp())


      df = jdbcDf.join(df, jdbcDf("nid") === df("nid"), "left")
        .drop(df("nid"))


      df.repartition(50).write
        .mode(SaveMode.Append).jdbc(url, writeBackTable, properties)

    }


}
