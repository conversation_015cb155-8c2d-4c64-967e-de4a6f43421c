package com.baidu.sql.lib

import com.baidu.sql.utils.MarioJDBC
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession, functions}

import java.util.Properties

/**
* <AUTHOR>
*/
object ReadMario2Mysql {
def main(args: Array[String]): Unit = {

    val YESTERDAY = args(0)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)

    var driver = MarioJDBC.driver
    var url = MarioJDBC.url
    var username = MarioJDBC.user
    var password = MarioJDBC.password
    var tb_table = "engine_strategy_experiment_result"


    val properties = new Properties()
    properties.setProperty("driver", driver)
    properties.setProperty("url", url)
    properties.setProperty("user", username)
    properties.setProperty("password", password)

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)
      .set("spark.jars", "/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()

    var sql =
        s"""
          |with mario_source as (
          |  SELECT
          |    date_format(create_time, 'yyyy-MM-dd') as c_month,
          |    mario_req,
          |    get_json_object(public_sentiment_out, '$$.name') as name,
          |    get_json_object(public_sentiment_out, '$$.desc') as descs,
          |    get_json_object(public_sentiment_out, '$$.res') as res,
          |    strategy_name
          |  FROM
          |    udw_ns.default.help_ods_engine_mario_publicsentiment_di
          |    where event_day = '${YESTERDAY}'
          |),
          |req_cnt as (
          |  SELECT
          |    c_month,
          |    count(1) as online_count
          |  FROM
          |    mario_source
          |  group by
          |    c_month
          |),
          |sent_cnt as (
          |  SELECT
          |    c_month,
          |    strategy_name,
          |    sum(
          |      if(
          |        name != ''
          |        and descs != ''
          |        and res != '',
          |        1,
          |        0
          |      )
          |    ) as online_hit_count
          |  FROM
          |    mario_source
          |  where strategy_name != ''
          |  group by
          |    c_month,strategy_name
          |)
          |SELECT
          |  sent_cnt.c_month as `current_date`,
          |  req_cnt.online_count,
          |  sent_cnt.online_hit_count,
          |  sent_cnt.strategy_name as strategy_name
          |FROM
          |  sent_cnt
          |LEFT JOIN req_cnt on sent_cnt.c_month = req_cnt.c_month
          |""".stripMargin

    var hdf = spark.sql(sql)

    hdf = hdf
      .withColumn("create_time", functions.current_timestamp())
      .withColumn("update_time", functions.current_timestamp())
      .withColumn("app_id", lit(11))
      .withColumn("app_name", lit("全网舆情回捞"))


    hdf.write.mode(SaveMode.Append).jdbc(url, tb_table, properties)

    }
}
