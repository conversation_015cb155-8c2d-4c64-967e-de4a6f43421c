package com.baidu.sql.hive2afs

import com.baidu.sql.lib.MysqlStatus
import com.baidu.sql.utils.JDBCHxUtils
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.log4j.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Row, SparkSession}

import java.sql.DriverManager

/**
 * <AUTHOR>
 */
object ReadHiveParseJson2Mysql11 {
  var fs: FileSystem = null
  val logger: Logger = Logger.getLogger(getClass.getName)
  var driver = JDBCHxUtils.driver
  var url = JDBCHxUtils.url
  var username = JDBCHxUtils.user
  var password = JDBCHxUtils.password
  var tb_table = "engine_profile_user"
  val connection = DriverManager.getConnection(url, username, password)

  def main(args: Array[String]): Unit = {

    // 分区时间
//    val YESTERDAY: String = "{YERSTERDAY}"

    // sql文件名
    val sql_file: String = args(0)
    // sql描述
    val sql_desc: String = args(1)
    // spark.cores.max
    val cores: String = args(2)
    // spark.task.maxFailures
    val maxFailures: String = args(3)
    // 任务名称name
    val sql_name: String = args(4)
    // 最终落地表
    val writeBackTable: String = args(5)

    // 创建conf对象，指定参数
    val conf = new SparkConf()
      .set("spark.sql.hive.convertMetastoreOrc", "false")
      .set("spark.sql.hive.convertMetastoreParquet", "false")
      .set("spark.cores.max", cores)
      .set("spark.task.maxFailures", maxFailures)
      .set("spark.jars", "/home/<USER>/bigdata_sql_executor/sql-exe-lib.jar")
      .setAppName("ReadHiveParseJson2Mysql")

    // SparkSession
    val spark: SparkSession = SparkSession
      .builder()
      .config(conf)
      .enableHiveSupport()
      .getOrCreate()


    import spark.implicits._

    var mSql =
      s"""
         |SELECT
         |   pass_id as passId,
         |   recent_profile_info_id as profileId
         |FROM ${tb_table}
         |WHERE portrait_status = 1
         |limit 500
         |""".stripMargin

    logger.info(s"Mysql 查询Sql { ${mSql} }")

    // 获取Mysql数据
    val jdbcDF = spark.read.format("jdbc")
      .option("url", url)
      .option("driver", driver)
      .option("user", username)
      .option("password", password)
      .option("query", mSql)
      .load()

    jdbcDF.cache()

    // 如果mysql数据为空就结束程序
    if (jdbcDF.isEmpty) {
      logger.error(s"No Such Data { ${tb_table}数据为空 }")
      sys.exit(1)
    }



    // 将mysql捞到的id,拼接为sql过滤条件 user_id in (allPassId)
    var passIdArray: Array[Row] = jdbcDF.select("passId").collect()
    var allPassId = "( " + passIdArray.mkString(", ").replaceAll("\\[", "'").replaceAll("\\]", "'") + " )"
    logger.info(s"allPassId = {${allPassId}}")

    var fetchHiveSql =
      s"""
         | with feed_source as(
         | select
         | 	user_id as passId,
         | 	case
         | 		when type3 = 'news' then '图文'
         | 		when type3 = 'shortvideo' then '横短视频'
         | 		when type3 = 'littlevideo' then '原生小视频'
         | 		when type3 = 'ittlevideo' then '竖短视频'
         | 		when type3 = 'dt_text' then '动态纯文本'
         | 		when type3 = 'dt_image_text' then '动态图文'
         | 		when type3 = 'dt_ugc_video' then '动态视频'
         | 		when type3 = 'gallery' then '图集'
         | 	end as type,
         | 	title,
         | 	m_content as content,
         | 	comment_pv as commentNum,
         | 	article_like_pv as likeNum,
         |  publish_time as time
         | from udw_ns.default.bjh_feed_resource_rf as tb_re
         | where tb_re.event_day = '********'
         | and user_id in ${allPassId}
         | ),
         | author_source as(
         | select
         | 	user_id,
         | 	name ,
         | 	v_type,
         | 	total_fans ,
         | 	is_news_active_weekely ,
         | 	click_pv_weekely ,
         | 	register_realinfo_status ,
         | 	author_fenceng ,
         | 	last_freeze_time,
         | 	last_freeze_reason,
         | 	last_unfreeze_time,
         | 	wb_name,
         | 	wb_id,
         | 	wb_node,
         | 	wx_account_alias,
         | 	wx_account_id,
         | 	wx_node
         | from udw_ns.default.bjh_author_view as tb_author
         | where tb_author.event_day = '********'
         | and user_id in ${allPassId}
         | ),
         | res_feed as(
         | select
         | 	passId,
         | 	collect_list(to_json(named_struct('type', type, 'title', title, 'content', content, 'commentNum', commentNum, 'likeNum', likeNum, 'time', time))) as contents
         | from feed_source
         | group by passId
         | ),
         | bjhFreezeStatus as (
         | 	select
         | 		user_id,
         | 		to_json(named_struct('lastFreezeTime', last_freeze_time, 'lastFreezeReason', last_freeze_reason, 'lastUnfreezeTime', last_unfreeze_time)) as bjhFreezeStatus
         | 	from  author_source
         | )
         | select
         | 	tb_author.user_id as passId,
         | 	name as bjhName,
         | 	case
         | 		when v_type = 0 then '无认证'
         | 		when v_type = 1 then '金V'
         | 		when v_type = 2 then '蓝V'
         | 		when v_type = 3 then '黄V'
         | 		when v_type = 4 then '机构真实性认证'
         | 		else v_type
         | 	end as bjhAuth,
         | 	total_fans as bjhFansNum,
         | 	is_news_active_weekely as bjh7DaysArticleNum,
         | 	click_pv_weekely as bjh7DaysIncreasedPv,
         | 	case
         | 		when register_realinfo_status = 'pass' then '通过'
         | 		when register_realinfo_status = 'risk' then '高危'
         | 		when register_realinfo_status = 'unpass' then '失败'
         | 		else '未填写'
         | 	end as bjhRealNameAuth,
         | 	case
         | 		when author_fenceng = 1 then '核心作者'
         | 		when author_fenceng = 2 then '优质作者'
         | 		when author_fenceng = 3 then '潜力作者'
         | 		when author_fenceng = 4 then '长尾作者'
         | 		when author_fenceng = 5 then '低质作者'
         | 		else '默认'
         | 	end as bjhUserLevel,
         | 	bjhFreezeStatus.bjhFreezeStatus,
         | 	wb_name as wbName,
         | 	wb_id as wbId,
         | 	wb_node as wbNode,
         | 	wx_account_alias as wxAccountAlias,
         | 	wx_account_id as wxAccountId,
         | 	wx_node as wxNode,
         | 	contents
         | from author_source as tb_author
         | join bjhFreezeStatus on tb_author.user_id = bjhFreezeStatus.user_id
         | join res_feed on tb_author.user_id = res_feed.passId
         |""".stripMargin

    logger.info(s"fetchHiveSql = {${fetchHiveSql}}")



    // 拉取hive数据
    var hdf = spark.sql(fetchHiveSql)

    // join mysql df 和 hive df 目的是在hive中加上profileId 为了后续的fileName
    hdf = hdf.join(jdbcDF, hdf("passId") === jdbcDF("passId"), "left")
      .select(
        hdf("passId"),
        hdf("bjhName"),
        hdf("bjhAuth"),
        hdf("bjhFansNum"),
        hdf("bjh7DaysArticleNum"),
        hdf("bjh7DaysIncreasedPv"),
        hdf("bjhRealNameAuth"),
        hdf("bjhUserLevel"),
        hdf("bjhFreezeStatus"),
        hdf("wbName"),
        hdf("wbId"),
        hdf("wbNode"),
        hdf("wxAccountAlias"),
        hdf("wxAccountId"),
        hdf("wxNode"),
        hdf("contents"),
        jdbcDF("profileId"))

    // 添加百家号文件名列
    hdf = hdf
      .withColumn("filename", concat(lit("profile_info_bjh_"), $"passId", lit("_"), $"profileId"))
      .selectExpr("to_json(struct(*)) as data")
      .withColumn("filename", get_json_object(col("data"), "$.filename"))
      .withColumn("passId", get_json_object(col("data"), "$.passId").cast("long"))

    hdf = hdf.dropDuplicates()

    hdf.printSchema()
    // 拉到数据后修改状态为2

    var updateSql =
      s"""
        |
        |update ${tb_table} set portrait_status = 2 where pass_id in ${allPassId}
        |""".stripMargin

    update2Mysql(updateSql)
    jdbcDF.unpersist()
    // 要将符合数据转换为json后写入afs
    hdf.repartition(1).foreach(row => {
      // 获取拼接好的文件名
      var fileName = row.getAs[String]("filename")
      // 获取json数据
      var json: String = row.getAs[String]("data")

      json = fileName + json

      sink2Mysql(json)

      // 修改mysql数据中状态为3
      update2Mysql(row, MysqlStatus.waitStatus)

    })

    connection.close()
    spark.close()
  }

  // 写入数据到afs
  def sink2Afs(fileName: String, json: String): Boolean = {
    // afs输出路径
    val outputPath = "afs://output"
    val outputAfsPath = new Path(outputPath, s"${fileName}.json")
    // 设置为true 如果有数据覆盖掉
    val outputStream = fs.create(outputAfsPath, true)
    outputStream.write(json.getBytes("UTF-8"))
    outputStream.close()
    fs.exists(outputAfsPath)
  }


  def sink2Mysql(json: String): Unit = {
    var sql = s"INSERT INTO `res_profile_user` (`datas`) VALUES (?)"
    val statement = connection.prepareStatement(sql)

    statement.setString(1, json)

    // 获取完整的SQL语句
    sql = s"INSERT INTO `res_profile_user` (`datas`) VALUES (${json})"
    logger.info(s"update Mysql sql { ${sql} }")

    statement.executeUpdate()
    statement.close()
  }



  // 修改mysql数据 写入afs后 修改mysql数据值为3
  def update2Mysql(row: Row, portrait_status: Int): Unit = {
    var sql = s"UPDATE ${tb_table} SET portrait_status = ? WHERE pass_id = ?"
    val statement = connection.prepareStatement(sql)

    val passId: Long = row.getAs[Long]("passId")
    statement.setInt(1, portrait_status)
    statement.setLong(2, passId)

    // 获取完整的SQL语句
    sql = s"UPDATE ${tb_table} SET portrait_status = ${portrait_status} WHERE pass_id = ${passId}"
    logger.info(s"update Mysql sql { ${sql} }")

    statement.executeUpdate()
    statement.close()
  }

  // 修改mysql数据 当读取到mysql数据后 修改mysql数据值为2
  def update2Mysql(sql: String): Unit = {
    val statement = connection.prepareStatement(sql)

    // 获取完整的SQL语句
    logger.info(s"update Mysql sql { ${sql} }")

    statement.executeUpdate()
    statement.close()
  }




}
