<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
            </Pattern>
        </layout>
    </appender>

    <!-- https://logback.qos.ch/manual/appenders.html#AsyncAppender -->
    <!-- AsyncAppender will drop events of level TRACE, DEBUG and warn if its queue is 80% full -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE-ROLLING" />
        <!-- defaulr 256 -->
        <queueSize>512</queueSize>
    </appender>

    <logger name="com.mkyong" level="warn" additivity="false">
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root level="warn">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>


