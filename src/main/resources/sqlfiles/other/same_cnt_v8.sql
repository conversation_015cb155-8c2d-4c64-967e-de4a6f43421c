with source_table as (
  select
    substr(pay_time, 1, 10) pay_time,
    cuid,
    app_id,
    passport_id,
    order_id,
    mobile,
    pay_passport_id,
    tracking_number,
    shop_id,
    ip
  from
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where
    event_day = '20230902'
    and cuid != '0'
    and cuid != ''
    and cuid is not null
    and pay_time is not null
    and pay_time != '0'
    and app_id in (269, 5, 10007, 100012)
    and substr(pay_time, 1, 10) >= '2023-07-26'
),

-- 同一个cuid 在过去三十天对应多少个passport_id
same_cuid_passid_cnt_last30 as
(select
  cuid,
  order_id,
  count(distinct passport_id) as same_cuid_passid_cnt_last30
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  cuid,
  order_id
),
-- 同订单号下同一个cuid 在过去的30天中的订单数
same_cuid_order_cnt_last30 as
(
select
  cuid,
  order_id,
  count(distinct order_id_b) as same_cuid_order_cnt_last30
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  cuid,
  order_id
),
-- 同一个手机号 在过去30天对应多少个passport_id数
same_phone_passid_cnt_last30 as
(
select
  mobile,
  order_id,
  count(distinct passport_id) as same_phone_passid_cnt_last30
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  mobile,
  order_id
),
-- 同一个手机号在过去30天内 对应的订单式
same_phone_order_cnt_last30 as
(
select
  mobile,
  order_id,
  count(distinct order_id_b) as same_phone_order_cnt_last30
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  mobile,
  order_id
),
-- 同一个cuid 在过去七天对应多少个passport_id
same_cuid_passid_cnt_last7 as
(select
  cuid,
  order_id,
  count(distinct passport_id) as same_cuid_passid_cnt_last7
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  cuid,
  order_id
),
-- 同订单号下同一个cuid 在过去的7天中的订单数
same_cuid_order_cnt_last7 as
(
select
  cuid,
  order_id,
  count(distinct order_id_b) as same_cuid_order_cnt_last7
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  cuid,
  order_id
),
-- 同一个手机号 在过去7天对应多少个passport_id数
same_phone_passid_cnt_last7 as
(
select
  mobile,
  order_id,
  count(distinct passport_id) as same_phone_passid_cnt_last7
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  mobile,
  order_id
),
-- 同一个手机号在过去7天内 对应的订单数
same_phone_order_cnt_last7 as
(
select
  mobile,
  order_id,
  count(distinct order_id_b) as same_phone_order_cnt_last7
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  mobile,
  order_id
),
-- 当日pay_passport_id下，cuid数
same_paypassid_cuid_cnt_last1 as
(
select
  pay_passport_id,
  order_id,
  count(distinct cuid) as same_paypassid_cuid_cnt_last1
from
  (
    select
      a.pay_passport_id as pay_passport_id,
      a.order_id as order_id,
      b.cuid as cuid
    from
      source_table a
      join source_table b on a.pay_passport_id = b.pay_passport_id and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  pay_passport_id,
  order_id
),
-- 当日支付id下对应的uid(passport_id)数
same_paypassid_uid_cnt_last1 as
(
select
  pay_passport_id,
  order_id,
  count(distinct passport_id) as same_paypassid_uid_cnt_last1
from
  (
    select
      a.pay_passport_id as pay_passport_id,
      a.order_id as order_id,
      b.cuid as passport_id
    from
      source_table a
      join source_table b on a.pay_passport_id = b.pay_passport_id and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  pay_passport_id,
  order_id
),
same_paypassid_cuid_cnt_last7 as
(
select
  pay_passport_id,
  order_id,
  count(distinct cuid) as same_paypassid_cuid_cnt_last7
from
  (
    select
      a.pay_passport_id as pay_passport_id,
      a.order_id as order_id,
      b.cuid as cuid
    from
      source_table a
      join source_table b on a.pay_passport_id = b.pay_passport_id and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  pay_passport_id,
  order_id
),
same_paypassid_uid_cnt_last7 as
(
select
  pay_passport_id,
  order_id,
  count(distinct passport_id) as same_paypassid_uid_cnt_last7
from
  (
    select
      a.pay_passport_id as pay_passport_id,
      a.order_id as order_id,
      b.cuid as passport_id
    from
      source_table a
      join source_table b on a.pay_passport_id = b.pay_passport_id and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  pay_passport_id,
  order_id
),
-- tracking_number空值过滤
source_track as (
    select
      source_table.tracking_number as tracking_number,
      source_table.order_id as order_id,
      source_table.app_id as app_id,
      source_table.shop_id as shop_id,
      source_table.pay_time as pay_time
    from
      source_table
    where (source_table.tracking_number is not null and source_table.tracking_number != '')
),
-- tracking_number 当日内物流单号对应订单量
same_expressnumber_order_cnt as
(
select
  tracking_number,
  order_id,
  count(distinct order_id_b) as same_expressnumber_order_cnt
from
  (
    select
      a.tracking_number as tracking_number,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_track a
      join source_track b on a.tracking_number = b.tracking_number and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  tracking_number,
  order_id
),
same_expressnumber_shop_cnt as
(
select
  tracking_number,
  order_id,
  count(distinct shop_id) as same_expressnumber_shop_cnt
from
  (
    select
      a.tracking_number as tracking_number,
      a.order_id as order_id,
      b.shop_id as shop_id
    from
      source_track a
      join source_track b on a.tracking_number = b.tracking_number and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  tracking_number,
  order_id
),
same_ip_cuid_cnt_last1 as
(
select
  ip,
  order_id,
  count(distinct cuid) as same_ip_cuid_cnt_last1
from
  (
    select
      a.ip as ip,
      a.order_id as order_id,
      b.cuid as cuid
    from
      source_table a
      join source_table b on a.ip = b.ip and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  ip,
  order_id
),
same_ip_uid_cnt_last1 as
(
select
  ip,
  order_id,
  count(distinct passport_id) as same_ip_uid_cnt_last1
from
  (
    select
      a.ip as ip,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.ip = b.ip and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
  ) t
group by
  ip,
  order_id
),
same_ip_cuid_cnt_last7 as
(
select
  ip,
  order_id,
  count(distinct cuid) as same_ip_cuid_cnt_last7
from
  (
    select
      a.ip as ip,
      a.order_id as order_id,
      b.cuid as cuid
    from
      source_table a
      join source_table b on a.ip = b.ip and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  ip,
  order_id
),
same_ip_uid_cnt_last7 as
(
select
  ip,
  order_id,
  count(distinct passport_id) as same_ip_uid_cnt_last7
from
  (
    select
      a.ip as ip,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.ip = b.ip and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-26'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 6
  ) t
group by
  ip,
  order_id
)


select *
from(
select
source_table.order_id,
same_cuid_passid_cnt_last30,
same_cuid_order_cnt_last30,
same_phone_passid_cnt_last30,
same_phone_order_cnt_last30,
same_cuid_passid_cnt_last7,
same_cuid_order_cnt_last7,
same_phone_passid_cnt_last7,
same_phone_order_cnt_last7,
same_paypassid_cuid_cnt_last1,
same_paypassid_uid_cnt_last1,
same_paypassid_cuid_cnt_last7,
same_paypassid_uid_cnt_last7,
same_expressnumber_order_cnt,
same_expressnumber_shop_cnt,
same_ip_cuid_cnt_last1,
same_ip_uid_cnt_last1,
same_ip_cuid_cnt_last7,
same_ip_uid_cnt_last7
from
source_table
left join
same_cuid_passid_cnt_last30 on source_table.order_id = same_cuid_passid_cnt_last30.order_id
left join
same_cuid_order_cnt_last30 on source_table.order_id = same_cuid_order_cnt_last30.order_id
left join
same_phone_passid_cnt_last30 on source_table.order_id = same_phone_passid_cnt_last30.order_id
left join
same_phone_order_cnt_last30 on source_table.order_id = same_phone_order_cnt_last30.order_id
left join
same_cuid_passid_cnt_last7 on source_table.order_id = same_cuid_passid_cnt_last7.order_id
left join
same_cuid_order_cnt_last7 on source_table.order_id = same_cuid_order_cnt_last7.order_id
left join
same_phone_passid_cnt_last7 on source_table.order_id = same_phone_passid_cnt_last7.order_id
left join
same_phone_order_cnt_last7 on source_table.order_id = same_phone_order_cnt_last7.order_id
left join
same_paypassid_cuid_cnt_last1 on source_table.order_id = same_paypassid_cuid_cnt_last1.order_id
left join
same_paypassid_uid_cnt_last1 on source_table.order_id = same_paypassid_uid_cnt_last1.order_id
left join
same_paypassid_cuid_cnt_last7 on source_table.order_id = same_paypassid_cuid_cnt_last7.order_id
left join
same_paypassid_uid_cnt_last7 on source_table.order_id = same_paypassid_uid_cnt_last7.order_id
left join
same_expressnumber_order_cnt on source_table.order_id = same_expressnumber_order_cnt.order_id
left join
same_expressnumber_shop_cnt on source_table.order_id = same_expressnumber_shop_cnt.order_id
left join
same_ip_cuid_cnt_last1 on source_table.order_id = same_ip_cuid_cnt_last1.order_id
left join
same_ip_uid_cnt_last1 on source_table.order_id = same_ip_uid_cnt_last1.order_id
left join
same_ip_cuid_cnt_last7 on source_table.order_id = same_ip_cuid_cnt_last7.order_id
left join
same_ip_uid_cnt_last7 on source_table.order_id = same_ip_uid_cnt_last7.order_id
)
where
same_cuid_passid_cnt_last30 is not null
or
same_cuid_order_cnt_last30 is not null
or
same_phone_passid_cnt_last30 is not null
or
same_phone_order_cnt_last30 is not null
or
same_cuid_passid_cnt_last7 is not null
or
same_cuid_order_cnt_last7 is not null
or
same_phone_passid_cnt_last7 is not null
or
same_phone_order_cnt_last7 is not null
or
same_paypassid_cuid_cnt_last1 is not null
or
same_paypassid_uid_cnt_last1 is not null
or
same_paypassid_cuid_cnt_last7 is not null
or
same_paypassid_uid_cnt_last7 is not null
or
same_expressnumber_order_cnt is not null
or
same_expressnumber_shop_cnt is not null
or
same_ip_cuid_cnt_last1 is not null
or
same_ip_uid_cnt_last1 is not null
or
same_ip_cuid_cnt_last7 is not null
or
same_ip_uid_cnt_last7 is not null
limit 100