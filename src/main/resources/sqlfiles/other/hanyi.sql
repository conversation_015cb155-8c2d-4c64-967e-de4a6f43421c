SELECT   user_id, view_count, article, article_id, article_like_pv, article_num, audit_status, author_name, author_total_fans, avatar, bstatus, check_type, collect_pv, comment_pv, cover_images, cstatus, es_id, img_str, insert_time, interest, m_content, nid, publish_time, quality_reject_rate, recommend_count, reject_reason, safe_reject_rate, self_del_rate, share_pv, title, v_intro,  wishes   FROM udw_ns.default.help_ods_bjh_base_info_df WHERE event_day = '20230916'  AND ((reject_reason = "违反法律法规") and (status = "pass" or status = "audit" or status = "unpass") and (author_sub_type in ("individual","other"))) limit 100