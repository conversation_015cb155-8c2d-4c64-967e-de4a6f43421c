select
    t1.order_id,
    count(t1.passport_id) over(partition by t1.cuid) as same_cuid_passid_cnt_last7,
    count(t1.order_id) over(partition by t1.cuid) as same_cuid_order_cnt_last7,
    count(t1.passport_id) over(partition by t1.mobile) as same_phone_passid_cnt_last7,
    count(t1.order_id) over(partition by t1.mobile) as same_phone_order_cnt_last7
from(
SELECT
    order_id,
    cuid,
    mobile,
    passport_id
from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
where event_day = "20230814"
and DATEDIFF(current_timestamp(),pay_time) between 1 and 6
group by order_id, cuid, mobile, passport_id
)t1
limit 100



