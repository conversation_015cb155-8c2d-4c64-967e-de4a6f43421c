select order_id,
	   case when pay_hours != 0 and refund_create_hours!= 0 then round(refund_create_hours-pay_hours,2) else 0 end refund_creat_time_interval,
	   case when pay_hours != 0 and refund_finish_hours!= 0 then round(refund_finish_hours-pay_hours,2) else 0 end refund_time_interval,
	   case when pay_hours != 0 and evaluate_time_hours!= 0 then round(evaluate_time_hours-pay_hours,2) else 0 end evaluate_time_interval
	from (
		select
		order_id,
		-- 下单时间为pay_time  按照需求所有时间转换为小时
		case when pay_time is not null then to_unix_timestamp(pay_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end pay_hours,
		case when refund_create_time is not null then to_unix_timestamp(refund_create_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end refund_create_hours,
		case when refund_finish_time is not null then to_unix_timestamp(refund_finish_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end refund_finish_hours,
		case when evaluate_time is not null then to_unix_timestamp(evaluate_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end evaluate_time_hours
		from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
        where event_day = '20230905'
        and app_id in (269,5,10007,100012)
		)
limit 100