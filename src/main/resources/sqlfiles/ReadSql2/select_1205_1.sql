with mario_source as (
  SELECT
    date_format(audit_time, 'yyyy-MM-dd') as c_month,
    mario_req,
    get_json_object(public_sentiment_out, '$.name') as name,
    get_json_object(public_sentiment_out, '$.desc') as descs,
    get_json_object(public_sentiment_out, '$.res') as res,
    strategy_name
  FROM
    udw_ns.default.help_ods_engine_mario_publicsentiment_di
),
-- 获取请求量
req_cnt as (
  SELECT
    c_month,
    count(1) as online_count
  FROM
    mario_source
  group by
    c_month
),
-- 根据每天每个策略求命中量
sent_cnt as (
  SELECT
    c_month,
    strategy_name,
    sum(
      if(
        name != ''
        and descs != ''
        and res != '',
        1,
        0
      )
    ) as online_hit_count
  FROM
    mario_source
  where strategy_name != ''
  group by
    c_month,strategy_name
)
SELECT
  sent_cnt.c_month as `current_date`,
  req_cnt.online_count,
  sent_cnt.online_hit_count,
  sent_cnt.strategy_name as strategy_name
FROM
  sent_cnt
LEFT JOIN req_cnt on sent_cnt.c_month = req_cnt.c_month