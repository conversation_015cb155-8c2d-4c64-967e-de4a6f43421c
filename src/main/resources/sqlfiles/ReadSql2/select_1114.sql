with feed_source as(
select
	user_id as passId,
	case
		when type3 = 'news' then '图文'
		when type3 = 'shortvideo' then '横短视频'
		when type3 = 'littlevideo' then '原生小视频'
		when type3 = 'ittlevideo' then '竖短视频'
		when type3 = 'dt_text' then '动态纯文本'
		when type3 = 'dt_image_text' then '动态图文'
		when type3 = 'dt_ugc_video' then '动态视频'
		when type3 = 'gallery' then '图集'
	end as type,
	title,
	m_content as content,
	comment_pv as commentNum,
	article_like_pv as likeNum
from udw_ns.default.bjh_feed_resource_rf as tb_re
where tb_re.event_day = '********'
),
res_feed as(
select
	passId,
	collect_list(to_json(named_struct('type', type, 'title', title, 'content', content, 'commentNum', commentNum, 'likeNum', likeNum ))) as contents
from feed_source
group by passId
),
 bjhFreezeStatus as (
	select
		user_id,
		to_json(named_struct('lastFreezeTime', last_freeze_time, 'lastFreezeReason', last_freeze_reason, 'lastUnfreezeTime', last_unfreeze_time)) as bjhFreezeStatus
	from udw_ns.default.bjh_author_view as tb_author
)
select
	tb_author.user_id as passId,
	name as bjhName,
	case
		when v_type = 0 then '无认证'
		when v_type = 1 then '金V'
		when v_type = 2 then '蓝V'
		when v_type = 3 then '黄V'
		when v_type = 4 then '机构真实性认证'
		else v_type
	end as bjhAuth,
	total_fans as bjhFansNum,
	is_news_active_weekely as bjh7DaysArticleNum,
	click_pv_weekely as bjh7DaysIncreasedPv,
	case
		when register_realinfo_status = 'pass' then '通过'
		when register_realinfo_status = 'risk' then '高危'
		when register_realinfo_status = 'unpass' then '失败'
		else '未填写'
	end as bjhRealNameAuth,
	case
		when author_fenceng = 1 then '核心作者'
		when author_fenceng = 2 then '优质作者'
		when author_fenceng = 3 then '潜力作者'
		when author_fenceng = 4 then '长尾作者'
		when author_fenceng = 5 then '低质作者'
		else '默认'
	end as bjhUserLevel,
	bjhFreezeStatus.bjhFreezeStatus,
	wb_name as wbName,
	wb_id as wbId,
	wb_node as wbNode,
	wx_account_alias as wxAccountAlias,
	wx_account_id as wxAccountId,
	wx_node as wxNode,
	contents
from udw_ns.default.bjh_author_view as tb_author
join bjhFreezeStatus on tb_author.user_id = bjhFreezeStatus.user_id
join res_feed on tb_author.user_id = res_feed.passId
where tb_author.event_day = '********'
limit 500