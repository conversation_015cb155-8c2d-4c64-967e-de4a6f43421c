    select
  user_id,
  name,
  max(rid_cnt_history) as history_cnt,
  sum(view_count) as view_cnt,
  sum(comment_pv) as comment_cnt,
  sum(article_like_pv) as article_like_cnt
from
  (
    select
      user_id,
      name,
      article_id,
      rid_cnt_history,
      view_count,
      comment_pv,
      article_like_pv
    from
      udw_ns.default.help_ods_bjh_base_info_df
    where
      event_day = '20231218'
      and user_id in (
       '159487508',
       '2438714693',
       '1490259802',
       '2438714693',
       '1490259802',
       '92207611',
       '282584946',
       '186904852',
       '1477994510',
       '405258625'
      )
    group by
      user_id,
      name,
      rid_cnt_history,
      article_id,
      view_count,
      comment_pv,
      article_like_pv
  ) t1
group by
  user_id,
  name