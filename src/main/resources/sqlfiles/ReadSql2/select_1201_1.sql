with mario_source as (
  SELECT
    month(create_time) as c_month,
    mario_req,
    get_json_object(public_sentiment_out, '$.name') as name,
    get_json_object(public_sentiment_out, '$.desc') as descs,
    get_json_object(public_sentiment_out, '$.res') as res
  FROM
    udw_ns.default.help_ods_engine_mario_publicsentiment_di
),
req_cnt as (
  SELECT
    c_month,
    count(1) as r_cnt
  FROM
    mario_source
  group by
    c_month
),
sent_cnt as (
  SELECT
    c_month,
    sum(
      if(
        name != ''
        and descs != ''
        and res != '',
        1,
        0
      )
    ) as s_cnt
  FROM
    mario_source
  group by
    c_month
)
SELECT
  req_cnt.c_month,
  req_cnt.r_cnt,
  sent_cnt.s_cnt
FROM
  req_cnt
  LEFT JOIN sent_cnt on req_cnt.c_month = sent_cnt.c_month