SELECT
    date_format(create_time, 'yyyy-MM-dd') as c_month,
    mario_req,
    get_json_object(public_sentiment_out, '$.name') as name,
    get_json_object(public_sentiment_out, '$.desc') as descs,
    get_json_object(public_sentiment_out, '$.res') as res,
    strategy_name
  FROM
    udw_ns.default.help_ods_engine_mario_publicsentiment_di
    where get_json_object(public_sentiment_out, '$.name') != ''
