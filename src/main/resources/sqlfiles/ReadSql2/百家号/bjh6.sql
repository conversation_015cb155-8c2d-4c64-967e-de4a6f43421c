SELECT
     name,
     count(distinct article_id) as acnt,
     count(CASE WHEN audit_status = 0 THEN 1 ELSE NULL END) AS jcnt
FROM (
    SELECT
        name,
        article_id,
        audit_status,
        ROW_NUMBER() OVER (PARTITION BY name,article_id  ORDER BY audit_status) AS row_num
    FROM udw_ns.default.help_ods_bjh_base_info_df
    where
        event_day = "20230917"
        and name in (
        '王落北',
        '互联网俊明说',
        '互联网八哥',
        '陈文伍',
        '庄志明律师',
        '小满',
        '互联网见闻录',
        '丁道师',
        '周蓬安',
        '知未科技'
        )
        and publish_time >= date_sub(current_date, 180) AND publish_time <= current_date
) t1
WHERE row_num = 1
group by name
limit 500