SELECT
  user_id,
  view_count,
  article,
  article_id,
  article_like_pv,
  article_num,
  audit_status,
  author_name,
  author_total_fans,
  avatar,
  bstatus,
  check_type,
  collect_pv,
  comment_pv,
  cover_images,
  cstatus,
  es_id,
  img_str,
  insert_time,
  interest,
  m_content,
  nid,
  publish_time,
  quality_reject_rate,
  recommend_count,
  reject_reason,
  safe_reject_rate,
  self_del_rate,
  share_pv,
  title,
  v_intro,
  wishes,
  main_url
FROM
  udw_ns.default.help_ods_bjh_base_info_df
WHERE
  event_day = '20231008'
  AND (
    (
      reject_reason like "%高危反动%"
      and status in ("unpass", "pass", "audit")
      and author_sub_type in ("individual", "other")
      and is_fake = "1"
      and is_active = "1"
    )
  )
 limit 500