with order_change_cnt as
(
	select
	  passport_id,
	  count(distinct address) as adress_change_cnt,
	  count(distinct name) as consignee_name_change_cnt
	from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info source_tabel
	where  event_day = '20231009'
	and (source_tabel.passport_id != null and source_tabel.passport_id != '')
	group by passport_id
),
order_risk as(
    select
         source_tabel.order_id
        ,source_tabel.passport_id
        ,if(source_tabel.passport_id != null and source_tabel.passport_id != '', order_change_cnt.adress_change_cnt, 0) as adress_change_cnt
        ,if(source_tabel.passport_id != null and source_tabel.passport_id != '', order_change_cnt.consignee_name_change_cnt, 0) as consignee_name_change_cnt
    from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info source_tabel
    left join
    order_change_cnt on source_tabel.passport_id = order_change_cnt.passport_id and (source_tabel.passport_id != null and source_tabel.passport_id != '')
    where  event_day = '20231009'
)
select * from order_risk
limit 500