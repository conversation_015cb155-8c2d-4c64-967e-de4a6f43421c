select
        name,
        count(distinct article_id) as act,
        count(distinct CASE WHEN audit_status = 0 THEN 1 ELSE NULL END) jujue
from udw_ns.default.help_ods_bjh_base_info_df
where
event_day = "20230917"
and name in (
'王落北',
'互联网俊明说',
'互联网八哥',
'陈文伍',
'庄志明律师',
'小满',
'互联网见闻录',
'丁道师',
'周蓬安',
'知未科技'
)
and publish_time >= date_sub(current_date, 180) AND publish_time <= current_date
group by name
limit 500