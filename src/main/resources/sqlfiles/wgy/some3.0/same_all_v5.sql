WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230819'
    AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 29
),
--cuid的 7天30天
same_cuid AS (
    SELECT
        source_table.cuid,
        COUNT(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) AS same_cuid_passid_cnt_last7,
        COUNT(distinct source_table.passport_id) AS same_cuid_passid_cnt_last30,
        COUNT(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.order_id END) AS same_cuid_order_cnt_last7,
        COUNT(distinct source_table.order_id) AS same_cuid_order_cnt_last30
    FROM source_table
    WHERE (cuid IS NOT NULL AND cuid != '""')
    GROUP BY source_table.cuid
),
--mobile的 7天30天
same_mobile AS (
    SELECT
        source_table.mobile,
        COUNT(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) AS same_phone_passid_cnt_last7,
        COUNT(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.order_id END) AS same_phone_order_cnt_last7,
        COUNT(distinct source_table.passport_id) AS same_phone_passid_cnt_last30,
        COUNT(distinct source_table.order_id) AS same_phone_order_cnt_last30
    FROM source_table
    WHERE (mobile IS NOT NULL AND mobile != '""')
    GROUP BY source_table.mobile
),
--ip的 当日和7天
same_ip AS (
    SELECT
        source_table.ip,
        --当日的还得确认一下
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.cuid END) as same_ip_cuid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.passport_id END) as same_ip_uid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.cuid END) as same_ip_cuid_cnt_last7,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) as same_ip_uid_cnt_last7
    FROM source_table
    WHERE (ip IS NOT NULL AND ip != '""')
    GROUP BY source_table.ip
),
--pay_passport_id的 当日和7天
same_pay_passport_id AS (
    SELECT
        source_table.pay_passport_id,
        --当日的还得确认一下
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.cuid END) as same_paypassid_cuid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.passport_id END) as same_paypassid_uid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.cuid END) as same_paypassid_cuid_cnt_last7,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) as same_paypassid_uid_cnt_last7
    FROM source_table
    WHERE (pay_passport_id IS NOT NULL AND pay_passport_id != '')
    GROUP BY source_table.pay_passport_id
),
--tracking_number的 当日的
same_tracking_number AS (
    SELECT
        source_table.tracking_number,
        --当日的还得确认一下
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.order_id END) as same_expressnumber_order_cnt,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.shop_id END) as same_expressnumber_shop_cnt
    FROM source_table
    WHERE (tracking_number IS NOT NULL AND tracking_number != '""')
    GROUP BY source_table.tracking_number
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id,
    same_cuid.same_cuid_passid_cnt_last7,
    same_cuid.same_cuid_passid_cnt_last30,
    same_cuid.same_cuid_order_cnt_last7,
    same_cuid.same_cuid_order_cnt_last30,
    same_mobile.same_phone_passid_cnt_last7,
    same_mobile.same_phone_order_cnt_last7,
    same_mobile.same_phone_passid_cnt_last30,
    same_mobile.same_phone_order_cnt_last30,
    same_ip.same_ip_cuid_cnt_last1,
    same_ip.same_ip_uid_cnt_last1,
    same_ip.same_ip_cuid_cnt_last7,
    same_ip.same_ip_uid_cnt_last7,
    same_pay_passport_id.same_paypassid_cuid_cnt_last1,
    same_pay_passport_id.same_paypassid_uid_cnt_last1,
    same_pay_passport_id.same_paypassid_cuid_cnt_last7,
    same_pay_passport_id.same_paypassid_uid_cnt_last7,
    same_tracking_number.same_expressnumber_order_cnt,
    same_tracking_number.same_expressnumber_shop_cnt
FROM source_table t1
LEFT JOIN same_cuid on t1.cuid = same_cuid.cuid and (t1.cuid is not null AND t1.cuid != '""' AND trim(t1.cuid) != '')
LEFT JOIN same_mobile on t1.mobile = same_mobile.mobile and (t1.mobile IS NOT NULL AND t1.mobile != '""' AND trim(t1.mobile) != '')
LEFT JOIN same_ip on t1.ip = same_ip.ip and (t1.ip IS NOT NULL AND t1.ip != '""' AND trim(t1.ip) != '')
LEFT JOIN same_pay_passport_id on t1.pay_passport_id = same_pay_passport_id.pay_passport_id and (t1.pay_passport_id IS NOT NULL AND t1.pay_passport_id != '' AND trim(t1.pay_passport_id) != '')
LEFT JOIN same_tracking_number on t1.tracking_number = same_tracking_number.tracking_number and (t1.tracking_number IS NOT NULL AND t1.tracking_number != '""' AND trim(t1.tracking_number) != '' )
limit 100
