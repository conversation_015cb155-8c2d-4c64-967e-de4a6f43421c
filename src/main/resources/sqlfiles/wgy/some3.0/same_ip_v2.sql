WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230814'
    AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 29
),
same_ip AS (
    SELECT
        source_table.ip,
        --当日的还得确认一下
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.cuid END) as same_ip_cuid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 1 THEN source_table.passport_id END) as same_ip_uid_cnt_last1,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.cuid END) as same_ip_cuid_cnt_last7,
        count(distinct CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) as same_ip_uid_cnt_last7
    FROM source_table
    WHERE (ip IS NOT NULL AND ip != '""')
    GROUP BY source_table.ip
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id,
    same_ip.same_ip_cuid_cnt_last1,
    same_ip.same_ip_uid_cnt_last1,
    same_ip.same_ip_cuid_cnt_last7,
    same_ip.same_ip_uid_cnt_last7
FROM source_table t1
LEFT JOIN same_ip on t1.ip = same_ip.ip
limit 100