SELECT
    cuid,
    COUNT( distinct CASE WHEN datediff(pay_time, lag_pay_time_last7) BETWEEN 1 AND 6 THEN passport_id END) AS same_cuid_passid_cnt_last7,
    COUNT( distinct CASE WHEN datediff(pay_time, lag_pay_time_last7) BETWEEN 1 AND 6 THEN order_id END) AS same_cuid_order_cnt_last7,
    COUNT( distinct CASE WHEN datediff(pay_time, lag_pay_time_last30) BETWEEN 1 AND 29 THEN passport_id END) AS same_cuid_passid_cnt_last30,
    COUNT( distinct CASE WHEN datediff(pay_time, lag_pay_time_last30) BETWEEN 1 AND 29 THEN order_id END) AS same_cuid_order_cnt_last30
FROM (
    SELECT
        cuid,
        pay_time,
        passport_id,
        order_id,
        date_format(pay_time, "yyyy-MM-dd") as fmt_pay_time,
        lag(pay_time, 6) OVER (PARTITION BY  date_format(pay_time, "yyyy-MM-dd") ORDER BY  date_format(pay_time, "yyyy-MM-dd")) AS lag_pay_time_last7,
        lag(pay_time, 29) OVER (PARTITION BY  date_format(pay_time, "yyyy-MM-dd") ORDER BY  date_format(pay_time, "yyyy-MM-dd")) AS lag_pay_time_last30
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = '20230818'
    AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 30
) t1
GROUP BY cuid
limit 100

