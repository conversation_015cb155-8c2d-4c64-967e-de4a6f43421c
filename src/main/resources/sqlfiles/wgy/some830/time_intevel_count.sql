-- 源表 逻辑有问题
with source_tabel as
(
	select
	case when (get_json_object(cashier_pay_info,'$.status')) is not null  then "支付成功"
		 else null
		 end pay_status
	,case when buy_num != 0 then round(total_amount/buy_num,2) else 0 end product_price
	,substr(mobile,1,10) as mobile_number_top10
	,case when buy_num>30 then 1 else 0 end is_many_order
    ,order_id
	,batch_id
	,sub_order_id
	,cuid
	,passport_id
	,ucid
	,copuser_id
	,shop_id
	,shop_name
	,product_id
	,product_name
	,sku_id
	,sku_desc
	,case when pay_type = 0 then '在线支付'
		  when pay_type = 100 then '货到付款'
		  else null
		  end pay_type
	,pay_channel
	,total_amount
	,payment_amount
	,freight_amount
	,cheap_amount
	,discount_amount_detail
	,baiduid
	,ip
	,name
	,mobile
	,province
	,city
	,area
	,address
	-- 100创建订单.200支付成功.300订单待商家确认.400商家备货中.500商家已发货.600买家已确认收货.700订单已取消.800订单已核销
	,case when status = 100 then '创建订单'
		  when status = 200 then '付成功'
		  when status = 300 then '订单待商家确认'
		  when status = 400 then '商家备货中'
		  when status = 500 then '商家已发货'
		  when status = 600 then '买家已确认收货'
		  when status = 700 then '订单已取消'
		  when status = 800 then '订单已核销'
		  else null
		  end status
	,express_name
	,tracking_number
	,create_time
	,pay_time
	,cancel_time
	,consign_time
	,confirm_time
	,refund_create_time
	,refund_finish_time
	,refund_reason
	,pay_passport_id
	,flow_info
	,content_type
	,sch
	,cps_type
	,parent_ext_info
	,order_ext_info
	,detail_ext_info
	,cashier_pay_info
	,agent_id
	,flow_channel
	,buy_num
	,tags
	,evaluate_time
	,product_score
	,shop_service_score
	,shop_logistics_score
	,original_content
	,images
	,refund_desc
	,complaint_create_time
	,complaint_status
	,complaint_reason
	,app_id
	-- 0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单
	,case when express_status = 0 then '在途中'
		  when express_status = 1 then '疑难'
		  when express_status = 2 then '在途中'
		  when express_status = 3 then '已签收'
		  when express_status = 4 then '退签'
		  when express_status = 5 then '同城派送中'
		  when express_status = 6 then '退回'
		  when express_status = 7 then '转单'
		  else null
		  end express_status
	,express_data
	,return_data
	,status_detail
	-- ,product_json
	,event_day
	from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	where  event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
time_intevel_count as
(
	select order_id,
		   case when pay_hours != 0 and refund_create_hours!= 0 then refund_create_hours-pay_hours else 0 end refund_creat_time_interval,
		   case when pay_hours != 0 and refund_finish_hours!= 0 then refund_finish_hours-pay_hours else 0 end refund_time_interval,
		   case when pay_hours != 0 and evaluate_time_hours!= 0 then evaluate_time_hours-pay_hours else 0 end evaluate_time_interval
	from (
		select
		order_id,
		-- 下单时间为pay_time  按照需求所有时间转换为小时
		case when pay_time is not null then to_unix_timestamp('pay_time','MM-dd-yyyy HH:mm:ss')/3600 else 0 end pay_hours,
		case when refund_create_time is not null then to_unix_timestamp('refund_create_time','MM-dd-yyyy HH:mm:ss')/3600 else 0 end refund_create_hours,
		case when refund_finish_time is not null then to_unix_timestamp('refund_finish_time','MM-dd-yyyy HH:mm:ss')/3600 else 0 end refund_finish_hours,
		case when evaluate_time is not null then to_unix_timestamp('evaluate_time','MM-dd-yyyy HH:mm:ss')/3600 else 0 end evaluate_time_hours
		from source_tabel
		)

)
select * from time_intevel_count
limit 100
