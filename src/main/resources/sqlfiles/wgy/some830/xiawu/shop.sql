select
    t1.order_id as order_id,
    t2.phone_number as shop_phone_number
from
(
    select order_id,shop_id
    from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    where event_day = '20230827'
)t1
	left join
(
    select shop_id,phone_number
    from udw_ns.default.fengchao_biz_ecom_dwd_shop_risk_control_info
    -- 日期需要替换
    where event_day = '20230827'
)t2
	on t1.shop_id = t2.shop_id