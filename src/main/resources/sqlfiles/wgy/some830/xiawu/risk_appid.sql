select
    	order_id
    	,app_id
    	,package_id
    	,package_desc
    	,product_type
    	,detail_original_price
    	,detail_actual_price
    	,b_prod_dec_promotion_id
    	,b_prod_dec_promotion_amount
    	,b_ord_dec_promotion_id
    	,b_ord_dec_promotion_amount
    	,p_flush_sale_promotion_id
    	,p_flush_sale_promotion_amount
    	,b_flush_sale_promotion_id
    	,b_flush_sale_promotion_amount
    	,p_full_dec_promotion_id
    	,p_full_dec_promotion_amount
    	,b_pintuan_promotion_id
    	,b_pintuan_promotion_amount
    	,p_reduc_to_coupon_id
    	,p_reduc_to_coupon_amount
    	,p_full_coupon_id
    	,p_full_coupon_amount
    	,b_full_coupon_id
    	,b_full_coupon_amount
    	,first_cate_id
    	,first_cate_name
    	,second_cate_id
    	,second_cate_name
    	,third_cate_id
    	,third_cate_name
    	,fourth_cate_id
    	,fourth_cate_name
    	,submit_type
    	,id_type
    	,open_id
    	,adjust_info
    	,p_discount_coupon_amount
    	,b_discount_coupon_amount
from udw_ns.default.fengchao_biz_ecom_dwd_order_detail
where
-- 分区条件日期使用f字符插值器传入需要的分区值
-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
event_day = '20230829'
and app_id in (269,5,10007,100012)