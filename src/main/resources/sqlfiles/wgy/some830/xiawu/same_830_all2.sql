with source_tabel as
(
	select
    order_id
	,batch_id
	,sub_order_id
	,cuid
	,passport_id
	,ucid
	,copuser_id
	,shop_id
	,shop_name
	,product_id
	,product_name
	,sku_id
	,sku_desc
	,case when pay_type = 0 then '在线支付'
		  when pay_type = 100 then '货到付款'
		  else null
		  end pay_type
	,pay_channel
	,total_amount
	,payment_amount
	,freight_amount
	,cheap_amount
	,discount_amount_detail
	,baiduid
	,ip
	,name
	,mobile
	,province
	,city
	,area
	,address
	-- 100创建订单.200支付成功.300订单待商家确认.400商家备货中.500商家已发货.600买家已确认收货.700订单已取消.800订单已核销
	,case when status = 100 then '创建订单'
		  when status = 200 then '付成功'
		  when status = 300 then '订单待商家确认'
		  when status = 400 then '商家备货中'
		  when status = 500 then '商家已发货'
		  when status = 600 then '买家已确认收货'
		  when status = 700 then '订单已取消'
		  when status = 800 then '订单已核销'
		  else null
		  end status
	,express_name
	,tracking_number
	,create_time
	,pay_time
	,cancel_time
	,consign_time
	,confirm_time
	,refund_create_time
	,refund_finish_time
	,refund_reason
	,pay_passport_id
	,flow_info
	,content_type
	,sch
	,cps_type
	,parent_ext_info
	,order_ext_info
	,detail_ext_info
	,cashier_pay_info
	,agent_id
	,flow_channel
	,buy_num
	,tags
	,evaluate_time
	,product_score
	,shop_service_score
	,shop_logistics_score
	,original_content
	,images
	,refund_desc
	,complaint_create_time
	,complaint_status
	,complaint_reason
	,app_id
	-- 0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单
	,case when express_status = 0 then '在途中'
		  when express_status = 1 then '疑难'
		  when express_status = 2 then '在途中'
		  when express_status = 3 then '已签收'
		  when express_status = 4 then '退签'
		  when express_status = 5 then '同城派送中'
		  when express_status = 6 then '退回'
		  when express_status = 7 then '转单'
		  else null
		  end express_status
	,express_data
	,return_data
	,status_detail
	,product_json
	,event_day
	from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	where  event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
-- 订单明细表数据源
dwd_order_detail as
(
	select
	order_id
	,app_id
	,package_id
	,package_desc
	,product_type
	,detail_original_price
	,detail_actual_price
	,b_prod_dec_promotion_id
	,b_prod_dec_promotion_amount
	,b_ord_dec_promotion_id
	,b_ord_dec_promotion_amount
	,p_flush_sale_promotion_id
	,p_flush_sale_promotion_amount
	,b_flush_sale_promotion_id
	,b_flush_sale_promotion_amount
	,p_full_dec_promotion_id
	,p_full_dec_promotion_amount
	,b_pintuan_promotion_id
	,b_pintuan_promotion_amount
	,p_reduc_to_coupon_id
	,p_reduc_to_coupon_amount
	,p_full_coupon_id
	,p_full_coupon_amount
	,b_full_coupon_id
	,b_full_coupon_amount
	,first_cate_id
	,first_cate_name
	,second_cate_id
	,second_cate_name
	,third_cate_id
	,third_cate_name
	,fourth_cate_id
	,fourth_cate_name
	,submit_type
	,id_type
	,open_id
	,adjust_info
	,p_discount_coupon_amount
	,b_discount_coupon_amount
	from udw_ns.default.fengchao_biz_ecom_dwd_order_detail
	where
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
shop_phone_number as
(
	select shop_id,phone_number
	from udw_ns.default.fengchao_biz_ecom_dwd_shop_risk_control_info
	-- 日期需要替换
	where event_day = '20230827'
)
select
    source_tabel.order_id
    ,source_tabel.batch_id
    ,source_tabel.sub_order_id
    ,source_tabel.cuid
    ,source_tabel.passport_id
    ,source_tabel.ucid
    ,source_tabel.copuser_id
    ,source_tabel.shop_id
    ,source_tabel.shop_name
    ,source_tabel.product_id
    ,source_tabel.product_name
    ,source_tabel.sku_id
    ,source_tabel.sku_desc
    ,source_tabel.pay_type
    ,source_tabel.pay_channel
    ,source_tabel.total_amount
    ,source_tabel.payment_amount
    ,source_tabel.freight_amount
    ,source_tabel.cheap_amount
    ,source_tabel.discount_amount_detail
    ,source_tabel.baiduid
    ,source_tabel.ip
    ,source_tabel.name
    ,source_tabel.mobile
    ,source_tabel.province
    ,source_tabel.city
    ,source_tabel.area
    ,source_tabel.address
    ,source_tabel.status
    ,source_tabel.express_name
    ,source_tabel.tracking_number
    ,source_tabel.create_time
    ,source_tabel.pay_time
    ,source_tabel.cancel_time
    ,source_tabel.consign_time
    ,source_tabel.confirm_time
    ,source_tabel.refund_create_time
    ,source_tabel.refund_finish_time
    ,source_tabel.refund_reason
    ,source_tabel.pay_passport_id
    ,source_tabel.flow_info
    ,source_tabel.content_type
    ,source_tabel.sch
    ,source_tabel.cps_type
    ,source_tabel.parent_ext_info
    ,source_tabel.order_ext_info
    ,source_tabel.detail_ext_info
    ,source_tabel.cashier_pay_info
    ,source_tabel.agent_id
    ,source_tabel.flow_channel
    ,source_tabel.buy_num
    ,source_tabel.tags
    ,source_tabel.evaluate_time
    ,source_tabel.product_score
    ,source_tabel.shop_service_score
    ,source_tabel.shop_logistics_score
    ,source_tabel.original_content
    ,source_tabel.images
    ,source_tabel.refund_desc
    ,source_tabel.complaint_create_time
    ,source_tabel.complaint_status
    ,source_tabel.complaint_reason
    ,source_tabel.app_id
    ,source_tabel.express_status
    ,source_tabel.express_data
    ,source_tabel.return_data
    ,source_tabel.status_detail
    ,source_tabel.product_json
    ,dwd_order_detail.package_id
    ,dwd_order_detail.package_desc
    ,dwd_order_detail.product_type
    ,dwd_order_detail.detail_original_price
    ,dwd_order_detail.detail_actual_price
    ,dwd_order_detail.b_prod_dec_promotion_id
    ,dwd_order_detail.b_prod_dec_promotion_amount
    ,dwd_order_detail.b_ord_dec_promotion_id
    ,dwd_order_detail.b_ord_dec_promotion_amount
    ,dwd_order_detail.p_flush_sale_promotion_id
    ,dwd_order_detail.p_flush_sale_promotion_amount
    ,dwd_order_detail.b_flush_sale_promotion_id
    ,dwd_order_detail.b_flush_sale_promotion_amount
    ,dwd_order_detail.p_full_dec_promotion_id
    ,dwd_order_detail.p_full_dec_promotion_amount
    ,dwd_order_detail.b_pintuan_promotion_id
    ,dwd_order_detail.b_pintuan_promotion_amount
    ,dwd_order_detail.p_reduc_to_coupon_id
    ,dwd_order_detail.p_reduc_to_coupon_amount
    ,dwd_order_detail.p_full_coupon_id
    ,dwd_order_detail.p_full_coupon_amount
    ,dwd_order_detail.b_full_coupon_id
    ,dwd_order_detail.b_full_coupon_amount
    ,dwd_order_detail.first_cate_id
    ,dwd_order_detail.first_cate_name
    ,dwd_order_detail.second_cate_id
    ,dwd_order_detail.second_cate_name
    ,dwd_order_detail.third_cate_id
    ,dwd_order_detail.third_cate_name
    ,dwd_order_detail.fourth_cate_id
    ,dwd_order_detail.fourth_cate_name
    ,dwd_order_detail.submit_type
    ,dwd_order_detail.id_type
    ,dwd_order_detail.open_id
    ,dwd_order_detail.adjust_info
    ,dwd_order_detail.p_discount_coupon_amount
    ,dwd_order_detail.b_discount_coupon_amount
from source_tabel
left join
dwd_order_detail on source_tabel.order_id = dwd_order_detail.order_id and source_tabel.app_id = dwd_order_detail.app_id
limit 100