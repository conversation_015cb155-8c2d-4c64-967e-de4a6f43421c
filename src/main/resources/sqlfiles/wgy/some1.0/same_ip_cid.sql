WITH dianshang_order AS (
  SELECT
      `cuid`,
      `ip`,
      `passport_id`,
      `pay_time`
  FROM
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where event_day = "20230814"
  and ip is not null
  and ip != ''
)
SELECT
  t1.ip,
  count(distinct t1.cuid) as same_ip_cuid_cnt_last1,
  count(distinct t1.passport_id) as same_ip_uid_cnt_last1,
  count(distinct t2.cuid) as same_ip_cuid_cnt_last7,
  count(distinct t2.passport_id) as same_ip_uid_cnt_last7
FROM
  (
    SELECT
     `cuid`,
     `ip`,
     `passport_id`,
     `pay_time`
    FROM dianshang_order
    WHERE DATE(pay_time) = current_date;
  )t1
  FULL OUTER JOIN
  (
    SELECT
      `cuid`,
      `ip`,
      `passport_id`,
      `pay_time`
    FROM dianshang_order
    WHERE DATEDIFF(current_timestamp(),pay_time) between 1 and 6
  )t2 ON t1.ip = t2.ip
GROUP BY t1.ip
limit 100