WITH source_tabel AS(
SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
WHERE event_day = "20230814"
AND DATEDIFF(current_timestamp(),pay_time) between 1 and 29
),
same_cuid_passid_cnt_last7 as(
    SELECT
      source_tabel.cuid,
      COUNT(distinct source_tabel.passport_id) AS same_cuid_passid_cnt_last7,
      COUNT(distinct source_tabel.order_id) AS same_cuid_order_cnt_last7
    FROM source_tabel
    WHERE cuid is not null and  cuid != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
    GROUP BY source_tabel.cuid
),
same_phone_passid_cnt_last7 as(
    SELECT
      source_tabel.mobile,
      COUNT(distinct source_tabel.passport_id) AS same_phone_passid_cnt_last7,
      COUNT(distinct source_tabel.order_id) AS same_phone_order_cnt_last7
    FROM source_tabel
    WHERE mobile is not null and  mobile != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
    GROUP BY source_tabel.mobile
),
same_cuid_passid_cnt_last30 as(
    SELECT
      source_tabel.cuid,
      COUNT(distinct source_tabel.passport_id) AS same_cuid_passid_cnt_last30,
      COUNT(distinct source_tabel.order_id) AS same_cuid_order_cnt_last30
    FROM source_tabel
    WHERE cuid is not null and  cuid != ''
    GROUP BY source_tabel.cuid
),
same_phone_passid_cnt_last30 as(
    SELECT
      source_tabel.mobile,
      COUNT(distinct source_tabel.passport_id) AS same_phone_passid_cnt_last30,
      COUNT(distinct source_tabel.order_id) AS same_phone_order_cnt_last30
    FROM source_tabel
    WHERE mobile is not null and  mobile != ''
    GROUP BY source_tabel.mobile
),
same_paypassid_cuid_cnt_last7 as(
    SELECT
      source_tabel.pay_passport_id,
      count(distinct source_tabel.cuid) as same_paypassid_cuid_cnt_last7,
      count(distinct source_tabel.passport_id) as same_paypassid_uid_cnt_last7
    FROM source_tabel
    WHERE pay_passport_id is not null and  pay_passport_id != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
    GROUP BY source_tabel.pay_passport_id
),
same_expressnumber_order_cnt as(
    SELECT
      source_tabel.tracking_number,
      count(distinct source_tabel.order_id) as same_expressnumber_order_cnt,
      count(distinct source_tabel.shop_id) as same_expressnumber_shop_cnt
    FROM source_tabel
    WHERE tracking_number is not null and  tracking_number != ''
    GROUP BY source_tabel.tracking_number
),
same_ip_cuid_cnt_last1 as (
    SELECT
      source_tabel.ip,
      count(distinct source_tabel.cuid) as same_ip_cuid_cnt_last1,
      count(distinct source_tabel.passport_id) as same_ip_uid_cnt_last1
    FROM source_tabel
    WHERE ip is not null and  ip != ''
    GROUP BY source_tabel.ip
),
same_ip_cuid_cnt_last7 as (
    SELECT
      source_tabel.ip,
      count(distinct source_tabel.cuid) as same_ip_cuid_cnt_last7,
      count(distinct source_tabel.passport_id) as same_ip_uid_cnt_last7
    FROM source_tabel
    WHERE ip is not null and  ip != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
    GROUP BY source_tabel.ip
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id
FROM source_tabel t1
LEFT JOIN same_cuid_passid_cnt_last7 on t1.cuid = same_cuid_passid_cnt_last7.cuid
LEFT JOIN same_phone_passid_cnt_last7 on t1.mobile = same_phone_passid_cnt_last7.mobile
LEFT JOIN same_cuid_passid_cnt_last30 on t1.cuid = same_cuid_passid_cnt_last30.cuid
LEFT JOIN same_phone_passid_cnt_last30 on t1.mobile = same_phone_passid_cnt_last30.mobile
LEFT JOIN same_paypassid_cuid_cnt_last7 on t1.pay_passport_id = same_paypassid_cuid_cnt_last7.pay_passport_id
LEFT JOIN same_expressnumber_order_cnt on t1.tracking_number = same_expressnumber_order_cnt.tracking_number
LEFT JOIN same_ip_cuid_cnt_last1 on t1.ip = same_ip_cuid_cnt_last1.ip
LEFT JOIN same_ip_cuid_cnt_last7  on t1.ip = same_ip_cuid_cnt_last7.ip
limit 100