WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230814'
        AND (cuid IS NOT NULL AND cuid != '')
        AND (mobile IS NOT NULL AND mobile != '')
        AND (pay_passport_id IS NOT NULL AND pay_passport_id != '')
        AND (tracking_number IS NOT NULL AND tracking_number != '')
        AND (ip IS NOT NULL AND ip != '')
        AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 29
),
same_cuid_passid_cnt AS (
    SELECT
        source_table.cuid,
        COUNT(DISTINCT source_table.passport_id) AS same_cuid_passid_cnt_last7,
        COUNT(DISTINCT source_table.order_id) AS same_cuid_order_cnt_last7,
        COUNT(DISTINCT source_table.passport_id) AS same_cuid_passid_cnt_last30,
        COUNT(DISTINCT source_table.order_id) AS same_cuid_order_cnt_last30
    FROM source_table
    WHERE DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 6
    GROUP BY source_table.cuid
),
same_phone_passid_cnt AS (
    SELECT
        source_table.mobile,
        COUNT(DISTINCT source_table.passport_id) AS same_phone_passid_cnt_last7,
        COUNT(DISTINCT source_table.order_id) AS same_phone_order_cnt_last7,
        COUNT(DISTINCT source_table.passport_id) AS same_phone_passid_cnt_last30,
        COUNT(DISTINCT source_table.order_id) AS same_phone_order_cnt_last30
    FROM source_table
    WHERE DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 6
    GROUP BY source_table.mobile
),
same_paypassid_cuid_cnt AS (
    SELECT
        source_table.pay_passport_id,
        COUNT(DISTINCT source_table.cuid) AS same_paypassid_cuid_cnt_last7,
        COUNT(DISTINCT source_table.passport_id) AS same_paypassid_uid_cnt_last7
    FROM source_table
    WHERE DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 6
    GROUP BY source_table.pay_passport_id
),
same_expressnumber_order_cnt AS (
    SELECT
        source_table.tracking_number,
        COUNT(DISTINCT source_table.order_id) AS same_expressnumber_order_cnt,
        COUNT(DISTINCT source_table.shop_id) AS same_expressnumber_shop_cnt
    FROM source_table
    GROUP BY source_table.tracking_number
),
same_ip_cuid_cnt AS (
    SELECT
        source_table.ip,
        COUNT(DISTINCT source_table.cuid) AS same_ip_cuid_cnt_last1,
        COUNT(DISTINCT source_table.passport_id) AS same_ip_uid_cnt_last1,
        COUNT(DISTINCT source_table.cuid) AS same_ip_cuid_cnt_last7,
        COUNT(DISTINCT source_table.passport_id) AS same_ip_uid_cnt_last7
    FROM source_table
    GROUP BY source_table.ip
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id,
    same_cuid_passid_cnt.same_cuid_passid_cnt_last7,
    same_cuid_passid_cnt.same_cuid_order_cnt_last7,
    same_cuid_passid_cnt.same_cuid_passid_cnt_last30,
    same_cuid_passid_cnt.same_cuid_order_cnt_last30,
    same_phone_passid_cnt.same_phone_passid_cnt_last7,
    same_phone_passid_cnt.same_phone_order_cnt_last7,
    same_phone_passid_cnt.same_phone_passid_cnt_last30,
    same_phone_passid_cnt.same_phone_order_cnt_last30,
    same_paypassid_cuid_cnt.same_paypassid_cuid_cnt_last7,
    same_paypassid_cuid_cnt.same_paypassid_uid_cnt_last7,
    same_expressnumber_order_cnt.same_expressnumber_order_cnt,
    same_expressnumber_order_cnt.same_expressnumber_shop_cnt,
    same_ip_cuid_cnt.same_ip_cuid_cnt_last1,
    same_ip_cuid_cnt.same_ip_uid_cnt_last1,
    same_ip_cuid_cnt.same_ip_cuid_cnt_last7,
    same_ip_cuid_cnt.same_ip_uid_cnt_last7
FROM source_table t1
LEFT JOIN same_cuid_passid_cnt ON t1.cuid = same_cuid_passid_cnt.cuid
LEFT JOIN same_phone_passid_cnt ON t1.mobile = same_phone_passid_cnt.mobile
LEFT JOIN same_paypassid_cuid_cnt ON t1.pay_passport_id = same_paypassid_cuid_cnt.pay_passport_id
LEFT JOIN same_expressnumber_order_cnt ON t1.tracking_number = same_expressnumber_order_cnt.tracking_number
LEFT JOIN same_ip_cuid_cnt ON t1.ip = same_ip_cuid_cnt.ip
LIMIT 100;
