WITH dianshang_order AS (
  SELECT
      `cuid`,
      `pay_passport_id`,
      `passport_id`,
      `pay_time`
  FROM
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where event_day = "20230814"
)
SELECT
  t1.pay_passport_id,
  count(distinct t1.cuid) as same_paypassid_cuid_cnt_last1,
  count(distinct t1.passport_id) as same_paypassid_uid_cnt_last1,
  count(distinct t2.cuid) as same_paypassid_cuid_cnt_last7,
  count(distinct t2.passport_id) as same_paypassid_uid_cnt_last7
FROM(
    SELECT
      `cuid`,
      `pay_passport_id`,
      `passport_id`,
      `pay_time`
    FROM dianshang_order
    WHERE DATEDIFF(current_timestamp(),pay_time) between 1 and 6
    )t1
  FULL OUTER JOIN
  (
    SELECT
      `cuid`,
      `pay_passport_id`,
      `passport_id`,
      `pay_time`
    FROM dianshang_order
    WHERE DATEDIFF(current_timestamp(),pay_time) between 1 and 29
  )t2 ON t1.pay_passport_id = t2.pay_passport_id
GROUP by t1.pay_passport_id
limit 100