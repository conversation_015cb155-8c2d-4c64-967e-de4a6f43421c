WITH dianshang_order AS (
  SELECT
  `pay_time`,
  `passport_id`,
  `cuid`,
  `order_id`,
  `mobile`
  FROM
 udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
where event_day = "20230814"
AND cuid IS NOT NULL AND cuid != ''
AND mobile IS NOT NULL AND mobile != ''
)
SELECT
  t1.cuid,
  t2.mobile,
  COUNT(DISTINCT t1.passport_id) AS same_cuid_passid_cnt_last30,
  COUNT(DISTINCT t1.order_id) AS same_cuid_order_cnt_last30,
  COUNT(DISTINCT t2.passport_id) AS same_phone_passid_cnt_last30,
  COUNT(DISTINCT t2.order_id) AS same_phone_order_cnt_last30,
  COUNT(DISTINCT t3.passport_id) AS same_cuid_passid_cnt_last7,
  COUNT(DISTINCT t3.order_id) AS same_cuid_order_cnt_last7,
  COUNT(DISTINCT t4.passport_id) AS same_phone_passid_cnt_last7,
  COUNT(DISTINCT t4.order_id) AS same_phone_order_cnt_last7
FROM
  (
    SELECT
      cuid,
      passport_id,
      order_id，
      mobile
    FROM
      dianshang_order --30天的cuid
    WHERE
      DATEDIFF(current_timestamp(),pay_time) between 1 and 29

  ) t1
FULL OUTER JOIN
  (
    SELECT
      mobile,
      passport_id,
      order_id
    FROM
      dianshang_order --30天的passport_id
    WHERE
      DATEDIFF(current_timestamp(),pay_time) between 1 and 29

  ) t2 ON t1.cuid = t2.cuid and t1.mobile = t2.mobile
FULL OUTER JOIN
  (
    SELECT
      cuid,
      passport_id,
      order_id
    FROM
      dianshang_order --7天的cuid
    WHERE
       DATEDIFF(current_timestamp(),pay_time) between 1 and 6
  ) t3 ON t1.cuid = t3.cuid
FULL OUTER JOIN
  (
    SELECT
      mobile,
      passport_id,
      order_id
    FROM
      dianshang_order --7天的passport_id
    WHERE
       DATEDIFF(current_timestamp(),pay_time) between 1 and 6
  ) t4 ON t2.mobile = t4.mobile
GROUP BY t1.cuid, t2.mobile;
limit 100