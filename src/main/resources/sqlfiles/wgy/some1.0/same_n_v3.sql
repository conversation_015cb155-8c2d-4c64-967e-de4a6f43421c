WITH dianshang_order AS(
SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id,
        t1.cuid as last1_cuid,
        t1.passport_id as last1_passport_id,
        t1.order_id as last1_order_id,
        t1.shop_id as last1_shop_id,
        t2.cuid as last7_cuid,
        t2.order_id as last7_order_id,
        t2.passport_id as last7_passport_id,
        t3.passport_id as last30_passport_id,
        t3.order_id as last30_order_id
FROM
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814" and order_id is not null and  order_id != ''
    AND DATE(pay_time) = current_date()
)t1
FULL OUTER JOIN
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814" and order_id is not null and  order_id != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
)t2 ON t1.order_id = t2.order_id
FULL OUTER JOIN
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814" and order_id is not null and  order_id != ''
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 29
)t3 ON t1.order_id = t3.order_id
)
SELECT
    dianshang_order.shop_id,
    `dianshang_order`.`name`,
    dianshang_order.passport_id,
    dianshang_order.cuid,
    dianshang_order.order_id,
    dianshang_order.mobile,
    dianshang_order.tracking_number,
    dianshang_order.ip,
    dianshang_order.pay_time,
    dianshang_order.pay_passport_id
FROM dianshang_order
limit 100