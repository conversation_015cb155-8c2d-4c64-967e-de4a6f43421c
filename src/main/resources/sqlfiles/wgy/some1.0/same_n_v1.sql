WITH dianshang_order AS(
SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id,
        t1.cuid as last1_cuid,
        t1.passport_id as last1_passport_id,
        t1.order_id as last1_order_id,
        t1.shop_id as last1_shop_id,
        t2.cuid as last7_cuid,
        t2.order_id as last7_order_id,
        t2.passport_id as last7_passport_id,
        t3.passport_id as last30_passport_id,
        t3.order_id as last30_order_id
FROM
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814"
    AND DATE(pay_time) = current_date()
)t1
INNER JOIN
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814"
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 6
)t2 ON t1.order_id = t2.order_id
INNER JOIN
(
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = "20230814"
    AND DATEDIFF(current_timestamp(),pay_time) between 1 and 29
)t3 ON t1.order_id = t3.order_id
where t1.cuid is not null and t1.cuid != '' and
t1.mobile is not null and t1.mobile != '' and
t1.passport_id is not null and t1.passport_id != '' and
t1.pay_passport_id is not null and t1.pay_passport_id != '' and
t1.tracking_number is not null and t1.tracking_number != '' and
t1.ip is not null and t1.ip != ''
),
same_paypassid as (
    SELECT
      orders.pay_passport_id,
      count(distinct orders.last1_cuid) as same_paypassid_cuid_cnt_last1,
      count(distinct orders.last1_passport_id) as same_paypassid_uid_cnt_last1,
      count(distinct orders.last7_cuid) as same_paypassid_cuid_cnt_last7,
      count(distinct orders.last1_passport_id) as same_paypassid_uid_cnt_last7
    FROM dianshang_order orders
    GROUP BY orders.pay_passport_id
)
SELECT
    dianshang_order.shop_id,
    `dianshang_order`.`name`,
    dianshang_order.passport_id,
    dianshang_order.cuid,
    dianshang_order.order_id,
    dianshang_order.mobile,
    dianshang_order.tracking_number,
    dianshang_order.ip,
    dianshang_order.pay_time,
    dianshang_order.pay_passport_id,
    same_paypassid.same_paypassid_cuid_cnt_last1,
    same_paypassid.same_paypassid_uid_cnt_last1,
    same_paypassid.same_paypassid_cuid_cnt_last7,
    same_paypassid.same_paypassid_uid_cnt_last7
FROM dianshang_order
LEFT JOIN same_paypassid ON dianshang_order.pay_passport_id = same_paypassid.pay_passport_id
limit 100