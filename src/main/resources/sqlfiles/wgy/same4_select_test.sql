WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230818'
    AND DATEDIFF(t1.pay_time, date_sub(t1.pay_time, 30)) BETWEEN 1 AND 29
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id
FROM source_table t1
limit 100



