with source_tabel as
(
	select
	    order_id
	    ,total_amount
        ,app_id
	from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	where  event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
-- 订单明细表数据源
dwd_order_detail as
(
	select
	 second_cate_id
	,third_cate_id
	,order_id
	,app_id
	from udw_ns.default.fengchao_biz_ecom_dwd_order_detail
	where
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	event_day = '20230827'
	and app_id in (269,5,10007,100012)
)
select
-- 以下为是否爆单
	case when source_tabel.total_amount >= 50000
	and (dwd_order_detail.third_cate_id in ('38935', '38936', '38929', '38940', '39238', '39239', '22070', '39251', '39250', '39241', '39240', '39243', '39242', '39245', '39244', '39247', '39246', '39249', '39248', '23398', '24597', '39971', '39970', '39973', '39972', '39975', '39974', '39977', '39976', '39944', '39943', '39946', '39945', '39947')
    or dwd_order_detail.second_cate_id in ('22071','22072','39621','23321','23314','23285','23286','23426','39499','23273','23387')) then 1
    when source_tabel.total_amount >= 50000 and dwd_order_detail.third_cate_id = '24597' then 2
    end is_hot_order
from source_tabel
left join
dwd_order_detail on (source_tabel.order_id = dwd_order_detail.order_id and source_tabel.app_id = dwd_order_detail.app_id)
limit 100
