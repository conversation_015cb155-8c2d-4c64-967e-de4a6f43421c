with source_tabel as
(
	select
    order_id
	,batch_id
	,sub_order_id
	,cuid
	,passport_id
	,ucid
	,copuser_id
	,shop_id
	,shop_name
	,product_id
	,product_name
	,sku_id
	,sku_desc
	,case when pay_type = 0 then '在线支付'
		  when pay_type = 100 then '货到付款'
		  else null
		  end pay_type
	,pay_channel
	,total_amount
	,payment_amount
	,freight_amount
	,cheap_amount
	,discount_amount_detail
	,baiduid
	,ip
	,name
	,mobile
	,province
	,city
	,area
	,address
	-- 100创建订单.200支付成功.300订单待商家确认.400商家备货中.500商家已发货.600买家已确认收货.700订单已取消.800订单已核销
	,case when status = 100 then '创建订单'
		  when status = 200 then '付成功'
		  when status = 300 then '订单待商家确认'
		  when status = 400 then '商家备货中'
		  when status = 500 then '商家已发货'
		  when status = 600 then '买家已确认收货'
		  when status = 700 then '订单已取消'
		  when status = 800 then '订单已核销'
		  else null
		  end status
	,express_name
	,tracking_number
	,create_time
	,pay_time
	,cancel_time
	,consign_time
	,confirm_time
	,refund_create_time
	,refund_finish_time
	,refund_reason
	,pay_passport_id
	,flow_info
	,content_type
	,sch
	,cps_type
	,parent_ext_info
	,order_ext_info
	,detail_ext_info
	,cashier_pay_info
	,agent_id
	,flow_channel
	,buy_num
	,tags
	,evaluate_time
	,product_score
	,shop_service_score
	,shop_logistics_score
	,original_content
	,images
	,refund_desc
	,complaint_create_time
	,complaint_status
	,complaint_reason
	,app_id
	-- 0在途中、1已揽收、2疑难、3已签收、4退签、5同城派送中、6退回、7转单
	,case when express_status = 0 then '在途中'
		  when express_status = 1 then '疑难'
		  when express_status = 2 then '在途中'
		  when express_status = 3 then '已签收'
		  when express_status = 4 then '退签'
		  when express_status = 5 then '同城派送中'
		  when express_status = 6 then '退回'
		  when express_status = 7 then '转单'
		  else null
		  end express_status
	,express_data
	,return_data
	,status_detail
	,product_json
	,event_day
	from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	where  event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
-- 订单明细表数据源
dwd_order_detail as
(
	select
	order_id
	,app_id
	,package_id
	,package_desc
	,product_type
	,detail_original_price
	,detail_actual_price
	,b_prod_dec_promotion_id
	,b_prod_dec_promotion_amount
	,b_ord_dec_promotion_id
	,b_ord_dec_promotion_amount
	,p_flush_sale_promotion_id
	,p_flush_sale_promotion_amount
	,b_flush_sale_promotion_id
	,b_flush_sale_promotion_amount
	,p_full_dec_promotion_id
	,p_full_dec_promotion_amount
	,b_pintuan_promotion_id
	,b_pintuan_promotion_amount
	,p_reduc_to_coupon_id
	,p_reduc_to_coupon_amount
	,p_full_coupon_id
	,p_full_coupon_amount
	,b_full_coupon_id
	,b_full_coupon_amount
	,first_cate_id
	,first_cate_name
	,second_cate_id
	,second_cate_name
	,third_cate_id
	,third_cate_name
	,fourth_cate_id
	,fourth_cate_name
	,submit_type
	,id_type
	,open_id
	,adjust_info
	,p_discount_coupon_amount
	,b_discount_coupon_amount
	from udw_ns.default.fengchao_biz_ecom_dwd_order_detail
	where
	-- 分区条件日期使用f字符插值器传入需要的分区值
	-- 或者使用 select date_format(current_timestamp(),'yyyyMMdd')
	event_day = '20230827'
	and app_id in (269,5,10007,100012)
),
shop_phone_number as
(
	select shop_id,phone_number
	from udw_ns.default.fengchao_biz_ecom_dwd_shop_risk_control_info
	-- 日期需要替换
	where event_day = '20230827'
),
----------------------------------------coupon begin---------------------------------------------
coupon_detail_info as (
select
  order_id,
  get_json_object(coupon_info_str,'$.couponBizType') as coupon_biz_type,
  get_json_object(coupon_info_str,'$.splitAmount') as split_amount
from
  (
    select
    t1.order_id as order_id,
    explode(from_json(t1.coupon_info_list, 'array< string >')) as coupon_info_str
    from (	select
    		order_id,
    		get_json_object(discount_amount_detail,'$.couponInfoList') as coupon_info_list
    		from source_tabel
    		where discount_amount_detail like '%couponInfoList%') t1
    where coupon_info_list is not null
  ) t2
),
-- 优惠劵和优惠比例
coupon as
(
	SELECT
	  order_id,
	  SUM(CASE WHEN coupon_biz_type = 'BUSSINESS' THEN split_amount ELSE 0 END) AS all_bussiness_coupon,
	  IF(SUM(split_amount) != 0, 1, 0) AS is_coupon
	FROM coupon_detail_info
	GROUP BY order_id
),
----------------------------------------coupon end---------------------------------------------
----------------------------------------lanshou begin---------------------------------------------
lanshou_time as
(
    select
        order_id,
        express_time as lanshou_time
    from
    (
        select
            order_id,
            express_time,row_number() over (PARTITION BY order_id ORDER BY express_time ) time_rank
        from
        (
        select
            t1.order_id as order_id,
            get_json_object(t1.express_array,'$.status') as express_status,
            get_json_object(t1.express_array,'$.time') as express_time
            from
            (
                select
                    order_id,
                    explode(from_json(express_data, 'array< string >')) as express_array
                from source_tabel
                where express_data is not null
            ) t1
            where express_array is not null
        )t2
        where express_status = '揽收'
    )t3
    where time_rank = 1
),
----------------------------------------lanshou end---------------------------------------------
----------------------------------------order_change_cnt begin---------------------------------------------
order_change_cnt as
(
	select
	  passport_id,
	  count(distinct address) as adress_change_cnt,
	  count(distinct name) as consignee_name_change_cnt
	from source_tabel
	where  (source_tabel.passport_id != null and source_tabel.passport_id != '')
	group by passport_id
),
----------------------------------------order_change_cnt end---------------------------------------------
----------------------------------------time_intevel_count bigin---------------------------------------------
time_intevel_count as (
    select
       order_id,
       case when pay_hours != 0 and refund_create_hours!= 0 then round(refund_create_hours-pay_hours,2) else 0 end refund_create_time_interval,
       case when pay_hours != 0 and refund_finish_hours!= 0 then round(refund_finish_hours-pay_hours,2) else 0 end refund_time_interval,
       case when pay_hours != 0 and evaluate_time_hours!= 0 then round(evaluate_time_hours-pay_hours,2) else 0 end evaluate_time_interval
    from
    (
        select
            order_id,
            -- 下单时间为pay_time  按照需求所有时间转换为小时
            case when pay_time is not null then to_unix_timestamp(pay_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end pay_hours,
            case when refund_create_time is not null then to_unix_timestamp(refund_create_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end refund_create_hours,
            case when refund_finish_time is not null then to_unix_timestamp(refund_finish_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end refund_finish_hours,
            case when evaluate_time is not null then to_unix_timestamp(evaluate_time,'yyyy-MM-dd HH:mm:ss')/3600 else 0 end evaluate_time_hours
        from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
        where event_day = '20230828'
        and app_id in (269,5,10007,100012)
    ) t1
),
----------------------------------------time_intevel_count end---------------------------------------------
order_risk as(
select
    source_tabel.order_id
    ,source_tabel.batch_id
    ,source_tabel.sub_order_id
    ,source_tabel.cuid
    ,source_tabel.passport_id
    ,source_tabel.ucid
    ,source_tabel.copuser_id
    ,source_tabel.shop_id
    ,source_tabel.shop_name
    ,source_tabel.product_id
    ,source_tabel.product_name
    ,source_tabel.sku_id
    ,source_tabel.sku_desc
    ,source_tabel.pay_type
    ,source_tabel.pay_channel
    ,source_tabel.total_amount
    ,source_tabel.payment_amount
    ,source_tabel.freight_amount
    ,source_tabel.cheap_amount
    ,source_tabel.discount_amount_detail
    ,source_tabel.baiduid
    ,source_tabel.ip
    ,source_tabel.name
    ,source_tabel.mobile
    ,source_tabel.province
    ,source_tabel.city
    ,source_tabel.area
    ,source_tabel.address
    ,source_tabel.status
    ,source_tabel.express_name
    ,source_tabel.tracking_number
    ,source_tabel.create_time
    ,source_tabel.pay_time
    ,source_tabel.cancel_time
    ,source_tabel.consign_time
    ,source_tabel.confirm_time
    ,source_tabel.refund_create_time
    ,source_tabel.refund_finish_time
    ,source_tabel.refund_reason
    ,source_tabel.pay_passport_id
    ,source_tabel.flow_info
    ,source_tabel.content_type
    ,source_tabel.sch
    ,source_tabel.cps_type
    ,source_tabel.parent_ext_info
    ,source_tabel.order_ext_info
    ,source_tabel.detail_ext_info
    ,source_tabel.cashier_pay_info
    ,source_tabel.agent_id
    ,source_tabel.flow_channel
    ,source_tabel.buy_num
    ,source_tabel.tags
    ,source_tabel.evaluate_time
    ,source_tabel.product_score
    ,source_tabel.shop_service_score
    ,source_tabel.shop_logistics_score
    ,source_tabel.original_content
    ,source_tabel.images
    ,source_tabel.refund_desc
    ,source_tabel.complaint_create_time
    ,source_tabel.complaint_status
    ,source_tabel.complaint_reason
    ,source_tabel.app_id
    ,source_tabel.express_status
    ,source_tabel.express_data
    ,source_tabel.return_data
    ,source_tabel.status_detail
    ,source_tabel.product_json
    ------开发指标
    ,case when (get_json_object(cashier_pay_info,'$.status')) is not null  then "支付成功"  else null end pay_status
    ,case when buy_num != 0 then round(total_amount/buy_num,2) else 0 end product_price
    ,substr(mobile,1,10) as mobile_number_top10
    ,case when buy_num>30 then 1 else 0 end is_many_order
    ,coupon.is_coupon
    ,round(coupon.all_bussiness_coupon/source_tabel.total_amount,2) as bussiness_coupon_rate
    ,lanshou_time.lanshou_time
    ,if(source_tabel.passport_id != null and source_tabel.passport_id != '', order_change_cnt.adress_change_cnt, 0)
    ,if(source_tabel.passport_id != null and source_tabel.passport_id != '', order_change_cnt.consignee_name_change_cnt, 0)
    ,time_intevel_count.refund_create_time_interval
    ,time_intevel_count.refund_time_interval
    ,time_intevel_count.evaluate_time_interval
from source_tabel
left join
coupon on source_tabel.order_id = coupon.order_id
left join
lanshou_time on source_tabel.order_id = lanshou_time.order_id
left join
time_intevel_count on source_tabel.order_id = time_intevel_count.order_id
left join
order_change_cnt on source_tabel.passport_id = order_change_cnt.passport_id and (source_tabel.passport_id != null and source_tabel.passport_id != '')
)
select * from order_risk
limit 100