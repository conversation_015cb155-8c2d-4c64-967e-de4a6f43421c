WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230818'
    --AND DATEDIFF(t1.pay_time, date_sub(t1.pay_time, 30)) BETWEEN 1 AND 29
),
--cuid的 7天30天
same_cuid AS (
    SELECT
        source_table.cuid,
        COUNT(distinct CASE WHEN DATEDIFF(source_table.pay_time, date_sub(source_table.pay_time, 7)) BETWEEN 1 AND 6 THEN source_table.passport_id END) AS same_cuid_passid_cnt_last7,
        COUNT(distinct CASE WHEN DATEDIFF(source_table.pay_time, date_sub(source_table.pay_time, 30)) BETWEEN 1 AND 29 THEN source_table.passport_id END) AS same_cuid_passid_cnt_last30,
        COUNT(distinct CASE WHEN DATEDIFF(source_table.pay_time, date_sub(source_table.pay_time, 7)) BETWEEN 1 AND 6 THEN source_table.order_id END) AS same_cuid_order_cnt_last7,
        COUNT(distinct CASE WHEN DATEDIFF(source_table.pay_time, date_sub(source_table.pay_time, 30)) BETWEEN 1 AND 29 THEN source_table.order_id END) AS same_cuid_order_cnt_last30
    FROM source_table
    WHERE cuid is not null and cuid != ''
    GROUP BY source_table.cuid
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id,
    if(t1.cuid is not null and t1.cuid != '',same_cuid.same_cuid_passid_cnt_last7,0) as same_cuid_passid_cnt_last7 ,
    if(t1.cuid is not null and t1.cuid != '',same_cuid.same_cuid_passid_cnt_last30,0) as same_cuid_passid_cnt_last30,
    if(t1.cuid is not null and t1.cuid != '',same_cuid.same_cuid_order_cnt_last7,0) as same_cuid_order_cnt_last7,
    if(t1.cuid is not null and t1.cuid != '',same_cuid.same_cuid_order_cnt_last30,0) as same_cuid_order_cnt_last30
FROM source_table t1
LEFT JOIN same_cuid on t1.cuid = same_cuid.cuid
limit 100



--datediff(pay_time,date_sub(pay_time, 7)) between 1 and 6
--datediff("2023-07-25 13:44:23",date_sub("2023-07-25 13:44:23", 7)) between 1 and 6