WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230814'
    AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 29
),
same_cuid_passid_cnt AS (
    SELECT
        source_table.cuid,
        COUNT(DISTINCT CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.passport_id END) AS same_cuid_passid_cnt_last7,
        COUNT(DISTINCT source_table.passport_id) AS same_cuid_passid_cnt_last30,
        COUNT(DISTINCT CASE WHEN DATEDIFF(current_timestamp(), source_table.pay_time) BETWEEN 1 AND 6 THEN source_table.order_id END) AS same_cuid_order_cnt_last7,
        COUNT(DISTINCT source_table.order_id) AS same_cuid_order_cnt_last30
    FROM source_table
    WHERE (cuid IS NOT NULL AND cuid != '')
    GROUP BY source_table.cuid
)
SELECT
    t1.shop_id,
    t1.`name`,
    t1.passport_id,
    t1.cuid,
    t1.order_id,
    t1.mobile,
    t1.tracking_number,
    t1.ip,
    t1.pay_time,
    t1.pay_passport_id,
    same_cuid_passid_cnt.same_cuid_passid_cnt_last7,
    same_cuid_passid_cnt.same_cuid_passid_cnt_last30,
    same_cuid_passid_cnt.same_cuid_order_cnt_last7,
    same_cuid_passid_cnt.same_cuid_order_cnt_last30
FROM source_table t1
LEFT JOIN same_cuid_passid_cnt on t1.cuid = same_cuid_passid_cnt.cuid
limit 100