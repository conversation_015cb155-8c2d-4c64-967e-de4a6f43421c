WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230814'
        AND (cuid IS NOT NULL AND cuid != '')
        AND (mobile IS NOT NULL AND mobile != '')
        AND (pay_passport_id IS NOT NULL AND pay_passport_id != '')
        AND (tracking_number IS NOT NULL AND tracking_number != '')
        AND (ip IS NOT NULL AND ip != '')
        AND DATEDIFF(current_timestamp(), pay_time) BETWEEN 1 AND 29
)
SELECT
   *
FROM source_table t1
limit 100