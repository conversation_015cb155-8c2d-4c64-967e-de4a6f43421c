WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id，
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230818'
),
source_last AS (
    SELECT *,
        ROW_NUMBER() OVER (PARTITION BY cuid ORDER BY pay_time) AS cuid_row_number,
        ROW_NUMBER() OVER (PARTITION BY mobile ORDER BY pay_time) AS mobile_row_number,
        ROW_NUMBER() OVER (PARTITION BY ip ORDER BY pay_time) AS ip_row_number,
        ROW_NUMBER() OVER (PARTITION BY pay_passport_id ORDER BY pay_time) AS paypassid_row_number,
        ROW_NUMBER() OVER (PARTITION BY tracking_number ORDER BY pay_time) AS tracking_number_row_number
    FROM source_table
)
SELECT
    sl1.cuid,
    datediff(sl1.pay_time, sl2.pay_time) AS date_diff,
    COUNT(DISTINCT CASE WHEN datediff(sl1.pay_time, sl2.pay_time) BETWEEN 1 AND 6 THEN sl1.passport_id END) AS same_cuid_passid_cnt_last7,
    COUNT(DISTINCT CASE WHEN datediff(sl1.pay_time, sl2.pay_time) BETWEEN 1 AND 6 THEN sl1.order_id END) AS same_cuid_order_cnt_last7,
    COUNT(DISTINCT CASE WHEN datediff(sl1.pay_time, sl2.pay_time) BETWEEN 1 AND 29 THEN sl1.passport_id END) AS same_cuid_passid_cnt_last30,
    COUNT(DISTINCT CASE WHEN datediff(sl1.pay_time, sl2.pay_time) BETWEEN 1 AND 29 THEN sl1.order_id END) AS same_cuid_order_cnt_last30
FROM source_last sl1
LEFT JOIN source_last sl2 ON sl1.cuid = sl2.cuid AND sl1.cuid_row_number = sl2.cuid_row_number + 1
GROUP BY sl1.cuid, datediff(sl1.pay_time, sl2.pay_time)
limit 100;
