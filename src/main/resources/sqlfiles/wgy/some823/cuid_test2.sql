WITH source_table AS (
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id,
        lag(pay_time, 6) OVER (PARTITION BY cuid ORDER BY pay_time) AS lag_pay_time_last7,
        lag(pay_time, 29, pay_time) OVER (PARTITION BY cuid ORDER BY pay_time) AS lag_pay_time_last30
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
    WHERE event_day = '20230818'
),
-- cuid7天
same_cuid_last7 AS (
    SELECT
        cuid,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag_pay_time_last7) BETWEEN 1 AND 6 THEN passport_id END) AS same_cuid_passid_cnt_last7,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag_pay_time_last7) BETWEEN 1 AND 6 THEN order_id END) AS same_cuid_order_cnt_last7
    FROM source_table
    WHERE datediff(pay_time, lag_pay_time_last7) BETWEEN 1 AND 6
    AND (cuid is not null and cuid != '')
    GROUP BY cuid
),
-- cuid30天
same_cuid_last30 AS (
    SELECT
        cuid,
        COUNT(DISTINCT passport_id ) AS same_cuid_passid_cnt_last30,
        COUNT(DISTINCT order_id ) AS same_cuid_order_cnt_last30
    FROM source_table
    WHERE datediff(pay_time, lag_pay_time_last30) BETWEEN 1 AND 29
    AND (cuid is not null and cuid != '')
    GROUP BY cuid
),
SELECT
    source_table.*,
    same_cuid_last7.same_cuid_passid_cnt_last7,
    same_cuid_last7.same_cuid_order_cnt_last7,
    same_cuid_last30.same_cuid_passid_cnt_last30,
    same_cuid_last30.same_cuid_order_cnt_last30
FROM source_table
LEFT JOIN same_cuid_last7 ON source_table.cuid = same_cuid_last7.cuid
LEFT JOIN same_cuid_last30 ON source_table.cuid = same_cuid_last30.cuid
LIMIT 100