WITH source_table AS (
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM test
    WHERE event_day = '20230818'
),
same_cuid AS (
    SELECT
        cuid,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag(pay_time, 6) OVER (PARTITION BY cuid ORDER BY pay_time)) BETWEEN 1 AND 6 THEN passport_id END) AS same_cuid_passid_cnt_last7,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag(pay_time, 6) OVER (PARTITION BY cuid ORDER BY pay_time)) BETWEEN 1 AND 6 THEN order_id END) AS same_cuid_order_cnt_last7,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag(pay_time, 29) OVER (PARTITION BY cuid ORDER BY pay_time)) BETWEEN 1 AND 29 THEN passport_id END) AS same_cuid_passid_cnt_last30,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, lag(pay_time, 29) OVER (PARTITION BY cuid ORDER BY pay_time)) BETWEEN 1 AND 29 THEN order_id END) AS same_cuid_order_cnt_last30
    FROM source_table
    where  cuid IS NOT NULL AND cuid != ''
    GROUP BY cuid
)
SELECT * FROM same_cuid
limit 100