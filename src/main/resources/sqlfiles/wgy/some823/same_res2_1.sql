--last指标
WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230818'
),
source_last AS (
    SELECT
       source_table.*,
       source2_last.pay_time as cha_pay_time
    FROM source_table left join source_table source2_last on source_table.order_id = source2_last.order_id
),
--cuid的 7天30天
same_cuid AS (
    SELECT
        source_last.cuid,
        COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END ) AS same_cuid_passid_cnt_last7,
        COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.order_id END ) AS same_cuid_order_cnt_last7,
        COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 29 THEN  source_last.passport_id  END) AS same_cuid_passid_cnt_last30,
        COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 29 THEN source_last.order_id  END) AS same_cuid_order_cnt_last30
        FROM source_last
        where (source_last.cuid is not null and source_last.cuid != '')
    GROUP BY source_last.cuid
),
--mobile的 7天30天
same_mobile AS (
    SELECT
        source_last.mobile,
        COUNT(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END) AS same_phone_passid_cnt_last7,
        COUNT(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.order_id END) AS same_phone_order_cnt_last7,
        COUNT(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 29 THEN source_last.passport_id END) AS same_phone_passid_cnt_last30,
        COUNT(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 29 THEN source_last.order_id END) AS same_phone_order_cnt_last30
        FROM source_last
        where (source_last.mobile is not null and source_last.mobile != '')
    GROUP BY source_last.mobile
),
--ip的 当日和7天
same_ip AS (
    SELECT
        source_last.ip,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 1 THEN source_last.cuid END) as same_ip_cuid_cnt_last1,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 1 THEN source_last.passport_id END) as same_ip_uid_cnt_last1,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 7 THEN source_last.cuid END) as same_ip_cuid_cnt_last7,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 7 THEN source_last.passport_id END) as same_ip_uid_cnt_last7
        FROM source_last
        where (source_last.ip is not null and source_last.ip != '')
    GROUP BY source_last.ip
),
--pay_passport_id的 当日和7天
same_pay_passport_id AS (
    SELECT
        source_last.pay_passport_id,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 1 THEN source_last.cuid END) as same_paypassid_cuid_cnt_last1,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 1 THEN source_last.passport_id END) as same_paypassid_uid_cnt_last1,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.cuid END) as same_paypassid_cuid_cnt_last7,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END) as same_paypassid_uid_cnt_last7
        FROM source_last
        where (source_last.pay_passport_id is not null and source_last.pay_passport_id != '')
    GROUP BY source_last.pay_passport_id
),
--tracking_number的 当日的
same_tracking_number AS (
    SELECT
        source_last.tracking_number,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time)  BETWEEN 1 AND 1 THEN source_last.order_id END) as same_expressnumber_order_cnt,
        count(distinct CASE WHEN datediff(source_last.pay_time,source_last.cha_pay_time)  BETWEEN 1 AND 1 THEN source_last.shop_id END) as same_expressnumber_shop_cnt
        FROM source_last
        where (source_last.tracking_number is not null and source_last.tracking_number != '')
    GROUP BY source_last.tracking_number
)
SELECT
    source_table.shop_id,
    source_table.`name`,
    source_table.passport_id,
    source_table.cuid,
    source_table.order_id,
    source_table.mobile,
    source_table.tracking_number,
    source_table.ip,
    source_table.pay_time,
    source_table.pay_passport_id,
    if(source_table.cuid is not null and source_table.cuid != '',same_cuid.same_cuid_passid_cnt_last7,0) as same_cuid_passid_cnt_last7,
    if(source_table.cuid is not null and source_table.cuid != '',same_cuid.same_cuid_order_cnt_last7,0) as same_cuid_order_cnt_last7,
    if(source_table.cuid is not null and source_table.cuid != '',same_cuid.same_cuid_passid_cnt_last30,0) as same_cuid_passid_cnt_last30,
    if(source_table.cuid is not null and source_table.cuid != '',same_cuid.same_cuid_order_cnt_last30,0) as same_cuid_order_cnt_last30,
    if(source_table.mobile is not null and source_table.mobile != '',same_mobile.same_phone_passid_cnt_last7,0) as same_phone_passid_cnt_last7,
    if(source_table.mobile is not null and source_table.mobile != '',same_mobile.same_phone_order_cnt_last7,0) as same_phone_order_cnt_last7,
    if(source_table.mobile is not null and source_table.mobile != '',same_mobile.same_phone_passid_cnt_last30,0) as same_phone_passid_cnt_last30,
    if(source_table.mobile is not null and source_table.mobile != '',same_mobile.same_phone_order_cnt_last30,0) as same_phone_order_cnt_last30,
    if(source_table.ip is not null and source_table.ip != '',same_ip.same_ip_cuid_cnt_last1,0) as same_ip_cuid_cnt_last1,
    if(source_table.ip is not null and source_table.ip != '',same_ip.same_ip_uid_cnt_last1,0) as same_ip_uid_cnt_last1,
    if(source_table.ip is not null and source_table.ip != '',same_ip.same_ip_cuid_cnt_last7,0) as same_ip_cuid_cnt_last7,
    if(source_table.ip is not null and source_table.ip != '',same_ip.same_ip_uid_cnt_last7,0) as same_ip_uid_cnt_last7,
    if(source_table.pay_passport_id is not null and source_table.pay_passport_id != '',same_pay_passport_id.same_paypassid_cuid_cnt_last1,0) as same_paypassid_cuid_cnt_last1,
    if(source_table.pay_passport_id is not null and source_table.pay_passport_id != '',same_pay_passport_id.same_paypassid_uid_cnt_last1,0) as same_paypassid_uid_cnt_last1,
    if(source_table.pay_passport_id is not null and source_table.pay_passport_id != '',same_pay_passport_id.same_paypassid_cuid_cnt_last7,0) as same_paypassid_cuid_cnt_last7,
    if(source_table.pay_passport_id is not null and source_table.pay_passport_id != '',same_pay_passport_id.same_paypassid_uid_cnt_last7,0) as same_paypassid_uid_cnt_last7,
    if(source_table.tracking_number is not null and source_table.tracking_number != '',same_tracking_number.same_expressnumber_order_cnt,0) as same_expressnumber_order_cnt,
    if(source_table.tracking_number is not null and source_table.tracking_number != '',same_tracking_number.same_expressnumber_shop_cnt,0) as same_expressnumber_shop_cnt
FROM source_table
LEFT JOIN same_cuid on source_table.cuid = same_cuid.cuid and (source_table.cuid is not null and source_table.cuid != '')
LEFT JOIN same_mobile on source_table.mobile = same_mobile.mobile and (source_table.mobile is not null and source_table.mobile != '')
LEFT JOIN same_ip on source_table.ip = same_ip.ip and (source_table.ip is not null and source_table.ip != '')
LEFT JOIN same_pay_passport_id on source_table.pay_passport_id = same_pay_passport_id.pay_passport_id and (source_table.pay_passport_id is not null and source_table.pay_passport_id != '')
LEFT JOIN same_tracking_number on source_table.tracking_number = same_tracking_number.tracking_number and (source_table.tracking_number is not null and source_table.tracking_number != '')
limit 100



