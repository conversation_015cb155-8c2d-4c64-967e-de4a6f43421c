WITH source_table AS (
    SELECT
        t1.shop_id,
        t1.`name`,
        t1.passport_id,
        t1.cuid,
        t1.order_id,
        t1.mobile,
        t1.tracking_number,
        t1.ip,
        t1.pay_time,
        t1.pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info t1
    WHERE event_day = '20230818'
)
SELECT
    source_last.order_id,
    source_last.shop_id,
    source_last.`name`,
    source_last.passport_id,
    source_last.cuid,
    source_last.order_id,
    source_last.mobile,
    source_last.tracking_number,
    source_last.ip,
    source_last.pay_time,
    source_last.pay_passport_id,
    COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END ) OVER (PARTITION BY source_last.cuid) AS same_cuid_passid_cnt_last7,
    COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.order_id END ) OVER (PARTITION BY source_last.cuid) AS same_cuid_order_cnt_last7,
    COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 29 THEN  source_last.passport_id  END) OVER (PARTITION BY source_last.cuid) AS same_cuid_passid_cnt_last30,
    COUNT( DISTINCT CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 29 THEN source_last.order_id  END) OVER (PARTITION BY source_last.cuid) AS same_cuid_order_cnt_last30,
    COUNT( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END) OVER (PARTITION BY source_last.mobile) AS same_phone_passid_cnt_last7,
    COUNT( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.order_id END) OVER (PARTITION BY source_last.mobile) AS same_phone_order_cnt_last7,
    COUNT( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 29 THEN source_last.passport_id END) OVER (PARTITION BY source_last.mobile) AS same_phone_passid_cnt_last30,
    COUNT( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 29 THEN source_last.order_id END) OVER (PARTITION BY source_last.mobile) AS same_phone_order_cnt_last30,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 1 THEN source_last.cuid END) OVER (PARTITION BY source_last.ip)  as same_ip_cuid_cnt_last1,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 1 THEN source_last.passport_id END) OVER (PARTITION BY source_last.ip)  as same_ip_uid_cnt_last1,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 7 THEN source_last.cuid END) OVER (PARTITION BY source_last.ip)  as same_ip_cuid_cnt_last7,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 7 THEN source_last.passport_id END) OVER (PARTITION BY source_last.ip)  as same_ip_uid_cnt_last7,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 1 THEN source_last.cuid END) OVER (PARTITION BY source_last.pay_passport_id) as same_paypassid_cuid_cnt_last1,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 1 THEN source_last.passport_id END) OVER (PARTITION BY source_last.pay_passport_id) as same_paypassid_uid_cnt_last1,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.cuid END) OVER (PARTITION BY source_last.pay_passport_id) as same_paypassid_cuid_cnt_last7,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time) BETWEEN 1 AND 6 THEN source_last.passport_id END) OVER (PARTITION BY source_last.pay_passport_id) as same_paypassid_uid_cnt_last7,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time)  BETWEEN 1 AND 1 THEN source_last.order_id END) OVER (PARTITION BY source_last.tracking_number) as same_expressnumber_order_cnt,
    count( distinct CASE WHEN datediff(source_last.pay_time,source2_last.pay_time)  BETWEEN 1 AND 1 THEN source_last.shop_id END) OVER (PARTITION BY source_last.tracking_number) as same_expressnumber_shop_cnt
FROM source_table source_last
JOIN source_table  source2_last ON source_last.order_id = source2_last.order_id
limit 100
