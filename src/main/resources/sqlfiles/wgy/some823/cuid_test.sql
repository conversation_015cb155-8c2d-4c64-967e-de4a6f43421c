WITH source_last AS (
    SELECT
        shop_id,
        `name`,
        passport_id,
        cuid,
        order_id,
        mobile,
        tracking_number,
        ip,
        pay_time,
        pay_passport_id
    FROM udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
        WHERE event_day = '20230818'
),
same_cuid AS (
    SELECT
        cuid,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, pay_time_lag) BETWEEN 1 AND 6 THEN passport_id END) OVER (PARTITION BY cuid) AS same_cuid_passid_cnt_last7,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, pay_time_lag) BETWEEN 1 AND 29 THEN passport_id END) OVER (PARTITION BY cuid) AS same_cuid_passid_cnt_last30,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, pay_time_lag) BETWEEN 1 AND 6 THEN order_id END) OVER (PARTITION BY cuid) AS same_cuid_order_cnt_last7,
        COUNT(DISTINCT CASE WHEN datediff(pay_time, pay_time_lag) BETWEEN 1 AND 29 THEN order_id END) OVER (PARTITION BY cuid) AS same_cuid_order_cnt_last30
    FROM (
        SELECT
            cuid,
            passport_id,
            order_id,
            pay_time,
            LAG(pay_time) OVER (PARTITION BY cuid ORDER BY pay_time) AS pay_time_lag
        FROM source_last
    ) subquery
)
SELECT * FROM same_cuid

