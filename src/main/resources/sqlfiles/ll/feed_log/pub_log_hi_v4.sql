select
is_feed_dau,
ts,
opts,
province,
city,
referrer,
channel,
baiduid,
appid,
page_type,
is_spam,
district,
browser,
rid_prefix,
r_type,
res_pos,
bhv_id,
bhv_from,
bhv_page,
bhv_source,
bhv_type,
bhv_value,
bhv_ct,
bhv_cst,
log_from,
log_info,
r_type_src,
page_type_src,
tab_id_src,
resourcetype,
is_microvideo,
collection_page_type,
merge_video_scene,
is_recom_after_play,
new_tab_id,
template_name,
topic_id,
r_sub_type,
sub_os_name,
action_id,
params,
log_src,
page_video_type,
rid,
publish_time,
dura,
play_dura,
paused_dura,
cuid,
is_auto_play,
play_form,
template,
video_params,
action_info,
click_id,
ums_mv_level,
mrtype,
feed_pay_type,
valid_play_cnt,
play_cnt,
send_cnt,
entry_dura,
click_cnt,
display_cnt,
uid,
ip,
is_microvideo_entry,
child_list,
dura_part_id,
res_tag,
event_action
from ubs_feed.feed_dwd_pub_log_hi
where
event_day = "20231221"
and event_hour = "05"
and event_action in (
"play",
"other",
"click",
"send"
)
limit 2000