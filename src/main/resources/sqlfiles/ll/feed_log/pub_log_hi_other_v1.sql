select
is_feed_dau,
logid,
logid_str,
ts,
opts,
os_version,
os,
app_type,
app_version,
brand,
device,
device_version,
resh,
resv,
net_provider_name,
net_type,
country,
province,
city,
referrer,
channel,
session_id,
baiduid,
appid,
ori_channel,
page_type,
is_spam,
district,
browser,
rid_prefix,
r_type,
res_pos,
tab_id,
rec_src,
rec_recall_type,
rec_type,
rec_cms_type,
layout,
bhv_id,
bhv_from,
bhv_page,
bhv_source,
bhv_type,
bhv_value,
bhv_ct,
bhv_cst,
log_from,
log_info,
r_type_src,
page_type_src,
tab_id_src,
resourcetype,
is_microvideo,
collection_page_type,
collection_action_type,
merge_video_scene,
is_recom_after_play,
new_tab_id,
template_name,
topic_id,
r_sub_type,
sub_os_name,
action_id,
params,
log_src,
page_video_type,
rid,
miniapp_id,
miniapp_name,
miniapp_key,
miniapp_type,
miniapp_2nd_type_name,
miniapp_rid,
publish_time,
dura,
play_dura,
paused_dura,
cuid,
miniapp_card_id,
res_vertical_type,
is_auto_play,
play_form,
template,
video_params,
action_info,
click_id,
ums_mv_level,
mrtype,
feed_pay_type,
is_top,
minapp_from,
jump_to,
refresh_state,
refresh_index,
refresh_type,
page_type_src_local,
is_local,
tab_pos,
framework,
video_merge_style,
entry_r_type,
ext_log_pd,
video_pdrec,
video_land_type,
screen_status,
entry_rid,
send_params,
bhv_play_cnt,
bhv_play_dura,
ori_paused_dura,
ori_part_dura,
ori_dura,
read_process,
xiding_cnt,
bar_click_cnt,
slide_down_cnt,
valid_play_cnt,
play_cnt,
send_cnt,
entry_dura,
click_cnt,
display_cnt,
uid,
ip,
is_microvideo_entry,
child_list,
dura_part_id,
res_tag,
event_action
from ubs_feed.feed_dwd_pub_log_hi
where
event_day = "20231221"
and event_hour = "05"
and event_action = "other"

limit 2000