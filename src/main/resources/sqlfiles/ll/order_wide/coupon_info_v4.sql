--分块处理
-- 过滤discount_amount_detail 为空的
--   过滤coupon_info_list 为空的
--    以上不为空的取出到 coupon_info_str
--     从coupon_info_str中取出 couponBizType  splitAmount
--     按照order_id 分组sum(splitAmount) as bussiness_coupon_rate  where couponBizType = 'BUSSINESS'
-- 此查询得到有优惠券信息的order_id all_bussiness_coupon(优惠券优惠金额)
-- 需要与主表关联后计算round(all_bussiness_coupon/total_mount,2)
--with coupon_info_view as
--(
--select
--t1.order_id as order_id,
--explode(from_json(t1.coupon_info_list, 'array< string >')) as coupon_info_str
--from (
        select
		order_id,
		get_json_object(discount_amount_detail,'$.couponInfoList') as coupon_info_list
		from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
		where event_day = "20230814"
		and discount_amount_detail like '%couponInfoList%'
--		) t1
--where coupon_info_list is not null
--),
--coupon_detail_info as
--(
--select order_id,
--get_json_object(coupon_info_str,'$.couponBizType') as coupon_biz_type,
--get_json_object(coupon_info_str,'$.splitAmount') as split_amount
--from coupon_info_view
--)
--select
--order_id,
--sum(split_amount) as all_bussiness_coupon
--from coupon_detail_info
--where coupon_biz_type = 'BUSSINESS'
--group by order_id
limit 100