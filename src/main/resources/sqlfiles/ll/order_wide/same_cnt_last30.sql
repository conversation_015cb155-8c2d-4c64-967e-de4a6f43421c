with source_table as (
  select
    substr(pay_time, 1, 10) pay_time,
        cuid,
        app_id,
        passport_id,
        order_id,
        mobile
  from
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where
  -- 加6天
    event_day = '20230831'
    and cuid != '0'
    and cuid != ''
    and cuid is not null
    and pay_time is not null
    and pay_time != '0'
    and app_id in (269, 5, 10007, 100012)
    and substr(pay_time, 1, 10) >= '2023-07-24'
),

-- 同一个cuid 在过去三十天对应多少个passport_id
same_cuid_passid_cnt_last30 as
(select
  cuid,
  order_id,
  count(distinct passport_id) as same_cuid_passid_cnt_last30
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-24'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  cuid,
  order_id
),
-- 同订单号下同一个cuid 在过去的30天中的订单数
same_cuid_order_cnt_last30 as
(
select
  cuid,
  order_id,
  count(distinct order_id_b) as same_cuid_order_cnt_last30
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-24'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  cuid,
  order_id
),
-- 同一个手机号 在过去30天对应多少个passport_id数
same_phone_passid_cnt_last30 as
(
select
  mobile,
  order_id,
  count(distinct passport_id) as same_phone_passid_cnt_last30
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.passport_id as passport_id
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-24'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  mobile,
  order_id
),
-- 同一个手机号在过去30天内 对应的订单式
same_phone_order_cnt_last30 as
(
select
  mobile,
  order_id,
  count(distinct order_id_b) as same_phone_order_cnt_last30
from
  (
    select
      a.mobile as mobile,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.mobile = b.mobile and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-24'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  mobile,
  order_id
)
-- 30日共4个
select
source_table.order_id,
same_cuid_passid_cnt_last30,
same_cuid_order_cnt_last30,
same_phone_passid_cnt_last30,
same_phone_order_cnt_last30
from
source_table
left join
same_cuid_passid_cnt_last30 on source_table.order_id = same_cuid_passid_cnt_last30.order_id
left join
same_cuid_order_cnt_last30 on source_table.order_id = same_cuid_order_cnt_last30.order_id
left join
same_phone_passid_cnt_last30 on source_table.order_id = same_phone_passid_cnt_last30.order_id
left join
same_phone_order_cnt_last30 on source_table.order_id = same_phone_order_cnt_last30.order_id
limit 100