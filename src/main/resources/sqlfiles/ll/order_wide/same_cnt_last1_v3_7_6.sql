with source_table as (
  select
    substr(pay_time, 1, 10) pay_time,
    cuid,
    app_id,
    passport_id,
    order_id,
    pay_passport_id,
    tracking_number,
    shop_id,
    ip
  from
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where
    event_day = '20230831'
    and cuid != '0'
    and cuid != ''
    and cuid is not null
    and pay_time is not null
    and pay_time != '0'
    and app_id in (269, 5, 10007, 100012)
    and substr(pay_time, 1, 10) >= '2023-07-24'
),
filter_tracking_number_null as
(
select /*+ BROADCAST(tracking_number) */
tracking_number,
order_id,
app_id,
pay_time,
shop_id
from
source_table
where tracking_number is not null
)

select
  a.tracking_number as tracking_number,
  a.order_id as order_id,
  b.order_id as order_id_b
from
  filter_tracking_number_null a
  join filter_tracking_number_null b on a.tracking_number = b.tracking_number
  and a.app_id = b.app_id
where
  filter_tracking_number_null.pay_time >= '2023-08-24'
  and datediff(to_date(a.pay_time), to_date(b.pay_time)) = 0
limit 100