with source_table as (
  select
    substr(pay_time, 1, 10) pay_time,
        cuid,
        app_id,
        passport_id,
        order_id,
        mobile
  from
    udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
  where
  -- 加6天
    event_day = '20230831'
    and cuid != '0'
    and cuid != ''
    and cuid is not null
    and pay_time is not null
    and pay_time != '0'
    and app_id in (269, 5, 10007, 100012)
    and substr(pay_time, 1, 10) >= '2023-07-24'
),

same_cuid_order_cnt_last30 as
(
select
  cuid,
  order_id,
  count(distinct order_id_b) as same_cuid_order_cnt_last30
from
  (
    select
      a.cuid as cuid,
      a.order_id as order_id,
      b.order_id as order_id_b
    from
      source_table a
      join source_table b on a.cuid = b.cuid and a.app_id = b.app_id
    where
      a.pay_time >= '2023-08-24'
      and datediff(to_date(a.pay_time), to_date(b.pay_time)) between 1 and 29
  ) t
group by
  cuid,
  order_id
)
select *
from same_cuid_order_cnt_last30
limit 100