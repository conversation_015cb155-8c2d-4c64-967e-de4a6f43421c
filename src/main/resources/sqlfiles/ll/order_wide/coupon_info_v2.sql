with coupon_info_view as
(
select
t1.order_id as order_id,
explode(from_json(t1.coupon_info_list, 'array< string >')) as coupon_info_str
from (	select
		order_id,
		get_json_object(discount_amount_detail,'$.couponInfoList') as coupon_info_list
		from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
		where event_day = "20230814"
		and discount_amount_detail is not null) t1
where coupon_info_list is not null
)
select order_id,
get_json_object(coupon_info_str,'$.couponBizType') as couponBizType,
get_json_object(coupon_info_str,'$.splitAmount') as splitAmount
from coupon_info_view
limit 100