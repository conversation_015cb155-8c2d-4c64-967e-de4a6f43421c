select
  uid,
  cuid,
  rid,
  from_unixtime(ceil(opts/1000),'yyyy-MM-dd HH:mm:ss') as op_time,
  app_type,
  brand,
  device,
  net_provider_name,
  country,
  province,
  city,
  referrer,
  baiduid,
  appid,
  district,
  browser,
  rid_prefix,
  r_type,
  publish_time
from
  ubs_feed.feed_dwd_pub_log_hi
where
  event_day in (
  '20231218'
  )
and rid in (
9743875919379101969,
10594416496493314720,
10594416496493314720,
9887724063580707758,
9628835735782838945,
9378462373765642979,
9651563233675472506,
9823176872613350041,
9549143850211912128,
9694809379683161572)
limit 500