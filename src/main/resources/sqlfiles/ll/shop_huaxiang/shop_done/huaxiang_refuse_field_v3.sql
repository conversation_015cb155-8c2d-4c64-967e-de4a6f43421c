--"[2]","名称", "[3]","简介","[4]","logo","[5]","类目","[2,3]","名称,简介","[2,5]""名称,类目"
select case when refuse_field = "[2]" then "名称"
            when refuse_field = "[3]" then "简介"
            when refuse_field = "[4]" then "logo"
            when refuse_field = "[5]" then "类目"
            when refuse_field = "[2,3]" then "名称,简介"
            when refuse_field = "[2,5]" then "名称,类目"
            else null
            end refuse_field,refuse_field,cnt
from (select refuse_field, count(1) as cnt
       from udw_ns.default.help_ods_shop_huaxiang_fengkong_wide_df
       where event_day = "20231007"
       group by refuse_field)
limit 500