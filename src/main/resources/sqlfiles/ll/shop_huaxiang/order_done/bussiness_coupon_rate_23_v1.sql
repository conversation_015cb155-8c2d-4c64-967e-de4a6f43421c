with source_tabel as (
  select
    order_id,
    total_amount,
    sub_order_id,
    discount_amount_detail
  from
    udw_ns.default.help_ods_order_huaxiang_fengkong_wide_df
  where
    event_day = '20231023'
    and order_id in (6646464851154,
                     6634874951154,
                     6634944351154,
                     6668064301154,
                     6614114731154,
                     6649322721154,
                     6637297281154,
                     6649569351154,
                     6639832771154,
                     6643338111154,
                     6640058171154,
                     6664920351154,
                     6652149211154,
                     6646574941154,
                     6646620311154,
                     6664855991154,
                     6646618081154,
                     6640188361154,
                     6649333431154,
                     6637295141154,
                     6655190981154,
                     6668257921154,
                     6634640151154,
                     6674261151154,
                     6643345451154,
                     6665060801154,
                     6640033021154,
                     6634750381154,
                     6634947391154,
                     6634994661154,
                     3965738790208)
),
coupon_source as (
 select
    order_id,
    sub_order_id,
    discount_amount_detail
 from source_tabel
 group by
    order_id,
    sub_order_id,
    discount_amount_detail
),
coupon_detail_info as (
  select
    t2.order_id,
    t2.sub_order_id,
    get_json_object(t2.coupon_info_str, '$.couponBizType') as coupon_biz_type,
    get_json_object(t2.coupon_info_str, '$.splitAmount') as split_amount
  from
    (
      select
        t1.order_id as order_id,
        t1.sub_order_id as sub_order_id,
        explode(
          from_json(t1.coupon_info_list, 'array< string >')
        ) as coupon_info_str
      from
        (
          select
            order_id,
            sub_order_id,
            get_json_object(discount_amount_detail, '$.couponInfoList') as coupon_info_list
          from
            coupon_source
          where
            discount_amount_detail like '%couponInfoList%'
        ) t1
      where
        coupon_info_list is not null
        and coupon_info_list != ''
    ) t2
),
coupon as (
  SELECT
    order_id,
    sub_order_id,
    SUM(
      CASE
        WHEN coupon_biz_type = 'BUSSINESS' THEN split_amount
        ELSE 0
      END
    ) AS all_bussiness_coupon,
    IF(SUM(split_amount) != 0, 1, 0) AS is_coupon
  FROM
    coupon_detail_info
  GROUP BY
    sub_order_id,
    order_id
)
select
  source_tabel.order_id,
  source_tabel.total_amount,
  case when coupon.all_bussiness_coupon is null then 0
  else round(
    if(source_tabel.total_amount = 0,0,coupon.all_bussiness_coupon / source_tabel.total_amount),
    2
  ) end bussiness_coupon_rate,
  case
      when (coupon.is_coupon is null or coupon.is_coupon = 0) then 0 else 1 end is_coupon
from
  source_tabel
  left join coupon on source_tabel.order_id = coupon.order_id and source_tabel.sub_order_id = coupon.sub_order_id

  limit 500