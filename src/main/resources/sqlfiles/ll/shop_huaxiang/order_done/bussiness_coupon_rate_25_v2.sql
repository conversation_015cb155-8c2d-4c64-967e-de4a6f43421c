with source_tabel as (
  select
    order_id,
    total_amount,
    sub_order_id,
    discount_amount_detail
  from
    udw_ns.default.help_ods_order_huaxiang_fengkong_wide_df
  where
    event_day = '20231025'
),
coupon_source as (
 select
    order_id,
    sub_order_id,
    discount_amount_detail
 from source_tabel
 group by
    order_id,
    sub_order_id,
    discount_amount_detail
),
coupon_detail_info as (
  select
    t2.order_id,
    t2.sub_order_id,
    get_json_object(t2.coupon_info_str, '$.couponBizType') as coupon_biz_type,
    get_json_object(t2.coupon_info_str, '$.splitAmount') as split_amount
  from
    (
      select
        t1.order_id as order_id,
        t1.sub_order_id as sub_order_id,
        explode(
          from_json(t1.coupon_info_list, 'array< string >')
        ) as coupon_info_str
      from
        (
          select
            order_id,
            sub_order_id,
            get_json_object(discount_amount_detail, '$.couponInfoList') as coupon_info_list
          from
            coupon_source
          where
            discount_amount_detail like '%couponInfoList%'
        ) t1
      where
        coupon_info_list is not null
        and coupon_info_list != ''
    ) t2
),
coupon as (
  SELECT
    order_id,
    sub_order_id,
    SUM(
      CASE
        WHEN coupon_biz_type = 'BUSSINESS' THEN split_amount
        ELSE 0
      END
    ) AS all_bussiness_coupon,
    IF(SUM(split_amount) != 0, 1, 0) AS is_coupon
  FROM
    coupon_detail_info
  GROUP BY
    sub_order_id,
    order_id
)
select
  source_tabel.order_id,
  source_tabel.total_amount,
  source_tabel.discount_amount_detail,
  source_tabel.sub_order_id,
  case when coupon.all_bussiness_coupon is null then 0
  else round(
    if(source_tabel.total_amount = 0,0,coupon.all_bussiness_coupon / source_tabel.total_amount),
    2
  ) end bussiness_coupon_rate,
  case
      when (coupon.is_coupon is null or coupon.is_coupon = 0) then 0 else 1 end is_coupon
from
  source_tabel
  left join coupon on source_tabel.order_id = coupon.order_id and source_tabel.sub_order_id = coupon.sub_order_id

  limit 500