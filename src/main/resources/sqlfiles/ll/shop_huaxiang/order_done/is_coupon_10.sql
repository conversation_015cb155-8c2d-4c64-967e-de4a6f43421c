with
coupon_detail_info as (
  select
    t2.order_id,
    get_json_object(t2.coupon_info_str, '$.couponBizType') as coupon_biz_type,
    get_json_object(t2.coupon_info_str, '$.splitAmount') as split_amount
  from
    (
      select
        t1.order_id as order_id,
        explode(
          from_json(t1.coupon_info_list, 'array< string >')
        ) as coupon_info_str
      from
        (
          select
            order_id,
            get_json_object(discount_amount_detail, '$.couponInfoList') as coupon_info_list
          from
            udw_ns.default.help_ods_order_huaxiang_fengkong_wide_df
          where
            event_day = '20231010'
            and discount_amount_detail like '%couponInfoList%'
        ) t1
      where
        coupon_info_list is not null
    ) t2
),
coupon as (
  SELECT
    order_id,
    SUM(
      CASE
        WHEN coupon_biz_type = 'BUSSINESS' THEN split_amount
        ELSE 0
      END
    ) AS all_bussiness_coupon,
    IF(SUM(split_amount) != 0, 1, 0) AS is_coupon
  FROM
    coupon_detail_info
  GROUP BY
    order_id
)

select
case
    when (coupon.is_coupon is null or coupon.is_coupon = '' or coupon.is_coupon = 0)
    then 0
    else 1 end is_coupon
from coupon

limit 500