with source_tabel as (
  select
    passport_id,
    name,
    address
  from
    udw_ns.default.help_ods_order_huaxiang_fengkong_wide_df
  where
    event_day = "20231011"
),
order_change_cnt as (
  select
    passport_id,
    count(distinct address) as adress_change_cnt,
    count(distinct name) as consignee_name_change_cnt
  from
    source_tabel
  group by
    passport_id
)

select
 adress_change_cnt,
 consignee_name_change_cnt
from
  source_tabel
  left join order_change_cnt on source_tabel.passport_id = order_change_cnt.passport_id
  and (
    source_tabel.passport_id != null
    and source_tabel.passport_id != ''
  )

  limit 500