select
case
    when total_amount >= 50000
    and (
      third_cate_id in (
        '38935',
        '38936',
        '38929',
        '38940',
        '39238',
        '39239',
        '22070',
        '39251',
        '39250',
        '39241',
        '39240',
        '39243',
        '39242',
        '39245',
        '39244',
        '39247',
        '39246',
        '39249',
        '39248',
        '23398',
        '24597',
        '39971',
        '39970',
        '39973',
        '39972',
        '39975',
        '39974',
        '39977',
        '39976',
        '39944',
        '39943',
        '39946',
        '39945',
        '39947'
      )
      or second_cate_id in (
        '22071',
        '22072',
        '39621',
        '23321',
        '23314',
        '23285',
        '23286',
        '23426',
        '39499',
        '23273',
        '23387'
      )
    ) then 1
    when third_cate_id = '24597' then 2
  end is_hot_order
  from udw_ns.default.help_ods_order_huaxiang_fengkong_wide_df
  where event_day = '20231009'
  limit 500