--从json中取出每条物流状态
--explode后 取出status = "揽收" 对应的时间
--按照order_id 分区 对time排序正序  取第一条
select order_id,express_time
from(
select order_id,express_time,row_number() over (PARTITION BY order_id ORDER BY express_time ) time_rank
from(
select
t1.order_id as order_id,
get_json_object(t1.express_array,'$.status') as express_status,
get_json_object(t1.express_array,'$.time') as express_time
from (	select
		order_id,
        explode(from_json(express_data, 'array< string >')) as express_array
		from udw_ns.default.fengchao_biz_ecom_dwd_order_risk_control_info
		where event_day = "20230814"
		and express_data is not null) t1
where express_array is not null
)t2
where express_status = '揽收'
)t3
where time_rank = 1
limit 100