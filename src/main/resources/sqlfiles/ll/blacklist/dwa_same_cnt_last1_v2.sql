with source_table as (
  select
       tracking_number,
       order_id,
       order_id_b,
       shop_id
  from
    dwb_same_cnt_last1
),

same_expressnumber_order_cnt as
(
select tracking_number,
order_id,
sum(same_expressnumber_order_cnt_with_random) as same_expressnumber_order_cnt
from(
        select
        substr(tracking_number_with_random,3,length(tracking_number_with_random)) as tracking_number,
        order_id,
        count(distinct order_id_b) as same_expressnumber_order_cnt_with_random
        from (
            select
                concat(cast(rand() * 10 as int), '_',tracking_number) as tracking_number_with_random,
                order_id,
                order_id_b
            from
                source_table
             )
        group by
        tracking_number_with_random,
        order_id
    )
group by
tracking_number,
order_id
),
same_expressnumber_shop_cnt as
(
select 
tracking_number,
order_id,
sum(same_expressnumber_order_cnt_with_random) as same_expressnumber_shop_cnt
from(
select
substr(tracking_number_with_random,3,length(tracking_number_with_random)) as tracking_number,
order_id,
count(distinct shop_id) as same_expressnumber_order_cnt_with_random
from (
    select
        concat(cast(rand() * 10 as int), '_',tracking_number) as tracking_number_with_random,
        order_id,
        shop_id
    from
        source_table
     )
group by
tracking_number_with_random,
order_id
)
group by
  tracking_number,
  order_id
)


select
source_table.order_id,
same_expressnumber_order_cnt,
same_expressnumber_shop_cnt
from
source_table
left join
same_expressnumber_order_cnt on source_table.order_id = same_expressnumber_order_cnt.order_id
left join
same_expressnumber_shop_cnt on source_table.order_id = same_expressnumber_shop_cnt.order_id


limit 100