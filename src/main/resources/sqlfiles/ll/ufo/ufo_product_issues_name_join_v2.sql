with udw_source as (
    select *
    from udw_temp_view
    ),
    product_issues_1 as (
        select t1.*,t2.label_name as 1_lavble_name
        from udw_source t1 left join
        (select *
        from csv_temp_view
        where issues_level = 1) t2 on t1.space_id = t2.space_id and t1.space_id = t2.label_id
    ),
     product_issues_2 as (
         select t1.*,t2.label_name as 2_lavble_name
         from product_issues_1 t1 left join
         (select *
          from csv_temp_view
          where issues_level = 2) t2 on t1.space_id = t2.space_id and t1.space_id = t2.label_id
     ),
     product_issues_3 as (
         select t1.*,t2.label_name as 3_lavble_name
         from product_issues_2 t1 left join
              (select *
               from csv_temp_view
               where issues_level = 3) t2 on t1.space_id = t2.space_id and t1.space_id = t2.label_id
     ),
     product_issues_4 as (
         select t1.*,t2.label_name as 4_lavble_name
         from product_issues_3 t1 left join
              (select *
               from csv_temp_view
               where issues_level = 4) t2 on t1.space_id = t2.space_id and t1.space_id = t2.label_id
     ),
     product_issues_5 as (
         select t1.*,t2.label_name as 5_lavble_name
         from product_issues_4 t1 left join
              (select *
               from csv_temp_view
               where issues_level = 5) t2 on t1.space_id = t2.space_id and t1.space_id = t2.label_id
     )
select *
from (select product_line,
        work_type,
        channel_platform_space,
        space_name,
        space_id,
        space_id_old,
        id,
        extend_feedback_channel,
        need_manual,
        1_lavble_name as product_type,
        2_lavble_name as function_type,
        3_lavble_name as function_detail_type,
        4_lavble_name as customlv4,
        5_lavble_name as customlv5,
        complaint_type,
        complaint_deal_result,
        complaint_is_result,
        robot_eva_stat,
        robot_eva_sloved,
        robot_eva_advise,
        robot_eva_stat_label,
        intp_digital_man,
        is_pipe,
        robot_eva_diss_content,
        mobile_app_name,
        evaluation_count,
        invite_evaluation_type,
        evaluation_filter,
        invite_evaluation_filter,
        discontent_reason_filter,
        discontent_labels_filter,
        audit_pass,
        audit_reason,
        violence_score,
        salacity_score,
        politics_score,
        robot_reply,
        first_reply_length_second,
        pipe_reply,
        manual_reply,
        userid,
        ip,
        ip_province,
        mobile_model,
        mobile_brand,
        update_user,
        status,
        valid,
        feedback_type,
        submit_time,
        icafe_filter,
        from_product_line,
        pre_product_info,
        risk_level
 from product_issues_5)
where   product_type is not null or
        function_type is not null or
        function_detail_type is not null or
        customlv4 is not null or
        customlv5 is not null
limit 500
