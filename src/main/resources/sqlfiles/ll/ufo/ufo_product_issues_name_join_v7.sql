select  product_issues_4.product_line,
        product_issues_4.work_type,
        product_issues_4.channel_platform_space,
        product_issues_4.space_name,
        product_issues_4.space_id,
        product_issues_4.space_id_old,
        product_issues_4.id,
        product_issues_4.extend_feedback_channel,
        product_issues_4.need_manual,
        product_issues_4.1_lable_name as product_type,
        product_issues_4.2_lable_name as function_type,
        product_issues_4.3_lable_name as function_detail_type,
        product_issues_4.4_lable_name as customlv4,
        t5.label_name as customlv5,
        product_issues_4.complaint_type,
        product_issues_4.complaint_deal_result,
        product_issues_4.complaint_is_result,
        product_issues_4.robot_eva_stat,
        product_issues_4.robot_eva_sloved,
        product_issues_4.robot_eva_advise,
        product_issues_4.robot_eva_stat_label,
        product_issues_4.intp_digital_man,
        product_issues_4.is_pipe,
        product_issues_4.robot_eva_diss_content,
        product_issues_4.mobile_app_name,
        product_issues_4.evaluation_count,
        product_issues_4.invite_evaluation_type,
        product_issues_4.evaluation_filter,
        product_issues_4.invite_evaluation_filter,
        product_issues_4.discontent_reason_filter,
        product_issues_4.discontent_labels_filter,
        product_issues_4.audit_pass,
        product_issues_4.audit_reason,
        product_issues_4.violence_score,
        product_issues_4.salacity_score,
        product_issues_4.politics_score,
        product_issues_4.robot_reply,
        product_issues_4.first_reply_length_second,
        product_issues_4.pipe_reply,
        product_issues_4.manual_reply,
        product_issues_4.userid,
        product_issues_4.ip,
        product_issues_4.ip_province,
        product_issues_4.mobile_model,
        product_issues_4.mobile_brand,
        product_issues_4.update_user,
        product_issues_4.status,
        product_issues_4.valid,
        product_issues_4.feedback_type,
        product_issues_4.submit_time,
        product_issues_4.icafe_filter,
        product_issues_4.from_product_line,
        product_issues_4.pre_product_info,
        product_issues_4.risk_level
from
    (
        select product_issues_3.*,t4.label_name as 4_lable_name
        from
            (
                select product_issues_2.*,t3.label_name as 3_lable_name
                from
                    (
                        select product_issues_1.*,t2.label_name as 2_lable_name
                        from
                            (
                                select udw_source.*,t1.label_name as 1_lable_name
                                from
                                    (
                                        select
                                            *
                                        from
                                            udw_ns.default.help_ods_ufo_key_point_productline_di
                                        where event_day BETWEEN 20231101 AND 20231130
                                    ) udw_source
                                        left join
                                    (select *
                                     from tmp_csv_temp_view
                                     where issues_level = 1) t1 on udw_source.space_id = t1.space_id and udw_source.product_type = t1.label_id
                            ) product_issues_1
                                left join
                            (select *
                             from tmp_csv_temp_view
                             where issues_level = 2) t2 on product_issues_1.space_id = t2.space_id and product_issues_1.function_type = t2.label_id
                    ) product_issues_2
                        left join
                    (select *
                     from tmp_csv_temp_view
                     where issues_level = 3) t3 on product_issues_2.space_id = t3.space_id and product_issues_2.function_detail_type = t3.label_id
            ) product_issues_3
                left join
            (select *
             from tmp_csv_temp_view
             where issues_level = 4) t4 on product_issues_3.space_id = t4.space_id and product_issues_3.customlv4 = t4.label_id
    ) product_issues_4
        left join
    (select *
     from tmp_csv_temp_view
     where issues_level = 5) t5 on product_issues_4.space_id = t5.space_id and product_issues_4.customlv5 = t5.label_id
limit 500