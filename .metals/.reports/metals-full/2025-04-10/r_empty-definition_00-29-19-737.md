error id: `<none>`.
file://<WORKSPACE>/src/main/scala/com/baidu/sql/customized/files801/label801search.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -com/baidu/sql/utils/TimeOperateUtil.broadcast.
	 -com/baidu/sql/utils/TimeOperateUtil.broadcast#
	 -com/baidu/sql/utils/TimeOperateUtil.broadcast().
	 -org/apache/spark/sql/functions/broadcast.
	 -org/apache/spark/sql/functions/broadcast#
	 -org/apache/spark/sql/functions/broadcast().
	 -spark/implicits/broadcast.
	 -spark/implicits/broadcast#
	 -spark/implicits/broadcast().
	 -broadcast.
	 -broadcast#
	 -broadcast().
	 -scala/Predef.broadcast.
	 -scala/Predef.broadcast#
	 -scala/Predef.broadcast().
offset: 4965
uri: file://<WORKSPACE>/src/main/scala/com/baidu/sql/customized/files801/label801search.scala
text:

```scala
package com.baidu.sql.customized.files801

import com.baidu.sql.customized.files801.Label801.{mergeAndAddColumns, readCsv}
import com.baidu.sql.utils.TimeOperateUtil._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.storage.StorageLevel

/**
 * @author: zhangrunjie 801风控用户标签表，搜索相关标签
 */
object label801search {
  //  source	str	数据来源
  //  type	str	标签类型
  //  sub_type	str	标签子类型
  //  label_lv1	str	标签一级分类
  //  label_lv2	str	标签二级分类
  //  label_lv3	str	标签三级分类
  //  score	float	分数/置信度
  //  event_day	str	时间分区

  // 昨日日期
  var YesterDay = ""
  // 今日日期 yyyyMMdd
  var ToDay = ""

  def main(args: Array[String]): Unit = {
    // 昨日日期 yyyyMMdd
    YesterDay = args(0)
    // 今日日期 yyyyMMdd
    ToDay = calcnDate(YesterDay, 1)

    val YesterTimeStamp = getTimeStampTake(YesterDay, 10)
    val ToDayTimeStamp = getTimeStampTake(ToDay, 10)
    println("T-1时间戳：" + YesterTimeStamp)
    println("T时间戳：" + ToDayTimeStamp)

    val spark = SparkSession.builder()
      .config("hive.exec.dynamic.partition.mode", "nonstrict")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.driver.allowMultipleContexts", true)
      .config("spark.sql.debug.maxToStringFields", "1000")
      .appName("labelTable801")
      .enableHiveSupport()
      //.master("local")
      .getOrCreate()

    import spark.implicits._

    val hadoopConf = spark.sparkContext.hadoopConfiguration
    val afsDir = "afs://pegasus.afs.baidu.com:9902"
    hadoopConf.set("spark.hadoop.hadoop.job.ugi", "baisheng,baishengfzxuyyazyqf")
    hadoopConf.set("spark.hadoop.fs.default.name", afsDir)

    // 读取search_pc_resultpage_query_click数据，分区36T左右
    val searchWiseSql =
      s"""
select
    uid,
    original_query as query
from
    things_turing_ns.ubs_search.view_search_wise_resultpage_query_click_crcdata
where
  event_day = '${YesterDay}'
  and log_source in ('se','tabse_se','secraft_se')
  and uid > 0
  and original_query != ''
""".stripMargin

    val searchWiseDf = spark.sql(searchWiseSql)
      .repartition(500)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("searchWiseDf count:" + searchWiseDf.count())
    searchWiseDf.createOrReplaceTempView("searchWise")

    // 读取search_pc_resultpage_query_click数据,分区大约4.3T
    val searchPcSql =
      s"""
select
    event_userid as uid,
    query
from
    ubs_search.search_pc_resultpage_query_click
where
  event_day = '${YesterDay}'
  and log_source = 'se'
  and event_userid > 0
  and query != ''
""".stripMargin

    val searchPcDf = spark.sql(searchPcSql)
      .repartition(200)
      .persist(StorageLevel.MEMORY_AND_DISK_SER)
    println("searchPcDf count:" + searchPcDf.count())
    searchPcDf.createOrReplaceTempView("searchPc")

    // 疆藏敏感词表与搜索数据关联
    val jiangzangCsv = readCsv(spark, "福临疆藏敏感词")
      .unionByName(readCsv(spark, "自建疆藏敏感词"))
      .unionByName(readCsv(spark, "贴吧疆藏敏感词"))
      .persist(StorageLevel.MEMORY_AND_DISK_SER) // 缓存敏感词表

    //多词匹配
    val multiwords = jiangzangCsv.filter(col("match_type").contains("多词"))
      .withColumn("voc_list", split(col("content"), "&"))
      .persist(StorageLevel.MEMORY_AND_DISK_SER) // 缓存多词匹配表

    //单词匹配
    val singlewords = jiangzangCsv.filter(col("match_type").contains("单词"))
      .persist(StorageLevel.MEMORY_AND_DISK_SER) // 缓存单词匹配表

    // 使用broadcast join优化小表join
    val multiBroad = spark.sparkContext.broadcast(multiwords)
    multiBroad.value.createOrReplaceTempView("multiwords")
    val singleBroad = spark.sparkContext.broadcast(singlewords)
    singleBroad.value.createOrReplaceTempView("singlewords")

    // 定义连接条件：检查 query 是否包含 voc_list 中的每一个元素
    val condition = expr("forall(T.voc_list, x -> instr(S.query, x) > 0)")

    //内容 单词匹配
    val contentPcDf = searchPcDf.as("S").join(
        broadcast(singleBroad.value.filter(col("match_type").contains("单词")).as("T")),
        col("S.query") contains col("T.content")
      )
      .repartition(200) // 调整分区数为200以匹配输入数据分区
    //contentPcDf.show(5,false)
    //println("PC疆藏敏感词单词匹配数据：" + contentPcDf.count())

    //内容 双词匹配
    val contentDouPcDf = searchPcDf.as("S")
      .join(
        broadcast(multiBroad.value.as("T")),
        condition, "inner"
      )
      .drop("voc_list")
      .repartition(200) // 调整分区数为200以匹配输入数据分区
    //contentDouPcDf.show(5,false)
    //println("PC疆藏敏感词双词匹配数据：" + contentDouPcDf.count())

    val jiangzangPcDf = mergeAndAddColumns("搜索", "日常行为", "搜索特征", "灰", "涉政", "疆藏涉政敏感内容", "80", contentPcDf, contentDouPcDf)
    println("searchPc疆藏敏感词共：" + jiangzangPcDf.count())
    jiangzangPcDf.show(5, false)

    //内容 单词匹配
    val contentWiseDf = searchWiseDf.as("S").join(
        broad @@ cast(singleBroad.value.filter(col("match_type").contains("单词")).as("T")),
        col("S.query") contains col("T.content")
      )
      .repartition(500) // 调整分区数为500以匹配输入数据分区
    contentWiseDf.show(5, false)
    println("Wise疆藏敏感词单词匹配数据：" + contentWiseDf.count())

    //内容 双词匹配
    /*val contentDouWiseDf = spark.sql(
      """
select
 S.uid,
 S.query,
 W.content as sensitive_word
from searchWise S
inner join multiwords W
on forall(W.voc_list, x -> instr(S.query, x) > 0)
""".stripMargin)
      .repartition(30)*/
    val contentDouWiseDf = searchWiseDf.as("S").join(
        broadcast(multiBroad.value.as("T")),
        condition, "inner"
      )
      .drop("voc_list")
      .repartition(500) // 调整分区数为500以匹配输入数据分区

    contentDouWiseDf.show(5, false)
    println("Wise疆藏敏感词双词匹配数据：" + contentDouWiseDf.count())

    val jiangzangWiseDf = mergeAndAddColumns("搜索", "日常行为", "搜索特征", "灰", "涉政", "疆藏涉政敏感内容", "80", contentWiseDf, contentDouWiseDf)
    println("searchWise疆藏敏感词共：" + jiangzangWiseDf.count())
    jiangzangWiseDf.show(5, false)

    spark.stop()
  }
}


```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.