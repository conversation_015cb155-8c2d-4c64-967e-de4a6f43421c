[12/11/2024, 9:47:57 AM]
"成功创建目录: root - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:47:57 AM]
"成功创建目录: prompt - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/prompt"
[12/11/2024, 9:47:57 AM]
"成功创建目录: model - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/model"
[12/11/2024, 9:47:57 AM]
"成功创建目录: logs - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:48:40 AM]
"成功创建目录: root - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:48:40 AM]
"成功创建目录: prompt - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/prompt"
[12/11/2024, 9:48:40 AM]
"成功创建目录: model - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/model"
[12/11/2024, 9:48:40 AM]
"成功创建目录: logs - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:49:28 AM]
"成功创建目录: root - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:49:28 AM]
"成功创建目录: prompt - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/prompt"
[12/11/2024, 9:49:28 AM]
"成功创建目录: model - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/model"
[12/11/2024, 9:49:28 AM]
"成功创建目录: logs - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:49:55 AM]
{
  "object": {
    "commandName": "comate.ievalue.prompt.createPrompt",
    "data": {
      "title": "你是一位专业的大数据(2024-12-11 09:49:36)",
      "prompt": "#指令描述\n\n生成下面的功能\n#任务要求\n\n1. 使用scala语言实现功能\n2. 保证功能正确性和效率\n3. 考虑到大数据处理场景的优化\n\n\n#输入\n\n\n\n生成的功能代码：",
      "system": "你是一位专业的大数据工程师，具备良好的大数据开发和调优经验，精通scala语言。"
    }
  }
}
[12/11/2024, 9:49:55 AM]
"提示词 你是一位专业的大数据(2024-12-11 09:49:36) 保存结果: create"
[12/11/2024, 9:50:15 AM]
"成功创建目录: root - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:50:15 AM]
"成功创建目录: prompt - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/prompt"
[12/11/2024, 9:50:15 AM]
"成功创建目录: model - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue/model"
[12/11/2024, 9:50:15 AM]
"成功创建目录: logs - /Users/<USER>/Desktop/baiduSql/baidu/bailing-sql/baidu/bailing/sql-exe-lib/.comate/ievalue"
[12/11/2024, 9:50:15 AM]
{
  "allModel": [
    {
      "data": {
        "model": "ernie-3.5-8k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-4.0-8k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-4.0-turbo-8k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-bot-4.0",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-bot",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-lite-8k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-speed-128k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "ernie-speed-8k",
        "modelParams": {
          "temperature": 0.8,
          "topP": 0.8
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-3.5-turbo-16k",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-3.5-turbo",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4-1106-preview",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4-turbo-preview",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4-turbo",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4-vision-preview",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4o-2024-08-06",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4o-mini",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    },
    {
      "data": {
        "model": "gpt-4o",
        "modelParams": {
          "temperature": 1,
          "topP": 1
        },
        "modelID": 0,
        "modelType": "PANDA"
      }
    }
  ]
}
[12/11/2024, 9:50:42 AM]
{
  "object": {
    "commandName": "comate.ievalue.prompt.selectModel",
    "data": {
      "modelKey": "ernie-4.0-turbo-8k"
    }
  }
}
[12/11/2024, 9:50:48 AM]
{
  "object": {
    "commandName": "comate.ievalue.prompt.runPrompt",
    "data": {
      "promptKey": "0_你是一位专业的大数据(2024-12-11 09:49:36)",
      "previousState": {
        "type": 1,
        "modelKey": "ernie-4.0-turbo-8k"
      }
    }
  }
}
