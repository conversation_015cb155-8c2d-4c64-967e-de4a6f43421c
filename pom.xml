<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baidu.bailing.sql.exe</groupId>
    <artifactId>sql-exe-lib</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>sql-exe-lib</name>
    <description>lib</description>

    <properties>
        <java.version>17</java.version>
        <source.encoding>UTF-8</source.encoding>
        <scala.version>2.12.15</scala.version>
        <!--<scala.version>2.11.8</scala.version>-->
        <slf4j.version>1.7.16</slf4j.version>
        <hadoop.version>********-baidu-pb2.5</hadoop.version>
        <!-- 区分厂版版本号 和 社区开源版版本号 -->
        <!--<spark.baidu.version>*******-baidu-SNAPSHOT</spark.baidu.version>-->
        <spark.baidu.version>*******-baidu</spark.baidu.version>
        <spark.version>2.4.2</spark.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bailing</groupId>
            <artifactId>lib-base</artifactId>
            <version>2.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baidu</groupId>
                    <artifactId>bns-client-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu</groupId>
            <artifactId>bns-client-spring</artifactId>
            <version>1.4.12-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.8.1</version>
        </dependency>

        <!-- json begin { -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.9.0</version>
            <!-- 厂版spark自带的gson版本过低 -->
            <scope>provided</scope>
        </dependency>

        <!-- Circe 核心库 -->
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-core_2.12</artifactId>
            <version>0.14.6</version>
        </dependency>

        <!-- Circe 通用库 -->
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-generic_2.12</artifactId>
            <version>0.14.6</version>
        </dependency>

        <!-- Circe 解析器 -->
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-parser_2.12</artifactId>
            <version>0.14.6</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.bailing</groupId>
            <artifactId>lib-ral</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <!-- hadoop/spark begin { -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_2.12</artifactId>
            <version>${spark.baidu.version}</version>
            <!--<scope>provided</scope>-->
        </dependency>
        <!-- 过滤emoji表情包-->
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_2.12</artifactId>
            <version>${spark.baidu.version}</version>
            <!--<scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>com.crealytics</groupId>
            <artifactId>spark-excel_2.12</artifactId>
            <version>0.13.7</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <!--es-->
        <!-- spark jars内没有该依赖, 打入最终jar包 -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch-spark-20_2.12</artifactId>
            <version>7.15.1</version>
            <!--<version>7.4.2</version>-->
            <!-- 去除es包中的scala-library, spark上采用了driver class path first 模式, 不去除会造成包版本冲突, 引发不稳定问题 -->
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-reflect</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.spark</groupId>
                    <artifactId>spark-core_2.12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.spark</groupId>
                    <artifactId>spark-sql_2.12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.spark</groupId>
                    <artifactId>spark-streaming_2.12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.spark</groupId>
                    <artifactId>spark-catalyst_2.12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.spark</groupId>
                    <artifactId>spark-yarn_2.12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  log4j      -->
<!--        <dependency>-->
<!--            <groupId>org.apache.logging.log4j</groupId>-->
<!--            <artifactId>log4j-core</artifactId>-->
<!--            <version>2.17.0</version>-->
<!--        </dependency>-->
        <!--es client-->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.4.2</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.4.2</version>
        </dependency>
        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <!--<version>8.0.31</version>-->
            <version>5.1.8</version>
        </dependency>
        <!--mongodb-->
        <dependency>
            <groupId>org.mongodb.spark</groupId>
            <artifactId>mongo-spark-connector_2.12</artifactId>
            <version>2.4.2</version>
        </dependency>
        <!--<dependency>
            <groupId>org.mongodb.spark</groupId>
            <artifactId>mongo-spark-connector_2.11</artifactId>
            <version>2.3.6</version>
        </dependency>-->
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>casbah-core_2.11</artifactId>
            <version>3.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.9</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.3.10</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>2.0.7.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <!-- The remote central repository -->
    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>sql-exe-lib</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                        <configuration>
                            <args>
                                <arg>-dependencyfile</arg>
                                <arg>${project.build.directory}/.scala_dependencies</arg>
                            </args>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <scalaVersion>2.12.15</scalaVersion>
                    <!--增加jvm参数，否则打包报错-->
                    <jvmArgs>
                        <jvmArg>-Xss4m</jvmArg>
                        <jvmArg>-Xms512m</jvmArg>
                        <jvmArg>-Xmx4096m</jvmArg>
                    </jvmArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <finalName>sql-exe-lib</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>

    </build>


</project>
