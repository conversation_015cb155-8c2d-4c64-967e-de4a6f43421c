#!/usr/bin/env bash

# Be sure your script exit whenever encounter errors
set -x
set -e
echo "start success"
project="sql-exe-lib" #这里是你的项目的名称

rm -rf output
mkdir  output

cp src/main/scala/com/baidu/sql/shells/run.sh output/
# cp src/main/scala/com/baidu/sql/datafile/ecom_black_list.csv output/
#打包
mvn -U clean package -Dmaven.test.skip=true
mv target/sql-exe-lib.jar output/

./postBuild.sh

echo "build success!!!"
